﻿using Microsoft.Win32;
using RecordMusicalNote;
using RecordMusicalNote.IRepository;
using RecordMusicalNote.Library;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Xml;

namespace MyWPF.Layout
{
    /// <summary>
    /// CalculateBD.xaml 的交互逻辑
    /// </summary>
    public partial class CalculateBD : Window
    {
        public CalculateBD()
        {
            InitializeComponent();
        }
        XmlDocument _xmlDoc;
        XmlElement _root;
        private void CalculateXDModel(string filePath)
        {

            IdolScene idolScene = new IdolScene();
            XmlNodeList listNodes = _root.SelectNodes("/Level/NoteInfo/Normal");
            idolScene.BPM = float.Parse(_root.SelectNodes("/Level/LevelInfo/BPM")[0].InnerText);
            idolScene.TotalMusicTime = float.Parse(_root.SelectNodes("/Level/LevelInfo/LevelTime")[0].InnerText);
            idolScene.ET = float.Parse(_root.SelectNodes("/Level/LevelInfo/EnterTimeAdjust")[0].InnerText);
            idolScene.TotalBarCount = int.Parse(_root.SelectNodes("/Level/LevelInfo/BarAmount")[0].InnerText);
            int BeginBarLen = int.Parse(_root.SelectNodes("/Level/LevelInfo/BeginBarLen")[0].InnerText);
            IList<IdolCellInfo> UsedCellInfos = new List<IdolCellInfo>();

            foreach (XmlNode item in listNodes[0])
            {
                foreach (var i in FillIdolNote(item, idolScene))
                {
                    UsedCellInfos.Add(i);
                }

            }
            CalculIdolTotalScoreNotInBQStatusAndNoTech(UsedCellInfos, idolScene.TotalBarCount, BeginBarLen);
            if (_idolScores != null && _idolScores.Count > 0)
            {
                IList<IdolScoreDetailInfo> statisticsScore = new List<IdolScoreDetailInfo>();
                _idolScores = _idolScores.OrderBy(a => a.CurrentCount).ToList();
                IScoreReposity repository = ScoreHelper.CreateRepositoryInstance();
                string artist = _root.SelectNodes("/Level/MusicInfo/Artist")[0].InnerText;
                int showTimeStartBar = int.Parse(_root.SelectNodes("/Level/SectionSeq/Section")[3].Attributes["startbar"].Value);
                if (repository.CheckIsExist(txtSongName.Text, artist, _mode))
                {
                    if (MessageBox.Show("已存在，是否继续?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.No) return;

                }
                if (repository.SaveScoreInfo(txtSongName.Text, artist, showTimeStartBar, _idolScores.Cast<IScoreDetailInfo>().ToList(), _mode))
                    MessageBox.Show("保存成功");
                else
                    MessageBox.Show("保存失败");
            }


        }


        IList<IIdolScoreDetailInfo> _idolScores;
        IList<IPinballScoreDetailInfo> _pinballScores;
        /// <summary>
        /// 返回星动没有曝气没有技能的最高分数 50-99combo实验室比排位少一分,这里首先统一向下取整
        /// </summary>
        /// <param name="usedCellInfos"></param>
        /// <param name="totalBar"></param>
        /// <param name="totalComboNum"></param>
        /// <returns></returns>
        private void CalculIdolTotalScoreNotInBQStatusAndNoTech(IList<IdolCellInfo> usedCellInfos, int totalBar, int BeginBarLen)
        {
            int currentCount = 0;
            int maxCount = 0;
            int currentCombo = 0;
            currentCount = usedCellInfos.FirstOrDefault().CurrentCount;
            maxCount = totalBar * 32;
            IList<IdolCellInfo> findedNote;
            _idolScores = new List<IIdolScoreDetailInfo>();
            for (int i = BeginBarLen * 32 - 1; i < maxCount; i++)
            {
                findedNote = usedCellInfos.Where(a => a.CurrentCount == i || (a.NoteType == 3 && a.CurrentCount <= i && a.LongNoteTypEndCount >= i)).ToList();
                foreach (var item in findedNote.OrderByDescending(a => a.NoteType))
                {
                    IdolScoreDetailInfo scoreObj = new IdolScoreDetailInfo();
                    if (item.NoteType == 1 || item.NoteType == 2)
                    {
                        currentCombo += 1;
                        scoreObj.CurrentCombo = currentCombo;
                        scoreObj.CurrentCount = i;
                        scoreObj.Bar = i / 32 + 1;
                        scoreObj.Pos = i % 32 * 2;
                        scoreObj.NoteType = item.NoteType;
                        _idolScores.Add(scoreObj);

                    }
                    else if (item.NoteType == 3)
                    {
                        if (item.IsFirstAccess)
                        {
                            if (usedCellInfos.Any(a => a.NoteType == 2 && a.CurrentCount == item.CurrentCount && a.EndTrackName == item.TargetTrackName))
                            {
                                item.IsFirstAccess = false;
                                continue;
                            }
                            currentCombo += 1;
                            scoreObj.CurrentCombo = currentCombo;
                            scoreObj.CurrentCount = i;
                            scoreObj.Bar = i / 32 + 1;
                            scoreObj.Pos = i % 32 * 2;
                            scoreObj.NoteType = item.NoteType;
                            _idolScores.Add(scoreObj);
                        }
                        else
                        {
                            int currentLongTypeCombo = Convert.ToInt32(Math.Ceiling(Convert.ToDecimal((i - item.CurrentCount + 1) * 2 / 4f + 1)));
                            int nextLongTypeCombo = Convert.ToInt32(Math.Ceiling(Convert.ToDecimal((i - item.CurrentCount + 2) * 2 / 4f + 1)));
                            if (i == item.LongNoteTypEndCount || nextLongTypeCombo > currentLongTypeCombo)
                            {
                                currentCombo += 1;
                                scoreObj.CurrentCombo = currentCombo;
                                scoreObj.CurrentCount = i;
                                scoreObj.Bar = i / 32 + 1;
                                scoreObj.Pos = i % 32 * 2;
                                scoreObj.NoteType = item.NoteType;
                                _idolScores.Add(scoreObj);

                            }
                        }
                        item.IsFirstAccess = false;
                    }

                }
            }
        }

        private IList<IdolCellInfo> FillIdolNote(XmlNode item, IdolScene idolScene)
        {
            IList<IdolCellInfo> UsedCellInfos = new List<IdolCellInfo>();
            if (!item.HasChildNodes)
            {
                UsedCellInfos.Add(IdolAssignment(item, idolScene));
            }
            else//combineNote
            {
                foreach (XmlNode combineItem in item.ChildNodes)
                {
                    UsedCellInfos.Add(IdolAssignment(combineItem, idolScene));
                }

            }
            return UsedCellInfos;
        }

        /// <summary>
        /// 星动赋值操作
        /// </summary>
        /// <param name="item"></param>
        /// <param name="idolScene"></param>
        private IdolCellInfo IdolAssignment(XmlNode item, IdolScene idolScene)
        {
            IdolCellInfo cellInfo = new IdolCellInfo();
            XmlAttributeCollection collection = item.Attributes;
            if (collection["note_type"].Value.ToLower() == "short")//0-50 combo 2600
            {
                string targetTrack = collection["target_track"].Value;
                int bar = int.Parse(collection["Bar"].Value);
                int pos = int.Parse(collection["Pos"].Value);
                if (targetTrack.ToLower() == "left1")
                {
                    cellInfo = idolScene.Left1.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (targetTrack.ToLower() == "left2")
                {
                    cellInfo = idolScene.Left2.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (targetTrack.ToLower() == "middle")
                {
                    cellInfo = idolScene.Middle.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();

                }
                else if (targetTrack.ToLower() == "right1")
                {
                    cellInfo = idolScene.Right1.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (targetTrack.ToLower() == "right2")
                {
                    cellInfo = idolScene.Right2.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();

                }
                cellInfo.NoteType = 1;

            }
            else if (collection["note_type"].Value.ToLower() == "long")
            {
                string targetTrack = collection["target_track"].Value;
                int bar = int.Parse(collection["Bar"].Value);
                int pos = int.Parse(collection["Pos"].Value);
                int startTotalPos = int.Parse(collection["Pos"].Value) + 64 * (int.Parse(collection["Bar"].Value));
                int endTotalPos = int.Parse(collection["EndPos"].Value) + 64 * int.Parse(collection["EndBar"].Value);
                int posDiff = endTotalPos - startTotalPos;
                if (targetTrack.ToLower() == "left1")
                {
                    cellInfo = idolScene.Left1.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (targetTrack.ToLower() == "left2")
                {
                    cellInfo = idolScene.Left2.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (targetTrack.ToLower() == "middle")
                {
                    cellInfo = idolScene.Middle.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();

                }
                else if (targetTrack.ToLower() == "right1")
                {
                    cellInfo = idolScene.Right1.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (targetTrack.ToLower() == "right2")
                {
                    cellInfo = idolScene.Right2.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();

                }
                cellInfo.NoteType = 3;
                cellInfo.LastedLength = posDiff;
            }
            else//slip
            {
                string targetTrack = collection["target_track"].Value;
                string endTrack = collection["end_track"].Value;
                int bar = int.Parse(collection["Bar"].Value);
                int pos = int.Parse(collection["Pos"].Value);
                if (targetTrack.ToLower() == "left1")
                {
                    cellInfo = idolScene.Left1.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();


                }
                else if (targetTrack.ToLower() == "left2")
                {
                    cellInfo = idolScene.Left2.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (targetTrack.ToLower() == "middle")
                {
                    cellInfo = idolScene.Middle.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();

                }
                else if (targetTrack.ToLower() == "right1")
                {
                    cellInfo = idolScene.Right1.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();

                }
                else if (targetTrack.ToLower() == "right2")
                {
                    cellInfo = idolScene.Right2.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                cellInfo.EndTrackName = endTrack;
                cellInfo.NoteType = 2;
            }
            return cellInfo;
        }
        string _filePath;
        int _mode;
        private void btnOpen_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            _filePath = dlg.FileName;
            txtFilePath.Text = _filePath;
            _xmlDoc = new XmlDocument();
            _xmlDoc.Load(_filePath);
            _root = _xmlDoc.DocumentElement;
            txtSongName.Text = _root.SelectNodes("/Level/MusicInfo/Title")[0].InnerText;
            if (_root.SelectNodes("/Level/NoteInfo/Normal")[0].InnerXml.ToLower().Contains("pinball"))
            {
                _mode = 2;
                modeDZ.IsChecked = true;
            }
            else if (_root.SelectNodes("/Level/NoteInfo/Normal")[0].ChildNodes[0].InnerXml.ToLower().Contains("flytrack"))
            {
                _mode = 3;
                modePP.IsChecked = true;
            }
            else if (_root.SelectNodes("/Level/LevelInfo")[0].InnerXml.ToLower().Contains("crescent"))
            {
                _mode = 4;
                modeXY.IsChecked = true;
            }
            else
            {
                _mode = 1;
                modeXD.IsChecked = true;
            }

        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(_filePath))
                return;
            if (_mode == 1)
                CalculateXDModel(_filePath);//星动
            else if (_mode == 2)
                CalculateDZModel(_filePath);//弹珠
            else if (_mode == 3)
                CalculatePPModel(_filePath);//泡泡
            else
                CalculateXYModel(_filePath);//弦月
        }
        /// <summary>
        /// 弦月
        /// </summary>
        /// <param name="filePath"></param>
        private void CalculateXYModel(string filePath)
        {

        }

        private IList<PinballCellInfo> FillPinballNote(XmlNode item, PinballScene pinballScene)
        {
            IList<PinballCellInfo> UsedCellInfos = new List<PinballCellInfo>();
            if (!item.HasChildNodes)
            {
                UsedCellInfos.Add(PinballAssignment(item, pinballScene));
            }
            return UsedCellInfos;
        }
        /// <summary>
        /// 弹珠赋值操作
        /// </summary>
        /// <param name="item"></param>
        /// <param name="idolScene"></param>
        private PinballCellInfo PinballAssignment(XmlNode item, PinballScene pinballScene)
        {
            PinballCellInfo cellInfo = new PinballCellInfo();
            XmlAttributeCollection collection = item.Attributes;
            if (collection["note_type"].Value.ToLower() == "pinballsingle")
            {
                string targetTrack = collection["EndArea"].Value;
                int bar = int.Parse(collection["Bar"].Value);
                int pos = int.Parse(collection["Pos"].Value);
                if (targetTrack == "1|2" || targetTrack == "2|1")
                {
                    cellInfo = pinballScene.Left.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (string.IsNullOrWhiteSpace(targetTrack))
                {
                    cellInfo = pinballScene.Middle.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (targetTrack == "3|4" || targetTrack == "4|3")
                {
                    cellInfo = pinballScene.Right.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();

                }
                cellInfo.NoteType = 1;

            }
            else if (collection["note_type"].Value.ToLower() == "pinballlong")
            {
                string targetTrack = collection["EndArea"].Value;
                int bar = int.Parse(collection["Bar"].Value);
                int pos = int.Parse(collection["Pos"].Value);
                int startTotalPos = int.Parse(collection["Pos"].Value) + 64 * (int.Parse(collection["Bar"].Value));
                int endTotalPos = int.Parse(collection["EndPos"].Value) + 64 * int.Parse(collection["EndBar"].Value);
                int posDiff = endTotalPos - startTotalPos;
                if (targetTrack == "1|2" || targetTrack == "2|1")
                {
                    cellInfo = pinballScene.Left.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (string.IsNullOrWhiteSpace(targetTrack))
                {
                    cellInfo = pinballScene.Middle.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (targetTrack == "3|4" || targetTrack == "4|3")
                {
                    cellInfo = pinballScene.Right.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();

                }
                cellInfo.NoteType = 4;
                cellInfo.LastedLength = posDiff;
            }
            else if (collection["note_type"].Value.ToLower() == "pinballslip")
            {
                string targetTrack = collection["EndArea"].Value;
                int bar = int.Parse(collection["Bar"].Value);
                int pos = int.Parse(collection["Pos"].Value);
                if (targetTrack == "1|2" || targetTrack == "2|1")
                {
                    cellInfo = pinballScene.Left.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (string.IsNullOrWhiteSpace(targetTrack))
                {
                    cellInfo = pinballScene.Middle.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (targetTrack == "3|4" || targetTrack == "4|3")
                {
                    cellInfo = pinballScene.Right.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();

                }
                if (!string.IsNullOrWhiteSpace(collection["Son"].Value))
                    cellInfo.SonId = int.Parse(collection["Son"].Value);
                else
                    cellInfo.SonId = 0;
                cellInfo.NoteType = 2;
            }
            else if (collection["note_type"].Value.ToLower() == "pinballseries")
            {
                string targetTrack = collection["EndArea"].Value;
                int bar = int.Parse(collection["Bar"].Value);
                int pos = int.Parse(collection["Pos"].Value);
                if (targetTrack == "1|2"|| targetTrack == "2|1")
                {
                    cellInfo = pinballScene.Left.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (string.IsNullOrWhiteSpace(targetTrack))
                {
                    cellInfo = pinballScene.Middle.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();
                }
                else if (targetTrack == "3|4"|| targetTrack == "4|3")
                {
                    cellInfo = pinballScene.Right.Where(a => a.CurrentBar == bar && a.CurrentPos == pos).FirstOrDefault();

                }
                if (!string.IsNullOrWhiteSpace(collection["Son"].Value))
                    cellInfo.SonId = int.Parse(collection["Son"].Value);
                else
                    cellInfo.SonId = 0;
                cellInfo.NoteType = 3;
            }
            int xmlNoteId = int.Parse(collection["ID"].Value);
            cellInfo.XMLNoteId = xmlNoteId;
            return cellInfo;
        }

        /// <summary>
        /// 泡泡
        /// </summary>
        /// <param name="filePath"></param>
        private void CalculatePPModel(string filePath)
        {

        }
        /// <summary>
        /// 弹珠
        /// </summary>
        /// <param name="filePath"></param>
        private void CalculateDZModel(string filePath)
        {
            PinballScene pinballScene = new PinballScene();
            XmlNodeList listNodes = _root.SelectNodes("/Level/NoteInfo/Normal");
            pinballScene.BPM = float.Parse(_root.SelectNodes("/Level/LevelInfo/BPM")[0].InnerText);
            pinballScene.TotalMusicTime = float.Parse(_root.SelectNodes("/Level/LevelInfo/LevelTime")[0].InnerText);
            pinballScene.ET = float.Parse(_root.SelectNodes("/Level/LevelInfo/EnterTimeAdjust")[0].InnerText);
            pinballScene.TotalBarCount = int.Parse(_root.SelectNodes("/Level/LevelInfo/BarAmount")[0].InnerText);
            int BeginBarLen = int.Parse(_root.SelectNodes("/Level/LevelInfo/BeginBarLen")[0].InnerText);
            IList<PinballCellInfo> UsedCellInfos = new List<PinballCellInfo>();

            foreach (XmlNode item in listNodes[0])
            {
                foreach (var i in FillPinballNote(item, pinballScene))
                {
                    UsedCellInfos.Add(i);
                }

            }
            CalculPinballTotalScoreNotInBQStatusAndNoTech(UsedCellInfos, pinballScene.TotalBarCount, BeginBarLen);
            if (_pinballScores != null && _pinballScores.Count > 0)
            {
                IList<IPinballScoreDetailInfo> statisticsScore = new List<IPinballScoreDetailInfo>();
                _pinballScores = _pinballScores.OrderBy(a => a.CurrentCount).ToList();
                IScoreReposity repository = ScoreHelper.CreateRepositoryInstance();
                string artist = _root.SelectNodes("/Level/MusicInfo/Artist")[0].InnerText;
                int showTimeStartBar = int.Parse(_root.SelectNodes("/Level/SectionSeq/Section")[3].Attributes["startbar"].Value);
                if (repository.CheckIsExist(txtSongName.Text, artist, _mode))
                {
                    if (MessageBox.Show("已存在，是否继续?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.No) return;

                }
                if (repository.SaveScoreInfo(txtSongName.Text, artist, showTimeStartBar, _pinballScores.Cast<IScoreDetailInfo>().ToList(), _mode))
                    MessageBox.Show("保存成功");
                else
                    MessageBox.Show("保存失败");
            }

        }

        private void CalculPinballTotalScoreNotInBQStatusAndNoTech(IList<PinballCellInfo> usedCellInfos, int totalBarCount, int beginBarLen)
        {
            int currentCount = 0;
            int maxCount = 0;
            int currentCombo = 0;
            currentCount = usedCellInfos.FirstOrDefault().CurrentCount;
            maxCount = totalBarCount * 32;
            IList<PinballCellInfo> findedNote;
            _pinballScores = new List<IPinballScoreDetailInfo>();
            for (int i = beginBarLen * 32 - 1; i < maxCount; i++)
            {
                findedNote = usedCellInfos.Where(a => a.CurrentCount == i || (a.NoteType == 4 && a.CurrentCount <= i && a.LongNoteTypEndCount >= i)).ToList();
                foreach (var item in findedNote.OrderByDescending(a => a.NoteType))
                {
                    IPinballScoreDetailInfo scoreObj = new PinballScoreDetailInfo();
                    if (item.NoteType == 1 || item.NoteType == 3)
                    {
                        currentCombo += 1;
                        scoreObj.CurrentCombo = currentCombo;
                        scoreObj.CurrentCount = i;
                        scoreObj.Bar = i / 32 + 1;
                        scoreObj.Pos = i % 32 * 2;
                        scoreObj.SonId = item.SonId;
                        scoreObj.NoteType = item.NoteType;
                        scoreObj.XMLNoteId = item.XMLNoteId;
                        _pinballScores.Add(scoreObj);

                    }
                    else if (item.NoteType == 2)//pinballSlip
                    {
                        currentCombo += 1;
                        scoreObj.CurrentCombo = currentCombo;
                        scoreObj.CurrentCount = i;
                        scoreObj.Bar = i / 32 + 1;
                        scoreObj.Pos = i % 32 * 2;
                        scoreObj.SonId = item.SonId;
                        scoreObj.NoteType = item.NoteType;
                        scoreObj.XMLNoteId = item.XMLNoteId;
                        _pinballScores.Add(scoreObj);

                    }
                    else if (item.NoteType == 4)
                    {
                        if (item.IsFirstAccess)
                        {
                            currentCombo += 1;
                            scoreObj.CurrentCombo = currentCombo;
                            scoreObj.CurrentCount = i;
                            scoreObj.Bar = i / 32 + 1;
                            scoreObj.Pos = i % 32 * 2;
                            scoreObj.NoteType = item.NoteType;
                            scoreObj.XMLNoteId = item.XMLNoteId;
                            _pinballScores.Add(scoreObj);
                        }
                        else
                        {
                            int currentLongTypeCombo = Convert.ToInt32(Math.Ceiling(Convert.ToDecimal((i - item.CurrentCount + 1) * 2 / 4f + 1)));
                            int nextLongTypeCombo = Convert.ToInt32(Math.Ceiling(Convert.ToDecimal((i - item.CurrentCount + 2) * 2 / 4f + 1)));
                            if (i == item.LongNoteTypEndCount || currentLongTypeCombo < nextLongTypeCombo)
                            {
                                currentCombo += 1;
                                scoreObj.CurrentCombo = currentCombo;
                                scoreObj.CurrentCount = i;
                                scoreObj.Bar = i / 32 + 1;
                                scoreObj.Pos = i % 32 * 2;
                                scoreObj.NoteType = item.NoteType;
                                scoreObj.XMLNoteId = item.XMLNoteId;
                                _pinballScores.Add(scoreObj);

                            }
                        }
                        item.IsFirstAccess = false;
                    }

                }
            }
        }
    }
}
