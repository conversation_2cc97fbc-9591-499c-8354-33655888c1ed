﻿using RecordMusicalNote;
using RecordMusicalNote.Library.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace MyWPF.Layout.BackGround
{
    public class ForeCommonMethod
    {
        public static ILevelInfo GetILevelInfo(string filePath)
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(filePath);
            XmlElement root = doc.DocumentElement;
            IdolRepository repository = new IdolRepository();
            ILevelInfo leveInfo = repository.CreateNewLevelInfo();
            XmlNodeList listNodes = null;
            listNodes = root.GetElementsByTagName("LevelInfo");
            XmlNodeList item = listNodes[0].ChildNodes;
            leveInfo.BPM = string.IsNullOrWhiteSpace(item[0].InnerText) ? 0 : float.Parse(item[0].InnerText);
            leveInfo.BeatPerBar = string.IsNullOrWhiteSpace(item[1].InnerText) ? 0 : int.Parse(item[1].InnerText);
            leveInfo.BeatLen = string.IsNullOrWhiteSpace(item[2].InnerText) ? 0 : int.Parse(item[2].InnerText);
            leveInfo.EnterTimeAdjust = string.IsNullOrWhiteSpace(item[3].InnerText) ? 0 : int.Parse(item[3].InnerText);
            leveInfo.NotePreShow = string.IsNullOrWhiteSpace(item[4].InnerText) ? 0 : int.Parse(item[4].InnerText);
            leveInfo.LevelTime = string.IsNullOrWhiteSpace(item[5].InnerText) ? 0 : int.Parse(item[5].InnerText);
            leveInfo.BarAmount = string.IsNullOrWhiteSpace(item[6].InnerText) ? 0 : int.Parse(item[6].InnerText);
            leveInfo.BeginBarLen = string.IsNullOrWhiteSpace(item[7].InnerText) ? 0 : int.Parse(item[7].InnerText);
            leveInfo.IsFourTrack = string.IsNullOrWhiteSpace(item[8].InnerText) ? false : bool.Parse(item[8].InnerText);
            leveInfo.TrackCount = string.IsNullOrWhiteSpace(item[9].InnerText) ? 0 : int.Parse(item[9].InnerText);
            leveInfo.LevelPreTime = string.IsNullOrWhiteSpace(item[10].InnerText) ? 0 : int.Parse(item[10].InnerText);
            leveInfo.Star = string.IsNullOrWhiteSpace(item[11].InnerText) ? 0 : int.Parse(item[11].InnerText);
            listNodes = root.GetElementsByTagName("MusicInfo");
            item = listNodes[0].ChildNodes;
            leveInfo.SongName = string.IsNullOrWhiteSpace(item[1].InnerText) ? " " : item[1].InnerText;
            leveInfo.Artist = string.IsNullOrWhiteSpace(item[2].InnerText) ? " " : item[2].InnerText;
            return leveInfo;
        }
    }
}
