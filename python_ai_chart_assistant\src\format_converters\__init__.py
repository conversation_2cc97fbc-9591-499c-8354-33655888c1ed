"""
格式转换器模块

支持多种音游格式的导入导出
"""

from .malody_converter import MalodyConverter
from .rhythm_master_converter import RhythmMasterConverter
from .osu_converter import OsuConverter
from .base_converter import BaseConverter

# 便捷导入函数
def import_chart_from_file(file_path: str):
    """
    从文件导入谱面数据

    Args:
        file_path: 谱面文件路径

    Returns:
        ChartData: 导入的谱面数据，失败时返回None
    """
    from pathlib import Path

    file_ext = Path(file_path).suffix.lower()

    if file_ext in ['.mc']:
        converter = MalodyConverter()
    elif file_ext in ['.imd', '.xml']:
        converter = RhythmMasterConverter()
    elif file_ext in ['.osu']:
        converter = OsuConverter()
    else:
        return None

    return converter.import_chart(file_path)

def get_converter(format_name: str):
    """
    根据格式名称获取转换器

    Args:
        format_name: 格式名称 ('malody', 'rhythm_master', 'osu')

    Returns:
        BaseConverter: 对应的转换器实例
    """
    converters = {
        'malody': MalodyConverter,
        'rhythm_master': RhythmMasterConverter,
        'osu': OsuConverter
    }

    if format_name not in converters:
        raise ValueError(f"不支持的格式: {format_name}")

    return converters[format_name]()

def get_available_converters():
    """
    获取所有可用的转换器

    Returns:
        Dict: 格式名称到转换器类的映射
    """
    return {
        'malody': MalodyConverter,
        'rhythm_master': RhythmMasterConverter,
        'osu': OsuConverter
    }

__all__ = [
    "MalodyConverter",
    "RhythmMasterConverter",
    "OsuConverter",
    "BaseConverter",
    "import_chart_from_file",
    "get_converter",
    "get_available_converters",
]
