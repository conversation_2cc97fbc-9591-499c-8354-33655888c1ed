using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Xml;
using Microsoft.Win32;
using System.Windows.Threading;
using MyWPF.Layout.AI;
using RecordMusicalNote;
using IOPath = System.IO.Path;

namespace MyWPF.Layout
{
    /// <summary>
    /// 音符编辑器
    /// </summary>
    public partial class NoteEditor : Window
    {
        private ObservableCollection<NoteViewModel> _notes;
        private List<NoteViewModel> _allNotes;
        private string _currentFilePath;
        private bool _isEditing = false;
        private NoteViewModel _editingNote;

        // 自动刷新相关
        private FileSystemWatcher _fileWatcher;
        private DispatcherTimer _refreshTimer;
        private DateTime _lastFileWriteTime;
        private bool _autoRefreshEnabled = true;

        public NoteEditor()
        {
            InitializeComponent();
            _notes = new ObservableCollection<NoteViewModel>();
            _allNotes = new List<NoteViewModel>();
            dgNotes.ItemsSource = _notes;

            // 设置默认值
            cmbNoteType.SelectedIndex = 0; // short
            cmbFromTrack.SelectedIndex = 0; // Left1
            cmbTargetTrack.SelectedIndex = 0; // Left1

            // 初始化自动刷新功能
            InitializeAutoRefresh();

            // 设置自动刷新默认开启（在控件完全初始化后）
            chkAutoRefresh.IsChecked = true;
            _autoRefreshEnabled = true;

            UpdateUI();
        }

        #region 音符数据模型
        public class NoteViewModel : INotifyPropertyChanged
        {
            private int _id;
            private int _bar;
            private double _pos;
            private string _fromTrack;
            private string _targetTrack;
            private string _endTrack;
            private string _noteType;
            private int? _endBar;
            private double? _endPos;

            public int ID
            {
                get => _id;
                set { _id = value; OnPropertyChanged(nameof(ID)); }
            }

            public int Bar
            {
                get => _bar;
                set { _bar = value; OnPropertyChanged(nameof(Bar)); OnPropertyChanged(nameof(TimeDisplay)); }
            }

            public double Pos
            {
                get => _pos;
                set { _pos = value; OnPropertyChanged(nameof(Pos)); OnPropertyChanged(nameof(TimeDisplay)); }
            }

            public string FromTrack
            {
                get => _fromTrack;
                set { _fromTrack = value; OnPropertyChanged(nameof(FromTrack)); }
            }

            public string TargetTrack
            {
                get => _targetTrack;
                set { _targetTrack = value; OnPropertyChanged(nameof(TargetTrack)); }
            }

            public string EndTrack
            {
                get => _endTrack;
                set { _endTrack = value; OnPropertyChanged(nameof(EndTrack)); }
            }

            public string NoteType
            {
                get => _noteType;
                set { _noteType = value; OnPropertyChanged(nameof(NoteType)); }
            }

            public int? EndBar
            {
                get => _endBar;
                set { _endBar = value; OnPropertyChanged(nameof(EndBar)); }
            }

            public double? EndPos
            {
                get => _endPos;
                set { _endPos = value; OnPropertyChanged(nameof(EndPos)); }
            }

            public string TimeDisplay => $"{Bar}:{Pos:F0}";

            public event PropertyChangedEventHandler PropertyChanged;
            protected virtual void OnPropertyChanged(string propertyName)
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
        }
        #endregion

        #region 文件操作
        private void BtnLoad_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                Title = "选择星动谱文件"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                LoadFile(openFileDialog.FileName);
            }
        }

        private void LoadFile(string filePath)
        {
            try
            {
                _currentFilePath = filePath;
                _allNotes.Clear();
                _notes.Clear();

                var doc = new XmlDocument();
                doc.Load(filePath);

                var noteNodes = doc.SelectNodes("//NoteInfo//Note");
                int id = 1;

                foreach (XmlNode node in noteNodes)
                {
                    // 处理不同的属性名称格式
                    string fromTrack, targetTrack, endTrack;

                    // 检查是否使用 from_track/target_track 格式
                    if (node.Attributes["from_track"] != null)
                    {
                        fromTrack = node.Attributes["from_track"].Value;
                        targetTrack = node.Attributes["target_track"]?.Value ?? fromTrack;
                        endTrack = node.Attributes["end_track"]?.Value ?? targetTrack;
                    }
                    // 检查是否使用 target_track/end_track 格式（slip音符）
                    else if (node.Attributes["target_track"] != null && node.Attributes["end_track"] != null)
                    {
                        fromTrack = node.Attributes["target_track"].Value;
                        targetTrack = node.Attributes["target_track"].Value;
                        endTrack = node.Attributes["end_track"].Value;
                    }
                    // 只有 target_track 的情况
                    else if (node.Attributes["target_track"] != null)
                    {
                        fromTrack = node.Attributes["target_track"].Value;
                        targetTrack = fromTrack;
                        endTrack = fromTrack;
                    }
                    else
                    {
                        fromTrack = "Left1";
                        targetTrack = "Left1";
                        endTrack = "Left1";
                    }

                    var note = new NoteViewModel
                    {
                        ID = id++,
                        Bar = int.Parse(node.Attributes["Bar"]?.Value ?? "1"),
                        Pos = double.Parse(node.Attributes["Pos"]?.Value ?? "0"),
                        FromTrack = fromTrack,
                        TargetTrack = targetTrack,
                        EndTrack = endTrack,
                        NoteType = node.Attributes["note_type"]?.Value ?? "short"
                    };

                    // 处理长音符的结束信息
                    if (node.Attributes["EndBar"] != null)
                        note.EndBar = int.Parse(node.Attributes["EndBar"].Value);
                    if (node.Attributes["EndPos"] != null)
                        note.EndPos = double.Parse(node.Attributes["EndPos"].Value);

                    _allNotes.Add(note);
                    _notes.Add(note);
                }

                txtStatus.Text = $"已加载文件: {IOPath.GetFileName(filePath)}";
                UpdateNoteCount();
                UpdateWindowTitle();

                // 设置文件监控
                SetupFileWatcher(filePath);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentFilePath))
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "XML文件 (*.xml)|*.xml",
                    Title = "保存星动谱文件"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    _currentFilePath = saveFileDialog.FileName;
                }
                else
                {
                    return;
                }
            }

            SaveFile(_currentFilePath, true); // 手动保存，显示成功消息
        }

        private void SaveFile(string filePath, bool showSuccessMessage = false)
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(_currentFilePath);

                // 找到NoteInfo节点并清空
                var noteInfoNode = doc.SelectSingleNode("//NoteInfo");
                if (noteInfoNode != null)
                {
                    noteInfoNode.RemoveAll();

                    // 创建Normal节点
                    var normalNode = doc.CreateElement("Normal");
                    noteInfoNode.AppendChild(normalNode);

                    // 按时间排序音符
                    var sortedNotes = _allNotes.OrderBy(n => n.Bar).ThenBy(n => n.Pos).ToList();

                    // 添加音符
                    int id = 1;
                    foreach (var note in sortedNotes)
                    {
                        var noteElement = doc.CreateElement("Note");
                        noteElement.SetAttribute("ID", id.ToString());
                        noteElement.SetAttribute("Bar", note.Bar.ToString());
                        noteElement.SetAttribute("Pos", note.Pos.ToString());
                        // 根据音符类型使用不同的属性名称
                        if (note.NoteType == "slip")
                        {
                            // slip音符使用 target_track 和 end_track
                            noteElement.SetAttribute("target_track", note.TargetTrack);
                            noteElement.SetAttribute("end_track", note.EndTrack ?? note.TargetTrack);
                        }
                        else
                        {
                            // 其他音符使用 from_track 和 target_track
                            noteElement.SetAttribute("from_track", note.FromTrack);
                            noteElement.SetAttribute("target_track", note.TargetTrack);
                            // 如果有EndTrack且与TargetTrack不同，也保存end_track
                            if (!string.IsNullOrEmpty(note.EndTrack) && note.EndTrack != note.TargetTrack)
                            {
                                noteElement.SetAttribute("end_track", note.EndTrack);
                            }
                        }
                        noteElement.SetAttribute("note_type", note.NoteType);

                        // 处理长音符
                        if (note.NoteType == "long")
                        {
                            if (note.EndBar.HasValue)
                                noteElement.SetAttribute("EndBar", note.EndBar.ToString());
                            if (note.EndPos.HasValue)
                                noteElement.SetAttribute("EndPos", note.EndPos.ToString());
                        }

                        normalNode.AppendChild(noteElement);
                        id++;
                    }
                }

                doc.Save(filePath);
                _currentFilePath = filePath; // 更新当前文件路径
                txtStatus.Text = $"文件已保存: {IOPath.GetFileName(filePath)}";
                UpdateWindowTitle();

                // 只有手动保存时才显示消息框
                if (showSuccessMessage)
                {
                    MessageBox.Show("文件保存成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        #endregion

        #region 音符操作
        private void BtnAdd_Click(object sender, RoutedEventArgs e)
        {
            _isEditing = false;
            _editingNote = null;
            ClearEditForm();
            txtStatus.Text = "添加新音符";
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            if (dgNotes.SelectedItem is NoteViewModel selectedNote)
            {
                _isEditing = true;
                _editingNote = selectedNote;
                LoadNoteToForm(selectedNote);
                txtStatus.Text = $"编辑音符 ID: {selectedNote.ID}";
            }
            else
            {
                MessageBox.Show("请先选择要编辑的音符", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            if (dgNotes.SelectedItem is NoteViewModel selectedNote)
            {
                var result = MessageBox.Show($"确定要删除音符 ID: {selectedNote.ID} 吗？",
                                           "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    _allNotes.Remove(selectedNote);
                    _notes.Remove(selectedNote);
                    UpdateNoteCount();
                    txtStatus.Text = $"已删除音符 ID: {selectedNote.ID}";

                    // 自动保存
                    if (!string.IsNullOrEmpty(_currentFilePath))
                    {
                        SaveFile(_currentFilePath);
                        txtStatus.Text += " 并已保存";
                    }
                }
            }
            else
            {
                MessageBox.Show("请先选择要删除的音符", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void BtnCopy_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                txtStatus.Text = "打开复制对话框...";

                var copyDialog = new CopyNotesDialog(_allNotes);
                copyDialog.Owner = this;
                copyDialog.ShowDialog();

                if (copyDialog.DialogResult)
                {
                    txtStatus.Text = "用户确认复制，开始执行...";
                    var settings = copyDialog.Settings;
                    CopyNotes(settings);
                }
                else
                {
                    txtStatus.Text = "用户取消了复制操作";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制音符失败: {ex.Message}\n\n详细错误:\n{ex.ToString()}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                txtStatus.Text = $"复制失败: {ex.Message}";
            }
        }
        #endregion

        #region 表单操作
        private void BtnSaveNote_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证输入
                if (!ValidateInput())
                    return;

                var note = _isEditing ? _editingNote : new NoteViewModel();

                // 设置音符属性
                note.Bar = int.Parse(txtBar.Text);
                note.Pos = double.Parse(txtPos.Text);
                note.FromTrack = ((ComboBoxItem)cmbFromTrack.SelectedItem).Content.ToString();
                note.TargetTrack = ((ComboBoxItem)cmbTargetTrack.SelectedItem).Content.ToString();
                note.NoteType = ((ComboBoxItem)cmbNoteType.SelectedItem).Content.ToString();

                // 处理滑动音符的EndTrack
                if (note.NoteType == "slip")
                {
                    // 对于滑动音符，EndTrack应该与TargetTrack不同
                    // 这里简化处理，可以根据需要添加UI控件来选择EndTrack
                    var tracks = new[] { "Left2", "Left1", "Right1", "Right2" };
                    var currentIndex = Array.IndexOf(tracks, note.TargetTrack);
                    if (currentIndex >= 0 && currentIndex < tracks.Length - 1)
                    {
                        note.EndTrack = tracks[currentIndex + 1]; // 滑动到下一个轨道
                    }
                    else if (currentIndex > 0)
                    {
                        note.EndTrack = tracks[currentIndex - 1]; // 滑动到上一个轨道
                    }
                    else
                    {
                        note.EndTrack = note.TargetTrack; // 默认情况
                    }
                }
                else
                {
                    note.EndTrack = note.TargetTrack; // 非滑动音符，EndTrack与TargetTrack相同
                }

                // 处理长音符
                if (note.NoteType == "long")
                {
                    if (!string.IsNullOrEmpty(txtEndBar.Text))
                        note.EndBar = int.Parse(txtEndBar.Text);
                    if (!string.IsNullOrEmpty(txtEndPos.Text))
                        note.EndPos = double.Parse(txtEndPos.Text);
                }
                else
                {
                    note.EndBar = null;
                    note.EndPos = null;
                }

                if (!_isEditing)
                {
                    // 添加新音符
                    note.ID = _allNotes.Count > 0 ? _allNotes.Max(n => n.ID) + 1 : 1;
                    _allNotes.Add(note);
                    _notes.Add(note);
                    txtStatus.Text = $"已添加音符 ID: {note.ID}";
                }
                else
                {
                    txtStatus.Text = $"已更新音符 ID: {note.ID}";
                }

                UpdateNoteCount();

                // 自动保存
                if (!string.IsNullOrEmpty(_currentFilePath))
                {
                    SaveFile(_currentFilePath);
                    txtStatus.Text += " 并已保存";
                }

                ClearEditForm();
                _isEditing = false;
                _editingNote = null;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存音符失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancelEdit_Click(object sender, RoutedEventArgs e)
        {
            ClearEditForm();
            _isEditing = false;
            _editingNote = null;
            txtStatus.Text = "已取消编辑";
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrEmpty(txtBar.Text) || !int.TryParse(txtBar.Text, out _))
            {
                MessageBox.Show("请输入有效的小节数", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrEmpty(txtPos.Text) || !double.TryParse(txtPos.Text, out _))
            {
                MessageBox.Show("请输入有效的位置", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (cmbFromTrack.SelectedItem == null)
            {
                MessageBox.Show("请选择起始轨道", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (cmbTargetTrack.SelectedItem == null)
            {
                MessageBox.Show("请选择目标轨道", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (cmbNoteType.SelectedItem == null)
            {
                MessageBox.Show("请选择音符类型", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // 验证长音符的结束信息
            var noteType = ((ComboBoxItem)cmbNoteType.SelectedItem).Content.ToString();
            if (noteType == "long")
            {
                if (!string.IsNullOrEmpty(txtEndBar.Text) && !int.TryParse(txtEndBar.Text, out _))
                {
                    MessageBox.Show("请输入有效的结束小节", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                if (!string.IsNullOrEmpty(txtEndPos.Text) && !double.TryParse(txtEndPos.Text, out _))
                {
                    MessageBox.Show("请输入有效的结束位置", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }
            }

            return true;
        }

        private void LoadNoteToForm(NoteViewModel note)
        {
            txtBar.Text = note.Bar.ToString();
            txtPos.Text = note.Pos.ToString();

            // 设置轨道选择
            SetComboBoxValue(cmbFromTrack, note.FromTrack);
            SetComboBoxValue(cmbTargetTrack, note.TargetTrack);
            SetComboBoxValue(cmbNoteType, note.NoteType);

            txtEndBar.Text = note.EndBar?.ToString() ?? "";
            txtEndPos.Text = note.EndPos?.ToString() ?? "";
        }

        private void SetComboBoxValue(ComboBox comboBox, string value)
        {
            foreach (ComboBoxItem item in comboBox.Items)
            {
                if (item.Content.ToString() == value)
                {
                    comboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private void ClearEditForm()
        {
            txtBar.Text = "";
            txtPos.Text = "";
            txtEndBar.Text = "";
            txtEndPos.Text = "";
            cmbFromTrack.SelectedIndex = 0;
            cmbTargetTrack.SelectedIndex = 0;
            cmbNoteType.SelectedIndex = 0;
        }
        #endregion

        #region 搜索功能
        private void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            string searchText = txtSearch.Text.Trim();
            if (string.IsNullOrEmpty(searchText))
            {
                BtnClearSearch_Click(sender, e);
                return;
            }

            _notes.Clear();
            var filteredNotes = _allNotes.Where(n =>
                n.Bar.ToString().Contains(searchText) ||
                n.Pos.ToString().Contains(searchText) ||
                (n.FromTrack ?? "").Contains(searchText) ||
                (n.TargetTrack ?? "").Contains(searchText) ||
                (n.NoteType ?? "").Contains(searchText) ||
                n.ID.ToString().Contains(searchText)
            ).ToList();

            foreach (var note in filteredNotes)
            {
                _notes.Add(note);
            }

            txtStatus.Text = $"搜索结果: {filteredNotes.Count} 个音符";
        }

        private void BtnClearSearch_Click(object sender, RoutedEventArgs e)
        {
            txtSearch.Text = "";
            _notes.Clear();
            foreach (var note in _allNotes)
            {
                _notes.Add(note);
            }
            UpdateNoteCount();
        }

        /// <summary>
        /// 刷新音符显示
        /// </summary>
        private void RefreshNoteDisplay()
        {
            // 如果有搜索条件，应用搜索过滤
            if (!string.IsNullOrEmpty(txtSearch.Text.Trim()))
            {
                BtnSearch_Click(null, null);
            }
            else
            {
                // 显示所有音符
                _notes.Clear();
                foreach (var note in _allNotes)
                {
                    _notes.Add(note);
                }
            }
        }
        #endregion

        #region 复制音符功能
        private void CopyNotes(CopyNotesDialog.CopySettings settings)
        {
            try
            {
                // 调试信息
                txtStatus.Text = $"开始复制：源({settings.SourceStartBar}-{settings.SourceEndBar}) 到 目标({settings.TargetStartBar}-{settings.TargetEndBar})";

                // 1. 获取源范围的音符
                var sourceNotes = _allNotes.Where(n => n.Bar >= settings.SourceStartBar && n.Bar <= settings.SourceEndBar).ToList();

                txtStatus.Text = $"找到源音符：{sourceNotes.Count}个";

                if (sourceNotes.Count == 0)
                {
                    MessageBox.Show($"源范围内没有找到音符\n范围：小节{settings.SourceStartBar}到{settings.SourceEndBar}\n总音符数：{_allNotes.Count}",
                                  "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 2. 如果需要，清空目标范围
                if (settings.ClearTargetFirst)
                {
                    var targetNotesToRemove = _allNotes.Where(n => n.Bar >= settings.TargetStartBar && n.Bar <= settings.TargetEndBar).ToList();
                    txtStatus.Text = $"清空目标区域：删除{targetNotesToRemove.Count}个音符";

                    foreach (var note in targetNotesToRemove)
                    {
                        _allNotes.Remove(note);
                        _notes.Remove(note);
                    }
                }

                // 3. 计算偏移量
                int barOffset = settings.TargetStartBar - settings.SourceStartBar;
                txtStatus.Text = $"偏移量：{barOffset}小节";

                // 4. 复制音符
                int copiedCount = 0;
                int nextId = _allNotes.Count > 0 ? _allNotes.Max(n => n.ID) + 1 : 1;

                foreach (var sourceNote in sourceNotes)
                {
                    int newBar = sourceNote.Bar + barOffset;

                    // 处理轨道映射
                    string fromTrack, targetTrack;
                    if (settings.IsMirrorCopy)
                    {
                        fromTrack = MirrorTrack(sourceNote.FromTrack);
                        targetTrack = MirrorTrack(sourceNote.TargetTrack);
                    }
                    else
                    {
                        fromTrack = sourceNote.FromTrack;
                        targetTrack = sourceNote.TargetTrack;
                    }

                    // 特殊处理slip音符：如果from和target相同，需要设置不同的轨道
                    if (sourceNote.NoteType == "slip" && fromTrack == targetTrack)
                    {
                        // 为slip音符创建合理的滑动轨道
                        if (fromTrack == "Left1") targetTrack = "Left2";
                        else if (fromTrack == "Left2") targetTrack = "Left1";
                        else if (fromTrack == "Right1") targetTrack = "Right2";
                        else if (fromTrack == "Right2") targetTrack = "Right1";
                    }

                    var newNote = new NoteViewModel
                    {
                        ID = nextId++,
                        Bar = newBar,
                        Pos = sourceNote.Pos,
                        FromTrack = fromTrack,
                        TargetTrack = targetTrack,
                        NoteType = sourceNote.NoteType,
                        EndBar = sourceNote.EndBar.HasValue ? sourceNote.EndBar + barOffset : null,
                        EndPos = sourceNote.EndPos
                    };

                    // 调试信息：显示复制的音符
                    Console.WriteLine($"复制音符：源Bar={sourceNote.Bar} -> 目标Bar={newBar}, 类型={sourceNote.NoteType}");

                    _allNotes.Add(newNote);
                    _notes.Add(newNote);
                    copiedCount++;
                }

                // 5. 验证复制结果 - 检查源范围是否被意外修改
                var sourceRangeNotes = _allNotes.Where(n => n.Bar >= settings.SourceStartBar && n.Bar <= settings.SourceEndBar).ToList();
                Console.WriteLine($"复制后源范围音符数量：{sourceRangeNotes.Count}");
                foreach (var note in sourceRangeNotes.Take(3)) // 只显示前3个
                {
                    Console.WriteLine($"源音符：Bar={note.Bar}, FromTrack={note.FromTrack}, TargetTrack={note.TargetTrack}, Type={note.NoteType}");
                }

                // 6. 更新界面
                UpdateNoteCount();
                RefreshNotesDisplay();

                // 6. 自动保存到原文件
                if (!string.IsNullOrEmpty(_currentFilePath))
                {
                    SaveFile(_currentFilePath);
                    txtStatus.Text = $"已复制 {copiedCount} 个音符并保存到文件";
                }
                else
                {
                    txtStatus.Text = $"已复制 {copiedCount} 个音符（请手动保存文件）";
                }

                string mirrorText = settings.IsMirrorCopy ? "（镜像）" : "";
                MessageBox.Show($"复制完成！\n复制了 {copiedCount} 个音符{mirrorText}\n\n" +
                              (!string.IsNullOrEmpty(_currentFilePath) ? "文件已自动保存" : "请手动保存文件"),
                              "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制音符时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string MirrorTrack(string track)
        {
            switch (track)
            {
                case "Left1": return "Right1";
                case "Left2": return "Right2";
                case "Right1": return "Left1";
                case "Right2": return "Left2";
                default: return track;
            }
        }

        private void RefreshNotesDisplay()
        {
            // 重新排序_allNotes
            var sortedNotes = _allNotes.OrderBy(n => n.Bar).ThenBy(n => n.Pos).ToList();
            _allNotes.Clear();
            _allNotes.AddRange(sortedNotes);

            // 刷新显示的_notes集合
            _notes.Clear();
            foreach (var note in _allNotes)
            {
                _notes.Add(note);
            }

            // 强制刷新DataGrid
            dgNotes.Items.Refresh();
        }
        #endregion

        #region 音频分析功能

        private void BtnAudioAnalysis_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 切换到音频分析标签页
                var tabControl = FindName("audioAnalysisPanel") as TabControl;
                if (tabControl?.Parent is TabControl parentTab)
                {
                    parentTab.SelectedIndex = 0; // 选择音频分析标签页
                }
                
                txtStatus.Text = "请在右侧面板加载音频文件进行分析";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开音频分析面板失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnImportSuggested_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var suggestedNotes = audioAnalysisPanel.GetSuggestedNotes();
                if (suggestedNotes == null || !suggestedNotes.Any())
                {
                    MessageBox.Show("没有找到建议音符，请先进行音频分析。", "提示", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var result = MessageBox.Show($"将导入 {suggestedNotes.Count} 个建议音符，是否继续？", 
                    "确认导入", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    ImportSuggestedNotes(suggestedNotes);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入建议音符失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSyncWithAudio_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 实现音频同步功能
                txtStatus.Text = "音频同步功能开发中...";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"音频同步失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnExportAnalysis_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "导出音频分析结果",
                    Filter = "JSON文件|*.json|文本文件|*.txt|所有文件|*.*",
                    DefaultExt = "json"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ExportAnalysisResults(saveFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出分析结果失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 导入建议音符
        /// </summary>
        private void ImportSuggestedNotes(List<Audio.AudioAnalysisPanel.SuggestedNote> suggestedNotes)
        {
            var importedCount = 0;
            var nextId = _allNotes.Count > 0 ? _allNotes.Max(n => n.ID) + 1 : 1;

            foreach (var suggested in suggestedNotes)
            {
                // 检查是否已存在相似的音符
                var existingNote = _allNotes.FirstOrDefault(n => 
                    n.Bar == suggested.Bar && 
                    Math.Abs(n.Pos - suggested.Pos) < 0.5 && 
                    n.TargetTrack == suggested.Track);

                if (existingNote == null)
                {
                    var newNote = new NoteViewModel
                    {
                        ID = nextId++,
                        Bar = suggested.Bar,
                        Pos = suggested.Pos,
                        FromTrack = suggested.Track,
                        TargetTrack = suggested.Track,
                        NoteType = suggested.NoteType,
                        EndBar = suggested.Bar,
                        EndPos = suggested.Pos
                    };

                    _allNotes.Add(newNote);
                    importedCount++;
                }
            }

            // 按时间排序
            _allNotes = _allNotes.OrderBy(n => n.Bar).ThenBy(n => n.Pos).ToList();
            
            // 刷新显示
            RefreshNotesDisplay();
            UpdateNoteCount();
            
            txtStatus.Text = $"成功导入 {importedCount} 个音符";
            btnImportSuggested.IsEnabled = false;
        }

        /// <summary>
        /// 导出分析结果
        /// </summary>
        private void ExportAnalysisResults(string filePath)
        {
            var analysisData = new
            {
                ExportTime = DateTime.Now,
                AudioFile = audioAnalysisPanel.GetCurrentAudioFile(),
                SuggestedNotes = audioAnalysisPanel.GetSuggestedNotes(),
                CurrentNotes = _allNotes.Select(n => new
                {
                    n.ID,
                    n.Bar,
                    n.Pos,
                    n.FromTrack,
                    n.TargetTrack,
                    n.NoteType,
                    n.EndBar,
                    n.EndPos
                }).ToList()
            };

            var json = Newtonsoft.Json.JsonConvert.SerializeObject(analysisData, Newtonsoft.Json.Formatting.Indented);

            File.WriteAllText(filePath, json, System.Text.Encoding.UTF8);
            txtStatus.Text = $"分析结果已导出到: {IOPath.GetFileName(filePath)}";
        }

        #endregion

        #region 辅助方法
        private void UpdateNoteCount()
        {
            txtNoteCount.Text = $"音符数量: {_allNotes.Count}";
        }

        private void UpdateUI()
        {
            // 更新音频分析相关按钮状态
            var hasSuggestedNotes = audioAnalysisPanel?.GetSuggestedNotes()?.Any() == true;
            btnImportSuggested.IsEnabled = hasSuggestedNotes;
            btnSyncWithAudio.IsEnabled = hasSuggestedNotes;
            btnExportAnalysis.IsEnabled = hasSuggestedNotes;
        }

        private void UpdateWindowTitle()
        {
            // 确保窗口已初始化
            if (this == null)
                return;

            if (!string.IsNullOrEmpty(_currentFilePath))
            {
                string autoRefreshStatus = _autoRefreshEnabled ? " [自动刷新]" : "";
                this.Title = $"音符编辑器 - {IOPath.GetFileName(_currentFilePath)}{autoRefreshStatus}";
            }
            else
            {
                this.Title = "音符编辑器";
            }
        }
        #endregion

        #region 自动刷新功能
        private void InitializeAutoRefresh()
        {
            // 初始化刷新定时器
            _refreshTimer = new DispatcherTimer();
            _refreshTimer.Interval = TimeSpan.FromMilliseconds(500); // 500ms延迟
            _refreshTimer.Tick += RefreshTimer_Tick;
        }

        private void SetupFileWatcher(string filePath)
        {
            // 清理旧的监控器
            if (_fileWatcher != null)
            {
                _fileWatcher.Dispose();
                _fileWatcher = null;
            }

            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return;

            try
            {
                string directory = IOPath.GetDirectoryName(filePath);
                string fileName = IOPath.GetFileName(filePath);

                _fileWatcher = new FileSystemWatcher(directory, fileName);
                _fileWatcher.NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size;
                _fileWatcher.Changed += FileWatcher_Changed;
                _fileWatcher.EnableRaisingEvents = _autoRefreshEnabled;

                // 记录当前文件的修改时间
                _lastFileWriteTime = File.GetLastWriteTime(filePath);

                txtStatus.Text += " (已启用文件监控)";
            }
            catch (Exception ex)
            {
                txtStatus.Text = $"文件监控启动失败: {ex.Message}";
            }
        }

        private void FileWatcher_Changed(object sender, FileSystemEventArgs e)
        {
            if (!_autoRefreshEnabled)
                return;

            // 使用定时器延迟刷新，避免频繁刷新
            Dispatcher.BeginInvoke(new Action(() =>
            {
                _refreshTimer.Stop();
                _refreshTimer.Start();
            }));
        }

        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            _refreshTimer.Stop();

            if (string.IsNullOrEmpty(_currentFilePath) || !File.Exists(_currentFilePath))
                return;

            try
            {
                // 检查文件是否真的被修改了
                var currentWriteTime = File.GetLastWriteTime(_currentFilePath);
                if (currentWriteTime <= _lastFileWriteTime)
                    return;

                _lastFileWriteTime = currentWriteTime;

                // 重新加载文件
                LoadFile(_currentFilePath);
                txtStatus.Text = $"文件已自动刷新: {DateTime.Now:HH:mm:ss}";
            }
            catch (Exception ex)
            {
                txtStatus.Text = $"自动刷新失败: {ex.Message}";
            }
        }

        private void ChkAutoRefresh_Changed(object sender, RoutedEventArgs e)
        {
            // 检查控件是否已初始化
            if (chkAutoRefresh == null)
                return;

            _autoRefreshEnabled = chkAutoRefresh.IsChecked == true;

            if (_fileWatcher != null)
            {
                _fileWatcher.EnableRaisingEvents = _autoRefreshEnabled;
            }

            UpdateWindowTitle();

            // 检查txtStatus是否已初始化
            if (txtStatus != null)
            {
                txtStatus.Text = _autoRefreshEnabled ? "已启用自动刷新" : "已禁用自动刷新";
            }
        }

        private void BtnManualRefresh_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentFilePath))
            {
                MessageBox.Show("请先加载一个文件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                LoadFile(_currentFilePath);
                txtStatus.Text = $"手动刷新完成: {DateTime.Now:HH:mm:ss}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"手动刷新失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            // 清理资源
            if (_fileWatcher != null)
            {
                _fileWatcher.Dispose();
                _fileWatcher = null;
            }

            if (_refreshTimer != null)
            {
                _refreshTimer.Stop();
                _refreshTimer = null;
            }

            base.OnClosed(e);
        }
        #endregion

        #region AI辅助功能
        private void BtnAIAssist_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentFilePath))
            {
                MessageBox.Show("请先加载一个MIDI文件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                // 获取当前的音符作为上下文
                var existingNotes = _allNotes.Select(n => ConvertToIIdol(n)).ToList();

                // 获取音频分析结果
                var audioAnalysis = GetAudioAnalysisFromPanel();

                // 打开AI辅助对话框，传递音频分析数据
                var aiDialog = new AIAssistDialog(_currentFilePath, existingNotes, audioAnalysis);
                aiDialog.Owner = this;

                if (aiDialog.ShowDialog() == true)
                {
                    // 用户选择导出音符
                    var generatedNotes = aiDialog.GeneratedNotes;

                    if (generatedNotes.Any())
                    {
                        // 询问用户是否要添加到当前谱面
                        var result = MessageBox.Show(
                            $"AI生成了 {generatedNotes.Count} 个音符，是否要添加到当前谱面？\n\n" +
                            "选择'是'将添加到现有音符中\n" +
                            "选择'否'将替换所有现有音符\n" +
                            "选择'取消'将不做任何更改",
                            "添加AI生成的音符",
                            MessageBoxButton.YesNoCancel,
                            MessageBoxImage.Question);

                        if (result == MessageBoxResult.Yes)
                        {
                            // 添加到现有音符
                            AddAIGeneratedNotes(generatedNotes);
                        }
                        else if (result == MessageBoxResult.No)
                        {
                            // 替换所有音符
                            ReplaceWithAIGeneratedNotes(generatedNotes);
                        }
                        // 取消则不做任何操作
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"AI辅助功能出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 将NoteViewModel转换为IIdol接口
        /// </summary>
        private IIdol ConvertToIIdol(NoteViewModel noteViewModel)
        {
            var idol = new RecordMusicalNote.Library.Idol();
            idol.Bar = noteViewModel.Bar;
            idol.Pos = noteViewModel.Pos;
            idol.FromTrack = noteViewModel.FromTrack;
            idol.TargetTrack = noteViewModel.TargetTrack;
            idol.EndTrack = noteViewModel.EndTrack;
            idol.NoteType = noteViewModel.NoteType;
            idol.EndBar = noteViewModel.EndBar ?? noteViewModel.Bar;
            idol.EndPos = noteViewModel.EndPos ?? noteViewModel.Pos;
            return idol;
        }

        /// <summary>
        /// 添加AI生成的音符到现有谱面
        /// </summary>
        private void AddAIGeneratedNotes(List<IIdol> generatedNotes)
        {
            try
            {
                int addedCount = 0;

                foreach (var aiNote in generatedNotes)
                {
                    // 检查是否与现有音符冲突
                    bool hasConflict = _allNotes.Any(existing =>
                        existing.Bar == aiNote.Bar &&
                        existing.Pos == aiNote.Pos &&
                        existing.TargetTrack == aiNote.TargetTrack);

                    if (!hasConflict)
                    {
                        var noteViewModel = new NoteViewModel
                        {
                            Bar = aiNote.Bar,
                            Pos = aiNote.Pos,
                            FromTrack = aiNote.FromTrack,
                            TargetTrack = aiNote.TargetTrack,
                            EndTrack = aiNote.EndTrack,
                            NoteType = aiNote.NoteType,
                            EndBar = aiNote.EndBar,
                            EndPos = aiNote.EndPos
                        };

                        _allNotes.Add(noteViewModel);
                        addedCount++;
                    }
                }

                // 刷新显示
                RefreshNoteDisplay();
                UpdateNoteCount();

                txtStatus.Text = $"AI辅助: 成功添加 {addedCount} 个音符 (跳过 {generatedNotes.Count - addedCount} 个冲突音符)";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加AI音符失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 用AI生成的音符替换所有现有音符
        /// </summary>
        private void ReplaceWithAIGeneratedNotes(List<IIdol> generatedNotes)
        {
            try
            {
                // 清空现有音符
                _allNotes.Clear();

                // 添加AI生成的音符
                foreach (var aiNote in generatedNotes)
                {
                    var noteViewModel = new NoteViewModel
                    {
                        Bar = aiNote.Bar,
                        Pos = aiNote.Pos,
                        FromTrack = aiNote.FromTrack,
                        TargetTrack = aiNote.TargetTrack,
                        EndTrack = aiNote.EndTrack,
                        NoteType = aiNote.NoteType,
                        EndBar = aiNote.EndBar,
                        EndPos = aiNote.EndPos
                    };

                    _allNotes.Add(noteViewModel);
                }

                // 刷新显示
                RefreshNoteDisplay();
                UpdateNoteCount();

                txtStatus.Text = $"AI辅助: 已替换为 {generatedNotes.Count} 个AI生成的音符";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"替换AI音符失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// AI聊天按钮点击
        /// </summary>
        private void BtnAIChat_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentFilePath))
            {
                MessageBox.Show("请先加载一个MIDI文件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                // 获取当前的音符作为上下文
                var currentNotes = _allNotes.Select(n => ConvertToIIdol(n)).ToList();

                // 获取音频分析结果
                var audioAnalysis = GetAudioAnalysisFromPanel();

                // 打开AI聊天对话框，传递音频分析和XML文件内容
                var chatDialog = new AIChatDialog(_currentFilePath, currentNotes, audioAnalysis);
                chatDialog.Owner = this;

                if (chatDialog.ShowDialog() == true)
                {
                    // 用户关闭对话框后，检查是否有生成的音符
                    var generatedNotes = chatDialog.GeneratedNotes;

                    if (generatedNotes.Any())
                    {
                        // 询问用户是否要应用生成的音符
                        var result = MessageBox.Show(
                            $"AI对话中生成了 {generatedNotes.Count} 个音符，是否要应用到谱面中？\n\n" +
                            "选择'是'将添加到现有音符中\n" +
                            "选择'否'将不做任何更改",
                            "应用AI生成的音符",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question);

                        if (result == MessageBoxResult.Yes)
                        {
                            // 添加到现有音符（聊天模式通常是增量添加）
                            AddAIGeneratedNotes(generatedNotes);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"AI聊天功能出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// AI配置按钮点击
        /// </summary>
        private void BtnAIConfig_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 打开AI配置窗口
                var configWindow = new AIConfigWindow();
                configWindow.Owner = this;

                if (configWindow.ShowDialog() == true)
                {
                    txtStatus.Text = "AI配置已更新";

                    // 可以选择重新初始化AI服务或提示用户重启应用
                    MessageBox.Show("AI配置已保存！新配置将在下次使用AI功能时生效。",
                                  "配置成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开AI配置窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 从音频分析面板获取分析结果
        /// </summary>
        private MyWPF.Layout.AI.AudioAnalysisData GetAudioAnalysisFromPanel()
        {
            try
            {
                // 获取音频分析面板
                var analysisPanel = audioAnalysisPanel;
                if (analysisPanel == null)
                {
                    return null;
                }

                // 检查是否有分析结果
                if (!analysisPanel.HasAnalysisResults())
                {
                    return null;
                }

                // 获取分析数据
                return analysisPanel.GetAudioAnalysisData();
            }
            catch (Exception ex)
            {
                // 记录错误但不中断流程
                System.Diagnostics.Debug.WriteLine($"获取音频分析结果失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 分析当前谱面，生成完整的上下文信息
        /// </summary>
        private MyWPF.Layout.AI.ChartContextData AnalyzeCurrentChart()
        {
            try
            {
                var chartContext = new MyWPF.Layout.AI.ChartContextData
                {
                    FilePath = _currentFilePath,
                    FileName = string.IsNullOrEmpty(_currentFilePath) ? "未保存" : System.IO.Path.GetFileName(_currentFilePath),
                    RawXmlContent = GetRawXmlContent(), // 添加原始XML内容
                    Statistics = new MyWPF.Layout.AI.ChartStatistics(),
                    Structure = new MyWPF.Layout.AI.ChartStructureAnalysis(),
                    Difficulty = new MyWPF.Layout.AI.ChartDifficultyAnalysis()
                };

                if (_allNotes == null || !_allNotes.Any())
                {
                    return chartContext;
                }

                var notes = _allNotes.Select(n => ConvertToIIdol(n)).ToList();

                chartContext.AllNotes = notes;
                chartContext.Statistics = AnalyzeChartStatistics(notes);
                chartContext.Structure = AnalyzeChartStructure(notes);
                chartContext.Difficulty = AnalyzeChartDifficulty(notes);
                chartContext.Patterns = AnalyzeChartPatterns(notes);
                chartContext.DetailedNoteInfo = AnalyzeDetailedNoteInfo(notes);

                return chartContext;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"谱面分析失败: {ex.Message}");
                return new MyWPF.Layout.AI.ChartContextData();
            }
        }

        /// <summary>
        /// 获取原始XML内容
        /// </summary>
        private string GetRawXmlContent()
        {
            try
            {
                if (string.IsNullOrEmpty(_currentFilePath) || !System.IO.File.Exists(_currentFilePath))
                    return null;

                return System.IO.File.ReadAllText(_currentFilePath, System.Text.Encoding.UTF8);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取XML文件失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 分析详细音符信息（优化版本）
        /// </summary>
        private MyWPF.Layout.AI.DetailedNoteInfo AnalyzeDetailedNoteInfo(List<IIdol> notes)
        {
            var detailedInfo = new MyWPF.Layout.AI.DetailedNoteInfo();

            if (!notes.Any()) return detailedInfo;

            // 对于大量音符，限制分析数量以提高性能
            var maxAnalysisNotes = 500; // 限制最多分析500个音符
            var notesToAnalyze = notes.Count > maxAnalysisNotes ?
                notes.OrderBy(n => n.Bar).ThenBy(n => n.Pos).Take(maxAnalysisNotes).ToList() :
                notes;

            // 按小节分组的详细信息（只保留关键信息）
            detailedInfo.NotesByBar = notesToAnalyze.GroupBy(n => n.Bar)
                .Take(50) // 最多分析50个小节
                .ToDictionary(g => g.Key, g => g.Select(n => new MyWPF.Layout.AI.NoteDetail
                {
                    Bar = n.Bar,
                    Pos = (int)n.Pos,
                    FromTrack = n.FromTrack,
                    TargetTrack = n.TargetTrack,
                    EndTrack = n.EndTrack,
                    NoteType = n.NoteType,
                    EndBar = n.EndBar,
                    EndPos = (int)n.EndPos
                }).ToList());

            // 预计算时间参数
            var audioAnalysis = GetAudioAnalysisFromPanel();
            var bpm = audioAnalysis?.BPM ?? 120.0;
            var secondsPerBar = 240.0 / bpm;

            // 时间轴分析（限制数量）
            detailedInfo.TimelineAnalysis = notesToAnalyze.Take(200) // 最多分析200个音符的时间轴
                .Select(n => new MyWPF.Layout.AI.NoteTimeInfo
                {
                    Bar = n.Bar,
                    Pos = (int)n.Pos,
                    EstimatedTime = (n.Bar - 1) * secondsPerBar + ((int)n.Pos / 64.0) * secondsPerBar,
                    NoteType = n.NoteType,
                    Track = n.TargetTrack
                }).OrderBy(t => t.EstimatedTime).ToList();

            // 轨道序列分析（限制数量）
            detailedInfo.TrackSequences = AnalyzeTrackSequences(notesToAnalyze.Take(100).ToList());

            // 音符间隔分析（限制数量）
            detailedInfo.NoteIntervals = AnalyzeNoteIntervals(notesToAnalyze.Take(100).ToList());

            return detailedInfo;
        }

        /// <summary>
        /// 计算音符的估算时间（秒）
        /// </summary>
        private double CalculateNoteTime(int bar, int pos)
        {
            // 获取音频分析的BPM，如果没有则使用默认120
            var audioAnalysis = GetAudioAnalysisFromPanel();
            var bpm = audioAnalysis?.BPM ?? 120.0;

            // 计算每小节的时间（秒）
            var secondsPerBar = 240.0 / bpm; // 4/4拍

            // 计算音符时间
            var barTime = (bar - 1) * secondsPerBar;
            var posTime = (pos / 64.0) * secondsPerBar; // 假设64分音符精度

            return barTime + posTime;
        }

        /// <summary>
        /// 分析轨道序列（优化版本）
        /// </summary>
        private List<MyWPF.Layout.AI.TrackSequence> AnalyzeTrackSequences(List<IIdol> notes)
        {
            var sequences = new List<MyWPF.Layout.AI.TrackSequence>();
            var sortedNotes = notes.OrderBy(n => n.Bar).ThenBy(n => n.Pos).ToList();

            if (sortedNotes.Count < 2) return sequences;

            // 预计算时间参数
            var audioAnalysis = GetAudioAnalysisFromPanel();
            var bpm = audioAnalysis?.BPM ?? 120.0;
            var secondsPerBar = 240.0 / bpm;

            for (int i = 0; i < sortedNotes.Count - 1; i++)
            {
                var current = sortedNotes[i];
                var next = sortedNotes[i + 1];

                // 内联时间计算
                var currentTime = (current.Bar - 1) * secondsPerBar + ((int)current.Pos / 64.0) * secondsPerBar;
                var nextTime = (next.Bar - 1) * secondsPerBar + ((int)next.Pos / 64.0) * secondsPerBar;

                sequences.Add(new MyWPF.Layout.AI.TrackSequence
                {
                    FromTrack = current.TargetTrack,
                    ToTrack = next.TargetTrack,
                    TimeInterval = nextTime - currentTime,
                    BarInterval = next.Bar - current.Bar,
                    PosInterval = (next.Bar == current.Bar) ? (int)(next.Pos - current.Pos) : -1
                });
            }

            return sequences;
        }

        /// <summary>
        /// 分析音符间隔（优化版本）
        /// </summary>
        private List<MyWPF.Layout.AI.NoteInterval> AnalyzeNoteIntervals(List<IIdol> notes)
        {
            var intervals = new List<MyWPF.Layout.AI.NoteInterval>();
            var sortedNotes = notes.OrderBy(n => n.Bar).ThenBy(n => n.Pos).ToList();

            if (sortedNotes.Count < 2) return intervals;

            // 预计算时间参数
            var audioAnalysis = GetAudioAnalysisFromPanel();
            var bpm = audioAnalysis?.BPM ?? 120.0;
            var secondsPerBar = 240.0 / bpm;

            for (int i = 0; i < sortedNotes.Count - 1; i++)
            {
                var current = sortedNotes[i];
                var next = sortedNotes[i + 1];

                // 内联时间计算
                var currentTime = (current.Bar - 1) * secondsPerBar + ((int)current.Pos / 64.0) * secondsPerBar;
                var nextTime = (next.Bar - 1) * secondsPerBar + ((int)next.Pos / 64.0) * secondsPerBar;
                var timeInterval = nextTime - currentTime;

                intervals.Add(new MyWPF.Layout.AI.NoteInterval
                {
                    FromNote = $"第{current.Bar}小节位置{(int)current.Pos}",
                    ToNote = $"第{next.Bar}小节位置{(int)next.Pos}",
                    TimeInterval = timeInterval,
                    IntervalType = GetIntervalType(timeInterval),
                    TrackChange = current.TargetTrack != next.TargetTrack
                });
            }

            return intervals;
        }

        /// <summary>
        /// 获取间隔类型
        /// </summary>
        private string GetIntervalType(double timeInterval)
        {
            if (timeInterval < 0.2) return "极快";
            if (timeInterval < 0.4) return "快速";
            if (timeInterval < 0.8) return "中等";
            if (timeInterval < 1.6) return "慢速";
            return "很慢";
        }

        /// <summary>
        /// 分析谱面统计信息
        /// </summary>
        private MyWPF.Layout.AI.ChartStatistics AnalyzeChartStatistics(List<IIdol> notes)
        {
            var stats = new MyWPF.Layout.AI.ChartStatistics();

            if (!notes.Any()) return stats;

            stats.TotalNotes = notes.Count;
            stats.TotalBars = notes.Max(n => n.Bar);

            // 音符类型统计
            stats.NoteTypeCount = notes.GroupBy(n => n.NoteType)
                .ToDictionary(g => g.Key, g => g.Count());

            // 轨道分布统计
            stats.TrackDistribution = notes.GroupBy(n => n.TargetTrack)
                .ToDictionary(g => g.Key, g => g.Count());

            stats.AverageNotesPerBar = (double)stats.TotalNotes / stats.TotalBars;

            // 估算时长（假设4/4拍，120 BPM）
            var audioAnalysis = GetAudioAnalysisFromPanel();
            if (audioAnalysis != null)
            {
                stats.EstimatedDuration = audioAnalysis.Duration;
                stats.NoteDensity = stats.TotalNotes / audioAnalysis.Duration.TotalSeconds;
            }
            else
            {
                // 默认估算：4拍/小节，120 BPM
                var estimatedSeconds = stats.TotalBars * 2.0; // 120 BPM = 2秒/小节
                stats.EstimatedDuration = TimeSpan.FromSeconds(estimatedSeconds);
                stats.NoteDensity = stats.TotalNotes / estimatedSeconds;
            }

            // 每小节密度分析
            stats.BarDensities = notes.GroupBy(n => n.Bar)
                .Select(g => new MyWPF.Layout.AI.BarDensity
                {
                    Bar = g.Key,
                    NoteCount = g.Count(),
                    Density = g.Count() / 2.0, // 假设每小节2秒
                    DifficultyLevel = GetDifficultyLevel(g.Count())
                }).ToList();

            return stats;
        }

        /// <summary>
        /// 根据音符数量判断难度等级
        /// </summary>
        private string GetDifficultyLevel(int noteCount)
        {
            if (noteCount <= 2) return "Easy";
            if (noteCount <= 4) return "Normal";
            if (noteCount <= 8) return "Hard";
            return "Expert";
        }

        /// <summary>
        /// 分析谱面结构
        /// </summary>
        private MyWPF.Layout.AI.ChartStructureAnalysis AnalyzeChartStructure(List<IIdol> notes)
        {
            var structure = new MyWPF.Layout.AI.ChartStructureAnalysis();

            if (!notes.Any()) return structure;

            var maxBar = notes.Max(n => n.Bar);
            var barNotes = notes.GroupBy(n => n.Bar).ToDictionary(g => g.Key, g => g.Count());

            // 找出空小节
            for (int bar = 1; bar <= maxBar; bar++)
            {
                if (!barNotes.ContainsKey(bar) || barNotes[bar] == 0)
                {
                    structure.EmptyBars.Add(bar);
                }
            }

            // 找出高密度小节（超过平均值2倍）
            var avgNotesPerBar = notes.Count / (double)maxBar;
            structure.HighDensityBars = barNotes
                .Where(kvp => kvp.Value > avgNotesPerBar * 2)
                .Select(kvp => kvp.Key)
                .ToList();

            // 分析段落（基于密度变化）
            structure.Sections = AnalyzeSections(barNotes, maxBar);

            // 分析间隔
            structure.Breaks = AnalyzeBreaks(structure.EmptyBars);

            return structure;
        }

        /// <summary>
        /// 分析谱面段落
        /// </summary>
        private List<MyWPF.Layout.AI.ChartSection> AnalyzeSections(Dictionary<int, int> barNotes, int maxBar)
        {
            var sections = new List<MyWPF.Layout.AI.ChartSection>();

            // 简单的段落划分逻辑：基于密度变化
            var currentSection = new MyWPF.Layout.AI.ChartSection { StartBar = 1 };
            var currentDensity = 0.0;

            for (int bar = 1; bar <= maxBar; bar++)
            {
                var noteCount = barNotes.ContainsKey(bar) ? barNotes[bar] : 0;

                // 如果密度变化超过50%，认为是新段落
                if (bar > 1 && Math.Abs(noteCount - currentDensity) > currentDensity * 0.5)
                {
                    currentSection.EndBar = bar - 1;
                    currentSection.NoteCount = GetNotesInRange(currentSection.StartBar, currentSection.EndBar, barNotes);
                    currentSection.Name = GetSectionName(sections.Count, currentSection.NoteCount);
                    currentSection.Characteristics = GetSectionCharacteristics(currentSection.NoteCount, currentSection.EndBar - currentSection.StartBar + 1);

                    sections.Add(currentSection);

                    currentSection = new MyWPF.Layout.AI.ChartSection { StartBar = bar };
                }

                currentDensity = noteCount;
            }

            // 添加最后一个段落
            if (currentSection.StartBar <= maxBar)
            {
                currentSection.EndBar = maxBar;
                currentSection.NoteCount = GetNotesInRange(currentSection.StartBar, currentSection.EndBar, barNotes);
                currentSection.Name = GetSectionName(sections.Count, currentSection.NoteCount);
                currentSection.Characteristics = GetSectionCharacteristics(currentSection.NoteCount, currentSection.EndBar - currentSection.StartBar + 1);
                sections.Add(currentSection);
            }

            return sections;
        }

        /// <summary>
        /// 获取指定范围内的音符数量
        /// </summary>
        private int GetNotesInRange(int startBar, int endBar, Dictionary<int, int> barNotes)
        {
            int total = 0;
            for (int bar = startBar; bar <= endBar; bar++)
            {
                if (barNotes.ContainsKey(bar))
                    total += barNotes[bar];
            }
            return total;
        }

        /// <summary>
        /// 获取段落名称
        /// </summary>
        private string GetSectionName(int index, int noteCount)
        {
            var names = new[] { "前奏", "主歌A", "副歌", "主歌B", "桥段", "尾奏" };
            if (index < names.Length)
                return names[index];
            return $"段落{index + 1}";
        }

        /// <summary>
        /// 获取段落特征描述
        /// </summary>
        private string GetSectionCharacteristics(int noteCount, int barCount)
        {
            var density = noteCount / (double)barCount;

            if (density < 2) return "稀疏，适合休息或过渡";
            if (density < 4) return "中等密度，基础节奏";
            if (density < 8) return "较高密度，有一定挑战";
            return "高密度，技术性强";
        }

        /// <summary>
        /// 分析间隔
        /// </summary>
        private List<MyWPF.Layout.AI.ChartBreak> AnalyzeBreaks(List<int> emptyBars)
        {
            var breaks = new List<MyWPF.Layout.AI.ChartBreak>();

            if (!emptyBars.Any()) return breaks;

            emptyBars.Sort();
            var currentBreak = new MyWPF.Layout.AI.ChartBreak { StartBar = emptyBars[0] };

            for (int i = 1; i < emptyBars.Count; i++)
            {
                if (emptyBars[i] == emptyBars[i - 1] + 1)
                {
                    // 连续的空小节
                    continue;
                }
                else
                {
                    // 间隔结束
                    currentBreak.EndBar = emptyBars[i - 1];
                    currentBreak.Duration = (currentBreak.EndBar - currentBreak.StartBar + 1) * 2.0; // 假设每小节2秒
                    currentBreak.Type = GetBreakType(currentBreak.Duration);
                    breaks.Add(currentBreak);

                    currentBreak = new MyWPF.Layout.AI.ChartBreak { StartBar = emptyBars[i] };
                }
            }

            // 添加最后一个间隔
            currentBreak.EndBar = emptyBars.Last();
            currentBreak.Duration = (currentBreak.EndBar - currentBreak.StartBar + 1) * 2.0;
            currentBreak.Type = GetBreakType(currentBreak.Duration);
            breaks.Add(currentBreak);

            return breaks;
        }

        /// <summary>
        /// 获取间隔类型
        /// </summary>
        private string GetBreakType(double duration)
        {
            if (duration < 4) return "Short";
            if (duration < 8) return "Long";
            return "Musical";
        }

        /// <summary>
        /// 分析谱面难度
        /// </summary>
        private MyWPF.Layout.AI.ChartDifficultyAnalysis AnalyzeChartDifficulty(List<IIdol> notes)
        {
            var difficulty = new MyWPF.Layout.AI.ChartDifficultyAnalysis();

            if (!notes.Any()) return difficulty;

            // 计算整体难度
            difficulty.OverallDifficulty = CalculateOverallDifficulty(notes);

            // 计算各轨道难度
            difficulty.TrackDifficulty = notes.GroupBy(n => n.TargetTrack)
                .ToDictionary(g => g.Key, g => CalculateTrackDifficulty(g.ToList()));

            // 找出困难段落
            difficulty.DifficultSections = FindDifficultSections(notes);

            // 分析技术元素
            difficulty.TechnicalElements = AnalyzeTechnicalElements(notes);

            // 计算可读性评分
            difficulty.ReadabilityScore = CalculateReadabilityScore(notes);

            return difficulty;
        }

        /// <summary>
        /// 计算整体难度
        /// </summary>
        private double CalculateOverallDifficulty(List<IIdol> notes)
        {
            var factors = new List<double>();

            // 音符密度因子
            var maxBar = notes.Max(n => n.Bar);
            var density = notes.Count / (double)maxBar;
            factors.Add(Math.Min(density / 2.0, 5.0)); // 密度因子，最高5分

            // 音符类型复杂度
            var typeComplexity = 0.0;
            var typeCount = notes.GroupBy(n => n.NoteType).ToDictionary(g => g.Key, g => g.Count());

            if (typeCount.ContainsKey("short")) typeComplexity += typeCount["short"] * 0.5;
            if (typeCount.ContainsKey("long")) typeComplexity += typeCount["long"] * 1.0;
            if (typeCount.ContainsKey("slip")) typeComplexity += typeCount["slip"] * 1.5;

            factors.Add(Math.Min(typeComplexity / notes.Count * 10, 3.0)); // 类型复杂度，最高3分

            // 轨道跨度
            var trackSpread = notes.GroupBy(n => n.TargetTrack).Count();
            factors.Add(Math.Min(trackSpread * 0.5, 2.0)); // 轨道跨度，最高2分

            return Math.Min(factors.Sum(), 10.0);
        }

        /// <summary>
        /// 计算轨道难度
        /// </summary>
        private double CalculateTrackDifficulty(List<IIdol> trackNotes)
        {
            if (!trackNotes.Any()) return 0.0;

            var difficulty = 0.0;

            // 音符数量
            difficulty += Math.Min(trackNotes.Count * 0.1, 3.0);

            // 音符类型复杂度
            var longNotes = trackNotes.Count(n => n.NoteType == "long");
            var slipNotes = trackNotes.Count(n => n.NoteType == "slip");

            difficulty += longNotes * 0.2 + slipNotes * 0.3;

            return Math.Min(difficulty, 10.0);
        }

        /// <summary>
        /// 找出困难段落
        /// </summary>
        private List<MyWPF.Layout.AI.DifficultSection> FindDifficultSections(List<IIdol> notes)
        {
            var sections = new List<MyWPF.Layout.AI.DifficultSection>();
            var barGroups = notes.GroupBy(n => n.Bar).OrderBy(g => g.Key);

            foreach (var barGroup in barGroups)
            {
                var barNotes = barGroup.ToList();
                var barDifficulty = CalculateBarDifficulty(barNotes);

                if (barDifficulty > 6.0) // 困难阈值
                {
                    var section = new MyWPF.Layout.AI.DifficultSection
                    {
                        StartBar = barGroup.Key,
                        EndBar = barGroup.Key,
                        Difficulty = barDifficulty,
                        Reasons = GetDifficultyReasons(barNotes)
                    };
                    sections.Add(section);
                }
            }

            return sections;
        }

        /// <summary>
        /// 计算小节难度
        /// </summary>
        private double CalculateBarDifficulty(List<IIdol> barNotes)
        {
            var difficulty = 0.0;

            // 音符数量
            difficulty += barNotes.Count * 0.5;

            // 音符类型
            difficulty += barNotes.Count(n => n.NoteType == "long") * 0.3;
            difficulty += barNotes.Count(n => n.NoteType == "slip") * 0.5;

            // 轨道跨度
            var trackCount = barNotes.GroupBy(n => n.TargetTrack).Count();
            difficulty += trackCount * 0.3;

            return difficulty;
        }

        /// <summary>
        /// 获取难度原因
        /// </summary>
        private List<string> GetDifficultyReasons(List<IIdol> barNotes)
        {
            var reasons = new List<string>();

            if (barNotes.Count > 8)
                reasons.Add("音符密度过高");

            if (barNotes.Count(n => n.NoteType == "slip") > 2)
                reasons.Add("滑动音符过多");

            if (barNotes.GroupBy(n => n.TargetTrack).Count() > 3)
                reasons.Add("需要多轨道操作");

            return reasons;
        }

        /// <summary>
        /// 分析技术元素
        /// </summary>
        private List<string> AnalyzeTechnicalElements(List<IIdol> notes)
        {
            var elements = new List<string>();

            var typeCount = notes.GroupBy(n => n.NoteType).ToDictionary(g => g.Key, g => g.Count());

            if (typeCount.ContainsKey("long") && typeCount["long"] > notes.Count * 0.2)
                elements.Add("长音符技巧");

            if (typeCount.ContainsKey("slip") && typeCount["slip"] > notes.Count * 0.1)
                elements.Add("滑动音符技巧");

            // 检查连续音符
            var sortedNotes = notes.OrderBy(n => n.Bar).ThenBy(n => n.Pos).ToList();
            var streamCount = 0;
            for (int i = 1; i < sortedNotes.Count; i++)
            {
                if (sortedNotes[i].Bar == sortedNotes[i-1].Bar &&
                    Math.Abs(sortedNotes[i].Pos - sortedNotes[i-1].Pos) < 4)
                {
                    streamCount++;
                }
            }

            if (streamCount > notes.Count * 0.3)
                elements.Add("连打技巧");

            return elements;
        }

        /// <summary>
        /// 计算可读性评分
        /// </summary>
        private double CalculateReadabilityScore(List<IIdol> notes)
        {
            var score = 10.0; // 满分开始

            // 音符过于密集会降低可读性
            var maxBar = notes.Max(n => n.Bar);
            var density = notes.Count / (double)maxBar;
            if (density > 8) score -= (density - 8) * 0.5;

            // 轨道分布不均会降低可读性
            var trackDistribution = notes.GroupBy(n => n.TargetTrack).ToDictionary(g => g.Key, g => g.Count());
            var maxTrackNotes = trackDistribution.Values.Max();
            var minTrackNotes = trackDistribution.Values.Min();
            var imbalance = (maxTrackNotes - minTrackNotes) / (double)notes.Count;
            score -= imbalance * 5;

            return Math.Max(score, 0.0);
        }

        /// <summary>
        /// 分析谱面模式
        /// </summary>
        private List<MyWPF.Layout.AI.ChartPattern> AnalyzeChartPatterns(List<IIdol> notes)
        {
            var patterns = new List<MyWPF.Layout.AI.ChartPattern>();

            // 分析连打模式
            patterns.AddRange(FindStreamPatterns(notes));

            // 分析跳跃模式
            patterns.AddRange(FindJumpPatterns(notes));

            // 分析技术模式
            patterns.AddRange(FindTechPatterns(notes));

            return patterns;
        }

        /// <summary>
        /// 找出连打模式
        /// </summary>
        private List<MyWPF.Layout.AI.ChartPattern> FindStreamPatterns(List<IIdol> notes)
        {
            var patterns = new List<MyWPF.Layout.AI.ChartPattern>();
            var sortedNotes = notes.OrderBy(n => n.Bar).ThenBy(n => n.Pos).ToList();

            var currentStream = new List<IIdol>();

            for (int i = 0; i < sortedNotes.Count; i++)
            {
                if (i > 0 && sortedNotes[i].Bar == sortedNotes[i-1].Bar &&
                    Math.Abs(sortedNotes[i].Pos - sortedNotes[i-1].Pos) < 4)
                {
                    if (currentStream.Count == 0)
                        currentStream.Add(sortedNotes[i-1]);
                    currentStream.Add(sortedNotes[i]);
                }
                else
                {
                    if (currentStream.Count >= 4) // 至少4个音符才算连打
                    {
                        patterns.Add(new MyWPF.Layout.AI.ChartPattern
                        {
                            Type = "Stream",
                            StartBar = currentStream.First().Bar,
                            EndBar = currentStream.Last().Bar,
                            Notes = new List<IIdol>(currentStream),
                            Description = $"{currentStream.Count}连打"
                        });
                    }
                    currentStream.Clear();
                }
            }

            return patterns;
        }

        /// <summary>
        /// 找出跳跃模式
        /// </summary>
        private List<MyWPF.Layout.AI.ChartPattern> FindJumpPatterns(List<IIdol> notes)
        {
            var patterns = new List<MyWPF.Layout.AI.ChartPattern>();
            // 简化实现：检查轨道跨度大的音符组合

            var barGroups = notes.GroupBy(n => n.Bar);
            foreach (var barGroup in barGroups)
            {
                var barNotes = barGroup.ToList();
                var trackSpread = barNotes.GroupBy(n => n.TargetTrack).Count();

                if (trackSpread >= 3 && barNotes.Count >= 3)
                {
                    patterns.Add(new MyWPF.Layout.AI.ChartPattern
                    {
                        Type = "Jump",
                        StartBar = barGroup.Key,
                        EndBar = barGroup.Key,
                        Notes = barNotes,
                        Description = $"跳跃模式({trackSpread}轨道)"
                    });
                }
            }

            return patterns;
        }

        /// <summary>
        /// 找出技术模式
        /// </summary>
        private List<MyWPF.Layout.AI.ChartPattern> FindTechPatterns(List<IIdol> notes)
        {
            var patterns = new List<MyWPF.Layout.AI.ChartPattern>();

            // 检查滑动音符组合
            var slipNotes = notes.Where(n => n.NoteType == "slip").ToList();
            if (slipNotes.Count >= 3)
            {
                var slipBars = slipNotes.GroupBy(n => n.Bar);
                foreach (var barGroup in slipBars.Where(g => g.Count() >= 2))
                {
                    patterns.Add(new MyWPF.Layout.AI.ChartPattern
                    {
                        Type = "Tech",
                        StartBar = barGroup.Key,
                        EndBar = barGroup.Key,
                        Notes = barGroup.ToList(),
                        Description = $"滑动技术({barGroup.Count()}个滑动音符)"
                    });
                }
            }

            return patterns;
        }

        #endregion
    }
}
