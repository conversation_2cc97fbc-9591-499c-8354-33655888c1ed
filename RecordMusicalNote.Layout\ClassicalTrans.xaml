﻿<Window x:Class="RecordMusicalNote.ClassicalTrans"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:RecordMusicalNote"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="传统单/连按转换" Height="220" Width="350"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        FontFamily="{DynamicResource MaterialDesignFont}"
        WindowStartupLocation="CenterOwner">

    <materialDesign:Card Margin="16" Padding="24">
        <StackPanel>
            <!-- 标题 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                <materialDesign:PackIcon Kind="SwapHorizontal" Width="24" Height="24" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Text="传统单/连按转换" Style="{StaticResource MaterialDesignHeadline6TextBlock}" VerticalAlignment="Center"/>
            </StackPanel>

            <!-- 选择区域 -->
            <TextBlock Text="请选择转换类型:" Style="{StaticResource MaterialDesignSubtitle2TextBlock}" Margin="0,0,0,16"/>

            <StackPanel Margin="0,0,0,24">
                <RadioButton Name="_rdSingle" Content="单按" GroupName="TransType"
                           Style="{StaticResource MaterialDesignRadioButton}"
                           Margin="0,0,0,12"/>
                <RadioButton Name="_rdDouble" Content="连按" GroupName="TransType"
                           Style="{StaticResource MaterialDesignRadioButton}"/>
            </StackPanel>

            <!-- 按钮区域 -->
            <Button Name="btnConfirm" Content="确定" Click="btnConfirm_Click"
                  Style="{StaticResource MaterialDesignRaisedButton}"
                  HorizontalAlignment="Right" Width="80"/>
        </StackPanel>
    </materialDesign:Card>
</Window>
