using System;
using System.IO;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace MyWPF.Layout.AI
{
    /// <summary>
    /// AI配置数据模型
    /// </summary>
    public class AIConfig
    {
        public string Provider { get; set; } = "deepseek";
        public string ApiKey { get; set; } = "";
        public string BaseUrl { get; set; } = "https://api.deepseek.com/v1/chat/completions";
        public string Model { get; set; } = "deepseek-chat";
        public double Temperature { get; set; } = 0.7;
        public int MaxTokens { get; set; } = 4000;
        public int TimeoutSeconds { get; set; } = 60;
        public int DefaultDifficulty { get; set; } = 5;
        public double DefaultNoteDensity { get; set; } = 0.7;
        public string DefaultStyle { get; set; } = "mixed";
        public DateTime LastModified { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// AI配置预设
    /// </summary>
    public class AIConfigPreset
    {
        public string Provider { get; set; }
        public string Name { get; set; }
        public string BaseUrl { get; set; }
        public string Model { get; set; }
        public string Description { get; set; }
    }

    /// <summary>
    /// AI配置预设管理
    /// </summary>
    public static class AIConfigPresets
    {
        private static readonly Dictionary<string, AIConfigPreset> _presets = new Dictionary<string, AIConfigPreset>
        {
            ["deepseek"] = new AIConfigPreset
            {
                Provider = "deepseek",
                Name = "DeepSeek",
                BaseUrl = "https://api.deepseek.com/v1/chat/completions",
                Model = "deepseek-chat",
                Description = "DeepSeek AI - 高性价比的中文AI模型，适合音乐创作"
            },
            ["openai"] = new AIConfigPreset
            {
                Provider = "openai",
                Name = "OpenAI",
                BaseUrl = "https://api.openai.com/v1/chat/completions",
                Model = "gpt-3.5-turbo",
                Description = "OpenAI GPT - 强大的通用AI模型，创意能力出色"
            },
            ["claude"] = new AIConfigPreset
            {
                Provider = "claude",
                Name = "Claude (Anthropic)",
                BaseUrl = "https://api.anthropic.com/v1/messages",
                Model = "claude-3-sonnet-20240229",
                Description = "Claude AI - Anthropic开发，逻辑推理能力强"
            },
            ["qwen"] = new AIConfigPreset
            {
                Provider = "qwen",
                Name = "通义千问",
                BaseUrl = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
                Model = "qwen-turbo",
                Description = "阿里云通义千问 - 中文理解能力优秀"
            },
            ["ernie"] = new AIConfigPreset
            {
                Provider = "ernie",
                Name = "文心一言",
                BaseUrl = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions",
                Model = "ernie-bot-turbo",
                Description = "百度文心一言 - 中文对话和创作能力强"
            },
            ["custom"] = new AIConfigPreset
            {
                Provider = "custom",
                Name = "自定义",
                BaseUrl = "",
                Model = "",
                Description = "自定义AI服务 - 支持兼容OpenAI API的服务"
            }
        };

        public static AIConfigPreset GetPreset(string provider)
        {
            return _presets.TryGetValue(provider, out var preset) ? preset : null;
        }

        public static Dictionary<string, AIConfigPreset> GetAllPresets()
        {
            return new Dictionary<string, AIConfigPreset>(_presets);
        }
    }

    /// <summary>
    /// AI配置管理器
    /// </summary>
    public static class AIConfigManager
    {
        private static readonly string ConfigFileName = "ai_config.json";
        private static readonly string ConfigDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "RecordMusicalNote",
            "AI"
        );
        private static readonly string ConfigFilePath = Path.Combine(ConfigDirectory, ConfigFileName);

        /// <summary>
        /// 加载AI配置
        /// </summary>
        public static AIConfig LoadConfig()
        {
            try
            {
                if (File.Exists(ConfigFilePath))
                {
                    var json = File.ReadAllText(ConfigFilePath);
                    var config = JsonConvert.DeserializeObject<AIConfig>(json);
                    
                    // 验证配置完整性
                    if (config != null && !string.IsNullOrEmpty(config.Provider))
                    {
                        return config;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载AI配置失败: {ex.Message}");
            }

            // 返回默认配置
            return GetDefaultConfig();
        }

        /// <summary>
        /// 保存AI配置
        /// </summary>
        public static void SaveConfig(AIConfig config)
        {
            try
            {
                // 确保目录存在
                Directory.CreateDirectory(ConfigDirectory);

                // 更新修改时间
                config.LastModified = DateTime.Now;

                // 序列化并保存
                var json = JsonConvert.SerializeObject(config, Formatting.Indented);
                File.WriteAllText(ConfigFilePath, json);

                // 同时更新到Settings中（向后兼容）
                MyWPF.Layout.Properties.Settings.Default.AIApiKey = config.ApiKey;
                MyWPF.Layout.Properties.Settings.Default.AIProvider = config.Provider;
                MyWPF.Layout.Properties.Settings.Default.DefaultDifficulty = config.DefaultDifficulty;
                MyWPF.Layout.Properties.Settings.Default.DefaultNoteDensity = config.DefaultNoteDensity;
                MyWPF.Layout.Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                throw new Exception($"保存AI配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取默认配置
        /// </summary>
        public static AIConfig GetDefaultConfig()
        {
            return new AIConfig
            {
                Provider = "deepseek",
                ApiKey = "",
                BaseUrl = "https://api.deepseek.com/v1/chat/completions",
                Model = "deepseek-chat",
                Temperature = 0.7,
                MaxTokens = 4000,
                TimeoutSeconds = 60,
                DefaultDifficulty = 5,
                DefaultNoteDensity = 0.7,
                DefaultStyle = "mixed"
            };
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public static void ResetToDefault()
        {
            var defaultConfig = GetDefaultConfig();
            SaveConfig(defaultConfig);
        }

        /// <summary>
        /// 检查配置是否有效
        /// </summary>
        public static bool IsConfigValid(AIConfig config)
        {
            return config != null &&
                   !string.IsNullOrEmpty(config.ApiKey) &&
                   !string.IsNullOrEmpty(config.BaseUrl) &&
                   !string.IsNullOrEmpty(config.Model) &&
                   config.Temperature >= 0 && config.Temperature <= 2 &&
                   config.MaxTokens > 0 &&
                   config.TimeoutSeconds > 0;
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        public static string GetConfigFilePath()
        {
            return ConfigFilePath;
        }

        /// <summary>
        /// 备份配置
        /// </summary>
        public static void BackupConfig()
        {
            try
            {
                if (File.Exists(ConfigFilePath))
                {
                    var backupPath = ConfigFilePath + $".backup.{DateTime.Now:yyyyMMdd_HHmmss}";
                    File.Copy(ConfigFilePath, backupPath);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"备份配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从旧版Settings迁移配置
        /// </summary>
        public static void MigrateFromSettings()
        {
            try
            {
                var settings = MyWPF.Layout.Properties.Settings.Default;
                
                if (!string.IsNullOrEmpty(settings.AIApiKey))
                {
                    var config = new AIConfig
                    {
                        Provider = settings.AIProvider ?? "deepseek",
                        ApiKey = settings.AIApiKey,
                        DefaultDifficulty = settings.DefaultDifficulty,
                        DefaultNoteDensity = settings.DefaultNoteDensity
                    };

                    // 根据Provider设置对应的URL和Model
                    var preset = AIConfigPresets.GetPreset(config.Provider);
                    if (preset != null)
                    {
                        config.BaseUrl = preset.BaseUrl;
                        config.Model = preset.Model;
                    }

                    SaveConfig(config);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"迁移配置失败: {ex.Message}");
            }
        }
    }
}
