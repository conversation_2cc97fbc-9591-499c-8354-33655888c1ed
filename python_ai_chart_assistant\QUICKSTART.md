# 🚀 快速开始指南

## 1️⃣ 启动UI界面

### Windows用户
```bash
# 方式1: 双击批处理文件
start_ui.bat

# 方式2: 命令行启动
python run_ui.py
```

### Linux/Mac用户
```bash
# 方式1: 运行shell脚本
chmod +x start_ui.sh
./start_ui.sh

# 方式2: 命令行启动
python run_ui.py
```

## 2️⃣ 访问界面

启动后在浏览器中访问: **http://localhost:8501**

## 3️⃣ 基础使用流程

### 🎵 生成节奏大师谱面

1. **选择"🎵 谱面生成"页面**
2. **上传MIDI文件** - 拖拽或点击上传
3. **设置参数**:
   - 输出格式: 选择 `rhythm_master`
   - 难度等级: 1-10（推荐5-7）
   - 生成风格: `balanced`（平衡）
   - 歌曲信息: 填写标题和艺术家
4. **点击"🚀 开始生成谱面"**
5. **下载生成的.imd文件**

### 🔍 分析MIDI文件

1. **选择"🔍 MIDI分析"页面**
2. **上传MIDI文件**
3. **点击"🔍 开始分析"**
4. **查看分析结果**:
   - 基本信息（时长、BPM、音符数）
   - 主旋律信息
   - 节奏模式
   - 和弦进行

### 🎯 预测难度

1. **选择"🎯 难度预测"页面**
2. **上传MIDI文件**
3. **点击"🎯 预测难度"**
4. **查看预测结果**:
   - 预测难度等级
   - 置信度
   - 游戏性评分

## 4️⃣ 常见问题

### ❓ 界面打不开？
- 检查端口8501是否被占用
- 尝试使用不同端口: `python run_ui.py --port 8502`
- 确保防火墙没有阻止访问

### ❓ 上传文件失败？
- 确保文件是标准MIDI格式（.mid或.midi）
- 文件大小不要超过10MB
- 检查文件是否损坏

### ❓ 生成谱面失败？
- 确保MIDI文件包含音符数据
- 尝试不同的生成参数
- 检查错误信息提示

### ❓ 依赖安装问题？
- 运行智能安装脚本: `python install_deps.py`
- 或使用最小依赖: `pip install -r requirements-minimal.txt`
- 查看详细安装指南: `INSTALL.md`

## 5️⃣ 高级功能

### 🔄 格式转换
- 在不同音游格式间转换现有谱面
- 支持节奏大师、Malody、osu!格式

### 🚀 模型训练
- 准备MIDI和谱面数据对
- 设置训练参数
- 监控训练进度

### ⚙️ 系统设置
- 配置模型路径
- 调整性能参数
- 设置输出选项

## 6️⃣ 提示和技巧

### 🎵 获得更好的谱面质量
- 使用高质量的MIDI文件
- 选择合适的难度等级
- 尝试不同的生成风格
- 根据歌曲特点调整参数

### 🔧 性能优化
- 关闭不必要的浏览器标签页
- 处理大文件时耐心等待
- 定期清理浏览器缓存

### 💡 最佳实践
- 先分析MIDI文件了解特征
- 根据分析结果选择合适参数
- 生成多个版本进行对比
- 保存满意的配置设置

## 🆘 需要帮助？

- 📖 查看完整文档: `README.md`
- 🔧 安装问题: `INSTALL.md`
- 🖥️ UI使用说明: `ui/README.md`
- 🐛 遇到Bug: 检查控制台错误信息

---

**🎉 现在你可以开始使用AI音游写谱助手了！**
