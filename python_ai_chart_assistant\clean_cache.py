#!/usr/bin/env python3
"""
清理Python缓存文件脚本

删除项目中的所有__pycache__目录和.pyc文件
"""

import os
import shutil
from pathlib import Path

def clean_pycache(root_dir="."):
    """清理Python缓存文件"""
    root_path = Path(root_dir)
    
    # 统计信息
    removed_dirs = 0
    removed_files = 0
    
    print("🧹 清理Python缓存文件...")
    print("=" * 30)
    
    # 查找并删除__pycache__目录
    for pycache_dir in root_path.rglob("__pycache__"):
        if pycache_dir.is_dir():
            print(f"删除目录: {pycache_dir}")
            shutil.rmtree(pycache_dir)
            removed_dirs += 1
    
    # 查找并删除.pyc文件
    for pyc_file in root_path.rglob("*.pyc"):
        if pyc_file.is_file():
            print(f"删除文件: {pyc_file}")
            pyc_file.unlink()
            removed_files += 1
    
    # 查找并删除.pyo文件
    for pyo_file in root_path.rglob("*.pyo"):
        if pyo_file.is_file():
            print(f"删除文件: {pyo_file}")
            pyo_file.unlink()
            removed_files += 1
    
    print("\n✅ 清理完成!")
    print(f"删除了 {removed_dirs} 个__pycache__目录")
    print(f"删除了 {removed_files} 个缓存文件")
    
    if removed_dirs == 0 and removed_files == 0:
        print("💡 没有找到需要清理的缓存文件")

def main():
    """主函数"""
    print("🎵 AI音游写谱助手 - 缓存清理工具")
    print()
    
    try:
        clean_pycache()
        print("\n💡 提示:")
        print("- 已创建.gitignore文件，今后这些文件不会被Git跟踪")
        print("- 可以定期运行此脚本清理缓存文件")
        print("- 或使用命令: find . -name '__pycache__' -type d -exec rm -rf {} +")
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")

if __name__ == "__main__":
    main()
