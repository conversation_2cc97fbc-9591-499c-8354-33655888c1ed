﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote
{
    public interface ILevelInfo
    {
        int LevelInfoId { get; }
        /// <summary>
        /// 歌曲名字
        /// </summary>
        string SongName { get; set; }
        /// <summary>
        /// 艺人
        /// </summary>
        string Artist { get; set; }
        float BPM { get; set; }
        int BeatLen { get; set; }
        int BeatPerBar { get; set; }
        int EnterTimeAdjust { get; set; }
        int NotePreShow { get; set; }
        int LevelTime { get; set; }
        int BarAmount { get; set; }
        int BeginBarLen { get; set; }
        bool IsFourTrack { get; set; }
        int TrackCount { get; set; }
        int LevelPreTime { get; set; }
        int Star { get; set; }


    }
}
