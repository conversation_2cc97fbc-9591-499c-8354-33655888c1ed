﻿<Window x:Class="MyWPF.Layout.ScoreInfoAnalyse"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MyWPF.Layout"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="得分情况分析" Height="800" Width="1200" MinHeight="600" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <Style x:Key="contentCenterStyle" TargetType="{x:Type TextBlock}">
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="30"></RowDefinition>
            <RowDefinition Height="2*"></RowDefinition>
            <RowDefinition Height="2*"></RowDefinition>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2.5*"></ColumnDefinition>
            <ColumnDefinition Width="6*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        <ToolBarTray Grid.Row="0" Grid.ColumnSpan="2">
            <ToolBar>
                <Button Height="25" Width="50" Content="刷新" Name="txtFresh"  Click="txtFresh_Click"></Button>
                <Button Height="25" Width="50" Content="非压爆" Name="txtUnYBAnaylyse"  Click="txtUnYBAnaylyse_Click" ></Button>
                <Button Height="25" Width="50" Content="压爆" Name="txtYBAnaylyse"  Click="txtYBAnaylyse_Click" ></Button>
                <Button Height="25" Width="50" Content="超极限" Name="txtCJXAnaylyse"  Click="txtCJXAnaylyse_Click"></Button>
                <Button Height="25" Width="50" Content="双人" Name="txtSRAnaylyse"  Click="txtSRAnaylyse_Click"></Button>

            </ToolBar>
        </ToolBarTray>
        <Grid Grid.Row="1" Grid.RowSpan="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="80"></RowDefinition>
                <RowDefinition Height="1.5*"></RowDefinition>
                <RowDefinition Height="3*"></RowDefinition>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="40*"></ColumnDefinition>
                <ColumnDefinition Width="70*"/>
                <ColumnDefinition Width="221*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <ComboBox Height="28" Name="_cmbSearchType" VerticalAlignment="Center" SelectedIndex="0" Grid.ColumnSpan="2" Margin="15,25,30,27">
                <ComboBoxItem>歌名</ComboBoxItem>
                <ComboBoxItem>歌手</ComboBoxItem>
                <ComboBoxItem>模式</ComboBoxItem>
            </ComboBox>
            <TextBox Height="30" Name="txtSearchConent" VerticalContentAlignment="Center" Margin="56,26,135,24" Grid.ColumnSpan="2" Grid.Column="1"/>
            <DataGrid Grid.Column="0" Grid.Row="1" Grid.RowSpan="1" Grid.ColumnSpan="3" Name="_searchDataGrid" AutoGenerateColumns="False" IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn  Header="歌名" Width="120" Binding="{Binding SongName}"></DataGridTextColumn>
                    <DataGridTextColumn  Header="艺术家" Width="120" Binding="{Binding Artist}"></DataGridTextColumn>
                    <DataGridTextColumn  Header="Mode" Width="50" Binding="{Binding ModeName}"></DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
            <Grid Grid.Row="2" Grid.ColumnSpan="3" Grid.RowSpan="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="1.2*"></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition  Width="*"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <Label Grid.Column="0" Grid.Row="0" HorizontalAlignment="Right" VerticalAlignment="Top">无技能满分(休闲房，实验室):</Label>
                <Label Grid.Column="0" Grid.Row="0" HorizontalAlignment="Right" VerticalAlignment="Bottom">无技能满分(排位，百人):</Label>
                <Label Grid.Column="1" Grid.Row="0" HorizontalAlignment="Left" VerticalAlignment="Top" Name="lblMFNoTechA" Foreground="Red"></Label>
                <Label Grid.Column="1" Grid.Row="0" HorizontalAlignment="Left" VerticalAlignment="Bottom" Name="lblMFNoTechB" Foreground="Red"></Label>
                <Label Grid.Column="0" Grid.Row="1" HorizontalAlignment="Right" VerticalAlignment="Center">Combo:</Label>
                <Label Grid.Column="1" Grid.Row="1" HorizontalAlignment="Left" VerticalAlignment="Center" Name="lblCombo" Foreground="Red"></Label>
                <Label Grid.Column="0" Grid.Row="2" HorizontalAlignment="Right" VerticalAlignment="Center">极限技能满分:</Label>
                <Label Grid.Column="1" Grid.Row="2" HorizontalAlignment="Left" VerticalAlignment="Center" Name="lblJXMF" Foreground="Red"></Label>
                <Label Grid.Column="0" Grid.Row="3" HorizontalAlignment="Right" VerticalAlignment="Center">当前位置爆气满分:</Label>
                <Label Grid.Column="1" Grid.Row="3" HorizontalAlignment="Left" VerticalAlignment="Center" Name="lblCurrentPostionMF" Foreground="Red"></Label>
                <Label Grid.Column="0" Grid.Row="4" HorizontalAlignment="Right" VerticalAlignment="Center">半combo数:</Label>
                <Label Grid.Column="1" Grid.Row="4" HorizontalAlignment="Left" VerticalAlignment="Center" Name="lblHalfOfCombo" Foreground="Red"></Label>
                <Label Grid.Column="0" Grid.Row="5" HorizontalAlignment="Right" VerticalAlignment="Center">全combo数:</Label>
                <Label Grid.Column="1" Grid.Row="5" HorizontalAlignment="Left" VerticalAlignment="Center" Name="lblTotalCombo" Foreground="Red"></Label>
            </Grid>
        </Grid>

        <DataGrid Grid.Row="1" Grid.Column="1" Name="_mainData" AutoGenerateColumns="False" 
                  VerticalContentAlignment="Center" 
                  HorizontalContentAlignment="Center" IsReadOnly="True">

            <DataGrid.Columns>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="主键" Binding="{Binding Id}" Width="50" Visibility="Collapsed"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="Bar" Binding="{Binding Bar}" Width="80" ></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="Pos" Binding="{Binding Pos}" Width="80" ></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="CurrentCombo" Binding="{Binding CurrentCombo}" Width="80"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="ScoreNum实验室" Binding="{Binding ShowScore}" Width="150"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="CurrentCount" Binding="{Binding CurrentCount}" Width="100"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="EndBar" Binding="{Binding EndBar}" Width="80"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="EndPos" Binding="{Binding EndPos}" Width="100"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="键类型" Binding="{Binding NoteTypeStr}" Width="100"></DataGridTextColumn>
            </DataGrid.Columns>
        </DataGrid>
        <Grid Grid.Row="2" Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition></RowDefinition>
                <RowDefinition></RowDefinition>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <Label  HorizontalAlignment="Center" VerticalAlignment="Top">单排:</Label>
            <Label  HorizontalAlignment="Center" VerticalAlignment="Top" Grid.Row="1">双排:</Label>
            <Label Grid.Row="0" Grid.Column="1" Name="lblSingleTech" VerticalAlignment="Top">技能:</Label>
            <Label Grid.Row="1" Grid.Column="1" Name="lblDoubleTech" VerticalAlignment="Top">技能:</Label>
            <Label Grid.Row="0" Grid.Column="2" Name="lblSingleUnYB" VerticalAlignment="Top">非压爆:</Label>
            <Label Grid.Row="1" Grid.Column="2" Name="lblDoubleUnYB" VerticalAlignment="Top">非压爆:</Label>
            <Label Grid.Row="0" Grid.Column="3" Name="lblSingleUnYBMF" VerticalAlignment="Top">满分:</Label>
            <Label Grid.Row="1" Grid.Column="3" Name="lblDoubleUnYBMF" VerticalAlignment="Top">满分:</Label>
            <Label Grid.Row="0" Grid.Column="4" Name="lblSingleYB" VerticalAlignment="Top">压爆:</Label>
            <Label Grid.Row="1" Grid.Column="4" Name="lblDoubleYB" VerticalAlignment="Top">压爆:</Label>
            <Label Grid.Row="0" Grid.Column="5" Name="lblSingleYBMF" VerticalAlignment="Top">满分:</Label>
            <Label Grid.Row="1" Grid.Column="5" Name="lblDoubleYBMF" VerticalAlignment="Top">满分:</Label>
            <Label Grid.Row="0" Grid.Column="6" Name="lblSingleCJX" VerticalAlignment="Top">超极限:</Label>
            <Label Grid.Row="1" Grid.Column="6" Name="lblDoubleCJX" VerticalAlignment="Top">超极限:</Label>
            <Label Grid.Row="0" Grid.Column="7" Name="lblSingleCJXMF" VerticalAlignment="Top">满分:</Label>
            <Label Grid.Row="1" Grid.Column="7" Name="lblDoubleCJXMF" VerticalAlignment="Top">满分:</Label>
        </Grid>
        
    </Grid>
</Window>
