﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote
{
    public interface IPinball
    {
        int PinBallNoteInfoID { get; }
        int LevelInfoId { get; set; }

        int PinballID { get; set; }
        string SonId { get; set; }
        int Bar { get; set; }
        int Pos { get; set; }
        int EndBar { get; set; }
        int EndPos { get; set; }
        string NoteType { get; set; }
        string EndArea { get; set; }
        int MoveTime { get; set; }
    }
}
