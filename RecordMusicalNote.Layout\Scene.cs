﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MyWPF.Layout
{
    /// <summary>
    /// 游戏场景
    /// </summary>
    public class IdolScene
    {
        public IdolScene()
        {
        }
        public float TotalMusicTime { get; set; }
        public float BPM { get; set; }
        public int TotalBarCount//一共有多少个Bar
        {
            get;
            set;    
        }
       /// <summary>
       /// 每个小pos所需要的时间
       /// </summary>
        public float TimeOfPerPos
        {
            get
            {
                if (TotalBarCount <= 0) return 0;
                return float.Parse((Math.Floor(TotalMusicTime / TotalBarCount) / 32).ToString());
            }
        }
        public float ET
        {
            get;set;
        }

        public IList<IdolCellInfo> Left2
        {
            get
            {
                IList<IdolCellInfo> _left2 = new List<IdolCellInfo>();
                for (int i = 0; i < TotalBarCount * 32; i++)
                {
                    IdolCellInfo info = new IdolCellInfo();
                    info.CurrentCount =  i;
                    if ((1 + i) % 8 == 0)
                        info.Tip = "一拍";
                    if ((1 + i) % 32 == 0)
                        info.Tip = "一小节";
                    info.CurrentTime = TimeOfPerPos * i+ET;
                    info.TargetTrackName = "Left2";
                    _left2.Add(info); 
                }
                return _left2;
            }
        }
        public IList<IdolCellInfo> Left1
        {
            get
            {
                IList<IdolCellInfo> _left1 = new List<IdolCellInfo>();
                for (int i = 0; i <TotalBarCount * 32; i++)
                {
                    IdolCellInfo info = new IdolCellInfo();
                    info.CurrentCount =  i;
                    if ((1 + i) % 8 == 0)
                        info.Tip = "一拍";
                    if ((1 + i) % 32 == 0)
                        info.Tip = "一小节";
                    info.CurrentTime = TimeOfPerPos * i + ET;
                    info.TargetTrackName = "Left1";
                    _left1.Add(info);
                }
                return _left1;
            }
        }
        public IList<IdolCellInfo> Middle
        {
            get
            {
                IList<IdolCellInfo> _middle = new List<IdolCellInfo>();
                for (int i = 0; i < TotalBarCount * 32; i++)
                {
                    IdolCellInfo info = new IdolCellInfo();
                    info.CurrentCount =  i;
                    if ((1 + i) % 8 == 0)
                        info.Tip = "一拍";
                    if ((1 + i) % 32 == 0)
                        info.Tip = "一小节";
                    info.CurrentTime = TimeOfPerPos * i + ET;
                    info.TargetTrackName = "Middle";
                    _middle.Add(info);
                }
                return _middle;
            }
        }

        public IList<IdolCellInfo> Right1
        {
            get
            {
                IList<IdolCellInfo> _right1 = new List<IdolCellInfo>();
                for (int i = 0; i < TotalBarCount * 32; i++)
                {
                    IdolCellInfo info = new IdolCellInfo();
                    info.CurrentCount = i;
                    if ((1 + i) % 8 == 0)
                        info.Tip = "一拍";
                    if ((1 + i) % 32 == 0)
                        info.Tip = "一小节";
                    info.CurrentTime = TimeOfPerPos * i + ET;
                    info.TargetTrackName = "Right1";
                    _right1.Add(info);
                }
                return _right1;
            }
        }

        public IList<IdolCellInfo> Right2
        {
            get
            {
                IList<IdolCellInfo> _right2 = new List<IdolCellInfo>();
                for (int i = 0; i < TotalBarCount * 32; i++)
                {
                    IdolCellInfo info = new IdolCellInfo();
                    info.CurrentCount =  i;
                    if ((1 + i) % 8 == 0)
                        info.Tip = "一拍";
                    if ((1 + i) % 32 == 0)
                        info.Tip = "一小节";
                    info.CurrentTime = TimeOfPerPos * i + ET;
                    info.TargetTrackName = "Right2";
                    _right2.Add(info);
                }
                return _right2;
            }
        }
    }

    public class PinballScene
    {
        public PinballScene()
        {
        }
        public float TotalMusicTime { get; set; }
        public float BPM { get; set; }
        public int TotalBarCount//一共有多少个Bar
        {
            get;
            set;
        }
        /// <summary>
        /// 每个小pos所需要的时间
        /// </summary>
        public float TimeOfPerPos
        {
            get
            {
                if (TotalBarCount <= 0) return 0;
                return float.Parse((Math.Floor(TotalMusicTime / TotalBarCount) / 32).ToString());
            }
        }
        public float ET
        {
            get; set;
        }

        public IList<PinballCellInfo> Left
        {
            get
            {
                IList<PinballCellInfo> _left = new List<PinballCellInfo>();
                for (int i = 0; i < TotalBarCount * 32; i++)
                {
                    PinballCellInfo info = new PinballCellInfo();
                    info.CurrentCount = i;
                    if ((1 + i) % 8 == 0)
                        info.Tip = "一拍";
                    if ((1 + i) % 32 == 0)
                        info.Tip = "一小节";
                    info.CurrentTime = TimeOfPerPos * i + ET;
                    info.TargetTrackName = "1|2";
                    _left.Add(info);
                }
                return _left;
            }
        }
        public IList<PinballCellInfo> Middle
        {
            get
            {
                IList<PinballCellInfo> _middle = new List<PinballCellInfo>();
                for (int i = 0; i < TotalBarCount * 32; i++)
                {
                    PinballCellInfo info = new PinballCellInfo();
                    info.CurrentCount = i;
                    if ((1 + i) % 8 == 0)
                        info.Tip = "一拍";
                    if ((1 + i) % 32 == 0)
                        info.Tip = "一小节";
                    info.CurrentTime = TimeOfPerPos * i + ET;
                    info.TargetTrackName = "0";
                    _middle.Add(info);
                }
                return _middle;
            }
        }
        public IList<PinballCellInfo> Right
        {
            get
            {
                IList<PinballCellInfo> _middle = new List<PinballCellInfo>();
                for (int i = 0; i < TotalBarCount * 32; i++)
                {
                    PinballCellInfo info = new PinballCellInfo();
                    info.CurrentCount = i;
                    if ((1 + i) % 8 == 0)
                        info.Tip = "一拍";
                    if ((1 + i) % 32 == 0)
                        info.Tip = "一小节";
                    info.CurrentTime = TimeOfPerPos * i + ET;
                    info.TargetTrackName = "3|4";
                    _middle.Add(info);
                }
                return _middle;
            }
        }

    }
}
