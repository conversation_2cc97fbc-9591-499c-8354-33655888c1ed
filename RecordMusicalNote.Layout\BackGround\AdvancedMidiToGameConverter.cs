using CommonModel;
using RecordMusicalNote;
using RecordMusicalNote.IRepository;
using RecordMusicalNote.Library.Repository;
using System;
using System.Collections.Generic;
using System.Linq;

namespace MyWPF.Layout.BackGround
{
    /// <summary>
    /// 高级MIDI转游戏谱面转换器 - 支持难度参数控制
    /// </summary>
    public class AdvancedMidiToGameConverter
    {
        private readonly int _quarternotetTime;
        private readonly DifficultySettings _settings;
        private readonly Dictionary<int, MidiTrack> _trackAnnotations;
        private readonly Random _random = new Random();
        private readonly IList<UsedTrack> _usedTracks = new List<UsedTrack>();
        private readonly TrackPositionManager _positionManager;

        public AdvancedMidiToGameConverter(int quarternotetTime, DifficultySettings settings, Dictionary<int, MidiTrack> trackAnnotations)
        {
            _quarternotetTime = quarternotetTime;
            _settings = settings;
            _trackAnnotations = trackAnnotations ?? new Dictionary<int, MidiTrack>();
            _positionManager = new TrackPositionManager(_settings.GetTrackNames(), _settings);

            // 应用难度调整
            _settings.ApplyDifficultyAdjustments();

            // 调试信息：显示最终的难度设置
            System.Diagnostics.Debug.WriteLine($"=== 最终难度设置 ===");
            System.Diagnostics.Debug.WriteLine($"难度等级: {_settings.DifficultyLevel}");
            System.Diagnostics.Debug.WriteLine($"音符密度: {_settings.NoteDensity:P0}");
            System.Diagnostics.Debug.WriteLine($"最大同时音符: {_settings.MaxSimultaneousNotes}");
            System.Diagnostics.Debug.WriteLine($"启用滑动音符: {_settings.EnableSlipNotes}");
            System.Diagnostics.Debug.WriteLine($"启用长音符: {_settings.EnableLongNotes}");
            System.Diagnostics.Debug.WriteLine($"轨道数量: {_settings.TrackCount}");
            System.Diagnostics.Debug.WriteLine($"==================");
        }

        /// <summary>
        /// 转换MIDI音符为游戏音符
        /// </summary>
        public List<IIdol> ConvertToGameNotes(IList<MidiTrackList> midiNotes)
        {
            var gameNotes = new List<IIdol>();
            var repository = new IdolRepository();

            // 过滤掉未标注的轨道
            var filteredNotes = FilterNotesByAnnotation(midiNotes);
            
            // 根据难度设置过滤音符密度
            var densityFilteredNotes = FilterNotesByDensity(filteredNotes);
            
            // 按时间排序
            var sortedNotes = densityFilteredNotes.OrderBy(n => n.StartTime).ToList();



            string lastGameTrack = "";
            int lastSyllabelNumber = 0;
            bool isFirst = true;
            int processedCount = 0;
            int skippedByAnnotation = 0;
            int skippedBySimultaneous = 0;

            foreach (var midiNote in sortedNotes)
            {
                var trackInfo = _trackAnnotations.ContainsKey(midiNote.MidiTrackID)
                    ? _trackAnnotations[midiNote.MidiTrackID]
                    : null;

                if (trackInfo == null || trackInfo.TrackType == TrackType.Unknown)
                {
                    skippedByAnnotation++;
                    continue;
                }

                processedCount++;

                var idol = repository.CreateNewIdol();

                // 计算开始位置信息（添加4小节准备时间）
                int oneBarTime = _quarternotetTime * 4;
                int preparationBars = 3; // 调整偏移，确保音符从第5小节开始

                // 调试信息：显示第一个音符的计算过程
                if (processedCount == 1)
                {
                    int barIndex = midiNote.StartTime / oneBarTime;
                    System.Diagnostics.Debug.WriteLine($"=== 第一个音符位置计算 ===");
                    System.Diagnostics.Debug.WriteLine($"MIDI开始时间: {midiNote.StartTime}");
                    System.Diagnostics.Debug.WriteLine($"一小节时间: {oneBarTime}");
                    System.Diagnostics.Debug.WriteLine($"小节索引: {barIndex}");
                    System.Diagnostics.Debug.WriteLine($"准备小节数: {preparationBars}");
                    System.Diagnostics.Debug.WriteLine($"计算结果: {barIndex} + {preparationBars} + 1 = {barIndex + preparationBars + 1}");
                }

                idol.Bar = midiNote.StartTime / oneBarTime + preparationBars + 1;
                idol.Pos = midiNote.StartTime / (_quarternotetTime * 4 / 64) % 64;

                // 智能确定音符类型
                idol.NoteType = DetermineNoteType(midiNote, trackInfo);

                // 根据音符类型设置结束位置
                if (idol.NoteType == "long")
                {
                    // 长音符需要计算结束位置（同样添加准备时间）
                    idol.EndBar = midiNote.EndTime / oneBarTime + preparationBars + 1;
                    idol.EndPos = midiNote.EndTime / (_quarternotetTime * 4 / 64) % 64;

                    // 计算实际持续的位置数
                    long startPosition = (long)(idol.Bar * 64 + idol.Pos);
                    long endPosition = (long)(idol.EndBar * 64 + idol.EndPos);
                    long duration = endPosition - startPosition;

                    // 如果持续时间太短（少于8个位置单位），转为短音符
                    if (duration < 8)
                    {
                        idol.NoteType = "short";
                        idol.EndBar = idol.Bar;
                        idol.EndPos = idol.Pos;
                    }
                    else
                    {
                        // 确保结束位置不早于开始位置
                        if (idol.EndBar < idol.Bar || (idol.EndBar == idol.Bar && idol.EndPos <= idol.Pos))
                        {
                            // 如果结束位置不合理，设置为开始位置后8个位置
                            idol.EndBar = idol.Bar;
                            idol.EndPos = Math.Min(63, idol.Pos + 8);
                        }
                    }
                }
                else
                {
                    // 短音符和滑动音符的结束位置与开始位置相同
                    idol.EndBar = idol.Bar;
                    idol.EndPos = idol.Pos;
                }

                // 智能分配轨道并检查位置可用性
                string assignedTrack = null;
                string endTrack = null;

                if (isFirst)
                {
                    assignedTrack = GetInitialTrack(midiNote, trackInfo);
                    isFirst = false;
                }
                else
                {
                    assignedTrack = AssignTrackIntelligently(midiNote, trackInfo, lastGameTrack, lastSyllabelNumber);
                }

                // 处理滑动音符的结束轨道
                if (idol.NoteType == "slip")
                {
                    endTrack = GetSlipEndTrack(assignedTrack, midiNote, trackInfo);
                }

                // 检查位置是否可用
                if (_positionManager.CanPlaceNote(idol, assignedTrack, endTrack))
                {
                    // 位置可用，设置轨道并占用位置
                    idol.FromTrack = assignedTrack;
                    idol.TargetTrack = assignedTrack;
                    if (idol.NoteType == "slip")
                    {
                        idol.EndTrack = endTrack;
                    }

                    // 占用位置
                    _positionManager.OccupyPosition(idol);

                    gameNotes.Add(idol);
                    lastGameTrack = idol.FromTrack;
                    lastSyllabelNumber = midiNote.SyllabelNumber;

                }
                else
                {
                    skippedBySimultaneous++;
                }
            }

            // 最终统计信息
            System.Diagnostics.Debug.WriteLine($"=== 转换完成统计 ===");
            System.Diagnostics.Debug.WriteLine($"处理音符: {processedCount}");
            System.Diagnostics.Debug.WriteLine($"跳过(标注): {skippedByAnnotation}");
            System.Diagnostics.Debug.WriteLine($"跳过(冲突): {skippedBySimultaneous}");
            System.Diagnostics.Debug.WriteLine($"最终生成: {gameNotes.Count}");
            System.Diagnostics.Debug.WriteLine($"生成效率: {(double)gameNotes.Count/processedCount:P0}");

            return gameNotes;
        }

        /// <summary>
        /// 根据轨道标注过滤音符
        /// </summary>
        private List<MidiTrackList> FilterNotesByAnnotation(IList<MidiTrackList> midiNotes)
        {
            return midiNotes.Where(note =>
            {
                if (_trackAnnotations.ContainsKey(note.MidiTrackID))
                {
                    var trackInfo = _trackAnnotations[note.MidiTrackID];
                    return trackInfo.TrackType != TrackType.Unknown;
                }
                return false;
            }).ToList();
        }

        /// <summary>
        /// 根据密度设置过滤音符
        /// </summary>
        private List<MidiTrackList> FilterNotesByDensity(List<MidiTrackList> notes)
        {
            System.Diagnostics.Debug.WriteLine($"=== 密度过滤开始 ===");
            System.Diagnostics.Debug.WriteLine($"输入音符数量: {notes.Count}");
            System.Diagnostics.Debug.WriteLine($"设置密度: {_settings.NoteDensity:P0}");

            if (_settings.NoteDensity >= 1.0)
            {
                System.Diagnostics.Debug.WriteLine("密度100%，跳过过滤");
                return notes;
            }

            // 按轨道类型分组，保持重要轨道的音符
            var groupedByTrack = notes.GroupBy(n => n.MidiTrackID).ToList();
            var filteredNotes = new List<MidiTrackList>();

            foreach (var trackGroup in groupedByTrack)
            {
                var trackInfo = _trackAnnotations.ContainsKey(trackGroup.Key)
                    ? _trackAnnotations[trackGroup.Key]
                    : null;

                var trackNotes = trackGroup.OrderBy(n => n.StartTime).ToList();
                double keepRatio = GetKeepRatioByTrackType(trackInfo?.TrackType ?? TrackType.Unknown);

                int keepCount = (int)(trackNotes.Count * keepRatio * _settings.NoteDensity);

                System.Diagnostics.Debug.WriteLine($"轨道ID: {trackGroup.Key}, 类型: {trackInfo?.TrackType}, 原始: {trackNotes.Count}, 保留比例: {keepRatio:P0}, 最终保留: {keepCount}");

                // 智能选择要保留的音符（保持音乐结构）
                var selectedNotes = SelectImportantNotes(trackNotes, keepCount, trackInfo);
                filteredNotes.AddRange(selectedNotes);
            }

            System.Diagnostics.Debug.WriteLine($"密度过滤结果: {filteredNotes.Count}/{notes.Count} ({(double)filteredNotes.Count/notes.Count:P0})");
            return filteredNotes;
        }

        /// <summary>
        /// 根据轨道类型获取保留比例
        /// </summary>
        private double GetKeepRatioByTrackType(TrackType trackType)
        {
            switch (trackType)
            {
                case TrackType.MainMelody:
                    return 1.0; // 主旋律保留100%
                case TrackType.Drums:
                    return 0.9; // 鼓点保留90%
                case TrackType.Bass:
                    return 0.8; // 贝斯保留80%
                case TrackType.SubMelody:
                    return 0.7; // 副旋律保留70%
                case TrackType.Harmony:
                case TrackType.Chord:
                    return 0.6; // 和声和弦保留60%
                default:
                    return 0.5; // 其他保留50%
            }
        }

        /// <summary>
        /// 智能选择重要音符
        /// </summary>
        private List<MidiTrackList> SelectImportantNotes(List<MidiTrackList> notes, int keepCount, MidiTrack trackInfo)
        {
            if (keepCount >= notes.Count)
                return notes;

            var selectedNotes = new List<MidiTrackList>();
            
            // 按强拍位置排序，优先保留强拍音符
            var notesWithBeatStrength = notes.Select(note => new
            {
                Note = note,
                BeatStrength = CalculateBeatStrength(note)
            }).OrderByDescending(x => x.BeatStrength).ToList();

            // 选择前keepCount个音符
            selectedNotes.AddRange(notesWithBeatStrength.Take(keepCount).Select(x => x.Note));

            return selectedNotes.OrderBy(n => n.StartTime).ToList();
        }

        /// <summary>
        /// 计算音符的节拍强度
        /// </summary>
        private double CalculateBeatStrength(MidiTrackList note)
        {
            int oneBarTime = _quarternotetTime * 4;
            int positionInBar = note.StartTime % oneBarTime;
            int beatPosition = positionInBar / _quarternotetTime;
            
            // 强拍（1拍、3拍）权重更高
            if (beatPosition == 0) return 1.0; // 第1拍最强
            if (beatPosition == 2) return 0.8; // 第3拍次强
            if (beatPosition == 1 || beatPosition == 3) return 0.6; // 第2、4拍中等
            return 0.4; // 其他位置较弱
        }

        /// <summary>
        /// 智能确定音符类型
        /// </summary>
        private string DetermineNoteType(MidiTrackList midiNote, MidiTrack trackInfo)
        {
            double durationInBeats = (double)midiNote.Duration / _quarternotetTime;

            // 计算在64进制位置系统中的持续时间
            double durationInPositions = durationInBeats * 16; // 一拍 = 16个位置单位

            // 根据设置过滤音符类型
            if (!_settings.EnableLongNotes)
                return _settings.EnableShortNotes ? "short" : "short";

            // 长音符必须满足两个条件：1.拍数足够 2.位置数足够
            bool durationEnough = durationInBeats >= _settings.LongNoteMinDuration;
            bool positionsEnough = durationInPositions >= 8; // 至少8个位置单位

            if (!durationEnough || !positionsEnough)
                return _settings.EnableShortNotes ? "short" : "short";
            
            if (!_settings.EnableSlipNotes && ShouldCreateSlip(midiNote, trackInfo))
            {
                return _settings.EnableShortNotes ? "short" : "short"; // 如果不允许滑动，转为短音符
            }

            // 根据轨道类型和持续时间判断
            switch (trackInfo.TrackType)
            {
                case TrackType.Drums:
                case TrackType.Percussion:
                    // 鼓点在强拍位置有较高概率生成滑动音符
                    if (_settings.EnableSlipNotes && IsOnStrongBeat(midiNote) && _random.NextDouble() > 0.4)
                        return "slip"; // 60%概率生成滑动音符
                    return _settings.EnableShortNotes ? "short" : "short";
                    
                case TrackType.MainMelody:
                    if (_settings.EnableLongNotes && durationInBeats >= _settings.LongNoteMinDuration)
                        return "long";
                    else if (_settings.EnableSlipNotes && IsOnStrongBeat(midiNote) && _random.NextDouble() > 0.8)
                        return "slip"; // 20%概率在强拍生成滑动音符
                    return _settings.EnableShortNotes ? "short" : "short";
                    
                case TrackType.Bass:
                    if (_settings.EnableLongNotes && durationInBeats >= _settings.LongNoteMinDuration * 1.5)
                        return "long";
                    else if (_settings.EnableSlipNotes && IsOnStrongBeat(midiNote) && _random.NextDouble() > 0.85)
                        return "slip"; // 15%概率在强拍生成滑动音符
                    return _settings.EnableShortNotes ? "short" : "short";
                    
                case TrackType.Chord:
                case TrackType.Harmony:
                    if (_settings.EnableLongNotes && durationInBeats >= _settings.LongNoteMinDuration * 0.8)
                        return "long";
                    else if (_settings.EnableSlipNotes && IsOnStrongBeat(midiNote) && _random.NextDouble() > 0.9)
                        return "slip"; // 10%概率在强拍生成滑动音符
                    return _settings.EnableShortNotes ? "short" : "short";
                    
                default:
                    if (_settings.EnableLongNotes && durationInBeats >= _settings.LongNoteMinDuration)
                        return "long";
                    else if (_settings.EnableSlipNotes && IsOnStrongBeat(midiNote) && _random.NextDouble() > 0.9)
                        return "slip"; // 10%概率在强拍生成滑动音符
                    return _settings.EnableShortNotes ? "short" : "short";
            }
        }

        /// <summary>
        /// 检查音符是否在强拍位置
        /// </summary>
        private bool IsOnStrongBeat(MidiTrackList midiNote)
        {
            // 计算在小节内的位置（64进制）
            int positionInBar = midiNote.StartTime / (_quarternotetTime * 4 / 64) % 64;

            // 强拍位置：第1拍(0)、第3拍(32)
            // 在4/4拍中，每拍占16个位置单位
            return positionInBar == 0 ||      // 第1拍（最强拍）
                   positionInBar == 32 ||     // 第3拍（强拍）
                   positionInBar == 16 ||     // 第2拍（次强拍）
                   positionInBar == 48;       // 第4拍（次强拍）
        }

        /// <summary>
        /// 判断是否应该创建滑动音符
        /// </summary>
        private bool ShouldCreateSlip(MidiTrackList midiNote, MidiTrack trackInfo)
        {
            if (!_settings.EnableSlipNotes)
                return false;
                
            // 根据难度等级调整滑动音符概率
            double slipProbability;
            switch (_settings.DifficultyLevel)
            {
                case DifficultyLevel.Easy:
                    slipProbability = 0.0;
                    break;
                case DifficultyLevel.Normal:
                    slipProbability = 0.2;
                    break;
                case DifficultyLevel.Hard:
                    slipProbability = 0.3;
                    break;
                case DifficultyLevel.Expert:
                    slipProbability = 0.4;
                    break;
                default:
                    slipProbability = 0.2;
                    break;
            }
            
            return _random.NextDouble() < slipProbability;
        }

        /// <summary>
        /// 获取初始轨道
        /// </summary>
        private string GetInitialTrack(MidiTrackList midiNote, MidiTrack trackInfo)
        {
            var trackNames = _settings.GetTrackNames();

            switch (trackInfo.TrackType)
            {
                case TrackType.MainMelody:
                    return trackNames.Length > 2 ? trackNames[trackNames.Length - 2] : trackNames[0]; // 右侧主要位置
                case TrackType.Bass:
                    return trackNames[1]; // 左下
                case TrackType.Drums:
                    return trackNames[0]; // 左上
                default:
                    return trackNames[_random.Next(trackNames.Length)]; // 随机选择
            }
        }



        /// <summary>
        /// 智能分配轨道
        /// </summary>
        private string AssignTrackIntelligently(MidiTrackList midiNote, MidiTrack trackInfo, string lastTrack, int lastPitch)
        {
            var trackNames = _settings.GetTrackNames();

            // 增加随机性，50%概率完全随机选择轨道
            if (_random.NextDouble() > 0.5)
            {
                return trackNames[_random.Next(trackNames.Length)];
            }

            // 50%概率根据音高变化选择
            var availableTracks = GetAvailableTracks(midiNote, trackNames);
            if (availableTracks.Count == 0)
                return trackNames[_random.Next(trackNames.Length)];

            string trackByPitch = AssignTrackByMelodyDirection(midiNote.SyllabelNumber, lastPitch, lastTrack, availableTracks);
            if (availableTracks.Contains(trackByPitch))
                return trackByPitch;

            // 最后随机选择
            return availableTracks[_random.Next(availableTracks.Count)];
        }



        /// <summary>
        /// 根据旋律走向分配轨道
        /// </summary>
        private string AssignTrackByMelodyDirection(int currentPitch, int lastPitch, string lastTrack, List<string> availableTracks)
        {
            var trackNames = _settings.GetTrackNames();

            // 简化逻辑：根据音高范围分配轨道
            // 低音 -> Left轨道，高音 -> Right轨道
            if (currentPitch < 60) // C4以下
            {
                var leftTracks = trackNames.Where(t => t.Contains("Left")).ToArray();
                if (leftTracks.Length > 0)
                    return leftTracks[_random.Next(leftTracks.Length)];
            }
            else // C4以上
            {
                var rightTracks = trackNames.Where(t => t.Contains("Right")).ToArray();
                if (rightTracks.Length > 0)
                    return rightTracks[_random.Next(rightTracks.Length)];
            }

            // 如果没有对应轨道，随机选择
            return trackNames[_random.Next(trackNames.Length)];
        }

        /// <summary>
        /// 获取可用轨道
        /// </summary>
        private List<string> GetAvailableTracks(MidiTrackList midiNote, string[] allTracks)
        {
            // 返回所有轨道，让位置管理器来处理冲突检测
            return allTracks.ToList();
        }



        /// <summary>
        /// 获取滑动音符的结束轨道
        /// </summary>
        private string GetSlipEndTrack(string fromTrack, MidiTrackList midiNote, MidiTrack trackInfo)
        {
            var trackNames = _settings.GetTrackNames();
            var fromIndex = Array.IndexOf(trackNames, fromTrack);

            if (fromIndex == -1) return fromTrack;

            // 四轨模式的正确顺序：Left2, Left1, Right1, Right2
            // 滑动音符应该只在相邻轨道之间进行
            var candidates = new List<string>();

            // 只允许相邻轨道滑动
            if (fromIndex > 0) // 可以向左滑动
                candidates.Add(trackNames[fromIndex - 1]);
            if (fromIndex < trackNames.Length - 1) // 可以向右滑动
                candidates.Add(trackNames[fromIndex + 1]);

            // 如果没有相邻轨道，返回原轨道（变成短音符）
            if (candidates.Count == 0)
                return fromTrack;

            // 优先选择音高变化方向对应的轨道
            if (candidates.Count > 1)
            {
                // 根据音高变化选择方向
                // 这里可以添加更智能的逻辑，暂时随机选择
                return candidates[_random.Next(candidates.Count)];
            }

            return candidates[0];
        }






    }

    /// <summary>
    /// 轨道位置管理器 - 管理所有轨道位置的占用状态
    /// </summary>
    public class TrackPositionManager
    {
        private readonly Dictionary<string, Dictionary<long, bool>> _occupiedPositions;
        private readonly string[] _trackNames;
        private readonly DifficultySettings _settings;

        public TrackPositionManager(string[] trackNames, DifficultySettings settings)
        {
            _trackNames = trackNames;
            _settings = settings;
            _occupiedPositions = new Dictionary<string, Dictionary<long, bool>>();

            // 初始化每个轨道的位置字典
            foreach (var track in trackNames)
            {
                _occupiedPositions[track] = new Dictionary<long, bool>();
            }
        }

        /// <summary>
        /// 检查是否可以放置音符
        /// </summary>
        public bool CanPlaceNote(IIdol note, string targetTrack, string endTrack = null)
        {
            // 1. 首先检查同时音符数量限制
            if (!CheckSimultaneousNotesLimit(note))
                return false;

            // 2. 然后检查具体的轨道占用
            if (note.NoteType == "long")
            {
                // 长音符需要检查整个持续时间范围
                return CanPlaceLongNote(note, targetTrack);
            }
            else if (note.NoteType == "slip")
            {
                // 滑动音符需要检查起始和结束轨道
                return CanPlaceSlipNote(note, targetTrack, endTrack);
            }
            else
            {
                // 短音符只需要检查一个位置
                return CanPlaceShortNote(note, targetTrack);
            }
        }

        /// <summary>
        /// 检查同时音符数量限制
        /// </summary>
        private bool CheckSimultaneousNotesLimit(IIdol note)
        {
            long position = CalculatePosition(note.Bar, note.Pos);
            int simultaneousCount = 0;

            // 统计同一时间点所有轨道的音符数量
            foreach (var track in _trackNames)
            {
                if (IsPositionOccupied(track, position))
                {
                    simultaneousCount++;
                }
            }

            // 如果是滑动音符，需要额外考虑结束轨道的占用
            if (note.NoteType == "slip")
            {
                // 检查新的滑动音符会占用几个轨道
                string targetTrack = GetTargetTrackForNote(note);
                string endTrack = GetEndTrackForSlipNote(note, targetTrack);

                // 如果起始轨道和结束轨道不同，滑动音符会占用2个轨道位置
                if (!string.IsNullOrEmpty(endTrack) && targetTrack != endTrack)
                {
                    // 滑动音符占用2个轨道，需要检查是否会超出限制
                    simultaneousCount += 1; // 加上即将占用的第二个轨道
                }
            }

            // 根据四指模式设置不同的限制
            int maxAllowed;
            if (_settings.IsFourFinger)
            {
                // 四指模式：使用用户设置的最大同时音符数
                maxAllowed = _settings.MaxSimultaneousNotes;
            }
            else
            {
                // 非四指模式（双手模式）：最多2个轨道被占用
                maxAllowed = Math.Min(2, _settings.MaxSimultaneousNotes);
            }

            bool canPlace = simultaneousCount < maxAllowed;

            if (!canPlace)
            {
                string mode = _settings.IsFourFinger ? "四指" : "双手";
                string noteTypeDesc = note.NoteType == "slip" ? "滑动音符" :
                                     note.NoteType == "long" ? "长音符" : "短音符";
                System.Diagnostics.Debug.WriteLine($"{mode}模式限制: 位置({note.Bar},{note.Pos}){noteTypeDesc}会占用{simultaneousCount}个轨道，限制为{maxAllowed}");
            }

            return canPlace;
        }

        /// <summary>
        /// 获取音符的目标轨道
        /// </summary>
        private string GetTargetTrackForNote(IIdol note)
        {
            return note.TargetTrack ?? note.FromTrack ?? _trackNames[0];
        }

        /// <summary>
        /// 获取滑动音符的结束轨道
        /// </summary>
        private string GetEndTrackForSlipNote(IIdol note, string startTrack)
        {
            if (note.NoteType != "slip") return null;

            // 如果已经设置了结束轨道，直接返回
            if (!string.IsNullOrEmpty(note.EndTrack)) return note.EndTrack;

            // 否则计算相邻轨道作为结束轨道
            var trackNames = _settings.GetTrackNames();
            var fromIndex = Array.IndexOf(trackNames, startTrack);

            if (fromIndex == -1) return startTrack;

            // 选择相邻轨道
            if (fromIndex > 0) return trackNames[fromIndex - 1];
            if (fromIndex < trackNames.Length - 1) return trackNames[fromIndex + 1];

            return startTrack;
        }

        /// <summary>
        /// 获取轨道所属的手（用于检测冲突）
        /// </summary>
        private string GetTrackHand(string track)
        {
            if (track.Contains("Left")) return "Left";
            if (track.Contains("Right")) return "Right";
            if (track.Contains("Center")) return "Center";
            return "Unknown";
        }

        /// <summary>
        /// 获取同一只手的所有轨道
        /// </summary>
        private List<string> GetSameHandTracks(string track)
        {
            string hand = GetTrackHand(track);
            return _trackNames.Where(t => GetTrackHand(t) == hand).ToList();
        }

        /// <summary>
        /// 占用位置
        /// </summary>
        public void OccupyPosition(IIdol note)
        {
            if (note.NoteType == "long")
            {
                OccupyLongNotePositions(note);
            }
            else if (note.NoteType == "slip")
            {
                OccupySlipNotePositions(note);
            }
            else
            {
                OccupyShortNotePosition(note);
            }
        }

        private bool CanPlaceShortNote(IIdol note, string track)
        {
            long position = CalculatePosition(note.Bar, note.Pos);
            return !IsPositionOccupied(track, position);
        }

        private bool CanPlaceLongNote(IIdol note, string track)
        {
            long startPos = CalculatePosition(note.Bar, note.Pos);
            long endPos = CalculatePosition(note.EndBar, note.EndPos);

            // 在非四指模式下，需要检查同一只手的所有轨道
            List<string> tracksToCheck = new List<string> { track };
            if (!_settings.IsFourFinger)
            {
                // 双手模式：检查同一只手的所有轨道
                tracksToCheck = GetSameHandTracks(track);
            }

            // 检查整个时间范围内的位置
            for (long pos = startPos; pos <= endPos; pos++)
            {
                foreach (string checkTrack in tracksToCheck)
                {
                    if (IsPositionOccupied(checkTrack, pos))
                    {
                        if (checkTrack != track)
                        {
                            System.Diagnostics.Debug.WriteLine($"长音符冲突: {track}轨道长音符与同手{checkTrack}轨道冲突，位置{pos}");
                        }
                        return false;
                    }
                }
            }
            return true;
        }

        private bool CanPlaceSlipNote(IIdol note, string startTrack, string endTrack)
        {
            long position = CalculatePosition(note.Bar, note.Pos);

            // 检查起始轨道和结束轨道是否都可用
            bool startTrackAvailable = !IsPositionOccupied(startTrack, position);
            bool endTrackAvailable = string.IsNullOrEmpty(endTrack) || !IsPositionOccupied(endTrack, position);

            if (!startTrackAvailable || !endTrackAvailable)
                return false;

            return true;
        }

        private void OccupyShortNotePosition(IIdol note)
        {
            long position = CalculatePosition(note.Bar, note.Pos);
            SetPositionOccupied(note.TargetTrack, position);
        }

        private void OccupyLongNotePositions(IIdol note)
        {
            long startPos = CalculatePosition(note.Bar, note.Pos);
            long endPos = CalculatePosition(note.EndBar, note.EndPos);

            // 在非四指模式下，长音符需要占用同一只手的所有轨道
            List<string> tracksToOccupy = new List<string> { note.TargetTrack };
            if (!_settings.IsFourFinger)
            {
                // 双手模式：占用同一只手的所有轨道
                tracksToOccupy = GetSameHandTracks(note.TargetTrack);
                System.Diagnostics.Debug.WriteLine($"长音符占用: {note.TargetTrack}轨道长音符占用同手所有轨道: {string.Join(",", tracksToOccupy)}");
            }

            // 占用整个时间范围内的位置
            for (long pos = startPos; pos <= endPos; pos++)
            {
                foreach (string track in tracksToOccupy)
                {
                    SetPositionOccupied(track, pos);
                }
            }
        }

        private void OccupySlipNotePositions(IIdol note)
        {
            long position = CalculatePosition(note.Bar, note.Pos);
            SetPositionOccupied(note.TargetTrack, position);
            if (!string.IsNullOrEmpty(note.EndTrack))
            {
                SetPositionOccupied(note.EndTrack, position);
            }
        }

        private long CalculatePosition(double bar, double pos)
        {
            return (long)(bar * 64 + pos);
        }

        private bool IsPositionOccupied(string track, long position)
        {
            return _occupiedPositions.ContainsKey(track) &&
                   _occupiedPositions[track].ContainsKey(position) &&
                   _occupiedPositions[track][position];
        }

        private void SetPositionOccupied(string track, long position)
        {
            if (!_occupiedPositions.ContainsKey(track))
            {
                _occupiedPositions[track] = new Dictionary<long, bool>();
            }
            _occupiedPositions[track][position] = true;
        }
    }
}
