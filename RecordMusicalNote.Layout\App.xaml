﻿<Application x:Class="MyWPF.Layout.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:MyWPF.Layout"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design主题 -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />

                <!-- 自定义样式 -->
                <ResourceDictionary>
                    <!-- 卡片样式 -->
                    <Style x:Key="MaterialCard" TargetType="materialDesign:Card">
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="Padding" Value="16"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                    </Style>

                    <!-- 按钮样式 -->
                    <Style x:Key="MaterialRaisedButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="Height" Value="36"/>
                        <Setter Property="MinWidth" Value="88"/>
                    </Style>

                    <!-- 文本框样式 -->
                    <Style x:Key="MaterialTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="VerticalAlignment" Value="Center"/>
                    </Style>

                    <!-- 标签样式 -->
                    <Style x:Key="MaterialLabel" TargetType="Label">
                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="VerticalAlignment" Value="Center"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
