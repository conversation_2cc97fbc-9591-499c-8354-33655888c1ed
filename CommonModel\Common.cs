﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CommonModel
{
  public   class Common
    {
        public static IList<SyllabelAndNum> GetSyllabelAndNum()
        {
            IList<SyllabelAndNum> SyllabelAndNum = new List<SyllabelAndNum>();
            SyllabelAndNum.Add(new SyllabelAndNum(1, "C"));
            SyllabelAndNum.Add(new SyllabelAndNum(2, "C+"));
            SyllabelAndNum.Add(new SyllabelAndNum(2, "D-"));
            SyllabelAndNum.Add(new SyllabelAndNum(3, "D"));
            SyllabelAndNum.Add(new SyllabelAndNum(4, "D+"));
            SyllabelAndNum.Add(new SyllabelAndNum(4, "E-"));
            SyllabelAndNum.Add(new SyllabelAndNum(5, "E"));
            SyllabelAndNum.Add(new SyllabelAndNum(6, "F"));
            SyllabelAndNum.Add(new SyllabelAndNum(7, "F+"));
            SyllabelAndNum.Add(new SyllabelAndNum(7, "G-"));
            SyllabelAndNum.Add(new SyllabelAndNum(8, "G"));
            SyllabelAndNum.Add(new SyllabelAndNum(9, "G+"));
            SyllabelAndNum.Add(new SyllabelAndNum(9, "A-"));
            SyllabelAndNum.Add(new SyllabelAndNum(10, "A"));
            SyllabelAndNum.Add(new SyllabelAndNum(11, "A+"));
            SyllabelAndNum.Add(new SyllabelAndNum(12, "B"));
            return SyllabelAndNum;
        }

        public static IList<IdolTrack>GetIdolTracks(int trackCount)
        {
            IList<IdolTrack> tracks = new List<IdolTrack>();
            if (trackCount == 4)
            {
                IdolTrack Left2 = new IdolTrack() { ID = 1, Name = "Left2", CanUse = true };
                IdolTrack Left1 = new IdolTrack() { ID = 2, Name = "Left1", CanUse = true };
                IdolTrack Right1 = new IdolTrack() { ID = 3, Name = "Right1", CanUse = true };
                IdolTrack Right2 = new IdolTrack() { ID = 4, Name = "Right2", CanUse = true };
                tracks.Add(Left2);
                tracks.Add(Left1);
                tracks.Add(Right1);
                tracks.Add(Right2);
            }
            else
            {
                IdolTrack Left2 = new IdolTrack() { ID = 1, Name = "Left2", CanUse = true };
                IdolTrack Left1 = new IdolTrack() { ID = 2, Name = "Left1", CanUse = true };
                IdolTrack Middle = new IdolTrack() { ID = 3, Name = "Middle", CanUse = true };
                IdolTrack Right1 = new IdolTrack() { ID = 4, Name = "Right1", CanUse = true };
                IdolTrack Right2 = new IdolTrack() { ID = 5, Name = "Right2", CanUse = true };
                tracks.Add(Left2);
                tracks.Add(Left1);
                tracks.Add(Middle);
                tracks.Add(Right1);
                tracks.Add(Right2);
            }
            return tracks;

        }
    }


    public class SyllabelAndNum
    {   
        public SyllabelAndNum(int cm,string syllabel)
        {
            this.CM = cm;
            this.Syllabel = syllabel;
        }

        /// <summary>
        /// 唱名
        /// </summary>
        public int CM { get; set; }
        /// <summary>
        /// 音名
        /// </summary>
        public string Syllabel { get; set; }
    }
}
