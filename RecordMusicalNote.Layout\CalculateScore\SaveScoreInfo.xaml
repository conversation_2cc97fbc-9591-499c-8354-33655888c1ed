﻿<Window x:Class="MyWPF.Layout.CalculateBD"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MyWPF.Layout"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="分数计算" Height="500" Width="800" MinHeight="400" MinWidth="600"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <materialDesign:DialogHost Identifier="SaveScoreInfoDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 顶部标题栏 -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
                <DockPanel>
                    <materialDesign:PackIcon Kind="Calculator" Width="24" Height="24" VerticalAlignment="Center" DockPanel.Dock="Left"/>
                    <TextBlock Text="分数计算" FontSize="20" FontWeight="Medium" VerticalAlignment="Center" Margin="16,0,0,0"/>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- 主要内容区域 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="24">
                <StackPanel>
                    <!-- 文件信息卡片 -->
                    <materialDesign:Card Style="{StaticResource MaterialCard}" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="文件信息" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBox Grid.Row="0" Name="txtSongName"
                                       materialDesign:HintAssist.Hint="歌曲名称"
                                       materialDesign:HintAssist.IsFloating="True"
                                       Style="{StaticResource MaterialTextBox}"
                                       IsReadOnly="True"
                                       Margin="0,0,0,16"/>

                                <TextBox Grid.Row="1" Name="txtFilePath"
                                       materialDesign:HintAssist.Hint="文件路径"
                                       materialDesign:HintAssist.IsFloating="True"
                                       Style="{StaticResource MaterialTextBox}"
                                       IsReadOnly="True"
                                       Margin="0,0,0,16"/>

                                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right">
                                    <Button Name="btnOpen" Content="选择文件" Click="btnOpen_Click"
                                          Style="{StaticResource MaterialRaisedButton}">
                                        <Button.ToolTip>
                                            <ToolTip Content="选择要计算分数的谱面文件"/>
                                        </Button.ToolTip>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 游戏模式选择卡片 -->
                    <materialDesign:Card Style="{StaticResource MaterialCard}">
                        <StackPanel>
                            <TextBlock Text="游戏模式" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                            <TextBlock Text="请选择游戏模式:" Style="{StaticResource MaterialDesignSubtitle2TextBlock}" Margin="0,0,0,12"/>

                            <StackPanel Orientation="Horizontal">
                                <RadioButton Name="modeXD" Content="星动" GroupName="b" IsChecked="True"
                                           Style="{StaticResource MaterialDesignRadioButton}" Margin="0,0,24,0"/>
                                <RadioButton Name="modeDZ" Content="弹珠" GroupName="b"
                                           Style="{StaticResource MaterialDesignRadioButton}" Margin="0,0,24,0"/>
                                <RadioButton Name="modePP" Content="泡泡" GroupName="b"
                                           Style="{StaticResource MaterialDesignRadioButton}" Margin="0,0,24,0"/>
                                <RadioButton Name="modeXY" Content="弦月" GroupName="b"
                                           Style="{StaticResource MaterialDesignRadioButton}"/>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </ScrollViewer>

            <!-- 底部按钮区域 -->
            <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Name="btnSave" Content="保存分数信息" Click="btnSave_Click"
                          Style="{StaticResource MaterialDesignRaisedButton}" MinWidth="120">
                        <Button.ToolTip>
                            <ToolTip Content="计算并保存分数信息"/>
                        </Button.ToolTip>
                    </Button>
                </StackPanel>
            </materialDesign:ColorZone>
        </Grid>
    </materialDesign:DialogHost>
</Window>
