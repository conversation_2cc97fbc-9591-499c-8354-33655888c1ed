# 🎵 Python AI音游写谱助手

## 项目概述

基于机器学习的音游谱面自动生成系统，支持从MIDI文件生成Malody、节奏大师等多种音游格式的谱面。

## 🎯 功能特性

### 核心功能
- **智能谱面生成** - 基于MIDI文件自动生成高质量谱面
- **多格式支持** - Malody(.mc)、节奏大师(.imd)、osu!(.osu)等
- **难度自适应** - 根据目标难度自动调整音符密度和复杂度
- **风格学习** - 学习特定制谱师的风格特征

### AI模型架构
- **MIDI特征提取** - 提取音符序列、节拍、和弦等特征
- **序列建模** - 使用LSTM/Transformer建模音符序列
- **质量评估** - 基于游戏性评分优化谱面质量

## 🛠️ 技术架构

```
MIDI输入 → 特征提取 → AI模型 → 谱面生成 → 格式转换 → 输出
    ↓         ↓        ↓        ↓        ↓        ↓
   .mid    音符序列   LSTM    音符序列   JSON/XML  .mc/.imd
```

## 📁 项目结构

```
python_ai_chart_assistant/
├── src/                    # 源代码
│   ├── audio_analysis/     # 音频分析模块
│   │   ├── midi_analyzer.py      # MIDI文件分析器
│   │   ├── feature_extractor.py  # 特征提取器
│   │   └── beat_detector.py      # 节拍检测器
│   ├── models/            # AI模型定义
│   │   ├── chart_generation_model.py  # 谱面生成模型
│   │   └── difficulty_predictor.py    # 难度预测器
│   ├── chart_generation/  # 谱面生成
│   │   ├── chart_data.py         # 谱面数据结构
│   │   ├── chart_generator.py    # 谱面生成器
│   │   └── post_processor.py     # 后处理器
│   ├── format_converters/ # 格式转换器
│   │   ├── base_converter.py     # 基础转换器
│   │   ├── malody_converter.py   # Malody格式转换器
│   │   └── rhythm_master_converter.py  # 节奏大师转换器
│   ├── utils/             # 工具模块
│   │   ├── config_manager.py     # 配置管理器
│   │   └── logger_setup.py       # 日志设置
│   ├── chart_generator.py # 主接口
│   └── cli.py            # 命令行接口
├── ui/                   # Streamlit Web界面
│   ├── streamlit_app.py         # 主UI应用
│   └── README.md               # UI使用说明
├── config/               # 配置文件
│   └── default_config.yaml      # 默认配置
├── examples/             # 示例代码
│   └── basic_usage.py           # 基础使用示例
├── data/                # 数据目录
│   ├── midi/            # MIDI训练数据
│   ├── charts/          # 训练谱面数据
│   └── processed/       # 预处理后的数据
├── models/              # 训练好的模型
├── output/              # 输出目录
├── logs/                # 日志目录
├── requirements.txt     # 依赖列表
├── setup.py            # 安装脚本
├── run_ui.py           # UI启动脚本
└── README.md           # 项目说明
```

## 🚀 快速开始

### 1. 环境配置
```bash
# 克隆项目（如果从git获取）
git clone <repository-url>
cd python_ai_chart_assistant

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 或者使用开发模式安装
pip install -e .
```

### 2. 启动UI界面（推荐）
```bash
# 启动Streamlit Web界面
python run_ui.py

# 指定端口和地址
python run_ui.py --port 8502 --host 0.0.0.0

# 然后在浏览器访问: http://localhost:8501
```

**UI界面功能:**
- 🎵 **谱面生成** - 拖拽上传MIDI，一键生成谱面
- 🔍 **MIDI分析** - 可视化分析音乐特征
- 🎯 **难度预测** - AI预测游戏难度
- 🔄 **格式转换** - 在不同音游格式间转换
- 🚀 **模型训练** - 可视化训练参数设置
- ⚙️ **系统设置** - 配置模型和参数

### 3. 运行示例
```bash
# 运行基础示例
python examples/basic_usage.py

# 使用命令行工具
python -m src.cli --help
```

### 3. 基础使用
```python
from src.chart_generator import AIChartGenerator

# 初始化生成器
generator = AIChartGenerator()

# 从MIDI文件生成谱面
success = generator.generate_from_midi(
    midi_path="path/to/your/song.mid",
    output_path="output/song.mc",
    format_name="malody",
    difficulty=7,
    style="balanced",
    title="我的歌曲",
    artist="艺术家"
)

if success:
    print("✅ 谱面生成成功！")
else:
    print("❌ 谱面生成失败")
```

### 4. 命令行使用
```bash
# 生成谱面
python -m src.cli generate input.mid output.mc --difficulty 6 --style balanced

# 分析MIDI文件
python -m src.cli analyze input.mid

# 预测难度
python -m src.cli predict input.mid

# 格式转换
python -m src.cli convert input.json output.mc --to-format malody

# 查看支持的格式
python -m src.cli formats

# 批量生成
python -m src.cli batch midi_folder/ output_folder/ --format malody
```

## 📊 数据需求

### 训练数据规模
- **音频数据**: 1000+ 首歌曲 (各种风格、BPM)
- **谱面数据**: 5000+ 个高质量谱面
- **标注数据**: 音乐结构、难度评级、风格标签

### 数据来源建议
1. **开源音游社区** - osu!, Malody社区谱面
2. **MIDI数据库** - 转换为训练数据
3. **音乐数据集** - Free Music Archive, MusicNet
4. **现有项目数据** - 利用当前C#项目的谱面数据

## 🧠 AI模型设计

### 1. 音频特征提取器
```python
class AudioFeatureExtractor:
    def extract_features(self, audio_path):
        # MFCC特征
        # 色度特征  
        # 节拍特征
        # 频谱质心
        return features
```

### 2. 谱面生成模型
```python
class ChartGenerationModel:
    def __init__(self):
        self.encoder = AudioEncoder()      # 音频编码器
        self.decoder = ChartDecoder()      # 谱面解码器
        self.attention = AttentionLayer()  # 注意力机制
```

### 3. 质量评估模型
```python
class ChartQualityEvaluator:
    def evaluate(self, chart, audio):
        # 游戏性评分
        # 难度一致性
        # 音乐匹配度
        return quality_score
```

## 🎮 支持的音游格式

### Malody (.mc)
```json
{
    "meta": {
        "creator": "AI Assistant",
        "version": "1.0"
    },
    "time": [
        {"beat": [0,0,1], "bpm": 120}
    ],
    "note": [
        {"beat": [1,0,1], "column": 0},
        {"beat": [2,0,1], "column": 1}
    ]
}
```

### 节奏大师 (.imd)
```xml
<level>
    <info bpm="120" difficulty="7"/>
    <notes>
        <note time="1000" track="1" type="click"/>
        <note time="2000" track="2" type="hold" duration="500"/>
    </notes>
</level>
```

## 📈 训练流程

### 阶段1: 数据预处理
1. 音频特征提取和标准化
2. 谱面数据清洗和格式统一
3. 创建训练/验证/测试数据集

### 阶段2: 模型训练
1. 预训练音频特征提取器
2. 训练谱面生成模型
3. 微调和优化超参数

### 阶段3: 质量优化
1. 训练质量评估模型
2. 使用强化学习优化生成质量
3. A/B测试和用户反馈收集

## 🔧 与现有C#项目集成

### 数据共享
```python
# 读取C#项目的XML谱面数据
def load_csharp_chart_data(xml_path):
    # 解析XML格式谱面
    # 转换为Python训练格式
    return chart_data

# 生成C#项目兼容的输出
def export_to_csharp_format(chart):
    # 转换为XML格式
    # 兼容现有数据模型
    return xml_data
```

### API接口
```python
# 提供REST API供C#项目调用
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/generate_chart', methods=['POST'])
def generate_chart():
    audio_file = request.files['audio']
    difficulty = request.json['difficulty']
    
    chart = ai_generator.generate(audio_file, difficulty)
    return jsonify(chart.to_dict())
```

## 📋 开发计划

### Phase 1: 基础框架 (4-6周)
- [ ] 项目结构搭建
- [ ] 音频处理模块
- [ ] 数据预处理管道
- [ ] 基础模型架构

### Phase 2: 模型训练 (6-8周)  
- [ ] 收集和清洗训练数据
- [ ] 实现和训练核心模型
- [ ] 模型评估和优化
- [ ] 格式转换器开发

### Phase 3: 集成优化 (4-6周)
- [ ] 与C#项目集成
- [ ] 用户界面开发
- [ ] 性能优化
- [ ] 文档和测试

## 🤝 贡献指南

欢迎贡献代码、数据或建议！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
