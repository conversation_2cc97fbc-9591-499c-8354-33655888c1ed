using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;

namespace MyWPF.Layout
{
    /// <summary>
    /// 复制音符对话框
    /// </summary>
    public partial class CopyNotesDialog : Window
    {
        public class CopySettings
        {
            public int SourceStartBar { get; set; }
            public int SourceEndBar { get; set; }
            public int TargetStartBar { get; set; }
            public int TargetEndBar { get; set; }
            public bool IsMirrorCopy { get; set; } = true;
            public bool ClearTargetFirst { get; set; } = true;
        }

        public CopySettings Settings { get; private set; }
        public bool DialogResult { get; private set; } = false;

        private List<NoteEditor.NoteViewModel> _allNotes;

        public CopyNotesDialog(List<NoteEditor.NoteViewModel> allNotes)
        {
            InitializeComponent();
            _allNotes = allNotes;
            
            // 设置默认值
            if (_allNotes.Any())
            {
                var minBar = _allNotes.Min(n => n.Bar);
                var maxBar = _allNotes.Max(n => n.Bar);
                
                txtSourceStartBar.Text = minBar.ToString();
                txtSourceEndBar.Text = Math.Min(minBar + 3, maxBar).ToString();
                txtTargetStartBar.Text = Math.Min(minBar + 4, maxBar).ToString();
                txtTargetEndBar.Text = Math.Min(minBar + 7, maxBar).ToString();
            }
            
            // 绑定事件
            txtSourceStartBar.TextChanged += UpdatePreview;
            txtSourceEndBar.TextChanged += UpdatePreview;
            txtTargetStartBar.TextChanged += UpdatePreview;
            txtTargetEndBar.TextChanged += UpdatePreview;
            chkMirrorCopy.Checked += UpdatePreview;
            chkMirrorCopy.Unchecked += UpdatePreview;
            chkClearTarget.Checked += UpdatePreview;
            chkClearTarget.Unchecked += UpdatePreview;
            
            UpdatePreview(null, null);
        }

        private void UpdatePreview(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput(false))
                {
                    txtPreviewInfo.Text = "请输入有效的范围参数";
                    return;
                }

                var settings = GetCurrentSettings();
                var sourceNotes = GetNotesInRange(settings.SourceStartBar, settings.SourceEndBar);
                var targetNotes = GetNotesInRange(settings.TargetStartBar, settings.TargetEndBar);
                
                var sourceCount = sourceNotes.Count;
                var targetCount = targetNotes.Count;
                var sourceRange = settings.SourceEndBar - settings.SourceStartBar + 1;
                var targetRange = settings.TargetEndBar - settings.TargetStartBar + 1;

                string mirrorText = settings.IsMirrorCopy ? "是" : "否";
                string clearText = settings.ClearTargetFirst ? "是" : "否";

                txtPreviewInfo.Text = $"源范围：{sourceRange}小节，{sourceCount}个音符\n" +
                                    $"目标范围：{targetRange}小节，{targetCount}个音符\n" +
                                    $"镜像复制：{mirrorText}\n" +
                                    $"清空目标：{clearText}\n" +
                                    $"操作：将复制{sourceCount}个音符到目标区域";
            }
            catch
            {
                txtPreviewInfo.Text = "预览信息计算失败";
            }
        }

        private void BtnPreview_Click(object sender, RoutedEventArgs e)
        {
            UpdatePreview(sender, e);
        }

        private void BtnOK_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput(true))
                return;

            Settings = GetCurrentSettings();
            DialogResult = true;
            Close();
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool ValidateInput(bool showErrors)
        {
            // 验证源范围
            if (!int.TryParse(txtSourceStartBar.Text, out int sourceStart))
            {
                if (showErrors) MessageBox.Show("请输入有效的源起始小节", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!int.TryParse(txtSourceEndBar.Text, out int sourceEnd))
            {
                if (showErrors) MessageBox.Show("请输入有效的源结束小节", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // 验证目标范围
            if (!int.TryParse(txtTargetStartBar.Text, out int targetStart))
            {
                if (showErrors) MessageBox.Show("请输入有效的目标起始小节", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!int.TryParse(txtTargetEndBar.Text, out int targetEnd))
            {
                if (showErrors) MessageBox.Show("请输入有效的目标结束小节", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // 验证范围逻辑
            if (sourceStart > sourceEnd)
            {
                if (showErrors) MessageBox.Show("源起始小节不能大于结束小节", "范围错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (targetStart > targetEnd)
            {
                if (showErrors) MessageBox.Show("目标起始小节不能大于结束小节", "范围错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // 验证范围长度相等
            int sourceLength = sourceEnd - sourceStart + 1;
            int targetLength = targetEnd - targetStart + 1;
            if (sourceLength != targetLength)
            {
                if (showErrors) MessageBox.Show($"源范围({sourceLength}小节)和目标范围({targetLength}小节)长度必须相等", 
                                              "范围错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // 验证范围不重叠
            if (!(sourceEnd < targetStart || targetEnd < sourceStart))
            {
                if (showErrors) MessageBox.Show("源范围和目标范围不能重叠", "范围错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private CopySettings GetCurrentSettings()
        {
            return new CopySettings
            {
                SourceStartBar = int.Parse(txtSourceStartBar.Text),
                SourceEndBar = int.Parse(txtSourceEndBar.Text),
                TargetStartBar = int.Parse(txtTargetStartBar.Text),
                TargetEndBar = int.Parse(txtTargetEndBar.Text),
                IsMirrorCopy = chkMirrorCopy.IsChecked == true,
                ClearTargetFirst = chkClearTarget.IsChecked == true
            };
        }

        private List<NoteEditor.NoteViewModel> GetNotesInRange(int startBar, int endBar)
        {
            return _allNotes.Where(n => n.Bar >= startBar && n.Bar <= endBar).ToList();
        }
    }
}
