﻿using Microsoft.Win32;
using MyWPF.Layout;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace RecordMusicalNote
{
    /// <summary>
    /// DrawNote.xaml 的交互逻辑
    /// </summary>
    public partial class DrawNote : Window
    {
        public DrawNote()
        {
            InitializeComponent();
            this.WindowStartupLocation = WindowStartupLocation.CenterScreen;
            //  _test.MouseDoubleClick += _test_MouseDoubleClick;
            _test.MouseDown += _test_MouseDown;
            _radSingle.IsChecked = true;
            btnPlay.IsEnabled = false;

        }
        private string currentTypeStr
        {
            get
            {
                if (_radSingle.IsChecked.Value)
                    return "点";
                if (_radLong.IsChecked.Value)
                    return "长条";
                else
                    return "箭头";
            }
        }
        private int currentType
        {
            get
            {
                if (_radSingle.IsChecked.Value)
                    return 0;
                if (_radLong.IsChecked.Value)
                    return 1;
                else
                    return 2;
            }
        }
        private void _test_MouseDown(object sender, MouseButtonEventArgs e)
        {

        }

        private void _test_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            IdolCellInfo info = _test.SelectedItem as IdolCellInfo;
            if (info != null)
            {
                int selectedColumnsIndex = _test.CurrentColumn.DisplayIndex;
            }
        }

    
        ObservableCollection<IdolCellInfo> infos = new ObservableCollection<IdolCellInfo>();
        float totalTimePerSmallMatter;
        private void GenerateCell(int columnCount)
        {
            if (TotalSmallMatter <= 0)
            {
                MessageBox.Show("总时间过小");
                return;
            }
            for (int i = 0; i < columnCount; i++)
            {

                DataGridTextColumn column = new DataGridTextColumn();
                System.Windows.Data.Binding binding = null;
                binding = new Binding("ColumnStr" + i);
                binding.Mode = BindingMode.TwoWay;
                binding.UpdateSourceTrigger = UpdateSourceTrigger.PropertyChanged;
                column.Binding = binding;
                column.Width = this.Width / columnCount / 2;
                _test.Columns.Add(column);
            }
            _test.Columns.Add(new DataGridTextColumn() { Header = "时间", Width = 100, Binding = new Binding("CurrentTime") });
            _test.Columns.Add(new DataGridTextColumn() { Header = "格子数", Width = 50, Binding = new Binding("CurrentCount") });
            _test.Columns.Add(new DataGridTextColumn() { Header = "提示", Width = 50, Binding = new Binding("Tip") });
            totalTimePerSmallMatter = TotalMusicTime / TotalSmallMatter / 32;

            for (int i = 0; i < TotalSmallMatter * 32; i++)
            {
                IdolCellInfo info = new IdolCellInfo();
                info.CurrentCount = 1 + i;
                if ((1 + i) % 8 == 0)
                    info.Tip = "一拍";
                if ((1 + i) % 32 == 0)
                    info.Tip = "一小节";
                info.CurrentTime = totalTimePerSmallMatter * i;
                infos.Add(info);


            }
            for (int i = -31; i <= 0; i++)
            {
                IdolCellInfo info = new IdolCellInfo();
                info.CurrentCount = i;
                info.CurrentTime = 0;
                infos.Add(info);
            }
            for (int i = TotalSmallMatter * 32 + 1; i <= TotalSmallMatter * 32 + 32; i++)
            {
                IdolCellInfo info = new IdolCellInfo();
                info.CurrentCount = i;
                info.CurrentTime = 0;
                infos.Add(info);
            }
            _test.ItemsSource = infos.OrderByDescending(a => a.CurrentCount);
            IdolCellInfo minCellInfo = infos.Where(a => a.CurrentCount == infos.Min(b => b.CurrentCount)).FirstOrDefault();
            _test.ScrollIntoView(minCellInfo);


        }
        DispatcherTimer timer = null;
        public int TotalSmallMatter
        {
            get
            {
                return (int)TotalMusicTime / 1000 / 60 * ((int)BPM / 4);
            }
        }
        public float BPM
        {
            get
            {
                float bpm = 0;
                float.TryParse(txtBPM.Text, out bpm);
                return bpm;

            }
        }
        public float TotalMusicTime
        {
            get
            {
                float totalTime = 0;
                float.TryParse(txtTotalTime.Text, out totalTime);
                return totalTime;

            }
        }
        public int KeyCount
        {
            get
            {
                int keyCount = 0;
                int.TryParse(txtKeyCount.Text, out keyCount);
                return keyCount;

            }
        }


        private void btnGenerate_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtKeyCount.Text))
            {
                MessageBox.Show("请输入键数");
                return;
            }
            int keyCount = 0;
            if (!int.TryParse(txtKeyCount.Text, out keyCount))
            {
                MessageBox.Show("输入的键数不是数字");
                return;
            }
            _test.Columns.Clear();
            infos.Clear();
            GenerateCell(keyCount);
            nextCount = 0;
            btnPlay.IsEnabled = true;
        }

        private void btnPlay_Click(object sender, RoutedEventArgs e)
        {

            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(totalTimePerSmallMatter / 1000);
            timer.Tick += new EventHandler(timer_tick);
            timer.Start();
            btnPlay.IsEnabled = false;
            mediaelement.Play();
        }
        private IList<IdolCellInfo> CellInfos
        {
            get
            {
                return _test.ItemsSource.Cast<IdolCellInfo>().OrderBy(a => a.CurrentCount).ToList();
            }
        }
        int nextCount = 0;
        private void timer_tick(object sender, EventArgs e)
        {
            IdolCellInfo currentInfo = CellInfos[nextCount];
            DataGridRow row = (DataGridRow)_test.ItemContainerGenerator.ContainerFromItem(currentInfo);
            _test.UpdateLayout();
            _test.ScrollIntoView(currentInfo);
            if (row != null)
                row.Background = new SolidColorBrush(Colors.Red);
            if (nextCount > 0)
            {
                IdolCellInfo lastCellInfo = CellInfos[nextCount - 1];
                DataGridRow lastRow = (DataGridRow)_test.ItemContainerGenerator.ContainerFromItem(lastCellInfo);
                if (lastRow != null)
                    lastRow.Background = new SolidColorBrush(Colors.White);
            }
            if (nextCount < CellInfos.Count - 1)
                nextCount += 1;
            if (nextCount == CellInfos.Count - 1)
            {
                if (timer != null)
                    timer.Stop();
            }
        }

        private void btnPause_Click(object sender, RoutedEventArgs e)
        {
            if (timer != null)
                timer.Stop();
            btnPlay.IsEnabled = true;
            mediaelement.Pause();
        }

        private void btnChooseMusic_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "音频格式(*.mp3,*.wav)|*.mp3;*.wav";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            mediaelement.Source = new Uri(dlg.FileName);
        }

        private void mediaelement_MediaOpened(object sender, RoutedEventArgs e)
        {

        }
    }
}
