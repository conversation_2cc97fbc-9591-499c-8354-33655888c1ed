﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote
{
    public interface IScoreDetailInfo
    {
        int Id { get; set; }

        /// <summary>
        /// 休闲房、音乐实验室的分数（因为与排位、百人的分数有差别）排位，百人比休闲房、音乐实验室50-99多一分
        /// 不带技能爆气的基础分
        /// </summary>
        double  ScoreNumInXXAndLab { get; }
        /// <summary>
        /// 排位百人的分数（因为与排位、百人的分数有差别）排位，百人比休闲房、音乐实验室50-99多一分
        /// 不带技能爆气的基础分
        /// </summary>
        double ScoreNumInBRAndRank { get; }
        /// <summary>
        /// 爆气技能，爆气状态(总分数：爆气+连击+技能)
        /// </summary>
        double ScoreNumBQTechAndInFire { get; }
        /// <summary>
        /// 极限技能，非爆气状态
        /// </summary>
        double ScoreNumJXTechAndNotInFire { get; }

        /// <summary>
        /// 极限技能，爆气状态（总分数：爆气+连击+技能）
        /// </summary>
        double ScoreNumJXTechAndInFire { get; }
        int Bar { get; set; }

        int Pos { get; set; }

        int EndBar { get; set; }

        int EndPos { get; set; }

        int CurrentCount { get; set; }

        int EndCount { get; set; }
        int CurrentCombo { get; set; }

        int StartCombo { get; set; }
        int EndCombo { get; set; }

        int NoteType { get; set; }
        /// <summary>
        /// 需要显示在datagrid的分数，如果有需要可以根据条件显示不同的分数
        /// </summary>
        double ShowScore { get; set; }

        string NoteTypeStr { get; }
        int SonId { get; set; }
        int XMLNoteId { get; set; }
    }
    public  interface IIdolScoreDetailInfo: IScoreDetailInfo
    {

    }

    public interface IPinballScoreDetailInfo: IScoreDetailInfo
    {
        
    }

    public interface IBubbleScoreDetailInfo: IScoreDetailInfo
    {

    }
}

