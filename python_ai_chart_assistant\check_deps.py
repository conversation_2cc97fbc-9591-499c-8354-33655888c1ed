#!/usr/bin/env python3
"""
依赖检查脚本

检查AI音游写谱助手所需的所有依赖是否正确安装
"""

import sys
import importlib

def check_module(module_name, description=""):
    """检查单个模块"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {module_name:<20} {description}")
        return True
    except ImportError as e:
        print(f"❌ {module_name:<20} {description} - {e}")
        return False

def main():
    """主检查函数"""
    print("🔍 AI音游写谱助手 - 依赖检查")
    print("=" * 50)
    
    # 核心依赖
    print("\n📦 核心依赖:")
    core_deps = [
        ("numpy", "数值计算"),
        ("pandas", "数据处理"),
        ("matplotlib", "图表绘制"),
        ("streamlit", "Web界面")
    ]
    
    core_ok = 0
    for module, desc in core_deps:
        if check_module(module, desc):
            core_ok += 1
    
    # MIDI处理依赖
    print("\n🎵 MIDI处理:")
    midi_deps = [
        ("pretty_midi", "MIDI文件处理"),
        ("mido", "MIDI I/O"),
        ("music21", "音乐分析")
    ]
    
    midi_ok = 0
    for module, desc in midi_deps:
        if check_module(module, desc):
            midi_ok += 1
    
    # AI/ML依赖
    print("\n🤖 机器学习:")
    ml_deps = [
        ("torch", "PyTorch深度学习"),
        ("torchvision", "PyTorch视觉"),
        ("torchaudio", "PyTorch音频"),
        ("sklearn", "机器学习工具")
    ]
    
    ml_ok = 0
    for module, desc in ml_deps:
        if check_module(module, desc):
            ml_ok += 1
    
    # 可选依赖
    print("\n🔧 可选依赖:")
    optional_deps = [
        ("tensorflow", "TensorFlow深度学习"),
        ("librosa", "音频分析"),
        ("soundfile", "音频文件处理"),
        ("plotly", "交互式图表")
    ]
    
    optional_ok = 0
    for module, desc in optional_deps:
        if check_module(module, desc):
            optional_ok += 1
    
    # 项目模块检查
    print("\n🎮 项目模块:")
    try:
        sys.path.insert(0, '.')
        from src.chart_generator import AIChartGenerator
        print("✅ chart_generator      谱面生成器")
        generator_ok = True
    except ImportError as e:
        print(f"❌ chart_generator      谱面生成器 - {e}")
        generator_ok = False
    
    try:
        from src.utils.config_manager import ConfigManager
        print("✅ config_manager       配置管理器")
        config_ok = True
    except ImportError as e:
        print(f"❌ config_manager       配置管理器 - {e}")
        config_ok = False
    
    # 总结
    print("\n📊 检查总结:")
    print("=" * 30)
    print(f"核心依赖: {core_ok}/{len(core_deps)}")
    print(f"MIDI处理: {midi_ok}/{len(midi_deps)}")
    print(f"机器学习: {ml_ok}/{len(ml_deps)}")
    print(f"可选依赖: {optional_ok}/{len(optional_deps)}")
    print(f"项目模块: {int(generator_ok + config_ok)}/2")
    
    # 建议
    print("\n💡 建议:")
    if midi_ok < len(midi_deps):
        print("- 安装MIDI处理库: pip install pretty_midi mido music21")
    if ml_ok < 2:  # 至少需要torch
        print("- 安装PyTorch: pip install torch torchvision torchaudio")
    if core_ok < len(core_deps):
        print("- 安装核心依赖: pip install numpy pandas matplotlib streamlit")
    
    if generator_ok and midi_ok >= 1 and core_ok >= 3:
        print("🎉 依赖检查通过！可以正常使用AI音游写谱助手")
        print("   重启UI界面: python run_ui.py")
    else:
        print("⚠️ 部分依赖缺失，建议运行完整安装:")
        print("   python install_deps.py")

if __name__ == "__main__":
    main()
