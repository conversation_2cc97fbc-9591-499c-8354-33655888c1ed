﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C3FF4408-7B95-4D21-BBD8-91612362CD8C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RecordMusicalNote.Library</RootNamespace>
    <AssemblyName>RecordMusicalNote.Library</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DataModel\IMDTxtDataModel.cs" />
    <Compile Include="Idol.cs" />
    <Compile Include="LevelInfo.cs" />
    <Compile Include="Pinball.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Repository\BubbleRepository.cs" />
    <Compile Include="Repository\Common.cs" />
    <Compile Include="Repository\IdolRepository.cs" />
    <Compile Include="Repository\MidiRepository.cs" />
    <Compile Include="Repository\PinballRepository.cs" />
    <Compile Include="Repository\ScoreReposity.cs" />
    <Compile Include="Score.cs" />
    <Compile Include="ScoreDetailInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CommonModel\CommonModel.csproj">
      <Project>{1b1cc2b6-c6ac-40db-9d24-c89c89cf024c}</Project>
      <Name>CommonModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\MidiSheetMusic\MidiSheetMusic.csproj">
      <Project>{0b5cba19-8987-4ebe-8d7f-4683fcaa5a2f}</Project>
      <Name>MidiSheetMusic</Name>
    </ProjectReference>
    <ProjectReference Include="..\RecordMusicalNote.DataModel\RecordMusicalNote.DataModel.csproj">
      <Project>{97E616EA-7301-4498-8E26-4981E4312A9B}</Project>
      <Name>RecordMusicalNote.DataModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\RecordMusicalNote\RecordMusicalNote.csproj">
      <Project>{7A2C3CF8-3691-4888-97FB-493BCD84F124}</Project>
      <Name>RecordMusicalNote</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.Config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>