using System;
using System.Collections.Generic;
using RecordMusicalNote;

namespace MyWPF.Layout.AI
{
    /// <summary>
    /// AI生成请求参数
    /// </summary>
    public class AIGenerationRequest
    {
        public string MidiFilePath { get; set; }
        public string GameMode { get; set; } = "idol"; // idol/pinball/bubble
        public int TrackCount { get; set; } = 4;
        public int Difficulty { get; set; } = 5; // 1-10
        public string Style { get; set; } = "rhythmic"; // rhythmic/melodic/hybrid
        public double NoteDensity { get; set; } = 0.7; // 0.0-1.0
        public int MaxNotesPerSecond { get; set; } = 8;
        public bool FollowMelody { get; set; } = true;
        public bool TrackBalance { get; set; } = true;
        public int MaxGeneratedNotes { get; set; } = 100;
        public double StartTime { get; set; } = 0; // 开始时间（秒）
        public double EndTime { get; set; } = 60; // 结束时间（秒）
        public List<IIdol> ExistingNotes { get; set; } = new List<IIdol>();

        // 新增：音频分析结果
        public AudioAnalysisData AudioAnalysis { get; set; }
    }

    /// <summary>
    /// AI生成结果
    /// </summary>
    public class AIGenerationResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public List<IIdol> GeneratedNotes { get; set; } = new List<IIdol>();
        public AIChartMetadata Metadata { get; set; }
        public string OriginalResponse { get; set; }
        public List<string> ValidationErrors { get; set; } = new List<string>();
        public AIGenerationStats Stats { get; set; }
    }

    /// <summary>
    /// 音频分析数据（来自MP3分析）
    /// </summary>
    public class AudioAnalysisData
    {
        public double BPM { get; set; }
        public double BPMConfidence { get; set; }
        public TimeSpan Duration { get; set; }
        public int SampleRate { get; set; }
        public int Channels { get; set; }
        public List<double> BeatTimes { get; set; } = new List<double>();
        public List<EnergyPeakData> EnergyPeaks { get; set; } = new List<EnergyPeakData>();
        public float[] EnergyLevels { get; set; }
        public string SourceFile { get; set; }
        public DateTime AnalysisTime { get; set; }

        // 扩展的详细分析数据
        public DetailedAudioAnalysis DetailedAnalysis { get; set; }
    }

    /// <summary>
    /// 详细音频分析
    /// </summary>
    public class DetailedAudioAnalysis
    {
        public List<BeatSegment> BeatSegments { get; set; } = new List<BeatSegment>();
        public List<EnergySegment> EnergySegments { get; set; } = new List<EnergySegment>();
        public List<TempoChange> TempoChanges { get; set; } = new List<TempoChange>();
        public AudioStructureAnalysis StructureAnalysis { get; set; }
        public List<FrequencyBand> FrequencyAnalysis { get; set; } = new List<FrequencyBand>();
    }

    /// <summary>
    /// 节拍段落
    /// </summary>
    public class BeatSegment
    {
        public double StartTime { get; set; }
        public double EndTime { get; set; }
        public List<double> BeatsInSegment { get; set; } = new List<double>();
        public double AverageBPM { get; set; }
        public double Confidence { get; set; }
        public string Characteristics { get; set; } // 稳定/变化/加速/减速
    }

    /// <summary>
    /// 能量段落
    /// </summary>
    public class EnergySegment
    {
        public double StartTime { get; set; }
        public double EndTime { get; set; }
        public float AverageEnergy { get; set; }
        public float PeakEnergy { get; set; }
        public List<EnergyPeakData> PeaksInSegment { get; set; } = new List<EnergyPeakData>();
        public string EnergyLevel { get; set; } // 低/中/高/极高
    }

    /// <summary>
    /// 速度变化
    /// </summary>
    public class TempoChange
    {
        public double Time { get; set; }
        public double FromBPM { get; set; }
        public double ToBPM { get; set; }
        public string ChangeType { get; set; } // 渐变/突变
    }

    /// <summary>
    /// 音频结构分析
    /// </summary>
    public class AudioStructureAnalysis
    {
        public List<AudioSection> Sections { get; set; } = new List<AudioSection>();
        public List<double> SilentPeriods { get; set; } = new List<double>();
        public List<double> HighActivityPeriods { get; set; } = new List<double>();
    }

    /// <summary>
    /// 音频段落
    /// </summary>
    public class AudioSection
    {
        public string Name { get; set; } // Intro/Verse/Chorus/Bridge/Outro
        public double StartTime { get; set; }
        public double EndTime { get; set; }
        public float AverageEnergy { get; set; }
        public double AverageBPM { get; set; }
        public string Characteristics { get; set; }
    }

    /// <summary>
    /// 频率带分析
    /// </summary>
    public class FrequencyBand
    {
        public double Time { get; set; }
        public float LowFrequency { get; set; }   // 低频能量
        public float MidFrequency { get; set; }   // 中频能量
        public float HighFrequency { get; set; }  // 高频能量
        public float SpectralCentroid { get; set; } // 频谱质心
    }

    /// <summary>
    /// 能量峰值数据
    /// </summary>
    public class EnergyPeakData
    {
        public double Time { get; set; }
        public float Energy { get; set; }
        public float Confidence { get; set; }
    }

    /// <summary>
    /// 音乐分析数据
    /// </summary>
    public class MusicAnalysisData
    {
        public BasicMusicInfo BasicInfo { get; set; }
        public StructureAnalysisData StructureAnalysis { get; set; }
        public RhythmPatternData RhythmPattern { get; set; }
        public MelodyFeaturesData MelodyFeatures { get; set; }
    }

    /// <summary>
    /// 基础音乐信息
    /// </summary>
    public class BasicMusicInfo
    {
        public string FileName { get; set; }
        public double Duration { get; set; }
        public int BPM { get; set; }
        public string TimeSignature { get; set; }
        public int TotalTracks { get; set; }
        public int QuarterNote { get; set; }
    }

    /// <summary>
    /// 音乐结构分析数据
    /// </summary>
    public class StructureAnalysisData
    {
        public List<MusicSection> Sections { get; set; } = new List<MusicSection>();
        public List<double> BeatGrid { get; set; } = new List<double>();
        public List<double> OnsetDetection { get; set; } = new List<double>();
    }

    /// <summary>
    /// 音乐段落
    /// </summary>
    public class MusicSection
    {
        public string Type { get; set; } // intro/verse/chorus/bridge/outro
        public double StartTime { get; set; }
        public double EndTime { get; set; }
        public double Intensity { get; set; } // 0.0-1.0
    }

    /// <summary>
    /// 节奏模式数据
    /// </summary>
    public class RhythmPatternData
    {
        public List<BeatStrength> BeatStrengths { get; set; } = new List<BeatStrength>();
        public double AverageNoteDensity { get; set; }
        public List<RhythmPattern> CommonPatterns { get; set; } = new List<RhythmPattern>();
    }

    /// <summary>
    /// 节拍强度
    /// </summary>
    public class BeatStrength
    {
        public double Time { get; set; }
        public double Strength { get; set; } // 0.0-1.0
        public bool IsStrongBeat { get; set; }
    }

    /// <summary>
    /// 节奏模式
    /// </summary>
    public class RhythmPattern
    {
        public string Pattern { get; set; }
        public double Frequency { get; set; }
        public double StartTime { get; set; }
        public double EndTime { get; set; }
    }

    /// <summary>
    /// 旋律特征数据
    /// </summary>
    public class MelodyFeaturesData
    {
        public List<MelodyNote> MainMelody { get; set; } = new List<MelodyNote>();
        public double AveragePitch { get; set; }
        public double PitchRange { get; set; }
        public List<MelodyDirection> Directions { get; set; } = new List<MelodyDirection>();
    }

    /// <summary>
    /// 旋律音符
    /// </summary>
    public class MelodyNote
    {
        public double Time { get; set; }
        public int Pitch { get; set; }
        public double Duration { get; set; }
        public double Velocity { get; set; }
    }

    /// <summary>
    /// 旋律走向
    /// </summary>
    public class MelodyDirection
    {
        public double Time { get; set; }
        public string Direction { get; set; } // up/down/stable
        public double Strength { get; set; }
    }

    /// <summary>
    /// AI响应的谱面数据
    /// </summary>
    public class AIChartResponse
    {
        public List<AIChartNote> notes { get; set; } = new List<AIChartNote>();
        public AIChartMetadata metadata { get; set; }
    }

    /// <summary>
    /// AI生成的音符数据
    /// </summary>
    public class AIChartNote
    {
        public int bar { get; set; }
        public double pos { get; set; }
        public string fromTrack { get; set; }
        public string targetTrack { get; set; }
        public string endTrack { get; set; }
        public string noteType { get; set; }
        public int? endBar { get; set; }
        public double? endPos { get; set; }
        public string reasoning { get; set; }
    }

    /// <summary>
    /// 谱面元数据
    /// </summary>
    public class AIChartMetadata
    {
        public int totalNotes { get; set; }
        public double estimatedDifficulty { get; set; }
        public double averageNotesPerSecond { get; set; }
        public Dictionary<string, int> noteTypeDistribution { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> trackDistribution { get; set; } = new Dictionary<string, int>();
    }

    /// <summary>
    /// DeepSeek API响应格式
    /// </summary>
    public class DeepSeekResponse
    {
        public string id { get; set; }
        public string @object { get; set; }
        public long created { get; set; }
        public string model { get; set; }
        public List<DeepSeekChoice> choices { get; set; }
        public DeepSeekUsage usage { get; set; }
    }

    public class DeepSeekChoice
    {
        public int index { get; set; }
        public DeepSeekMessage message { get; set; }
        public string finish_reason { get; set; }
    }

    public class DeepSeekMessage
    {
        public string role { get; set; }
        public string content { get; set; }
    }

    public class DeepSeekUsage
    {
        public int prompt_tokens { get; set; }
        public int completion_tokens { get; set; }
        public int total_tokens { get; set; }
    }



    /// <summary>
    /// AI生成统计信息
    /// </summary>
    public class AIGenerationStats
    {
        public DateTime GenerationTime { get; set; }
        public TimeSpan Duration { get; set; }
        public int TokensUsed { get; set; }
        public int NotesGenerated { get; set; }
        public int NotesValidated { get; set; }
        public double SuccessRate => NotesGenerated > 0 ? (double)NotesValidated / NotesGenerated : 0;
    }

    /// <summary>
    /// 聊天消息
    /// </summary>
    public class ChatMessage
    {
        public string Role { get; set; } // user/assistant/system
        public string Content { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 用户意图解析结果
    /// </summary>
    public class UserIntent
    {
        public string OriginalMessage { get; set; }
        public string Action { get; set; } // generate/analyze/optimize/delete
        public bool HasBarRange { get; set; }
        public int StartBar { get; set; }
        public int EndBar { get; set; }
        public List<string> PreferredNoteTypes { get; set; } = new List<string>();
        public string DifficultyHint { get; set; } // easy/medium/hard
        public List<string> Keywords { get; set; } = new List<string>();
    }

    /// <summary>
    /// 完整的谱面上下文信息
    /// </summary>
    public class ChartContextData
    {
        public string FilePath { get; set; }
        public string FileName { get; set; }
        public string RawXmlContent { get; set; } // 原始XML文件内容
        public List<IIdol> AllNotes { get; set; } = new List<IIdol>();
        public ChartStatistics Statistics { get; set; }
        public ChartStructureAnalysis Structure { get; set; }
        public ChartDifficultyAnalysis Difficulty { get; set; }
        public List<ChartPattern> Patterns { get; set; } = new List<ChartPattern>();
        public DetailedNoteInfo DetailedNoteInfo { get; set; } // 详细音符信息
    }

    /// <summary>
    /// 详细音符信息
    /// </summary>
    public class DetailedNoteInfo
    {
        public Dictionary<int, List<NoteDetail>> NotesByBar { get; set; } = new Dictionary<int, List<NoteDetail>>();
        public List<NoteTimeInfo> TimelineAnalysis { get; set; } = new List<NoteTimeInfo>();
        public List<TrackSequence> TrackSequences { get; set; } = new List<TrackSequence>();
        public List<NoteInterval> NoteIntervals { get; set; } = new List<NoteInterval>();
    }

    /// <summary>
    /// 音符详细信息
    /// </summary>
    public class NoteDetail
    {
        public int Bar { get; set; }
        public int Pos { get; set; }
        public string FromTrack { get; set; }
        public string TargetTrack { get; set; }
        public string EndTrack { get; set; }
        public string NoteType { get; set; }
        public int EndBar { get; set; }
        public int EndPos { get; set; }
    }

    /// <summary>
    /// 音符时间信息
    /// </summary>
    public class NoteTimeInfo
    {
        public int Bar { get; set; }
        public int Pos { get; set; }
        public double EstimatedTime { get; set; } // 估算时间（秒）
        public string NoteType { get; set; }
        public string Track { get; set; }
    }

    /// <summary>
    /// 轨道序列
    /// </summary>
    public class TrackSequence
    {
        public string FromTrack { get; set; }
        public string ToTrack { get; set; }
        public double TimeInterval { get; set; }
        public int BarInterval { get; set; }
        public int PosInterval { get; set; }
    }

    /// <summary>
    /// 音符间隔
    /// </summary>
    public class NoteInterval
    {
        public string FromNote { get; set; }
        public string ToNote { get; set; }
        public double TimeInterval { get; set; }
        public string IntervalType { get; set; } // 极快/快速/中等/慢速/很慢
        public bool TrackChange { get; set; }
    }

    /// <summary>
    /// 谱面统计信息
    /// </summary>
    public class ChartStatistics
    {
        public int TotalNotes { get; set; }
        public int TotalBars { get; set; }
        public Dictionary<string, int> NoteTypeCount { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> TrackDistribution { get; set; } = new Dictionary<string, int>();
        public double AverageNotesPerBar { get; set; }
        public double NoteDensity { get; set; } // 音符/秒
        public TimeSpan EstimatedDuration { get; set; }
        public List<BarDensity> BarDensities { get; set; } = new List<BarDensity>();
    }

    /// <summary>
    /// 小节密度信息
    /// </summary>
    public class BarDensity
    {
        public int Bar { get; set; }
        public int NoteCount { get; set; }
        public double Density { get; set; }
        public string DifficultyLevel { get; set; } // Easy/Normal/Hard/Expert
    }

    /// <summary>
    /// 谱面结构分析
    /// </summary>
    public class ChartStructureAnalysis
    {
        public List<ChartSection> Sections { get; set; } = new List<ChartSection>();
        public List<int> EmptyBars { get; set; } = new List<int>();
        public List<int> HighDensityBars { get; set; } = new List<int>();
        public List<ChartBreak> Breaks { get; set; } = new List<ChartBreak>();
    }

    /// <summary>
    /// 谱面段落
    /// </summary>
    public class ChartSection
    {
        public string Name { get; set; } // Intro/Verse/Chorus/Bridge/Outro
        public int StartBar { get; set; }
        public int EndBar { get; set; }
        public int NoteCount { get; set; }
        public string Characteristics { get; set; } // 段落特征描述
    }

    /// <summary>
    /// 谱面间隔
    /// </summary>
    public class ChartBreak
    {
        public int StartBar { get; set; }
        public int EndBar { get; set; }
        public double Duration { get; set; } // 秒
        public string Type { get; set; } // Short/Long/Musical
    }

    /// <summary>
    /// 谱面难度分析
    /// </summary>
    public class ChartDifficultyAnalysis
    {
        public double OverallDifficulty { get; set; } // 1-10
        public Dictionary<string, double> TrackDifficulty { get; set; } = new Dictionary<string, double>();
        public List<DifficultSection> DifficultSections { get; set; } = new List<DifficultSection>();
        public List<string> TechnicalElements { get; set; } = new List<string>();
        public double ReadabilityScore { get; set; } // 可读性评分
    }

    /// <summary>
    /// 困难段落
    /// </summary>
    public class DifficultSection
    {
        public int StartBar { get; set; }
        public int EndBar { get; set; }
        public double Difficulty { get; set; }
        public List<string> Reasons { get; set; } = new List<string>();
    }

    /// <summary>
    /// 谱面模式
    /// </summary>
    public class ChartPattern
    {
        public string Type { get; set; } // Stream/Jump/Tech/Roll等
        public int StartBar { get; set; }
        public int EndBar { get; set; }
        public List<IIdol> Notes { get; set; } = new List<IIdol>();
        public string Description { get; set; }
    }

    /// <summary>
    /// 聊天上下文
    /// </summary>
    public class ChatContext
    {
        public List<IIdol> CurrentNotes { get; set; } = new List<IIdol>();
        public List<IIdol> RelevantNotes { get; set; } = new List<IIdol>();
        public int TotalBars { get; set; }
        public List<ChatMessage> ChatHistory { get; set; } = new List<ChatMessage>();
        public string MusicInfo { get; set; }
        public AudioAnalysisData AudioAnalysis { get; set; }
        public string XmlContent { get; set; } // 直接传递XML文件内容
        public UserIntent Intent { get; set; }
    }

    /// <summary>
    /// 聊天响应
    /// </summary>
    public class ChatResponse
    {
        public string Message { get; set; }
        public string Action { get; set; }
        public List<IIdol> GeneratedNotes { get; set; } = new List<IIdol>();
    }

    /// <summary>
    /// AI聊天响应格式
    /// </summary>
    public class AIChatResponse
    {
        public string message { get; set; }
        public string action { get; set; }
        public List<AIChartNote> notes { get; set; } = new List<AIChartNote>();
    }
}
