"""
谱面生成器

使用AI模型生成音游谱面的核心类
"""

import torch
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from pathlib import Path

from ..audio_analysis import MidiAnalyzer, FeatureExtractor, BeatDetector
from ..models import ChartGenerationModel, DifficultyPredictor
from .chart_data import ChartData, ChartMetadata, NoteInfo
from .post_processor import ChartPostProcessor

logger = logging.getLogger(__name__)


class ChartGenerator:
    """AI谱面生成器"""
    
    def __init__(
        self,
        model_path: Optional[str] = None,
        device: str = 'cpu',
        time_resolution: float = 0.125
    ):
        """
        初始化谱面生成器
        
        Args:
            model_path: 预训练模型路径
            device: 计算设备
            time_resolution: 时间分辨率（秒）
        """
        self.device = device
        self.time_resolution = time_resolution
        
        # 初始化分析器
        self.midi_analyzer = MidiAnalyzer()
        self.feature_extractor = FeatureExtractor(time_resolution)
        self.beat_detector = BeatDetector()
        
        # 初始化模型
        self.generation_model = None
        self.difficulty_predictor = None
        
        # 初始化后处理器
        self.post_processor = ChartPostProcessor()
        
        # 加载模型
        if model_path and Path(model_path).exists():
            self.load_model(model_path)
        else:
            logger.warning("未提供模型路径或模型文件不存在，将使用规则生成")
    
    def load_model(self, model_path: str):
        """
        加载预训练模型
        
        Args:
            model_path: 模型文件路径
        """
        try:
            self.generation_model = ChartGenerationModel.load_model(model_path, self.device)
            logger.info(f"成功加载生成模型: {model_path}")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            self.generation_model = None
    
    def load_difficulty_predictor(self, predictor_path: str):
        """
        加载难度预测器
        
        Args:
            predictor_path: 预测器文件路径
        """
        try:
            self.difficulty_predictor = DifficultyPredictor.load_model(predictor_path, self.device)
            logger.info(f"成功加载难度预测器: {predictor_path}")
        except Exception as e:
            logger.error(f"加载难度预测器失败: {e}")
            self.difficulty_predictor = None
    
    def generate_from_midi(
        self,
        midi_path: str,
        difficulty: int = 5,
        track_count: int = 4,
        style: str = "balanced",
        title: Optional[str] = None,
        artist: Optional[str] = None
    ) -> ChartData:
        """
        从MIDI文件生成谱面
        
        Args:
            midi_path: MIDI文件路径
            difficulty: 目标难度 (1-10)
            track_count: 轨道数量
            style: 生成风格 ("balanced", "dense", "sparse", "rhythmic")
            title: 歌曲标题
            artist: 艺术家
            
        Returns:
            ChartData: 生成的谱面数据
        """
        logger.info(f"开始生成谱面: {midi_path}")
        
        # 1. 分析MIDI文件
        if not self.midi_analyzer.load_midi(midi_path):
            raise ValueError(f"无法加载MIDI文件: {midi_path}")
        
        basic_info = self.midi_analyzer.get_basic_info()
        
        # 2. 提取特征
        features = self.feature_extractor.extract_features(midi_path)
        
        # 3. 检测节拍
        beat_info = self.beat_detector.detect_beats(midi_path)
        
        # 4. 创建元数据
        metadata = ChartMetadata(
            title=title or Path(midi_path).stem,
            artist=artist or "Unknown",
            difficulty=difficulty,
            bpm=basic_info['initial_bpm'],
            duration=basic_info['duration'],
            track_count=track_count
        )
        
        # 5. 生成谱面
        if self.generation_model:
            chart_data = self._generate_with_ai_model(features, beat_info, metadata, style)
        else:
            chart_data = self._generate_with_rules(features, beat_info, metadata, style)
        
        # 6. 后处理
        chart_data = self.post_processor.process(chart_data, beat_info)
        
        logger.info(f"谱面生成完成，共{chart_data.metadata.total_notes}个音符")
        return chart_data
    
    def _generate_with_ai_model(
        self,
        features: Dict,
        beat_info: Dict,
        metadata: ChartMetadata,
        style: str
    ) -> ChartData:
        """
        使用AI模型生成谱面
        
        Args:
            features: 音乐特征
            beat_info: 节拍信息
            metadata: 谱面元数据
            style: 生成风格
            
        Returns:
            ChartData: 生成的谱面数据
        """
        logger.info("使用AI模型生成谱面")
        
        # 准备模型输入
        feature_vector = self.feature_extractor.extract_training_features(
            "", metadata.difficulty  # 这里需要重构以支持已提取的特征
        )
        
        # 转换为张量
        features_tensor = torch.tensor(feature_vector, dtype=torch.float32).unsqueeze(0)
        
        # 计算目标长度
        target_length = int(metadata.duration / self.time_resolution)
        
        # 生成谱面
        generated_sequences = self.generation_model.generate_chart(
            features_tensor,
            metadata.difficulty,
            target_length,
            temperature=self._get_temperature_for_style(style)
        )
        
        # 转换为ChartData格式
        chart_data = ChartData.from_sequence_format(
            generated_sequences,
            self.time_resolution,
            metadata
        )
        
        return chart_data
    
    def _generate_with_rules(
        self,
        features: Dict,
        beat_info: Dict,
        metadata: ChartMetadata,
        style: str
    ) -> ChartData:
        """
        使用规则生成谱面
        
        Args:
            features: 音乐特征
            beat_info: 节拍信息
            metadata: 谱面元数据
            style: 生成风格
            
        Returns:
            ChartData: 生成的谱面数据
        """
        logger.info("使用规则生成谱面")
        
        chart_data = ChartData(metadata)
        
        # 获取节拍网格
        beat_grid = self.beat_detector.generate_beat_grid(
            "", self.time_resolution  # 这里需要重构
        )
        
        # 根据风格调整参数
        style_params = self._get_style_parameters(style, metadata.difficulty)
        
        # 生成音符
        notes = self._generate_notes_by_rules(
            beat_grid,
            features,
            metadata,
            style_params
        )
        
        chart_data.add_notes(notes)
        return chart_data
    
    def _get_style_parameters(self, style: str, difficulty: int) -> Dict:
        """
        获取风格参数
        
        Args:
            style: 生成风格
            difficulty: 难度等级
            
        Returns:
            Dict: 风格参数
        """
        base_density = 0.1 + (difficulty - 1) * 0.05  # 基础密度随难度增加
        
        style_configs = {
            "balanced": {
                "note_density": base_density,
                "long_note_ratio": 0.2,
                "track_balance": True,
                "rhythm_emphasis": 0.5
            },
            "dense": {
                "note_density": base_density * 1.5,
                "long_note_ratio": 0.1,
                "track_balance": False,
                "rhythm_emphasis": 0.3
            },
            "sparse": {
                "note_density": base_density * 0.6,
                "long_note_ratio": 0.3,
                "track_balance": True,
                "rhythm_emphasis": 0.7
            },
            "rhythmic": {
                "note_density": base_density,
                "long_note_ratio": 0.15,
                "track_balance": True,
                "rhythm_emphasis": 0.9
            }
        }
        
        return style_configs.get(style, style_configs["balanced"])
    
    def _generate_notes_by_rules(
        self,
        beat_grid: Dict,
        features: Dict,
        metadata: ChartMetadata,
        style_params: Dict
    ) -> List[NoteInfo]:
        """
        基于规则生成音符
        
        Args:
            beat_grid: 节拍网格
            features: 音乐特征
            metadata: 谱面元数据
            style_params: 风格参数
            
        Returns:
            List[NoteInfo]: 生成的音符列表
        """
        notes = []
        grid_points = beat_grid.get('grid_points', [])
        
        if not grid_points:
            logger.warning("没有节拍网格数据，使用简单时间网格")
            # 创建简单的时间网格
            duration = metadata.duration
            current_time = 0.0
            while current_time < duration:
                grid_points.append({
                    'time': current_time,
                    'strength': 0.5,
                    'beat_type': 'regular',
                    'is_beat': current_time % (60.0 / metadata.bpm) < 0.1
                })
                current_time += self.time_resolution
        
        # 根据节拍强度和风格参数生成音符
        for point in grid_points:
            time = point['time']
            strength = point['strength']
            is_beat = point.get('is_beat', False)
            
            # 计算在此时间点生成音符的概率
            base_prob = style_params['note_density']
            
            # 强拍增加概率
            if is_beat:
                base_prob *= (1 + style_params['rhythm_emphasis'])
            
            # 根据强度调整
            adjusted_prob = base_prob * (0.5 + strength * 0.5)
            
            # 决定是否生成音符
            if np.random.random() < adjusted_prob:
                # 选择轨道
                track = self._select_track(
                    time, notes, metadata.track_count, style_params
                )
                
                # 选择音符类型
                note_type = self._select_note_type(
                    time, strength, style_params
                )
                
                # 计算持续时间（如果是长音符）
                duration = 0.0
                if note_type == 2:  # 长音符
                    duration = self._calculate_note_duration(
                        time, metadata.bpm, style_params
                    )
                
                note = NoteInfo(
                    time=time,
                    track=track,
                    note_type=note_type,
                    duration=duration,
                    velocity=int(127 * strength)
                )
                
                notes.append(note)
        
        return notes
    
    def _select_track(
        self,
        time: float,
        existing_notes: List[NoteInfo],
        track_count: int,
        style_params: Dict
    ) -> int:
        """
        选择音符轨道
        
        Args:
            time: 当前时间
            existing_notes: 已有音符
            track_count: 轨道数量
            style_params: 风格参数
            
        Returns:
            int: 选择的轨道编号
        """
        if not style_params.get('track_balance', True):
            # 不考虑平衡，随机选择
            return np.random.randint(0, track_count)
        
        # 计算各轨道在近期的音符数量
        recent_time_window = 2.0  # 2秒窗口
        track_counts = [0] * track_count
        
        for note in existing_notes:
            if note.time >= time - recent_time_window and note.note_type > 0:
                if 0 <= note.track < track_count:
                    track_counts[note.track] += 1
        
        # 选择音符数量最少的轨道
        min_count = min(track_counts)
        candidate_tracks = [i for i, count in enumerate(track_counts) if count == min_count]
        
        return np.random.choice(candidate_tracks)
    
    def _select_note_type(
        self,
        time: float,
        strength: float,
        style_params: Dict
    ) -> int:
        """
        选择音符类型
        
        Args:
            time: 时间位置
            strength: 节拍强度
            style_params: 风格参数
            
        Returns:
            int: 音符类型 (1=短音符, 2=长音符)
        """
        long_note_prob = style_params.get('long_note_ratio', 0.2)
        
        # 强拍更容易生成长音符
        if strength > 0.7:
            long_note_prob *= 1.5
        
        if np.random.random() < long_note_prob:
            return 2  # 长音符
        else:
            return 1  # 短音符
    
    def _calculate_note_duration(
        self,
        time: float,
        bpm: float,
        style_params: Dict
    ) -> float:
        """
        计算长音符持续时间
        
        Args:
            time: 开始时间
            bpm: BPM
            style_params: 风格参数
            
        Returns:
            float: 持续时间（秒）
        """
        # 基于BPM计算基础持续时间
        beat_duration = 60.0 / bpm
        
        # 随机选择持续时间（0.5拍到2拍）
        duration_beats = np.random.uniform(0.5, 2.0)
        duration = duration_beats * beat_duration
        
        return duration
    
    def _get_temperature_for_style(self, style: str) -> float:
        """
        根据风格获取采样温度
        
        Args:
            style: 生成风格
            
        Returns:
            float: 采样温度
        """
        temperature_map = {
            "balanced": 1.0,
            "dense": 0.8,
            "sparse": 1.2,
            "rhythmic": 0.9
        }
        
        return temperature_map.get(style, 1.0)
    
    def predict_difficulty(self, midi_path: str) -> Dict:
        """
        预测MIDI文件的难度
        
        Args:
            midi_path: MIDI文件路径
            
        Returns:
            Dict: 难度预测结果
        """
        if not self.difficulty_predictor:
            logger.warning("难度预测器未加载")
            return {'difficulty': 5.0, 'confidence': 0.0}
        
        try:
            features = self.feature_extractor.extract_training_features(midi_path)
            prediction = self.difficulty_predictor.predict_difficulty(features)
            return prediction
        except Exception as e:
            logger.error(f"难度预测失败: {e}")
            return {'difficulty': 5.0, 'confidence': 0.0}
