<Window x:Class="MyWPF.Layout.ChartGenerator.ChartGeneratorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="音乐游戏谱面生成器" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="音乐游戏谱面生成器" 
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- MIDI文件选择 -->
        <materialDesign:Card Grid.Row="1" Padding="16" Margin="0,0,0,10">
            <StackPanel>
                <TextBlock Text="MIDI文件选择" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,10"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBox x:Name="txtMidiPath" Grid.Column="0" 
                             materialDesign:HintAssist.Hint="请选择MIDI文件"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             IsReadOnly="True" Margin="0,0,10,0"/>
                    <Button x:Name="btnBrowseMidi" Grid.Column="1" Content="浏览"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Click="BtnBrowseMidi_Click"/>
                </Grid>
            </StackPanel>
        </materialDesign:Card>

        <!-- 游戏模式选择 -->
        <materialDesign:Card Grid.Row="2" Padding="16" Margin="0,0,0,10">
            <StackPanel>
                <TextBlock Text="游戏模式" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,10"/>
                <StackPanel Orientation="Horizontal">
                    <RadioButton x:Name="rbIdol" Content="星动模式" IsChecked="True" Margin="0,0,20,0"/>
                    <RadioButton x:Name="rbPinball" Content="弹珠模式" Margin="0,0,20,0"/>
                    <RadioButton x:Name="rbBubble" Content="泡泡模式"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- 谱面参数设置 -->
        <materialDesign:Card Grid.Row="3" Padding="16" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="难度等级" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,10"/>
                    <Slider x:Name="sliderDifficulty" Minimum="1" Maximum="10" Value="5" 
                            TickFrequency="1" IsSnapToTickEnabled="True"
                            Style="{StaticResource MaterialDesignSlider}"/>
                    <TextBlock x:Name="lblDifficultyValue" Text="5" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Margin="10,0">
                    <TextBlock Text="轨道数量" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,10"/>
                    <ComboBox x:Name="cbTrackCount" SelectedIndex="0"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}">
                        <ComboBoxItem Content="4轨道"/>
                        <ComboBoxItem Content="5轨道"/>
                        <ComboBoxItem Content="6轨道"/>
                        <ComboBoxItem Content="8轨道"/>
                    </ComboBox>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Margin="10,0,0,0">
                    <TextBlock Text="音符密度" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,10"/>
                    <Slider x:Name="sliderDensity" Minimum="0.1" Maximum="1.0" Value="0.7" 
                            TickFrequency="0.1" IsSnapToTickEnabled="True"
                            Style="{StaticResource MaterialDesignSlider}"/>
                    <TextBlock x:Name="lblDensityValue" Text="70%" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- 高级设置 -->
        <materialDesign:Card Grid.Row="4" Padding="16" Margin="0,0,0,10">
            <Expander Header="高级设置" Style="{StaticResource MaterialDesignExpander}">
                <Grid Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <CheckBox x:Name="cbAutoAdjustBPM" Content="自动调整BPM" IsChecked="True" Margin="0,0,0,10"/>
                        <CheckBox x:Name="cbFilterShortNotes" Content="过滤短音符" IsChecked="True" Margin="0,0,0,10"/>
                        <CheckBox x:Name="cbAddLongNotes" Content="添加长按音符" IsChecked="False" Margin="0,0,0,10"/>
                        <CheckBox x:Name="cbEnableCurveNotes" Content="启用拐弯音符(星动)" IsChecked="True"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="10,0,0,0">
                        <TextBox x:Name="txtSongName" 
                                 materialDesign:HintAssist.Hint="歌曲名称"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 Margin="0,0,0,10"/>
                        <TextBox x:Name="txtArtist" 
                                 materialDesign:HintAssist.Hint="艺术家"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                    </StackPanel>
                </Grid>
            </Expander>
        </materialDesign:Card>

        <!-- 生成进度和预览 -->
        <materialDesign:Card Grid.Row="5" Padding="16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <StackPanel Grid.Row="0" Margin="0,0,0,10">
                    <TextBlock Text="生成进度" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,5"/>
                    <ProgressBar x:Name="progressBar" Height="4" Style="{StaticResource MaterialDesignLinearProgressBar}"/>
                    <TextBlock x:Name="lblStatus" Text="准备就绪" Margin="0,5,0,0"/>
                </StackPanel>
                
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <TextBox x:Name="txtPreview" 
                             materialDesign:HintAssist.Hint="谱面预览将在这里显示..."
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             AcceptsReturn="True" IsReadOnly="True"
                             MinHeight="200"/>
                </ScrollViewer>
            </Grid>
        </materialDesign:Card>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="6" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="btnTest" Content="测试解析"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0" Click="BtnTest_Click"/>
            <Button x:Name="btnPreview" Content="预览谱面"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0" Click="BtnPreview_Click"/>
            <Button x:Name="btnGenerate" Content="生成谱面"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Margin="0,0,10,0" Click="BtnGenerate_Click"/>
            <Button x:Name="btnSave" Content="保存谱面"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    IsEnabled="False" Click="BtnSave_Click"/>
        </StackPanel>
    </Grid>
</Window>
