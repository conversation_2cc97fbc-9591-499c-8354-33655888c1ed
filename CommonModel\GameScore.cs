using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml;
using System.Xml.Linq;

namespace CommonModel
{
    /// <summary>
    /// 游戏谱面类 - 用于解析和处理XML谱面文件
    /// </summary>
    public class GameScore
    {
        /// <summary>
        /// 谱面基本信息
        /// </summary>
        public LevelInfo LevelInfo { get; set; }
        
        /// <summary>
        /// Normal难度的音符列表
        /// </summary>
        public List<GameNote> NormalNotes { get; set; }
        
        /// <summary>
        /// 组合音符列表
        /// </summary>
        public List<CombineNote> CombineNotes { get; set; }
        
        public GameScore()
        {
            LevelInfo = new LevelInfo();
            NormalNotes = new List<GameNote>();
            CombineNotes = new List<CombineNote>();
        }
        
        /// <summary>
        /// 从XML文件加载谱面
        /// </summary>
        /// <param name="xmlFilePath">XML文件路径</param>
        /// <returns>游戏谱面对象</returns>
        public static GameScore LoadFromXml(string xmlFilePath)
        {
            var gameScore = new GameScore();
            
            try
            {
                XDocument doc = XDocument.Load(xmlFilePath);
                var root = doc.Root;
                
                // 解析LevelInfo
                var levelInfoElement = root.Element("LevelInfo");
                if (levelInfoElement != null)
                {
                    gameScore.LevelInfo = ParseLevelInfo(levelInfoElement);
                }
                
                // 解析Normal节点中的音符
                var normalElement = root.Element("Normal");
                if (normalElement != null)
                {
                    gameScore.NormalNotes = ParseNormalNotes(normalElement);
                    gameScore.CombineNotes = ParseCombineNotes(normalElement);
                }
                
                return gameScore;
            }
            catch (Exception ex)
            {
                throw new Exception($"加载XML谱面文件失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 解析LevelInfo信息
        /// </summary>
        private static LevelInfo ParseLevelInfo(XElement levelInfoElement)
        {
            var levelInfo = new LevelInfo();
            
            levelInfo.BPM = GetIntValue(levelInfoElement, "BPM", 120);
            levelInfo.BeatPerBar = GetIntValue(levelInfoElement, "BeatPerBar", 4);
            levelInfo.BeatLen = GetIntValue(levelInfoElement, "BeatLen", 16);
            levelInfo.BarAmount = GetIntValue(levelInfoElement, "BarAmount", 1);
            levelInfo.TrackCount = GetIntValue(levelInfoElement, "TrackCount", 4);
            levelInfo.IsFourTrack = GetBoolValue(levelInfoElement, "IsFourTrack", true);
            
            return levelInfo;
        }
        
        /// <summary>
        /// 解析Normal节点中的普通音符
        /// </summary>
        private static List<GameNote> ParseNormalNotes(XElement normalElement)
        {
            var notes = new List<GameNote>();
            
            // 解析单独的Note元素
            var noteElements = normalElement.Elements("Note");
            foreach (var noteElement in noteElements)
            {
                var note = ParseSingleNote(noteElement);
                if (note != null)
                {
                    notes.Add(note);
                }
            }
            
            return notes;
        }
        
        /// <summary>
        /// 解析Normal节点中的组合音符
        /// </summary>
        private static List<CombineNote> ParseCombineNotes(XElement normalElement)
        {
            var combineNotes = new List<CombineNote>();
            
            // 解析CombineNote元素
            var combineElements = normalElement.Elements("CombineNote");
            foreach (var combineElement in combineElements)
            {
                var combineNote = new CombineNote();
                combineNote.Notes = new List<GameNote>();
                
                // 解析组合音符中的每个Note
                var noteElements = combineElement.Elements("Note");
                foreach (var noteElement in noteElements)
                {
                    var note = ParseSingleNote(noteElement);
                    if (note != null)
                    {
                        combineNote.Notes.Add(note);
                    }
                }
                
                if (combineNote.Notes.Count > 0)
                {
                    combineNotes.Add(combineNote);
                }
            }
            
            return combineNotes;
        }
        
        /// <summary>
        /// 解析单个音符
        /// </summary>
        private static GameNote ParseSingleNote(XElement noteElement)
        {
            try
            {
                var note = new GameNote();
                
                note.NoteType = GetStringValue(noteElement, "note_type", "short");
                note.Bar = GetIntValue(noteElement, "Bar", 1);
                note.Pos = GetIntValue(noteElement, "Pos", 0);
                note.FromTrack = GetStringValue(noteElement, "from_track", "");
                note.TargetTrack = GetStringValue(noteElement, "target_track", "");
                
                // 长音符的结束位置
                if (note.NoteType == "long")
                {
                    note.EndBar = GetIntValue(noteElement, "EndBar", note.Bar);
                    note.EndPos = GetIntValue(noteElement, "EndPos", note.Pos);
                }
                
                // 滑动音符的结束轨道
                if (note.NoteType == "slip")
                {
                    note.EndTrack = GetStringValue(noteElement, "end_track", note.TargetTrack);
                }
                
                return note;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析音符失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 保存谱面到XML文件
        /// </summary>
        /// <param name="xmlFilePath">保存路径</param>
        public void SaveToXml(string xmlFilePath)
        {
            try
            {
                var doc = new XDocument();
                var root = new XElement("Root");
                
                // 添加LevelInfo
                root.Add(CreateLevelInfoElement());
                
                // 添加Normal节点
                var normalElement = new XElement("Normal");
                
                // 添加普通音符
                foreach (var note in NormalNotes)
                {
                    normalElement.Add(CreateNoteElement(note));
                }
                
                // 添加组合音符
                foreach (var combineNote in CombineNotes)
                {
                    var combineElement = new XElement("CombineNote");
                    foreach (var note in combineNote.Notes)
                    {
                        combineElement.Add(CreateNoteElement(note));
                    }
                    normalElement.Add(combineElement);
                }
                
                root.Add(normalElement);
                doc.Add(root);
                doc.Save(xmlFilePath);
            }
            catch (Exception ex)
            {
                throw new Exception($"保存XML谱面文件失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 创建LevelInfo XML元素
        /// </summary>
        private XElement CreateLevelInfoElement()
        {
            return new XElement("LevelInfo",
                new XElement("BPM", LevelInfo.BPM),
                new XElement("BeatPerBar", LevelInfo.BeatPerBar),
                new XElement("BeatLen", LevelInfo.BeatLen),
                new XElement("BarAmount", LevelInfo.BarAmount),
                new XElement("TrackCount", LevelInfo.TrackCount),
                new XElement("IsFourTrack", LevelInfo.IsFourTrack)
            );
        }
        
        /// <summary>
        /// 创建Note XML元素
        /// </summary>
        private XElement CreateNoteElement(GameNote note)
        {
            var element = new XElement("Note",
                new XAttribute("note_type", note.NoteType),
                new XAttribute("Bar", note.Bar),
                new XAttribute("Pos", note.Pos),
                new XAttribute("from_track", note.FromTrack),
                new XAttribute("target_track", note.TargetTrack)
            );
            
            // 长音符添加结束位置
            if (note.NoteType == "long")
            {
                element.Add(new XAttribute("EndBar", note.EndBar));
                element.Add(new XAttribute("EndPos", note.EndPos));
            }
            
            // 滑动音符添加结束轨道
            if (note.NoteType == "slip")
            {
                element.Add(new XAttribute("end_track", note.EndTrack));
            }
            
            return element;
        }
        
        /// <summary>
        /// 获取XML元素的整数值
        /// </summary>
        private static int GetIntValue(XElement parent, string elementName, int defaultValue)
        {
            var element = parent.Element(elementName);
            if (element != null && int.TryParse(element.Value, out int value))
            {
                return value;
            }
            
            var attribute = parent.Attribute(elementName);
            if (attribute != null && int.TryParse(attribute.Value, out value))
            {
                return value;
            }
            
            return defaultValue;
        }
        
        /// <summary>
        /// 获取XML元素的字符串值
        /// </summary>
        private static string GetStringValue(XElement parent, string elementName, string defaultValue)
        {
            var element = parent.Element(elementName);
            if (element != null)
            {
                return element.Value;
            }
            
            var attribute = parent.Attribute(elementName);
            if (attribute != null)
            {
                return attribute.Value;
            }
            
            return defaultValue;
        }
        
        /// <summary>
        /// 获取XML元素的布尔值
        /// </summary>
        private static bool GetBoolValue(XElement parent, string elementName, bool defaultValue)
        {
            var element = parent.Element(elementName);
            if (element != null && bool.TryParse(element.Value, out bool value))
            {
                return value;
            }
            
            var attribute = parent.Attribute(elementName);
            if (attribute != null && bool.TryParse(attribute.Value, out value))
            {
                return value;
            }
            
            return defaultValue;
        }
        
        /// <summary>
        /// 获取谱面统计信息
        /// </summary>
        public GameScoreStats GetStats()
        {
            var stats = new GameScoreStats();
            
            stats.TotalNotes = NormalNotes.Count + CombineNotes.Sum(c => c.Notes.Count);
            stats.ShortNotes = NormalNotes.Count(n => n.NoteType == "short") + 
                              CombineNotes.Sum(c => c.Notes.Count(n => n.NoteType == "short"));
            stats.LongNotes = NormalNotes.Count(n => n.NoteType == "long") + 
                             CombineNotes.Sum(c => c.Notes.Count(n => n.NoteType == "long"));
            stats.SlipNotes = NormalNotes.Count(n => n.NoteType == "slip") + 
                             CombineNotes.Sum(c => c.Notes.Count(n => n.NoteType == "slip"));
            stats.CombineNotes = CombineNotes.Count;
            
            return stats;
        }
    }
    
    /// <summary>
    /// 谱面基本信息
    /// </summary>
    public class LevelInfo
    {
        public int BPM { get; set; } = 120;
        public int BeatPerBar { get; set; } = 4;
        public int BeatLen { get; set; } = 16;
        public int BarAmount { get; set; } = 1;
        public int TrackCount { get; set; } = 4;
        public bool IsFourTrack { get; set; } = true;
    }
    
    /// <summary>
    /// 游戏音符
    /// </summary>
    public class GameNote
    {
        public string NoteType { get; set; } = "short"; // short, long, slip
        public int Bar { get; set; }
        public int Pos { get; set; }
        public string FromTrack { get; set; } = "";
        public string TargetTrack { get; set; } = "";
        public int EndBar { get; set; } // 长音符结束小节
        public int EndPos { get; set; } // 长音符结束位置
        public string EndTrack { get; set; } = ""; // 滑动音符结束轨道
        
        public override string ToString()
        {
            if (NoteType == "long")
            {
                return $"<Note note_type=\"{NoteType}\" Bar=\"{Bar}\" Pos=\"{Pos}\" from_track=\"{FromTrack}\" target_track=\"{TargetTrack}\" EndBar=\"{EndBar}\" EndPos=\"{EndPos}\" />";
            }
            else if (NoteType == "slip")
            {
                return $"<Note note_type=\"{NoteType}\" Bar=\"{Bar}\" Pos=\"{Pos}\" target_track=\"{TargetTrack}\" end_track=\"{EndTrack}\" />";
            }
            else
            {
                return $"<Note note_type=\"{NoteType}\" Bar=\"{Bar}\" Pos=\"{Pos}\" from_track=\"{FromTrack}\" target_track=\"{TargetTrack}\" />";
            }
        }
    }
    
    /// <summary>
    /// 组合音符
    /// </summary>
    public class CombineNote
    {
        public List<GameNote> Notes { get; set; } = new List<GameNote>();
    }
    
    /// <summary>
    /// 谱面统计信息
    /// </summary>
    public class GameScoreStats
    {
        public int TotalNotes { get; set; }
        public int ShortNotes { get; set; }
        public int LongNotes { get; set; }
        public int SlipNotes { get; set; }
        public int CombineNotes { get; set; }
    }
}
