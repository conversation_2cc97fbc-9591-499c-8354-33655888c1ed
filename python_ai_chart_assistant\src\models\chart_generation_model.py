"""
谱面生成模型

基于LSTM/Transformer的序列到序列模型，用于生成音游谱面
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class ChartGenerationModel(nn.Module):
    """谱面生成模型"""
    
    def __init__(
        self,
        input_dim: int = 28,  # 输入特征维度
        hidden_dim: int = 256,
        num_layers: int = 3,
        num_tracks: int = 4,
        max_sequence_length: int = 1000,
        dropout: float = 0.2
    ):
        """
        初始化模型
        
        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            num_layers: LSTM层数
            num_tracks: 音游轨道数
            max_sequence_length: 最大序列长度
            dropout: Dropout比例
        """
        super(ChartGenerationModel, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.num_tracks = num_tracks
        self.max_sequence_length = max_sequence_length
        
        # 输入特征编码器
        self.feature_encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # LSTM编码器
        self.lstm_encoder = nn.LSTM(
            hidden_dim,
            hidden_dim,
            num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=True
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            hidden_dim * 2,  # 双向LSTM输出
            num_heads=8,
            dropout=dropout
        )
        
        # LSTM解码器
        self.lstm_decoder = nn.LSTM(
            hidden_dim * 2,
            hidden_dim,
            num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )
        
        # 输出层 - 为每个轨道生成音符概率
        self.output_layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dim // 2, 3)  # 3种音符类型：无音符(0)、短音符(1)、长音符(2)
            )
            for _ in range(num_tracks)
        ])
        
        # 难度调节层
        self.difficulty_embedding = nn.Embedding(11, hidden_dim // 4)  # 难度1-10 + 0
        self.difficulty_projection = nn.Linear(hidden_dim + hidden_dim // 4, hidden_dim)
        
    def forward(
        self,
        features: torch.Tensor,
        difficulty: torch.Tensor,
        target_length: Optional[int] = None
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            features: 输入特征 [batch_size, seq_len, input_dim]
            difficulty: 难度等级 [batch_size]
            target_length: 目标序列长度
            
        Returns:
            Dict: 包含各轨道音符概率的字典
        """
        batch_size, seq_len, _ = features.shape
        
        if target_length is None:
            target_length = seq_len
        
        # 1. 特征编码
        encoded_features = self.feature_encoder(features)  # [batch_size, seq_len, hidden_dim]
        
        # 2. LSTM编码
        encoder_output, (hidden, cell) = self.lstm_encoder(encoded_features)
        # encoder_output: [batch_size, seq_len, hidden_dim * 2]
        
        # 3. 注意力机制
        # 转换维度用于注意力计算 [seq_len, batch_size, hidden_dim * 2]
        encoder_output_transposed = encoder_output.transpose(0, 1)
        attended_output, attention_weights = self.attention(
            encoder_output_transposed,
            encoder_output_transposed,
            encoder_output_transposed
        )
        # 转换回 [batch_size, seq_len, hidden_dim * 2]
        attended_output = attended_output.transpose(0, 1)
        
        # 4. 难度嵌入
        difficulty_emb = self.difficulty_embedding(difficulty)  # [batch_size, hidden_dim // 4]
        difficulty_emb = difficulty_emb.unsqueeze(1).expand(-1, target_length, -1)
        
        # 5. 解码器输入准备
        # 将注意力输出调整到目标长度
        if target_length != seq_len:
            # 简单的线性插值调整长度
            attended_output = F.interpolate(
                attended_output.transpose(1, 2),
                size=target_length,
                mode='linear',
                align_corners=False
            ).transpose(1, 2)
        
        # 合并难度信息
        decoder_input = torch.cat([attended_output, difficulty_emb], dim=-1)
        decoder_input = self.difficulty_projection(decoder_input)
        
        # 6. LSTM解码
        # 调整隐藏状态维度
        hidden = hidden[-self.num_layers:].contiguous()  # 取最后几层
        cell = cell[-self.num_layers:].contiguous()
        
        # 如果是双向LSTM，需要调整隐藏状态维度
        if hidden.size(-1) != self.hidden_dim:
            hidden = hidden[:, :, :self.hidden_dim].contiguous()
            cell = cell[:, :, :self.hidden_dim].contiguous()
        
        decoder_output, _ = self.lstm_decoder(decoder_input, (hidden, cell))
        # decoder_output: [batch_size, target_length, hidden_dim]
        
        # 7. 生成各轨道输出
        track_outputs = {}
        for track_id in range(self.num_tracks):
            track_output = self.output_layers[track_id](decoder_output)
            track_outputs[f'track_{track_id}'] = F.softmax(track_output, dim=-1)
        
        return {
            'track_outputs': track_outputs,
            'attention_weights': attention_weights,
            'decoder_output': decoder_output
        }
    
    def generate_chart(
        self,
        features: torch.Tensor,
        difficulty: int,
        target_length: int,
        temperature: float = 1.0,
        top_k: int = 5
    ) -> Dict[str, List[int]]:
        """
        生成谱面
        
        Args:
            features: 输入特征
            difficulty: 难度等级 (1-10)
            target_length: 目标长度
            temperature: 采样温度
            top_k: Top-K采样
            
        Returns:
            Dict: 各轨道的音符序列
        """
        self.eval()
        
        with torch.no_grad():
            # 确保输入是正确的形状
            if features.dim() == 2:
                features = features.unsqueeze(0)  # 添加batch维度
            
            difficulty_tensor = torch.tensor([difficulty], dtype=torch.long)
            
            # 前向传播
            outputs = self.forward(features, difficulty_tensor, target_length)
            track_outputs = outputs['track_outputs']
            
            # 生成音符序列
            generated_chart = {}
            
            for track_name, probs in track_outputs.items():
                # probs: [1, target_length, 3]
                probs = probs.squeeze(0)  # [target_length, 3]
                
                # 应用温度
                if temperature != 1.0:
                    probs = torch.pow(probs, 1.0 / temperature)
                    probs = probs / probs.sum(dim=-1, keepdim=True)
                
                # Top-K采样
                if top_k > 0:
                    top_probs, top_indices = torch.topk(probs, min(top_k, probs.size(-1)), dim=-1)
                    # 创建mask
                    mask = torch.zeros_like(probs)
                    mask.scatter_(-1, top_indices, top_probs)
                    probs = mask / mask.sum(dim=-1, keepdim=True)
                
                # 采样生成音符
                note_sequence = []
                for t in range(target_length):
                    note_type = torch.multinomial(probs[t], 1).item()
                    note_sequence.append(note_type)
                
                generated_chart[track_name] = note_sequence
        
        return generated_chart
    
    def calculate_loss(
        self,
        predictions: Dict[str, torch.Tensor],
        targets: Dict[str, torch.Tensor],
        weights: Optional[Dict[str, float]] = None
    ) -> torch.Tensor:
        """
        计算损失函数
        
        Args:
            predictions: 模型预测结果
            targets: 目标标签
            weights: 各轨道权重
            
        Returns:
            torch.Tensor: 总损失
        """
        if weights is None:
            weights = {f'track_{i}': 1.0 for i in range(self.num_tracks)}
        
        total_loss = 0.0
        track_outputs = predictions['track_outputs']
        
        for track_name, pred_probs in track_outputs.items():
            if track_name in targets:
                target_labels = targets[track_name]
                
                # 交叉熵损失
                loss = F.cross_entropy(
                    pred_probs.view(-1, pred_probs.size(-1)),
                    target_labels.view(-1)
                )
                
                # 应用权重
                weight = weights.get(track_name, 1.0)
                total_loss += weight * loss
        
        return total_loss
    
    def save_model(self, path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.state_dict(),
            'model_config': {
                'input_dim': self.input_dim,
                'hidden_dim': self.hidden_dim,
                'num_layers': self.num_layers,
                'num_tracks': self.num_tracks,
                'max_sequence_length': self.max_sequence_length
            }
        }, path)
        logger.info(f"模型已保存到: {path}")
    
    @classmethod
    def load_model(cls, path: str, device: str = 'cpu'):
        """加载模型"""
        checkpoint = torch.load(path, map_location=device)
        config = checkpoint['model_config']
        
        model = cls(**config)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(device)
        
        logger.info(f"模型已从 {path} 加载")
        return model
