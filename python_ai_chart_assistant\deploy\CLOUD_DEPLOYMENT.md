# 🌩️ 云算力部署指南

## 📋 平台选择建议

### 🇨🇳 国内平台推荐

#### 1. AutoDL（推荐新手）
- **网址**: https://www.autodl.com/
- **优势**: 价格便宜，操作简单，支持Jupyter
- **配置推荐**:
  - RTX 3080 (10GB显存) - ¥2.5/小时
  - RTX 4090 (24GB显存) - ¥5/小时
- **适合**: 模型训练、开发测试

#### 2. 恒源云
- **网址**: https://gpushare.com/
- **优势**: 稳定性好，GPU选择多
- **配置推荐**:
  - RTX 3090 (24GB显存) - ¥3/小时
  - A100 (40GB显存) - ¥8/小时
- **适合**: 大规模训练

#### 3. 阿里云PAI
- **网址**: https://pai.console.aliyun.com/
- **优势**: 企业级稳定，集成服务多
- **价格**: 相对较高，但服务完善
- **适合**: 生产环境

### 🌍 国外平台

#### Google Colab Pro
- **价格**: $10/月
- **优势**: 免费版本可试用
- **限制**: 使用时间有限制

## 🚀 快速部署流程

### 步骤1: 租用服务器
1. 选择平台并注册账号
2. 选择GPU配置（推荐RTX 3080以上）
3. 选择镜像（推荐PyTorch官方镜像）
4. 启动实例

### 步骤2: 上传项目代码
```bash
# 方式1: 使用git克隆
git clone <your-repo-url>
cd python_ai_chart_assistant

# 方式2: 直接上传压缩包
# 在平台界面上传项目文件
```

### 步骤3: 运行部署脚本
```bash
# 运行自动部署脚本
python deploy/cloud_setup.py

# 或手动安装
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install -r requirements.txt
```

### 步骤4: 启动服务
```bash
# 启动UI界面
./start_cloud.sh

# 或直接启动
python run_ui.py --host 0.0.0.0 --port 8501
```

## 🔧 配置优化

### GPU配置检查
```python
import torch
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"GPU数量: {torch.cuda.device_count()}")
print(f"GPU名称: {torch.cuda.get_device_name(0)}")
```

### 内存优化
```bash
# 查看GPU内存
nvidia-smi

# 设置环境变量
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
```

## 📊 训练配置建议

### 小规模训练 (RTX 3080)
```yaml
training:
  batch_size: 32
  num_epochs: 100
  learning_rate: 0.001
```

### 大规模训练 (RTX 4090/A100)
```yaml
training:
  batch_size: 64
  num_epochs: 200
  learning_rate: 0.001
  mixed_precision: true
```

## 🌐 网络访问设置

### 端口配置
- **Streamlit UI**: 8501
- **Jupyter Lab**: 8888
- **Flask API**: 5000

### 防火墙设置
```bash
# 开放端口（如果有权限）
sudo ufw allow 8501
sudo ufw allow 8888
sudo ufw allow 5000
```

### 访问地址
- UI界面: `http://服务器IP:8501`
- Jupyter: `http://服务器IP:8888`

## 💾 数据管理

### 数据上传
```bash
# 创建数据目录
mkdir -p /data/midi /data/charts

# 上传数据文件
# 使用平台提供的文件上传功能
# 或使用scp/rsync命令
```

### 模型保存
```bash
# 模型自动保存到
/models/best_model.pth
/models/checkpoint_epoch_*.pth

# 定期下载重要模型
```

## 🔍 监控和调试

### 系统监控
```bash
# GPU使用情况
watch -n 1 nvidia-smi

# 系统资源
htop

# 磁盘空间
df -h
```

### 日志查看
```bash
# 查看训练日志
tail -f logs/ai_chart_assistant.log

# Streamlit日志
tail -f ~/.streamlit/logs/streamlit.log
```

## 💰 成本优化建议

### 1. 按需使用
- 开发时使用便宜的CPU实例
- 训练时切换到GPU实例
- 训练完成后及时关闭

### 2. 数据预处理
- 在本地预处理数据
- 只上传处理后的数据

### 3. 模型优化
- 使用混合精度训练
- 适当减小批次大小
- 使用模型剪枝技术

### 4. 时间管理
- 避开高峰期（价格可能更高）
- 使用包月套餐（如果长期使用）

## 🛠️ 故障排除

### 常见问题

#### GPU内存不足
```bash
# 减小批次大小
export BATCH_SIZE=16

# 启用梯度检查点
export GRADIENT_CHECKPOINTING=true
```

#### 网络连接问题
```bash
# 检查端口是否开放
netstat -tlnp | grep 8501

# 检查防火墙
sudo ufw status
```

#### 依赖安装失败
```bash
# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple torch

# 或使用conda
conda install pytorch torchvision torchaudio -c pytorch
```

## 📋 部署检查清单

- [ ] GPU服务器已启动
- [ ] CUDA环境正常
- [ ] PyTorch GPU版本已安装
- [ ] 项目代码已上传
- [ ] 依赖包安装完成
- [ ] 端口已开放
- [ ] UI界面可访问
- [ ] 训练数据已准备
- [ ] 模型保存路径正确

## 🎯 最佳实践

1. **使用tmux/screen保持会话**
2. **定期备份重要数据和模型**
3. **监控GPU使用率和成本**
4. **合理设置自动关机时间**
5. **使用版本控制管理代码**

完成以上步骤后，你就可以在云端高效地训练和使用AI音游写谱助手了！
