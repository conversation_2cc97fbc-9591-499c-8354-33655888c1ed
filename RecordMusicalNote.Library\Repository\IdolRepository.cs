﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RecordMusicalNote.DataModel;
using System.Data;
using Helper;

namespace RecordMusicalNote.Library.Repository
{
    public class IdolRepository : IIdolRepository
    {
        public IIdol CreateNewIdol()
        {
            return new Idol();
        }

        public ILevelInfo CreateNewLevelInfo()
        {
            return new LevelInfo();
        }

        public IList<IIdol> GetInfo(int levelInfoId)
        {
            IList<IIdol> idols = new List<IIdol>();
            IList<IdolNoteInfo> DtoIdols = new List<IdolNoteInfo>();
            string sql = "select * from IdolNoteInfo where LevelInfoId="+levelInfoId;
            SqlHelper helper = new SqlHelper();
            DataSet ds = helper.ExecuteDataSet(sql);
            if(ds.Tables.Count>0)
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    IdolNoteInfo dtoIdol = new IdolNoteInfo();
                    dtoIdol.IdolNoteInfoID = (int)dr[0];
                    dtoIdol.LevelInfoId = (int)dr[1];
                    dtoIdol.Bar = (int)dr[2];
                    dtoIdol.Pos = (int)dr[3];
                    dtoIdol.FromTrack = dr[4].ToString();
                    dtoIdol.TargetTrack = dr[5].ToString();
                    dtoIdol.EndTrack = dr[6].ToString();
                    dtoIdol.EndBar = (int)dr[7];
                    dtoIdol.EndPos = (int)dr[8];
                    dtoIdol.NoteType = dr[9].ToString();
                    dtoIdol.CombineNoteNum = (int)dr[10];
                    DtoIdols.Add(dtoIdol);
                }
                foreach (var dto in DtoIdols)
                {
                    idols.Add(new Idol(dto));
                }
            }
            return idols;
        }

        public IList<ILevelInfo> GetInfo(int cmbIndex, string content)
        {
            IList<ILevelInfo> levelInfos = new List<ILevelInfo>();
            IList<DataModel.LevelInfo>  modelLIs = new List<DataModel.LevelInfo>();
            string sql = "";
            if (cmbIndex == 0)
            {
                sql = "select * from LevelInfo where SongName like '%{0}%'";
            }
            else if (cmbIndex == 1)
            {
                sql = "select * from LevelInfo where Artist like '%{0}%'";
            }
            else if (cmbIndex == 2)
            {
                sql = "select * from LevelInfo where BPM = '{0}'";
            }
            else if (cmbIndex == 3)
            {
                sql = "select * from LevelInfo where Star = '{0}'";
            }
            sql = string.Format(sql, content);
            SqlHelper sqlHelper = new SqlHelper();
            DataSet ds=  sqlHelper.ExecuteDataSet(sql);
            if (ds.Tables.Count > 0)
            {
                DataTable dt = ds.Tables[0];
              
                foreach (DataRow dr in dt.Rows)
                {
                    DataModel.LevelInfo li = new DataModel.LevelInfo();
                    li.LevelInfoId = (int)dr[0];
                    li.BPM = (double)dr[1];
                    li.BeatPerBar = (int)dr[2];
                    li.BeatLen = (int)dr[3];
                    li.EnterTimeAdjust = (int)dr[4];
                    li.NotePreShow = (int)dr[5];
                    li.LevelPreTime = (int)dr[6];
                    li.BarAmount = (int)dr[7];
                    li.BeginBarLen = (int)dr[8];
                    li.IsFourTrack = (bool)dr[9];
                    li.TrackCount = (int)dr[10];
                    li.LevelPreTime = (int)dr[11];
                    li.Star = (int)dr[12];
                    li.SongName = (string)dr[13];
                    li.Artist = (string)dr[14];
                    modelLIs.Add(li);
                }
            }
            foreach (DataModel.LevelInfo item in modelLIs)
            {
                levelInfos.Add( new LevelInfo(item));
            }
            return levelInfos;
        }

        public bool SaveInfo(IList<IIdol> modes, ILevelInfo levelInfo, out string msg)
        {
            msg = "";
            try
            {
                DataModel.LevelInfo dto = Common.EntityToDto(levelInfo);
                string sql = "";
                int NewLevelInfoId = -1;
                SqlHelper helper = new SqlHelper();
                if (dto != null && dto.LevelInfoId > 0)
                {
                    sql = @"UPDATE [Music].[dbo].[LevelInfo]
               SET [BPM] ={0}
               ,[BeatPerBar] = {1}
               ,[BeatLen] = {2}
               ,[EnterTimeAdjust] = {3}
               ,[NotePreShow] ={4}
               ,[LevelTime] = {5}
              ,[BarAmount] ={6}
              ,[BeginBarLen] = {7}
              ,[IsFourTrack] ={8}
              ,[TrackCount] = {9}
              ,[LevelPreTime] = {10}
              ,[Star] = {11}
              ,[SongName] = '{12}'
              ,[Artist] = '{13}'
              WHERE LevelInfo={14}";
                    sql = string.Format(sql, levelInfo.BPM, levelInfo.BeatPerBar, levelInfo.BeatLen, levelInfo.EnterTimeAdjust, levelInfo.NotePreShow, levelInfo.LevelTime, levelInfo.BarAmount, levelInfo.BeginBarLen, levelInfo.IsFourTrack ? 1 : 0, levelInfo.TrackCount, levelInfo.LevelPreTime, levelInfo.Star, levelInfo.SongName, levelInfo.Artist, levelInfo.LevelInfoId);
                    helper.ExecuteNonQuery(sql);
                }
                else
                {
                    sql = @"INSERT INTO [Music].[dbo].[LevelInfo]   ([BPM],[BeatPerBar]  ,[BeatLen],[EnterTimeAdjust],[NotePreShow]
           ,[LevelTime] ,[BarAmount]  ,[BeginBarLen] ,[IsFourTrack],[TrackCount],[LevelPreTime]  ,[Star] ,[SongName] ,[Artist])  VALUES
           ({0},{1} ,{2} ,{3} ,{4},{5},{6},{7} ,{8},{9} ,{10} ,{11},'{12}','{13}') select @@IDENTITY";
                    sql = string.Format(sql, levelInfo.BPM, levelInfo.BeatPerBar, levelInfo.BeatLen, levelInfo.EnterTimeAdjust, levelInfo.NotePreShow, levelInfo.LevelTime, levelInfo.BarAmount, levelInfo.BeginBarLen, levelInfo.IsFourTrack ? 1 : 0, levelInfo.TrackCount, levelInfo.LevelPreTime, levelInfo.Star, levelInfo.SongName, levelInfo.Artist);
                    NewLevelInfoId =Convert.ToInt32(helper.ExecuteScalar(sql));
                }
                IList<IdolNoteInfo> idolDtos = new List<IdolNoteInfo>();
                foreach (IIdol item in modes)
                {
                    IdolNoteInfo idolDto = new IdolNoteInfo();
                    idolDto.IdolNoteInfoID = item.IdolNoteInfoID;
                    idolDto.Bar = item.Bar;
                    idolDto.CombineNoteNum = item.CombineNoteNum;
                    idolDto.EndBar = item.EndBar;
                    idolDto.EndPos = item.EndPos;
                    idolDto.EndTrack = item.EndTrack;
                    idolDto.FromTrack = item.FromTrack;
                    idolDto.LevelInfoId = item.LevelInfoId;
                    idolDto.NoteType = item.NoteType;
                    idolDto.Pos = item.Pos;
                    idolDto.TargetTrack = item.TargetTrack;
                    idolDtos.Add(idolDto);

                }
                foreach (IdolNoteInfo idolDto in idolDtos)
                {
                    if (idolDto != null && idolDto.IdolNoteInfoID > 0)
                    {
                        sql = @"UPDATE [Music].[dbo].[IdolNoteInfo] SET [LevelInfoId] = {0}
                         ,[Bar] = {1} ,[Pos] = {2} ,[FromTrack] = '{3}',[TargetTrack] = '{4}' ,[EndTrack] = '{5}'  ,[EndBar] ={6}
                   ,[EndPos] ={7} ,[NoteType] = '{8}'   ,[CombineNoteNum] = {9} WHERE IdolNoteInfoID={10}";
                        sql = string.Format(sql, idolDto.LevelInfoId, idolDto.Bar, idolDto.Pos, idolDto.FromTrack, idolDto.TargetTrack, idolDto.EndTrack,
                            idolDto.EndBar, idolDto.EndPos, idolDto.NoteType, idolDto.CombineNoteNum, idolDto.IdolNoteInfoID);
                        helper.ExecuteNonQuery(sql);
                    }
                    else
                    {
                        sql = @"INSERT INTO [Music].[dbo].[IdolNoteInfo]    ([LevelInfoId]  ,[Bar],[Pos],[FromTrack]
                          ,[TargetTrack] ,[EndTrack] ,[EndBar] ,[EndPos] ,[NoteType] ,[CombineNoteNum])VALUES
                          ({0} ,{1},{2},'{3}','{4}','{5}' ,{6} ,{7},'{8}' ,{9})";
                        sql = string.Format(sql, NewLevelInfoId, idolDto.Bar, idolDto.Pos, idolDto.FromTrack, idolDto.TargetTrack, idolDto.EndTrack,
                            idolDto.EndBar, idolDto.EndPos, idolDto.NoteType, idolDto.CombineNoteNum);
                        helper.ExecuteNonQuery(sql);
                    }
                }
            }
            catch (Exception ex)
            {
                msg = ex.Message;
                return false;

            }
            return true;
        }
    }
}
