﻿using CommonModel;
using MidiSheetMusic.Helper;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Forms;

namespace MidiSheetMusic
{
    public partial class RecordMidiInfo : Form
    {
        public RecordMidiInfo()
        {
            InitializeComponent();
        }
        MidiFile _midifile;
        private void btnOpen_Click(object sender, EventArgs e)
        {
            OpenFileDialog dialog = new OpenFileDialog();
            dialog.Filter = "Midi Files (*.mid)|*.mid*|All Files (*.*)|*.*";
            dialog.FilterIndex = 1;
            dialog.RestoreDirectory = true;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                _midifile = new MidiFile(dialog.FileName);
               
            }
        }

        private void btnReocrd_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtSongName.Text))
            {
                MessageBox.Show("请输入歌曲名字");
                return;
            }
            bool isExist = Common.CheckHasExist(txtSongName.Text.Trim());
            if (isExist)
            {
                if (MessageBox.Show("已存在改歌曲数据，是否继续?", "提示",MessageBoxButtons.YesNo)==DialogResult.No) return;
            }
            if (_midifile == null) return;
            MidiSheetMusicMidi midi = new MidiSheetMusicMidi();
            midi.SongName = txtSongName.Text;
            midi.TrackCount = _midifile.TotalTracks;
            midi.QuarternotetTime = _midifile.Time.QuarterNote;
            midi.Meter = _midifile.Time.Numerator.ToString() + "/" 
                + _midifile.Time.Denominator.ToString();
            IList<CommonModel.MidiSheetMusicMidiTrack> midiTracks = new List<CommonModel.MidiSheetMusicMidiTrack>();         
            for (int i = 1; i <= _midifile.TotalTracks; i++)
            {
               MidiTrack mt=  _midifile.GetTrack(i);
                IList<MidiSheetMusicMidiTrackList> midiTrackLists = new List<MidiSheetMusicMidiTrackList>();
                CommonModel.MidiSheetMusicMidiTrack commonMT = new CommonModel.MidiSheetMusicMidiTrack();
                commonMT.Number = mt.Number;
                commonMT.Instrument = mt.Instrument;           
                foreach (MidiNote note in mt.Notes)
                {
                    MidiSheetMusicMidiTrackList trackList = new MidiSheetMusicMidiTrackList();
                    trackList.Channel = note.Channel;
                    trackList.Duration = note.Duration;
                    trackList.EndTime = note.EndTime;
                    trackList.StartTime = note.StartTime;
                    trackList.SyllabelNumber = note.Number;//音名数字
                    midiTrackLists.Add(trackList);
                }
                commonMT.MidiTrackList = midiTrackLists;
                midiTracks.Add(commonMT);
            }
            bool isSuccess = Common.SaveMidiInfo(midi, midiTracks);
            if (isSuccess)
                MessageBox.Show("成功保存midi信息");
            else
                MessageBox.Show("成功保存midi失败");
        }
    }
}
