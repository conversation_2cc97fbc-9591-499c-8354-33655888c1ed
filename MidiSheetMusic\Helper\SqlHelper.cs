﻿using System;
using System.Data.SqlClient;
using System.Data;

namespace Helper
{
    public class SqlHelper
    {

       public static readonly string conStr = @"server=.;database=MusicGame;user Id=sa;Pwd=**********;Connection Timeout=30;";
        #region ExecuteConnection
        private  SqlConnection ExecuteConnection()
        {
            SqlConnection con = new SqlConnection(conStr);
            try
            {
                con.Open();
            }
            catch(Exception ex)
            {
                throw new Exception($"数据库连接失败：{ex.Message}");
            }
            return con;

        }
        #endregion
        #region ExecuteScalar+
        public  Object ExecuteScalar(string sql, params SqlParameter[] param)
        {
            SqlConnection con = null;
            try
            {
                con = ExecuteConnection();
                SqlCommand cmd = con.CreateCommand();
                cmd.CommandText = sql;
                cmd.Parameters.AddRange(param);
                cmd.CommandType = System.Data.CommandType.Text;
                return cmd.ExecuteScalar();
            }
            finally
            {

                con.Close();

            }

        }
        #endregion
        #region ExecuteNonQuery+
        public  int ExecuteNonQuery(string sql, params SqlParameter[] param)
        {
            SqlConnection con = null; ;
            try
            {
                con = ExecuteConnection();
                SqlCommand cmd = con.CreateCommand();
                cmd.CommandText = sql;
                cmd.Parameters.AddRange(param);
                return cmd.ExecuteNonQuery();
            }
            finally
            {

                con.Close();

            }

        }
        #endregion
        #region ExecuteDataSet+
        public  DataSet ExecuteDataSet(string sql, params SqlParameter[] param)
        {
            SqlConnection con = null;
            DataSet ds = null;
            try
            {
                con = ExecuteConnection();
                SqlCommand cmd = con.CreateCommand();
                cmd.CommandText = sql;
                cmd.CommandTimeout = 30; // 设置命令超时时间
                if (param != null && param.Length > 0)
                {
                    cmd.Parameters.AddRange(param);
                }
                using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                {
                    ds = new DataSet();
                    da.Fill(ds);

                    return ds;

                }
            }
            catch (Exception ex)
            {
                throw new Exception($"执行SQL查询时发生错误：{ex.Message}\nSQL: {sql}");
            }
            finally
            {
                if (con != null && con.State == ConnectionState.Open)
                {
                    con.Close();
                }
            }
        }
        #endregion
        #region ExecuteReader+
        public  SqlDataReader ExecuteReader(string sql, params SqlParameter[] param)
        {
            SqlConnection con = null; ;
            try
            {
                con = ExecuteConnection();
                SqlCommand cmd = new SqlCommand(sql, con);
                cmd.CommandText = sql;
                cmd.Parameters.AddRange(param);
                return cmd.ExecuteReader();
            }

            catch(Exception ex)
            {
                con.Close();
                throw ex;
            }
        }
        #endregion


        public  bool RunProcedure(string storedProcName, params SqlParameter[] parameters)
        {
            SqlConnection con = null;
            //DataSet ds = null;
            try
            {
                con = ExecuteConnection();
                SqlCommand cmd = con.CreateCommand();
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = storedProcName;
                cmd.Parameters.AddRange(parameters);
               return (bool)cmd.ExecuteScalar();
                
            }
            finally
            {

                con.Close();

            }


        }
     
   
    }
}
