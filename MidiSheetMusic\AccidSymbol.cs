/*
 * Copyright (c) 2007-2008 <PERSON><PERSON><PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License version 2.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace MidiSheetMusic {


/*
 * An accidental (accid) symbol represents a sharp, flat, or natural
 * accidental.
 */

public class AccidSymbol : MusicSymbol {
    Accid accid;          /* The accidental (sharp, flat, natural) */
    WhiteNote whitenote;  /* The white note where the symbol occurs */
    Clef clef;            /* Which clef the symbols is in */
    int width;            /* Width of symbol */

    public AccidSymbol(Accid accid, WhiteNote note, Clef clef) {
        this.accid = accid;
        this.whitenote = note;
        this.clef = clef;
        width = MinWidth;
    }

    public WhiteNote Note  {
        get { return whitenote; }
    }

    public override int StartTime { 
        get { return -1; }  
    }

    public override int MinWidth { 
        get { return 3*SheetMusic.NoteHeight/2; }
    }

    public override int Width {
        get { return width; }
        set { width = value; }
    }

    public override int AboveStaff {
        get { return GetAboveStaff(); }
    }

    int GetAboveStaff() {
        int dist = WhiteNote.Top(clef).Dist(whitenote) * 
                   SheetMusic.NoteHeight/2;
        if (accid == Accid.Sharp || accid == Accid.Natural)
            dist -= SheetMusic.NoteHeight;
        else if (accid == Accid.Flat)
            dist -= 3*SheetMusic.NoteHeight/2;

        if (dist < 0)
            return -dist;
        else
            return 0;
    }

    public override int BelowStaff {
        get { return GetBelowStaff(); }
    }

    int GetBelowStaff() {
        int dist = WhiteNote.Bottom(clef).Dist(whitenote) * 
                   SheetMusic.NoteHeight/2 + 
                   SheetMusic.NoteHeight;
        if (accid == Accid.Sharp || accid == Accid.Natural) 
            dist += SheetMusic.NoteHeight;

        if (dist > 0)
            return dist;
        else 
            return 0;
    }

    public override void Draw(Graphics g, Pen pen, int ytop) {
        g.TranslateTransform(Width - MinWidth, 0);

        /* Store the y-pixel value of the top of the note in ynote. */
        int ynote = ytop + WhiteNote.Top(clef).Dist(whitenote) * 
                    SheetMusic.NoteHeight/2;

        if (accid == Accid.Sharp)
            DrawSharp(g, pen, ynote);
        else if (accid == Accid.Flat)
            DrawFlat(g, pen, ynote);
        else if (accid == Accid.Natural)
            DrawNatural(g, pen, ynote);

        g.TranslateTransform(-(Width - MinWidth), 0);
    }

    /* Draw a sharp symbol. */
    public void DrawSharp(Graphics g, Pen pen, int ynote) {

        /* Draw the two vertical lines */
        int ystart = ynote - SheetMusic.NoteHeight;
        int yend = ynote + 2*SheetMusic.NoteHeight;
        int x = SheetMusic.NoteHeight/2;
        pen.Width = SheetMusic.LineWidth;
        g.DrawLine(pen, x, ystart + 2, x, yend);
        x += SheetMusic.NoteHeight/2;
        g.DrawLine(pen, x, ystart, x, yend - 2);

        /* Draw the slightly upwards horizontal lines */
        int xstart = SheetMusic.NoteHeight/2 - SheetMusic.NoteHeight/4;
        int xend = SheetMusic.NoteHeight + SheetMusic.NoteHeight/4;
        ystart = ynote + SheetMusic.LineWidth;
        yend = ystart - SheetMusic.LineWidth - SheetMusic.LineSpace/4;
        pen.Width = SheetMusic.LineSpace/2;
        g.DrawLine(pen, xstart, ystart, xend, yend);
        ystart += SheetMusic.LineSpace;
        yend += SheetMusic.LineSpace;
        g.DrawLine(pen, xstart, ystart, xend, yend);
    }

    /* Draw a flat symbol.  */
    public void DrawFlat(Graphics g, Pen pen, int ynote) {
        int x = SheetMusic.LineSpace/4;

        /* Draw the vertical line */
        pen.Width = SheetMusic.LineWidth;
        g.DrawLine(pen, x, ynote - SheetMusic.NoteHeight - SheetMusic.NoteHeight/2,
                        x, ynote + SheetMusic.NoteHeight);

        /* Draw 3 bezier curves.
         * All 3 curves start and stop at the same points.
         * Each subsequent curve bulges more and more towards 
         * the topright corner, making the curve look thicker
         * towards the top-right.
         */
        g.DrawBezier(pen, x, ynote + SheetMusic.LineSpace/4,
            x + SheetMusic.LineSpace/2, ynote - SheetMusic.LineSpace/2,
            x + SheetMusic.LineSpace, ynote + SheetMusic.LineSpace/3,
            x, ynote + SheetMusic.LineSpace + SheetMusic.LineWidth + 1);

        g.DrawBezier(pen, x, ynote + SheetMusic.LineSpace/4,
            x + SheetMusic.LineSpace/2, ynote - SheetMusic.LineSpace/2,
            x + SheetMusic.LineSpace + SheetMusic.LineSpace/4, 
              ynote + SheetMusic.LineSpace/3 - SheetMusic.LineSpace/4,
            x, ynote + SheetMusic.LineSpace + SheetMusic.LineWidth + 1);


        g.DrawBezier(pen, x, ynote + SheetMusic.LineSpace/4,
            x + SheetMusic.LineSpace/2, ynote - SheetMusic.LineSpace/2,
            x + SheetMusic.LineSpace + SheetMusic.LineSpace/2, 
             ynote + SheetMusic.LineSpace/3 - SheetMusic.LineSpace/2,
            x, ynote + SheetMusic.LineSpace + SheetMusic.LineWidth + 1);


    }

    /* Draw a natural symbol. */
    public void DrawNatural(Graphics g, Pen pen, int ynote) {

        /* Draw the two vertical lines */
        int ystart = ynote - SheetMusic.LineSpace - SheetMusic.LineWidth;
        int yend = ynote + SheetMusic.LineSpace + SheetMusic.LineWidth;
        int x = SheetMusic.LineSpace/2;
        pen.Width = SheetMusic.LineWidth;
        g.DrawLine(pen, x, ystart, x, yend);
        x += SheetMusic.LineSpace - SheetMusic.LineSpace/4;
        ystart = ynote - SheetMusic.LineSpace/4;
        yend = ynote + 2*SheetMusic.LineSpace + SheetMusic.LineWidth - 
                 SheetMusic.LineSpace/4;
        g.DrawLine(pen, x, ystart, x, yend);

        /* Draw the slightly upwards horizontal lines */
        int xstart = SheetMusic.LineSpace/2;
        int xend = xstart + SheetMusic.LineSpace - SheetMusic.LineSpace/4;
        ystart = ynote + SheetMusic.LineWidth;
        yend = ystart - SheetMusic.LineWidth - SheetMusic.LineSpace/4;
        pen.Width = SheetMusic.LineSpace/2;
        g.DrawLine(pen, xstart, ystart, xend, yend);
        ystart += SheetMusic.LineSpace;
        yend += SheetMusic.LineSpace;
        g.DrawLine(pen, xstart, ystart, xend, yend);
    }

    public override string ToString() {
        return string.Format(
          "AccidSymbol accid={0} whitenote={1} clef={2} width={3}",
          accid, whitenote, clef, width);
    }

}

}


