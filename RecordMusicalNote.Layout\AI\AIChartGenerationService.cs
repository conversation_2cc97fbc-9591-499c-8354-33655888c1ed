using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;
using System.Threading.Tasks;
using CommonModel;
using RecordMusicalNote;
using MidiSheetMusic;

namespace MyWPF.Layout.AI
{
    /// <summary>
    /// AI辅助谱面生成服务
    /// </summary>
    public class AIChartGenerationService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly AIConfig _config;

        public AIChartGenerationService(AIConfig config = null)
        {
            _config = config ?? AIConfigManager.LoadConfig();
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_config.ApiKey}");
            _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
        }

        public AIChartGenerationService(string apiKey, string baseUrl = "https://api.deepseek.com/v1/chat/completions")
            : this(new AIConfig { ApiKey = apiKey, BaseUrl = baseUrl, Model = "deepseek-chat" })
        {
        }

        /// <summary>
        /// 生成AI辅助谱面
        /// </summary>
        public async Task<AIGenerationResult> GenerateChartAsync(AIGenerationRequest request)
        {
            var startTime = DateTime.Now;

            try
            {
                // 1. 分析音乐文件
                var musicAnalysis = AnalyzeMusicFile(request.MidiFilePath);

                // 2. 构建AI提示词
                var prompt = BuildPrompt(musicAnalysis, request);

                // 3. 调用AI服务
                var aiResponse = await CallAIServiceAsync(prompt);

                // 4. 解析AI响应
                var result = ParseAIResponse(aiResponse, request);

                // 5. 添加统计信息
                result.Stats = new AIGenerationStats
                {
                    GenerationTime = startTime,
                    Duration = DateTime.Now - startTime,
                    NotesGenerated = result.GeneratedNotes.Count,
                    NotesValidated = result.GeneratedNotes.Count
                };

                return result;
            }
            catch (Exception ex)
            {
                return new AIGenerationResult
                {
                    Success = false,
                    ErrorMessage = $"AI生成失败: {ex.Message}",
                    GeneratedNotes = new List<IIdol>(),
                    Stats = new AIGenerationStats
                    {
                        GenerationTime = startTime,
                        Duration = DateTime.Now - startTime,
                        NotesGenerated = 0,
                        NotesValidated = 0
                    }
                };
            }
        }

        /// <summary>
        /// 分析MIDI文件
        /// </summary>
        private MusicAnalysisData AnalyzeMusicFile(string midiFilePath)
        {
            var midiFile = new MidiFile(midiFilePath);
            
            return new MusicAnalysisData
            {
                BasicInfo = new BasicMusicInfo
                {
                    FileName = System.IO.Path.GetFileName(midiFilePath),
                    Duration = CalculateDuration(midiFile),
                    BPM = CalculateBPM(midiFile),
                    TimeSignature = $"{midiFile.Time.Numerator}/{midiFile.Time.Denominator}",
                    TotalTracks = midiFile.TotalTracks,
                    QuarterNote = midiFile.Time.QuarterNote
                },
                StructureAnalysis = AnalyzeStructure(midiFile),
                RhythmPattern = AnalyzeRhythm(midiFile),
                MelodyFeatures = AnalyzeMelody(midiFile)
            };
        }

        /// <summary>
        /// 构建AI提示词
        /// </summary>
        private string BuildPrompt(MusicAnalysisData musicAnalysis, AIGenerationRequest request)
        {
            var context = new
            {
                task = "音游谱面生成",
                musicAnalysis = musicAnalysis,
                audioAnalysis = BuildAudioAnalysisContext(request.AudioAnalysis),
                gameMode = new
                {
                    type = request.GameMode,
                    trackCount = request.TrackCount,
                    trackNames = GetTrackNames(request.TrackCount),
                    noteTypes = GetNoteTypes(request.GameMode),
                    difficulty = request.Difficulty
                },
                generationParams = new
                {
                    style = request.Style,
                    notesDensity = request.NoteDensity,
                    maxNotesPerSecond = request.MaxNotesPerSecond,
                    followMelody = request.FollowMelody,
                    trackBalance = request.TrackBalance
                },
                existingChart = request.ExistingNotes?.Take(50).Select(n => new // 增加到50个作为参考
                {
                    bar = n.Bar,
                    pos = n.Pos,
                    fromTrack = n.FromTrack,
                    targetTrack = n.TargetTrack,
                    noteType = n.NoteType
                }).ToList(),
                requirements = new
                {
                    outputFormat = "JSON",
                    maxNotes = request.MaxGeneratedNotes,
                    timeRange = new { start = request.StartTime, end = request.EndTime }
                }
            };

            var systemPrompt = GetSystemPrompt();
            var userPrompt = $@"
请根据以下音乐分析数据生成高质量的音游谱面：

{JsonConvert.SerializeObject(context, Formatting.Indented)}

请严格按照以下JSON格式输出：
{{
  ""notes"": [
    {{
      ""bar"": 1,
      ""pos"": 0,
      ""fromTrack"": ""Left1"",
      ""targetTrack"": ""Left1"",
      ""endTrack"": ""Left1"",
      ""noteType"": ""short"",
      ""endBar"": 1,
      ""endPos"": 0,
      ""reasoning"": ""对应音乐的强拍""
    }}
  ],
  ""metadata"": {{
    ""totalNotes"": 0,
    ""estimatedDifficulty"": 0.0,
    ""averageNotesPerSecond"": 0.0
  }}
}}

注意事项：
1. bar和pos必须是整数
2. noteType只能是: short, long, slip
3. trackName只能是: {string.Join(", ", GetTrackNames(request.TrackCount))}
4. 确保音符时间分布合理，避免过于密集
5. 长音符(long)的endBar和endPos必须大于开始位置
6. 滑动音符(slip)的endTrack必须与targetTrack不同
";

            return userPrompt;
        }

        /// <summary>
        /// 调用AI服务
        /// </summary>
        private async Task<string> CallAIServiceAsync(string prompt)
        {
            var requestBody = new
            {
                model = _config.Model,
                messages = new[]
                {
                    new { role = "system", content = GetSystemPrompt() },
                    new { role = "user", content = prompt }
                },
                temperature = _config.Temperature,
                max_tokens = _config.MaxTokens,
                response_format = new { type = "json_object" }
            };

            var json = JsonConvert.SerializeObject(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(_config.BaseUrl, content);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<DeepSeekResponse>(responseContent);

            return result.choices[0].message.content;
        }

        /// <summary>
        /// 解析AI响应
        /// </summary>
        private AIGenerationResult ParseAIResponse(string aiResponse, AIGenerationRequest request)
        {
            try
            {
                var jsonResponse = JsonConvert.DeserializeObject<AIChartResponse>(aiResponse);
                var generatedNotes = new List<IIdol>();
                var validationErrors = new List<string>();

                foreach (var noteData in jsonResponse.notes)
                {
                    var idol = CreateIdolFromAIData(noteData);
                    var validationResult = ValidateNote(idol, request);

                    if (validationResult.IsValid)
                    {
                        generatedNotes.Add(idol);
                    }
                    else
                    {
                        validationErrors.Add($"音符验证失败: Bar={noteData.bar}, Pos={noteData.pos}, 错误={validationResult.ErrorMessage}");
                    }
                }

                return new AIGenerationResult
                {
                    Success = true,
                    GeneratedNotes = generatedNotes,
                    Metadata = jsonResponse.metadata,
                    OriginalResponse = aiResponse,
                    ValidationErrors = validationErrors
                };
            }
            catch (Exception ex)
            {
                return new AIGenerationResult
                {
                    Success = false,
                    ErrorMessage = $"解析AI响应失败: {ex.Message}",
                    GeneratedNotes = new List<IIdol>(),
                    OriginalResponse = aiResponse
                };
            }
        }

        /// <summary>
        /// 从AI数据创建Idol音符
        /// </summary>
        private IIdol CreateIdolFromAIData(AIChartNote noteData)
        {
            var idol = new RecordMusicalNote.Library.Idol();
            
            idol.Bar = noteData.bar;
            idol.Pos = noteData.pos;
            idol.FromTrack = noteData.fromTrack;
            idol.TargetTrack = noteData.targetTrack;
            idol.EndTrack = noteData.endTrack ?? noteData.targetTrack;
            idol.NoteType = noteData.noteType;
            idol.EndBar = noteData.endBar ?? noteData.bar;
            idol.EndPos = noteData.endPos ?? noteData.pos;

            return idol;
        }

        /// <summary>
        /// 验证音符数据
        /// </summary>
        private ValidationResult ValidateNote(IIdol note, AIGenerationRequest request)
        {
            // 验证轨道名称
            var validTracks = GetTrackNames(request.TrackCount);
            if (!validTracks.Contains(note.FromTrack) || !validTracks.Contains(note.TargetTrack))
                return new ValidationResult { IsValid = false, ErrorMessage = "无效的轨道名称" };

            // 验证音符类型
            var validNoteTypes = new[] { "short", "long", "slip" };
            if (!validNoteTypes.Contains(note.NoteType))
                return new ValidationResult { IsValid = false, ErrorMessage = "无效的音符类型" };

            // 验证时间范围
            if (note.Bar < 1 || note.Pos < 0 || note.Pos >= 64)
                return new ValidationResult { IsValid = false, ErrorMessage = "时间位置超出范围" };

            // 验证长音符的结束位置
            if (note.NoteType == "long")
            {
                if (note.EndBar < note.Bar || (note.EndBar == note.Bar && note.EndPos <= note.Pos))
                    return new ValidationResult { IsValid = false, ErrorMessage = "长音符结束位置无效" };
            }

            // 验证滑动音符的结束轨道
            if (note.NoteType == "slip")
            {
                if (note.EndTrack == note.TargetTrack)
                    return new ValidationResult { IsValid = false, ErrorMessage = "滑动音符起始和结束轨道相同" };

                if (!IsAdjacentTrack(note.TargetTrack, note.EndTrack, request.TrackCount))
                    return new ValidationResult { IsValid = false, ErrorMessage = "滑动音符轨道不相邻" };
            }

            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// 检查轨道是否相邻
        /// </summary>
        private bool IsAdjacentTrack(string track1, string track2, int trackCount)
        {
            var tracks = GetTrackNames(trackCount);
            var index1 = Array.IndexOf(tracks, track1);
            var index2 = Array.IndexOf(tracks, track2);

            return Math.Abs(index1 - index2) == 1;
        }

        /// <summary>
        /// 获取系统提示词
        /// </summary>
        private string GetSystemPrompt()
        {
            return @"你是一个专业的音游谱面制作AI助手。你的任务是根据音乐分析数据生成高质量、可玩性强的音游谱面。

核心原则：
1. 音符必须与音乐的节拍和旋律对齐
2. 确保谱面的可玩性和流畅性
3. 根据难度等级合理分配音符密度和复杂度
4. 保持轨道之间的平衡
5. 避免不合理的手部动作

音符类型说明：
- short: 点击音符，瞬间按下即可
- long: 长按音符，需要持续按住一段时间
- slip: 滑动音符，从一个轨道滑动到另一个轨道

轨道布局（从左到右）：
- 4轨道模式: Left2, Left1, Right1, Right2

请始终以JSON格式输出结果，确保数据格式正确。";
        }

        // 辅助方法
        private double CalculateDuration(MidiFile midiFile)
        {
            // 简化实现：返回默认时长
            // 实际项目中可以根据具体的MidiFile结构来计算
            try
            {
                // 这里简化处理，返回一个基于文件大小的估算值
                return 240.0; // 默认4分钟
            }
            catch
            {
                return 240.0; // 出错时返回默认值
            }
        }

        private int CalculateBPM(MidiFile midiFile)
        {
            // 简化实现：返回默认BPM
            // 实际项目中可以从MIDI文件的tempo事件中提取
            try
            {
                return 120; // 默认120 BPM
            }
            catch
            {
                return 120; // 出错时返回默认值
            }
        }
        private string[] GetTrackNames(int trackCount) => trackCount == 4 ? new[] { "Left2", "Left1", "Right1", "Right2" } : new[] { "Left1", "Right1" };
        private string[] GetNoteTypes(string gameMode) => new[] { "short", "long", "slip" };
        
        private StructureAnalysisData AnalyzeStructure(MidiFile midiFile)
        {
            // 简化实现：创建基本的段落结构
            var duration = CalculateDuration(midiFile);
            return new StructureAnalysisData
            {
                Sections = new List<MusicSection>
                {
                    new MusicSection { Type = "intro", StartTime = 0, EndTime = Math.Min(16, duration * 0.1), Intensity = 0.3 },
                    new MusicSection { Type = "verse", StartTime = Math.Min(16, duration * 0.1), EndTime = Math.Min(duration * 0.4, duration), Intensity = 0.6 },
                    new MusicSection { Type = "chorus", StartTime = Math.Min(duration * 0.4, duration), EndTime = Math.Min(duration * 0.8, duration), Intensity = 0.9 },
                    new MusicSection { Type = "outro", StartTime = Math.Min(duration * 0.8, duration), EndTime = duration, Intensity = 0.4 }
                },
                BeatGrid = GenerateBeatGrid(duration, CalculateBPM(midiFile))
            };
        }

        private RhythmPatternData AnalyzeRhythm(MidiFile midiFile)
        {
            var bpm = CalculateBPM(midiFile);
            var duration = CalculateDuration(midiFile);

            return new RhythmPatternData
            {
                BeatStrengths = GenerateBeatStrengths(duration, bpm),
                AverageNoteDensity = 4.0, // 简化：每秒4个音符
                CommonPatterns = new List<RhythmPattern>
                {
                    new RhythmPattern { Pattern = "4/4", Frequency = 1.0, StartTime = 0, EndTime = duration }
                }
            };
        }

        private MelodyFeaturesData AnalyzeMelody(MidiFile midiFile)
        {
            return new MelodyFeaturesData
            {
                MainMelody = new List<MelodyNote>(),
                AveragePitch = 60, // C4
                PitchRange = 24, // 2个八度
                Directions = new List<MelodyDirection>()
            };
        }

        private List<double> GenerateBeatGrid(double duration, int bpm)
        {
            var beatGrid = new List<double>();
            var beatInterval = 60.0 / bpm; // 每拍的秒数

            for (double time = 0; time < duration; time += beatInterval)
            {
                beatGrid.Add(time);
            }

            return beatGrid;
        }

        private List<BeatStrength> GenerateBeatStrengths(double duration, int bpm)
        {
            var beatStrengths = new List<BeatStrength>();
            var beatInterval = 60.0 / bpm;

            for (double time = 0; time < duration; time += beatInterval)
            {
                var beatInMeasure = (int)(time / beatInterval) % 4;
                var strength = beatInMeasure == 0 ? 1.0 : (beatInMeasure == 2 ? 0.8 : 0.6);

                beatStrengths.Add(new BeatStrength
                {
                    Time = time,
                    Strength = strength,
                    IsStrongBeat = beatInMeasure == 0 || beatInMeasure == 2
                });
            }

            return beatStrengths;
        }

        /// <summary>
        /// 构建音频分析上下文
        /// </summary>
        private object BuildAudioAnalysisContext(AudioAnalysisData audioAnalysis)
        {
            if (audioAnalysis == null)
            {
                return new
                {
                    available = false,
                    message = "未提供音频分析数据，将基于MIDI数据进行分析"
                };
            }

            return new
            {
                available = true,
                bpm = new
                {
                    value = audioAnalysis.BPM,
                    confidence = audioAnalysis.BPMConfidence,
                    description = $"检测到BPM为 {audioAnalysis.BPM:F1}，置信度 {audioAnalysis.BPMConfidence:P1}"
                },
                duration = new
                {
                    totalSeconds = audioAnalysis.Duration.TotalSeconds,
                    formatted = audioAnalysis.Duration.ToString(@"mm\:ss"),
                    description = $"音频时长 {audioAnalysis.Duration:mm\\:ss}"
                },
                beatPoints = new
                {
                    count = audioAnalysis.BeatTimes?.Count ?? 0,
                    allTimes = audioAnalysis.BeatTimes, // 包含所有节拍点
                    description = $"检测到 {audioAnalysis.BeatTimes?.Count ?? 0} 个节拍点"
                },
                energyPeaks = new
                {
                    count = audioAnalysis.EnergyPeaks?.Count ?? 0,
                    allPeaks = audioAnalysis.EnergyPeaks?.Select(p => new
                    {
                        time = p.Time,
                        energy = p.Energy,
                        confidence = p.Confidence
                    }).ToList(), // 包含所有能量峰值
                    description = $"检测到 {audioAnalysis.EnergyPeaks?.Count ?? 0} 个能量峰值"
                },
                energyLevels = audioAnalysis.EnergyLevels, // 包含完整能量级别数组
                detailedAnalysis = audioAnalysis.DetailedAnalysis != null ? new
                {
                    beatSegments = audioAnalysis.DetailedAnalysis.BeatSegments?.Select(s => new
                    {
                        startTime = s.StartTime,
                        endTime = s.EndTime,
                        averageBPM = s.AverageBPM,
                        confidence = s.Confidence,
                        characteristics = s.Characteristics,
                        beatsCount = s.BeatsInSegment?.Count ?? 0
                    }).ToList(),
                    energySegments = audioAnalysis.DetailedAnalysis.EnergySegments?.Select(s => new
                    {
                        startTime = s.StartTime,
                        endTime = s.EndTime,
                        averageEnergy = s.AverageEnergy,
                        peakEnergy = s.PeakEnergy,
                        energyLevel = s.EnergyLevel,
                        peaksCount = s.PeaksInSegment?.Count ?? 0
                    }).ToList(),
                    structureAnalysis = audioAnalysis.DetailedAnalysis.StructureAnalysis?.Sections?.Select(s => new
                    {
                        name = s.Name,
                        startTime = s.StartTime,
                        endTime = s.EndTime,
                        averageEnergy = s.AverageEnergy,
                        averageBPM = s.AverageBPM,
                        characteristics = s.Characteristics
                    }).ToList(),
                    frequencyAnalysis = audioAnalysis.DetailedAnalysis.FrequencyAnalysis?.Take(100).Select(f => new
                    {
                        time = f.Time,
                        lowFrequency = f.LowFrequency,
                        midFrequency = f.MidFrequency,
                        highFrequency = f.HighFrequency,
                        spectralCentroid = f.SpectralCentroid
                    }).ToList()
                } : null,
                recommendations = GenerateAudioBasedRecommendations(audioAnalysis)
            };
        }

        /// <summary>
        /// 基于音频分析生成制谱建议
        /// </summary>
        private object GenerateAudioBasedRecommendations(AudioAnalysisData audioAnalysis)
        {
            var recommendations = new List<string>();

            // BPM相关建议
            if (audioAnalysis.BPM > 0)
            {
                if (audioAnalysis.BPM < 80)
                    recommendations.Add("慢节奏音乐，建议使用较少的音符密度，重点放在强拍上");
                else if (audioAnalysis.BPM > 140)
                    recommendations.Add("快节奏音乐，可以使用较高的音符密度，注意节拍细分");
                else
                    recommendations.Add("中等节奏音乐，平衡音符密度和可玩性");
            }

            // 节拍点建议
            if (audioAnalysis.BeatTimes?.Count > 0)
            {
                recommendations.Add($"建议在检测到的 {audioAnalysis.BeatTimes.Count} 个节拍点上放置主要音符");

                if (audioAnalysis.BeatTimes.Count > 100)
                    recommendations.Add("节拍点较多，可以选择性使用强拍和重要节拍点");
            }

            // 能量峰值建议
            if (audioAnalysis.EnergyPeaks?.Count > 0)
            {
                var highEnergyPeaks = audioAnalysis.EnergyPeaks.Where(p => p.Confidence > 0.7).Count();
                recommendations.Add($"在 {highEnergyPeaks} 个高置信度能量峰值处建议放置重音符或特殊音符");

                if (audioAnalysis.EnergyPeaks.Count > 50)
                    recommendations.Add("能量变化丰富，可以根据能量强度调整音符类型和轨道分布");
            }

            // 时长相关建议
            if (audioAnalysis.Duration.TotalSeconds > 0)
            {
                var estimatedNotes = (int)(audioAnalysis.Duration.TotalSeconds * audioAnalysis.BPM / 60 * 0.7);
                recommendations.Add($"根据时长和BPM，建议生成约 {estimatedNotes} 个音符");
            }

            return new
            {
                suggestions = recommendations,
                summary = $"基于音频分析，这是一首 {audioAnalysis.BPM:F0} BPM 的音乐，时长 {audioAnalysis.Duration:mm\\:ss}，" +
                         $"包含 {audioAnalysis.BeatTimes?.Count ?? 0} 个节拍点和 {audioAnalysis.EnergyPeaks?.Count ?? 0} 个能量峰值"
            };
        }



        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
    }
}
