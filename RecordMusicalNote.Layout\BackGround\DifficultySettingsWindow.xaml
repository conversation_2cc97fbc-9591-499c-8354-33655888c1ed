<Window x:Class="MyWPF.Layout.BackGround.DifficultySettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="谱面难度设置" Height="500" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="谱面难度参数设置" 
                   FontSize="20" FontWeight="Bold"
                   HorizontalAlignment="Center" Margin="0,0,0,24"/>
        
        <!-- 设置面板 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- 轨道数量设置 -->
                <GroupBox Header="轨道设置" Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <TextBlock Text="轨道数量:" Margin="0,0,0,8"/>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <RadioButton Name="rbTrack4" Content="4轨" IsChecked="True" Margin="0,0,16,0"/>
                            <RadioButton Name="rbTrack5" Content="5轨" Margin="0,0,16,0"/>
                            <RadioButton Name="rbTrack6" Content="6轨" Margin="0,0,16,0"/>
                        </StackPanel>
                        
                        <CheckBox Name="cbIsFourFinger" Content="四指模式" IsChecked="True" Margin="0,0,0,8"/>
                        <TextBlock Text="勾选后将优化为四指操作模式" 
                                 FontSize="11" Foreground="Gray"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- 难度等级设置 -->
                <GroupBox Header="难度等级" Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <TextBlock Text="整体难度:" Margin="0,0,0,8"/>
                        <ComboBox Name="cmbDifficultyLevel" SelectedIndex="1" Margin="0,0,0,16">
                            <ComboBoxItem Content="简单 - 适合新手"/>
                            <ComboBoxItem Content="普通 - 适合一般玩家"/>
                            <ComboBoxItem Content="困难 - 适合熟练玩家"/>
                            <ComboBoxItem Content="专家 - 适合高手"/>
                        </ComboBox>
                        
                        <TextBlock Text="音符密度控制:" Margin="0,0,0,8"/>
                        <Slider Name="sliderNoteDensity" Minimum="0.3" Maximum="1.0" Value="0.7" 
                                TickFrequency="0.1" IsSnapToTickEnabled="True" Margin="0,0,0,8"/>
                        <TextBlock Name="txtNoteDensityValue" Text="70% - 保留70%的音符" 
                                 FontSize="11" Foreground="Gray" Margin="0,0,0,16"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- 音符类型设置 -->
                <GroupBox Header="音符类型控制" Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <CheckBox Name="cbEnableShortNotes" Content="短音符" IsChecked="True" Margin="0,0,0,8"/>
                        <CheckBox Name="cbEnableLongNotes" Content="长音符" IsChecked="True" Margin="0,0,0,8"/>
                        <CheckBox Name="cbEnableSlipNotes" Content="滑动音符" IsChecked="True" Margin="0,0,0,8"/>
                        <CheckBox Name="cbEnableCombineNotes" Content="组合音符" IsChecked="False" Margin="0,0,0,16"/>
                        
                        <TextBlock Text="长音符最小持续时间 (拍):" Margin="0,0,0,8"/>
                        <Slider Name="sliderLongNoteMinDuration" Minimum="0.5" Maximum="4.0" Value="1.0" 
                                TickFrequency="0.5" IsSnapToTickEnabled="True" Margin="0,0,0,8"/>
                        <TextBlock Name="txtLongNoteDurationValue" Text="1.0 拍" 
                                 FontSize="11" Foreground="Gray" Margin="0,0,0,16"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- 高级设置 -->
                <GroupBox Header="高级设置" Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <TextBlock Text="同时音符数量限制:" Margin="0,0,0,8"/>
                        <Slider Name="sliderMaxSimultaneousNotes" Minimum="1" Maximum="6" Value="2" 
                                TickFrequency="1" IsSnapToTickEnabled="True" Margin="0,0,0,8"/>
                        <TextBlock Name="txtMaxSimultaneousValue" Text="最多2个同时音符" 
                                 FontSize="11" Foreground="Gray" Margin="0,0,0,8"/>
                        
                        <CheckBox Name="cbSmartTrackAssignment" Content="智能轨道分配" IsChecked="True" Margin="0,0,0,8"/>
                        <TextBlock Text="根据音乐理论智能分配轨道位置" 
                                 FontSize="11" Foreground="Gray" Margin="0,0,0,8"/>
                        
                        <CheckBox Name="cbAvoidCrossHand" Content="避免交叉手型" IsChecked="True" Margin="0,0,0,8"/>
                        <TextBlock Text="减少需要交叉手指的操作" 
                                 FontSize="11" Foreground="Gray"/>
                    </StackPanel>
                </GroupBox>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,24,0,0">
            <Button Name="btnReset" Content="重置默认" Click="btnReset_Click" Margin="0,0,16,0" Padding="16,8"/>
            <Button Name="btnCancel" Content="取消" Click="btnCancel_Click" Margin="0,0,16,0" Padding="16,8"/>
            <Button Name="btnOK" Content="确定" Click="btnOK_Click" Padding="16,8"/>
        </StackPanel>
        
    </Grid>
</Window>
