"""
数据加载器

用于加载和预处理训练数据
"""

import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import logging
import json
import pickle

from ..audio_analysis import FeatureExtractor
from ..chart_generation.chart_data import ChartData
from ..format_converters import import_chart_from_file

logger = logging.getLogger(__name__)


class ChartDataset(Dataset):
    """谱面数据集"""
    
    def __init__(
        self,
        midi_files: List[str],
        chart_files: List[str],
        feature_extractor: FeatureExtractor,
        max_sequence_length: int = 1000,
        time_resolution: float = 0.125
    ):
        """
        初始化数据集
        
        Args:
            midi_files: MIDI文件路径列表
            chart_files: 谱面文件路径列表
            feature_extractor: 特征提取器
            max_sequence_length: 最大序列长度
            time_resolution: 时间分辨率
        """
        self.midi_files = midi_files
        self.chart_files = chart_files
        self.feature_extractor = feature_extractor
        self.max_sequence_length = max_sequence_length
        self.time_resolution = time_resolution
        
        # 验证数据
        if len(midi_files) != len(chart_files):
            raise ValueError("MIDI文件和谱面文件数量不匹配")
        
        self.data_pairs = list(zip(midi_files, chart_files))
        
        # 缓存
        self.feature_cache = {}
        self.chart_cache = {}
        
        logger.info(f"数据集初始化完成，共 {len(self.data_pairs)} 个样本")
    
    def __len__(self) -> int:
        return len(self.data_pairs)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        获取数据样本
        
        Args:
            idx: 样本索引
            
        Returns:
            Dict: 包含特征和标签的字典
        """
        midi_file, chart_file = self.data_pairs[idx]
        
        try:
            # 提取特征
            features = self._get_features(midi_file)
            
            # 加载谱面
            chart_data = self._get_chart_data(chart_file)
            
            # 转换为序列格式
            sequences = chart_data.to_sequence_format(self.time_resolution)
            
            # 处理序列长度
            processed_sequences = self._process_sequences(sequences)
            
            # 提取难度信息
            difficulty = chart_data.metadata.difficulty
            
            return {
                'features': torch.tensor(features, dtype=torch.float32),
                'sequences': processed_sequences,
                'difficulty': torch.tensor(difficulty, dtype=torch.long),
                'metadata': {
                    'midi_file': midi_file,
                    'chart_file': chart_file,
                    'duration': chart_data.metadata.duration,
                    'bpm': chart_data.metadata.bpm
                }
            }
            
        except Exception as e:
            logger.error(f"加载样本失败 {idx}: {e}")
            # 返回空样本
            return self._get_empty_sample()
    
    def _get_features(self, midi_file: str) -> np.ndarray:
        """
        获取MIDI特征
        
        Args:
            midi_file: MIDI文件路径
            
        Returns:
            np.ndarray: 特征向量
        """
        if midi_file in self.feature_cache:
            return self.feature_cache[midi_file]
        
        try:
            features = self.feature_extractor.extract_training_features(midi_file)
            self.feature_cache[midi_file] = features
            return features
        except Exception as e:
            logger.error(f"特征提取失败 {midi_file}: {e}")
            # 返回零特征
            return np.zeros(self.feature_extractor.get_feature_dim())
    
    def _get_chart_data(self, chart_file: str) -> ChartData:
        """
        获取谱面数据
        
        Args:
            chart_file: 谱面文件路径
            
        Returns:
            ChartData: 谱面数据
        """
        if chart_file in self.chart_cache:
            return self.chart_cache[chart_file]
        
        try:
            chart_data = import_chart_from_file(chart_file)
            if chart_data is None:
                raise ValueError("无法加载谱面文件")
            
            self.chart_cache[chart_file] = chart_data
            return chart_data
        except Exception as e:
            logger.error(f"谱面加载失败 {chart_file}: {e}")
            # 返回空谱面
            from ..chart_generation.chart_data import ChartMetadata
            return ChartData(ChartMetadata())
    
    def _process_sequences(self, sequences: Dict[str, List[int]]) -> Dict[str, torch.Tensor]:
        """
        处理序列数据
        
        Args:
            sequences: 原始序列字典
            
        Returns:
            Dict: 处理后的序列张量
        """
        processed = {}
        
        for track_name, sequence in sequences.items():
            # 截断或填充到固定长度
            if len(sequence) > self.max_sequence_length:
                sequence = sequence[:self.max_sequence_length]
            else:
                sequence = sequence + [0] * (self.max_sequence_length - len(sequence))
            
            processed[track_name] = torch.tensor(sequence, dtype=torch.long)
        
        return processed
    
    def _get_empty_sample(self) -> Dict[str, torch.Tensor]:
        """获取空样本"""
        feature_dim = getattr(self.feature_extractor, 'get_feature_dim', lambda: 28)()
        
        empty_sequences = {}
        for i in range(4):  # 默认4轨道
            track_name = f'track_{i}'
            empty_sequences[track_name] = torch.zeros(self.max_sequence_length, dtype=torch.long)
        
        return {
            'features': torch.zeros(feature_dim, dtype=torch.float32),
            'sequences': empty_sequences,
            'difficulty': torch.tensor(5, dtype=torch.long),
            'metadata': {
                'midi_file': '',
                'chart_file': '',
                'duration': 0.0,
                'bpm': 120.0
            }
        }


class ChartDataLoader:
    """谱面数据加载器"""
    
    def __init__(
        self,
        data_dir: str,
        feature_extractor: FeatureExtractor,
        batch_size: int = 32,
        validation_split: float = 0.2,
        max_sequence_length: int = 1000,
        time_resolution: float = 0.125
    ):
        """
        初始化数据加载器
        
        Args:
            data_dir: 数据目录
            feature_extractor: 特征提取器
            batch_size: 批次大小
            validation_split: 验证集比例
            max_sequence_length: 最大序列长度
            time_resolution: 时间分辨率
        """
        self.data_dir = Path(data_dir)
        self.feature_extractor = feature_extractor
        self.batch_size = batch_size
        self.validation_split = validation_split
        self.max_sequence_length = max_sequence_length
        self.time_resolution = time_resolution
        
        # 查找数据文件
        self.midi_files, self.chart_files = self._find_data_files()
        
        # 分割数据集
        self.train_files, self.val_files = self._split_dataset()
        
        logger.info(f"数据加载器初始化完成")
        logger.info(f"训练集: {len(self.train_files[0])} 个样本")
        logger.info(f"验证集: {len(self.val_files[0])} 个样本")
    
    def _find_data_files(self) -> Tuple[List[str], List[str]]:
        """
        查找数据文件
        
        Returns:
            Tuple: (MIDI文件列表, 谱面文件列表)
        """
        midi_dir = self.data_dir / "midi"
        chart_dir = self.data_dir / "charts"
        
        if not midi_dir.exists() or not chart_dir.exists():
            raise FileNotFoundError(f"数据目录不存在: {midi_dir} 或 {chart_dir}")
        
        # 查找MIDI文件
        midi_files = []
        for ext in ['.mid', '.midi']:
            midi_files.extend(midi_dir.glob(f"*{ext}"))
        
        # 查找对应的谱面文件
        chart_files = []
        matched_midi_files = []
        
        for midi_file in midi_files:
            # 查找同名的谱面文件
            base_name = midi_file.stem
            
            # 支持的谱面格式
            chart_extensions = ['.json', '.mc', '.imd', '.xml']
            chart_file = None
            
            for ext in chart_extensions:
                potential_chart = chart_dir / f"{base_name}{ext}"
                if potential_chart.exists():
                    chart_file = potential_chart
                    break
            
            if chart_file:
                matched_midi_files.append(str(midi_file))
                chart_files.append(str(chart_file))
        
        logger.info(f"找到 {len(matched_midi_files)} 对匹配的数据文件")
        
        return matched_midi_files, chart_files
    
    def _split_dataset(self) -> Tuple[Tuple[List[str], List[str]], Tuple[List[str], List[str]]]:
        """
        分割数据集
        
        Returns:
            Tuple: ((训练MIDI, 训练谱面), (验证MIDI, 验证谱面))
        """
        total_samples = len(self.midi_files)
        val_size = int(total_samples * self.validation_split)
        
        # 随机打乱
        indices = np.random.permutation(total_samples)
        
        val_indices = indices[:val_size]
        train_indices = indices[val_size:]
        
        train_midi = [self.midi_files[i] for i in train_indices]
        train_chart = [self.chart_files[i] for i in train_indices]
        
        val_midi = [self.midi_files[i] for i in val_indices]
        val_chart = [self.chart_files[i] for i in val_indices]
        
        return (train_midi, train_chart), (val_midi, val_chart)
    
    def get_train_loader(self) -> DataLoader:
        """获取训练数据加载器"""
        train_dataset = ChartDataset(
            self.train_files[0],
            self.train_files[1],
            self.feature_extractor,
            self.max_sequence_length,
            self.time_resolution
        )
        
        return DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=0,  # Windows兼容性
            collate_fn=self._collate_fn
        )
    
    def get_val_loader(self) -> DataLoader:
        """获取验证数据加载器"""
        val_dataset = ChartDataset(
            self.val_files[0],
            self.val_files[1],
            self.feature_extractor,
            self.max_sequence_length,
            self.time_resolution
        )
        
        return DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=0,
            collate_fn=self._collate_fn
        )
    
    def _collate_fn(self, batch: List[Dict]) -> Dict[str, torch.Tensor]:
        """
        批次整理函数
        
        Args:
            batch: 批次数据
            
        Returns:
            Dict: 整理后的批次数据
        """
        # 提取特征
        features = torch.stack([item['features'] for item in batch])
        difficulties = torch.stack([item['difficulty'] for item in batch])
        
        # 提取序列
        sequences = {}
        if batch:
            track_names = list(batch[0]['sequences'].keys())
            for track_name in track_names:
                sequences[track_name] = torch.stack([
                    item['sequences'][track_name] for item in batch
                ])
        
        return {
            'features': features,
            'sequences': sequences,
            'difficulties': difficulties
        }
    
    def save_cache(self, cache_file: str):
        """保存缓存"""
        cache_data = {
            'midi_files': self.midi_files,
            'chart_files': self.chart_files,
            'train_files': self.train_files,
            'val_files': self.val_files
        }
        
        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)
        
        logger.info(f"缓存已保存到: {cache_file}")
    
    def load_cache(self, cache_file: str) -> bool:
        """加载缓存"""
        try:
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            
            self.midi_files = cache_data['midi_files']
            self.chart_files = cache_data['chart_files']
            self.train_files = cache_data['train_files']
            self.val_files = cache_data['val_files']
            
            logger.info(f"缓存已从 {cache_file} 加载")
            return True
            
        except Exception as e:
            logger.error(f"加载缓存失败: {e}")
            return False
