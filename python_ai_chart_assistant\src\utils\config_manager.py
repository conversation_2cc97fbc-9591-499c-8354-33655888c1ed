"""
配置管理器

管理应用程序配置
"""

import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = {}
        self.config_path = None
        
        # 加载默认配置
        self._load_default_config()
        
        # 如果提供了配置文件路径，加载它
        if config_path:
            self.load_config(config_path)
    
    def _load_default_config(self):
        """加载默认配置"""
        default_config_path = Path(__file__).parent.parent.parent / "config" / "default_config.yaml"
        
        if default_config_path.exists():
            try:
                with open(default_config_path, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f)
                logger.info(f"加载默认配置: {default_config_path}")
            except Exception as e:
                logger.error(f"加载默认配置失败: {e}")
                self.config = self._get_fallback_config()
        else:
            logger.warning("默认配置文件不存在，使用内置配置")
            self.config = self._get_fallback_config()
    
    def _get_fallback_config(self) -> Dict[str, Any]:
        """获取后备配置"""
        return {
            'model': {
                'device': 'cpu',
                'input_dim': 28,
                'hidden_dim': 256,
                'num_layers': 3,
                'dropout': 0.2
            },
            'audio_analysis': {
                'time_resolution': 0.125
            },
            'chart_generation': {
                'default_difficulty': 5,
                'default_track_count': 4,
                'default_style': 'balanced'
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            }
        }
    
    def load_config(self, config_path: str):
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
        """
        config_path = Path(config_path)
        
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    user_config = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    user_config = json.load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
            
            # 合并配置
            self.config = self._merge_configs(self.config, user_config)
            self.config_path = str(config_path)
            
            logger.info(f"加载用户配置: {config_path}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def _merge_configs(self, default_config: Dict, user_config: Dict) -> Dict:
        """
        合并配置字典
        
        Args:
            default_config: 默认配置
            user_config: 用户配置
            
        Returns:
            Dict: 合并后的配置
        """
        merged = default_config.copy()
        
        for key, value in user_config.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key] = self._merge_configs(merged[key], value)
            else:
                merged[key] = value
        
        return merged
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取完整配置
        
        Returns:
            Dict: 配置字典
        """
        return self.config.copy()
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 导航到最后一级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
    
    def save_config(self, output_path: Optional[str] = None):
        """
        保存配置到文件
        
        Args:
            output_path: 输出文件路径，None表示保存到原路径
        """
        if output_path is None:
            if self.config_path is None:
                raise ValueError("没有指定输出路径且没有原始配置文件路径")
            output_path = self.config_path
        
        output_path = Path(output_path)
        
        try:
            # 创建目录
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                if output_path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(self.config, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                elif output_path.suffix.lower() == '.json':
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"不支持的输出格式: {output_path.suffix}")
            
            logger.info(f"配置已保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            raise
    
    def validate_config(self) -> List[str]:
        """
        验证配置的有效性
        
        Returns:
            List[str]: 错误信息列表，空列表表示配置有效
        """
        errors = []
        
        # 验证模型配置
        model_config = self.get('model', {})
        if not isinstance(model_config.get('input_dim'), int) or model_config.get('input_dim', 0) <= 0:
            errors.append("model.input_dim 必须是正整数")
        
        if not isinstance(model_config.get('hidden_dim'), int) or model_config.get('hidden_dim', 0) <= 0:
            errors.append("model.hidden_dim 必须是正整数")
        
        if model_config.get('device') not in ['cpu', 'cuda']:
            errors.append("model.device 必须是 'cpu' 或 'cuda'")
        
        # 验证音频分析配置
        audio_config = self.get('audio_analysis', {})
        time_resolution = audio_config.get('time_resolution', 0)
        if not isinstance(time_resolution, (int, float)) or time_resolution <= 0:
            errors.append("audio_analysis.time_resolution 必须是正数")
        
        # 验证谱面生成配置
        chart_config = self.get('chart_generation', {})
        difficulty = chart_config.get('default_difficulty', 0)
        if not isinstance(difficulty, int) or not (1 <= difficulty <= 10):
            errors.append("chart_generation.default_difficulty 必须是1-10的整数")
        
        track_count = chart_config.get('default_track_count', 0)
        if not isinstance(track_count, int) or not (1 <= track_count <= 8):
            errors.append("chart_generation.default_track_count 必须是1-8的整数")
        
        return errors
    
    def get_model_config(self) -> Dict[str, Any]:
        """获取模型配置"""
        return self.get('model', {})
    
    def get_audio_analysis_config(self) -> Dict[str, Any]:
        """获取音频分析配置"""
        return self.get('audio_analysis', {})
    
    def get_chart_generation_config(self) -> Dict[str, Any]:
        """获取谱面生成配置"""
        return self.get('chart_generation', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get('logging', {})
    
    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return self.get('api', {})
    
    def update_from_dict(self, updates: Dict[str, Any]):
        """
        从字典更新配置
        
        Args:
            updates: 更新的配置字典
        """
        self.config = self._merge_configs(self.config, updates)
    
    def reset_to_default(self):
        """重置为默认配置"""
        self._load_default_config()
        self.config_path = None
    
    def export_config(self, format: str = 'yaml') -> str:
        """
        导出配置为字符串
        
        Args:
            format: 导出格式 ('yaml' 或 'json')
            
        Returns:
            str: 配置字符串
        """
        if format.lower() == 'yaml':
            return yaml.dump(self.config, default_flow_style=False, 
                           allow_unicode=True, indent=2)
        elif format.lower() == 'json':
            return json.dumps(self.config, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的导出格式: {format}")


# 全局配置管理器实例
_global_config_manager = None


def get_global_config() -> ConfigManager:
    """
    获取全局配置管理器实例
    
    Returns:
        ConfigManager: 全局配置管理器
    """
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = ConfigManager()
    return _global_config_manager


def set_global_config(config_manager: ConfigManager):
    """
    设置全局配置管理器
    
    Args:
        config_manager: 配置管理器实例
    """
    global _global_config_manager
    _global_config_manager = config_manager
