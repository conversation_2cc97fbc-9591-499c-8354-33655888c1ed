<Window x:Class="MyWPF.Layout.CopyNotesDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="复制音符" Height="600" Width="550" WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize" MinHeight="700" MinWidth="800">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>    <!-- 标题 -->
            <RowDefinition Height="Auto"/>    <!-- 源范围和目标范围 -->
            <RowDefinition Height="Auto"/>    <!-- 复制选项 -->
            <RowDefinition Height="Auto"/>    <!-- 预览信息 -->
            <RowDefinition Height="*"/>       <!-- 说明 -->
            <RowDefinition Height="Auto"/>    <!-- 按钮 -->
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="复制音符设置" FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>
        
        <!-- 源范围和目标范围 -->
        <Grid Grid.Row="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 源范围 -->
            <GroupBox Grid.Column="0" Header="源范围（复制从哪里）">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="80"/>
                    </Grid.ColumnDefinitions>

                    <Label Grid.Column="0" Content="从:" VerticalAlignment="Center"/>
                    <TextBox Grid.Column="1" Name="txtSourceStartBar" Height="25" Margin="2"
                             materialDesign:HintAssist.Hint="起始"/>

                    <Label Grid.Column="2" Content="到:" VerticalAlignment="Center"/>
                    <TextBox Grid.Column="3" Name="txtSourceEndBar" Height="25" Margin="2"
                             materialDesign:HintAssist.Hint="结束"/>
                </Grid>
            </GroupBox>

            <!-- 目标范围 -->
            <GroupBox Grid.Column="2" Header="目标范围（复制到哪里）">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="80"/>
                    </Grid.ColumnDefinitions>

                    <Label Grid.Column="0" Content="从:" VerticalAlignment="Center"/>
                    <TextBox Grid.Column="1" Name="txtTargetStartBar" Height="25" Margin="2"
                             materialDesign:HintAssist.Hint="起始"/>

                    <Label Grid.Column="2" Content="到:" VerticalAlignment="Center"/>
                    <TextBox Grid.Column="3" Name="txtTargetEndBar" Height="25" Margin="2"
                             materialDesign:HintAssist.Hint="结束"/>
                </Grid>
            </GroupBox>
        </Grid>
        
        <!-- 复制选项 -->
        <GroupBox Grid.Row="2" Header="复制选项" Margin="0,0,0,10">
            <StackPanel Margin="10">
                <CheckBox Name="chkMirrorCopy" Content="镜像复制（左右轨道互换）" IsChecked="True" Margin="0,5"/>
                <CheckBox Name="chkClearTarget" Content="复制前清空目标区域" IsChecked="True" Margin="0,5"/>
            </StackPanel>
        </GroupBox>

        <!-- 预览信息 -->
        <GroupBox Grid.Row="3" Header="预览信息" Margin="0,0,0,10">
            <StackPanel Margin="10">
                <TextBlock Name="txtPreviewInfo" Text="请输入范围参数" FontStyle="Italic"
                           Foreground="Gray" TextWrapping="Wrap"/>
            </StackPanel>
        </GroupBox>

        <!-- 说明 -->
        <GroupBox Grid.Row="4" Header="说明">
            <TextBlock Margin="10" TextWrapping="Wrap" FontSize="12" Foreground="DarkBlue">
                <Run Text="• 源范围：选择要复制的音符所在的小节范围"/>
                <LineBreak/>
                <Run Text="• 目标范围：选择要粘贴到的小节范围"/>
                <LineBreak/>
                <Run Text="• 镜像复制：Left1↔Right1, Left2↔Right2"/>
                <LineBreak/>
                <Run Text="• 清空目标：复制前会删除目标范围内的所有音符"/>
                <LineBreak/>
                <Run Text="• 范围必须相等：源范围和目标范围的小节数必须相同"/>
            </TextBlock>
        </GroupBox>
        
        <!-- 按钮 -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,10">
            <Button Name="btnPreview" Content="预览" Width="80" Height="35" Margin="5" Click="BtnPreview_Click"/>
            <Button Name="btnOK" Content="确定" Width="80" Height="35" Margin="5" Click="BtnOK_Click"/>
            <Button Name="btnCancel" Content="取消" Width="80" Height="35" Margin="5" Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
