@echo off
REM Windows批处理脚本 - 启动AI音游写谱助手UI界面

echo 🎵 AI音游写谱助手 - 启动UI界面
echo =====================================

REM 设置环境变量跳过Streamlit欢迎信息
set STREAMLIT_SERVER_HEADLESS=true
set STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未找到，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

REM 启动UI界面
echo 🚀 正在启动UI界面...
echo 📍 地址: http://localhost:8501
echo.
echo 💡 提示: 按 Ctrl+C 可以停止服务
echo.

python run_ui.py

pause
