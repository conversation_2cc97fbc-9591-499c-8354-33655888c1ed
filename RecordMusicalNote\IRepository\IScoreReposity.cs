﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote.IRepository
{
   public  interface IScoreReposity
    {
        bool CheckIsExist(string songName, string artist,int mode);
        bool SaveScoreInfo(string songName, string artist, int showTimeStartBar, IList<IScoreDetailInfo> detailInfo,int mode);

        IList<IScore> GetScoreBySearch(int index, string searchContent);

        IList<IPinballScoreDetailInfo> GetPinballDetailInfoByScoreId(int scoreId);
        IList<IIdolScoreDetailInfo> GetIdolDetailInfoByScoreId(int scoreId);

    }
}
