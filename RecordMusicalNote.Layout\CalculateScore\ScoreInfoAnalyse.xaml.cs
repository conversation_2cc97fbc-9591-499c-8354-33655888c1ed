﻿using RecordMusicalNote;
using RecordMusicalNote.IRepository;
using RecordMusicalNote.Library;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace MyWPF.Layout
{
    /// <summary>
    /// ScoreInfoAnalyse.xaml 的交互逻辑
    /// </summary>
    public partial class ScoreInfoAnalyse : Window
    {
        public ScoreInfoAnalyse()
        {
            InitializeComponent();
            txtSearchConent.KeyDown += TxtSearchConent_KeyDown;
            _searchDataGrid.MouseDoubleClick += _searchDataGrid_MouseDoubleClick;
            _mainData.MouseDoubleClick += _mainData_MouseDoubleClick;
            SetDataGridColumns(false);
        }
        
        private void _mainData_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            IScoreDetailInfo detailInfo;
            if (_mode==1)
             detailInfo = _mainData.SelectedItem as IIdolScoreDetailInfo;
            else
             detailInfo = _mainData.SelectedItem as IPinballScoreDetailInfo;
            BaseInfoAnalyse(detailInfo);
        }

        IList<IScoreDetailInfo> _detailInfos;
        IList<IIdolScoreDetailInfo> _idolDetailInfos;
        IList<IPinballScoreDetailInfo> _pinballDetailInfos;
        IScore _selectedScore;
        int _mode = 0;
        private void _searchDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            _selectedScore = _searchDataGrid.SelectedItem as IScore;
            BindMainData();
        }
        private double GetBQIndex(int startIndex,int endIndex)
        {
            double BQIndex = 0;
            for (int i = startIndex; i <= endIndex; i++)
            {
              var currentCountInfos=  _detailInfos.Where(a => a.CurrentCount == i);
                if (currentCountInfos != null)
                {
                    foreach (var item in currentCountInfos)
                    {
                        if (_mode == 1)//星动模式
                        {
                            if (item.NoteType == 1 || item.NoteType == 2)
                                BQIndex += 1;
                            else
                                BQIndex += 0.3;
                        }
                        else if (_mode == 2)//弹珠    
                        {
                            if (item.NoteType == 1 || item.NoteType == 3)
                                BQIndex += 1;
                            else if(item.NoteType ==4)
                                BQIndex += 0.3;
                            else if (item.NoteType == 2)
                            {
                                if(item.SonId>0)
                                    BQIndex += 1;
                                else
                                    BQIndex += 2;
                            }
                        }
                    }
                }
            }
            return BQIndex;
        }
        private int _selectedTechId;
        private void BaseInfoAnalyse(IScoreDetailInfo detailInfo)
        {
            if (detailInfo != null)
            {
                int fireStartIndex = (detailInfo.Bar - 1) * 32 + detailInfo.Pos / 2;
                int endIndex = (detailInfo.EndBar - 1) * 32 + detailInfo.EndPos / 2;
                double JXTechNofireTotalScore = _detailInfos.Sum(a => a.ScoreNumJXTechAndNotInFire);//极限技能不爆气时候所获得的总分           
                lblJXMF.Content = JXTechNofireTotalScore;
                var bdDetailInfo = _detailInfos.Where(a => a.Bar == detailInfo.Bar && a.Pos == detailInfo.Pos).OrderBy(a => a.Id).FirstOrDefault();
                if (bdDetailInfo != null)
                    lblCombo.Content = (bdDetailInfo.CurrentCombo-1).ToString();
                double NofireNoTechTotalScore = _detailInfos.Sum(a => a.ScoreNumInBRAndRank);//取排位的不爆气无技总分数
                double currentPositionScore=0;
                if (_selectedTechId == 1)
                {
                
                    double JXTechNotInfireRangeScore = _detailInfos.Where(a =>a.CurrentCount>=fireStartIndex&&a.CurrentCount<= endIndex).Sum(a => a.ScoreNumJXTechAndNotInFire); //极限技能在爆气范围不爆气所获得的总分
                    double JXTechAndInFireScore = _detailInfos.Where(a => a.CurrentCount >= fireStartIndex && a.CurrentCount <= endIndex).Sum(a=>a.ScoreNumJXTechAndInFire);//极限技能爆气范围时候所获得的总分      
                    currentPositionScore = JXTechAndInFireScore + JXTechNofireTotalScore - JXTechNotInfireRangeScore;//极限技能总分数
                  

                }
                else if (_selectedTechId == 2)
                {
                    double BQTechAndInFireScore = _detailInfos.Where(a => a.CurrentCount >= fireStartIndex && a.CurrentCount <= endIndex).Sum(a => a.ScoreNumBQTechAndInFire); //爆气技能爆气范围时候所获得的总分
                    double BQTechNotInfireRangeScore = _detailInfos.Where(a => a.CurrentCount >= fireStartIndex && a.CurrentCount <= endIndex).Sum(a => a.ScoreNumInBRAndRank); ;//爆气技能在爆气范围不爆气所获得的总分
                    currentPositionScore = BQTechAndInFireScore + NofireNoTechTotalScore - BQTechNotInfireRangeScore;//爆气技能总分数
                }
                lblCurrentPostionMF.Content = currentPositionScore.ToString();
            }
            SetDataGridColumns(false);
        }

        private void BindMainData()
        {
            if (_selectedScore != null)
            {
                _mode = _selectedScore.Mode;
                IScoreReposity repository = ScoreHelper.CreateRepositoryInstance();
                if (_mode == 1)
                {
                    _idolDetailInfos = repository.GetIdolDetailInfoByScoreId(_selectedScore.Id);
                 
                }
                else  //if (_mode == 2)
                {
                    _pinballDetailInfos = repository.GetPinballDetailInfoByScoreId(_selectedScore.Id);
                }
                RangeAnalyse();//范围分析
                _mainData.ItemsSource = _detailInfos;
                lblMFNoTechA.Content = _detailInfos.Sum(a => a.ScoreNumInXXAndLab).ToString();//无技能无曝气实验室分数
                lblMFNoTechB.Content = _detailInfos.Sum(a => a.ScoreNumInBRAndRank).ToString();// 无技能排位,百人实验室分数
                var halfOfCombo = _detailInfos.Where(a => a.Bar == _selectedScore.ShowTimeStartBar).LastOrDefault();
                if (halfOfCombo == null)
                {
                    halfOfCombo= GetShowtimeLastElement(_detailInfos);
                }
                findhalfOfComboIndex = 1;
                if (halfOfCombo!=null)
                lblHalfOfCombo.Content = halfOfCombo.CurrentCombo.ToString();
                lblTotalCombo.Content = _detailInfos.LastOrDefault().CurrentCombo;
                double JXTechNofireTotalScore = _detailInfos.Sum(a => a.ScoreNumJXTechAndNotInFire);//极限技能不爆气时候所获得的总分           
                lblJXMF.Content = JXTechNofireTotalScore;
                BQDetailAnalyse(_analyseBDs);
                
            }
        }
        /// <summary>
        /// 是否是长条开始，其他类型的可以通过(非压爆)
        /// </summary>
        /// <param name="analysedDatas"></param>
        /// <returns></returns>
        private IList<BDAnalyse>FindUnYB(IList<BDAnalyse> analysedDatas)
        {
        
           var unYBAnalysedDatas = analysedDatas.OrderByDescending(a => a.UnYBScoreNumInBRAndRank).Take(50).ToList();
            IList<BDAnalyse> analysDatas = new List<BDAnalyse>();
            foreach (var unYBAnalysedData in unYBAnalysedDatas)
            {
                string noteTypeStr = "";
                if (_mode == 1 && unYBAnalysedData.UnYBFirstNoteType == 3)
                {
                    noteTypeStr = "长条";
                }
                else if (_mode == 2 && unYBAnalysedData.UnYBFirstNoteType == 4)
                {
                    noteTypeStr = "长条";

                }
                if (noteTypeStr != "长条")
                {
                    analysDatas.Add(unYBAnalysedData);
                    continue;
                }
                int currentCount = (unYBAnalysedData.UnYBBeginBar - 1) * 32 + (unYBAnalysedData.UnYBBeginPos) / 2;
                var c = _detailInfos.Where(a => a.CurrentCount == currentCount);
                foreach (var item in c)
                {
                    int xmlNoteId = item.XMLNoteId;
                    var first = _detailInfos.Where(a => a.XMLNoteId == xmlNoteId).FirstOrDefault();
                    if (unYBAnalysedData.UnYBBeginBar != first.Bar || unYBAnalysedData.UnYBBeginPos != first.Pos)
                        continue;
                    else analysDatas.Add(unYBAnalysedData);
                }
            }
            return analysDatas;
           
        }

        /// <summary>
        /// 是否是长条开始，其他类型的可以通过(压爆)
        /// </summary>
        /// <param name="analysedDatas"></param>
        /// <returns></returns>
        private IList<BDAnalyse> FindYB(IList<BDAnalyse> analysedDatas)
        {

            var YBAnalysedDatas = analysedDatas.OrderByDescending(a => a.YBScoreNumInBRAndRank).Take(50).ToList();
            IList<BDAnalyse> analysDatas = new List<BDAnalyse>();
            foreach (var YBAnalysedData in YBAnalysedDatas)
            {
                string noteTypeStr = "";
                if (_mode == 1 && YBAnalysedData.UnYBFirstNoteType == 3)
                {
                    noteTypeStr = "长条";
                }
                else if (_mode == 2 && YBAnalysedData.UnYBFirstNoteType == 4)
                {
                    noteTypeStr = "长条";

                }
                if (noteTypeStr != "长条")
                {
                    analysDatas.Add(YBAnalysedData);
                    continue;
                }
                int currentCount = (YBAnalysedData.UnYBBeginBar - 1) * 32 + (YBAnalysedData.UnYBBeginPos) / 2;
                var c = _detailInfos.Where(a => a.CurrentCount == currentCount);
                foreach (var item in c)
                {
                    int xmlNoteId = item.XMLNoteId;
                    var first = _detailInfos.Where(a => a.XMLNoteId == xmlNoteId).FirstOrDefault();
                    if (YBAnalysedData.UnYBBeginBar != first.Bar || YBAnalysedData.UnYBBeginPos != first.Pos)
                        continue;
                    else analysDatas.Add(YBAnalysedData);
                }
            }
            return analysDatas;

        }
        private void BQDetailAnalyse(IList<BDAnalyse> analysedDatas)
        {
           if(analysedDatas!=null&& analysedDatas.Count>0)
            {
                    IList<BDAnalyse> foundUnYBScoreDatas= FindUnYB(analysedDatas);
                     BDAnalyse foundScoreData= foundUnYBScoreDatas.FirstOrDefault();
                    double NofireNoTechTotalScore = _detailInfos.Sum(a => a.ScoreNumInBRAndRank);//取排位的不爆气无技能分数
                    double JXTechNofireTotalScore = _detailInfos.Sum(a => a.ScoreNumJXTechAndNotInFire);//极限技能不爆气时候所获得的总分

                    //非压爆
                    double unYBJXTechAndInFireScore = foundScoreData.UnYBJXTechInfire;//非压爆极限技能爆气范围时候所获得的总分
                    double unYBBQTechAndInFireScore = foundScoreData.UnYBBQTechInfire; //非压爆爆气技能爆气范围时候所获得的总分
                    double unJXTechfireRangeScore = foundScoreData.UnYBJXTechNotInfire; //非压爆极限技能在爆气范围不爆气所获得的总分
                    double unBQTechfireRangeScore = foundScoreData.UnYBScoreNumInBRAndRank;//非压爆爆气技能在爆气范围不爆气所获得的总分

                    double unJXTechFireAllScore = unYBJXTechAndInFireScore + JXTechNofireTotalScore - unJXTechfireRangeScore;//极限技能总分数
                    double unBQTechFireAllScore = unYBBQTechAndInFireScore + NofireNoTechTotalScore - unBQTechfireRangeScore;//爆气技能总分数
                  

                    if (unJXTechFireAllScore > unBQTechFireAllScore)
                    {
                        _selectedTechId = 1;
                        lblSingleTech.Content = "技能:<极限>";
                        lblSingleUnYBMF.Content = "满分:"+"\r\n"+"<" + unJXTechFireAllScore+">";
                    }
                    else
                    {
                        _selectedTechId = 2;
                        lblSingleTech.Content = "技能:<爆气>";
                        lblSingleUnYBMF.Content = "满分:<" + unBQTechFireAllScore+">";
                    }
                lblSingleUnYB.Content = "非压爆:" + "\r\n";
                int combo;
                    //寻找相同分数的爆点
                IList<BDAnalyse> sameBDs = foundUnYBScoreDatas.Where(a => a.UnYBScoreNumInBRAndRank == foundScoreData.UnYBScoreNumInBRAndRank).ToList();
                foreach (BDAnalyse sameBD in sameBDs)
                {
                     combo = _detailInfos.Where(a => a.CurrentCount == (sameBD.UnYBBeginBar - 1) * 32 +
                   sameBD.UnYBBeginPos / 2).FirstOrDefault().CurrentCombo - sameBDs.Count;
                    lblSingleUnYB.Content += "Combo:" + combo + "\r\n" + "指数:" + sameBD.UnYBIndex+"\r\n";
                }
                //压爆
                IList<BDAnalyse> foundYBScoreDatas = FindYB(analysedDatas);
                 foundScoreData = foundYBScoreDatas.FirstOrDefault();
                foundScoreData = FindYB(analysedDatas).FirstOrDefault();
                double YBJXTechAndInFireScore = foundScoreData.YBJXTechInfire;//压爆极限技能爆气范围时候所获得的总分
                double YBBQTechAndInFireScore = foundScoreData.YBBQTechInfire; //压爆爆气技能爆气范围时候所获得的总分
                double JXTechfireRangeScore = foundScoreData.YBJXTechNotInfire; ;//压爆极限技能在爆气范围不爆气所获得的总分
                double BQTechfireRangeScore = foundScoreData.YBScoreNumInBRAndRank;//压爆爆气技能在爆气范围不爆气所获得的总分

                double JXTechFireAllScore = YBJXTechAndInFireScore + JXTechNofireTotalScore - JXTechfireRangeScore;//极限技能总分数
                double BQTechFireAllScore = YBBQTechAndInFireScore + NofireNoTechTotalScore - BQTechfireRangeScore;//爆气技能总分数
                combo = _detailInfos.Where(a => a.CurrentCount == (foundScoreData.YBBeginBar - 1) * 32 +
                 foundScoreData.YBBeginPos / 2).FirstOrDefault().CurrentCombo - 1;

                if (JXTechFireAllScore > BQTechFireAllScore)
                {
                    _selectedTechId = 1;
                    lblSingleTech.Content = "技能:<极限>";
                    lblSingleYBMF.Content = "满分:" + "\r\n" + "<" + JXTechFireAllScore + ">";
                }
                else
                {
                    _selectedTechId = 2;
                    lblSingleTech.Content = "技能:<爆气>";
                    lblSingleYBMF.Content = "满分:<" + BQTechFireAllScore + ">";
                }
                lblSingleYB.Content = "压爆:" + "\r\n";
                sameBDs = foundYBScoreDatas.Where(a => a.YBScoreNumInBRAndRank == foundScoreData.YBScoreNumInBRAndRank).ToList();
                foreach (BDAnalyse sameBD in sameBDs)
                {
                    if (sameBD.YBScoreNumInBRAndRank > sameBD.UnYBScoreNumInBRAndRank)//压爆的分数一定比非压爆的高
                    {
                        combo = _detailInfos.Where(a => a.CurrentCount == (sameBD.YBBeginBar - 1) * 32 +
                      sameBD.YBBeginPos / 2).FirstOrDefault().CurrentCombo - 1;
                        lblSingleYB.Content += "Combo:" + combo + "\r\n" + "指数:" + sameBD.YBIndex + "\r\n";
                    }
                }

            }
        }

        int findhalfOfComboIndex = 1;
        private IScoreDetailInfo GetShowtimeLastElement(IList<IScoreDetailInfo> list)
        {
        
            var foundElement= list.Where(a => a.Bar == _selectedScore.ShowTimeStartBar - findhalfOfComboIndex).LastOrDefault();
            findhalfOfComboIndex += 1;
            if (findhalfOfComboIndex > 1000)
                return foundElement;
            if (foundElement == null)
                GetShowtimeLastElement(list);
            return foundElement;
        }
        private void TxtSearchConent_KeyDown(object sender, KeyEventArgs e)
        {
            if(e.Key==Key.Enter)
            Refresh();
           
        }

        private void txtFresh_Click(object sender, RoutedEventArgs e)
        {
            Refresh();
        }
        private void Refresh()
        {
            IScoreReposity repository = ScoreHelper.CreateRepositoryInstance();
            if (string.IsNullOrWhiteSpace(txtSearchConent.Text)) return;
            IList<IScore> scores = repository.GetScoreBySearch(_cmbSearchType.SelectedIndex, txtSearchConent.Text);
            _searchDataGrid.ItemsSource = scores;
            if (scores == null || scores.Count <= 0)
                ClearInfo();
            else if (scores.Count == 1)
            {
                _selectedScore = scores.FirstOrDefault();
                BindMainData();
            }
            SetDataGridColumns(false);
        }
        private void ClearInfo()
        {

            lblHalfOfCombo.Content = "";
            lblMFNoTechA.Content = "";
            lblMFNoTechB.Content = "";
            lblTotalCombo.Content = "";
            _mainData.ItemsSource = null;
            _searchDataGrid.ItemsSource = null;
            _idolDetailInfos = null;
            _detailInfos = null;
            _analyseBDs = null;
        }
        IList<BDAnalyse> _analyseBDs;
        /// <summary>
        /// 根据非压爆，压爆，超极限的范围来统计相应的数据
        /// </summary>
        private void RangeAnalyse()
        {
            if (_mode == 1)
            {
             _detailInfos=   _idolDetailInfos.Cast<IScoreDetailInfo>().ToList();
              
            }
            else
            {
                _detailInfos = _pinballDetailInfos.Cast<IScoreDetailInfo>().ToList();
            }
            var groupData = _detailInfos.GroupBy(a => new { a.Bar, a.Pos }).
                         Select(b => (new
                         {
                             Bar = b.FirstOrDefault().Bar,
                             Pos = b.FirstOrDefault().Pos,
                             ScoreNum = b.Sum(c => c.ScoreNumInBRAndRank),
                             CurrentCount = b.FirstOrDefault().CurrentCount,
                             NoteType = b.FirstOrDefault().NoteType,
                             BQTechInfire=b.Sum(c=>c.ScoreNumBQTechAndInFire),
                             JXTechInfire = b.Sum(c => c.ScoreNumJXTechAndInFire),
                             JXTechNotInfire = b.Sum(c => c.ScoreNumJXTechAndNotInFire)
                         }));
            _analyseBDs = new List<BDAnalyse>();
            var firstCurrentCount = (groupData.FirstOrDefault().Bar - 1) * 32 + groupData.FirstOrDefault().Pos;
            for (int i = firstCurrentCount; i < groupData.LastOrDefault().Bar * 32; i++)
            {
                int startIndex = i;
                var foundDetailInfo = groupData.Where(a => a.CurrentCount == startIndex);
                if (foundDetailInfo.Count()<=0) continue;
                int endIndex = i + 159;
                BDAnalyse score = new BDAnalyse();
                //非压爆统计
                var unYBStatisticScore = groupData.Where(a => a.CurrentCount >= startIndex && a.CurrentCount <= endIndex);     
                if (unYBStatisticScore != null && unYBStatisticScore.Count() > 0)
                {
                    score.UnYBScoreNumInBRAndRank = unYBStatisticScore.Sum(a => a.ScoreNum);
                    score.UnYBBQTechInfire = unYBStatisticScore.Sum(a => a.BQTechInfire);
                    score.UnYBJXTechInfire = unYBStatisticScore.Sum(a => a.JXTechInfire);
                    score.UnYBJXTechNotInfire = unYBStatisticScore.Sum(a => a.JXTechNotInfire);
                    score.UnYBBeginBar = startIndex / 32 + 1;
                    score.UnYBBeginPos = startIndex % 32 * 2;
                    score.UnYBEndBar = endIndex / 32 + 1;
                    score.UnYBEndPos = endIndex % 32 * 2;
                    score.UnYBFirstNoteType = unYBStatisticScore.FirstOrDefault().NoteType;
                    //score.UnYBBeginDesc = statisticScore.FirstOrDefault().NoteType;
                    //score.UnYBEndDesc = "";
                    score.UnYBIndex = GetBQIndex(startIndex, endIndex);
                }

                //压爆统计
                var YBStatisticScore = groupData.Where(a => a.CurrentCount >= startIndex && a.CurrentCount <= endIndex+1);
                if (YBStatisticScore != null && YBStatisticScore.Count() > 0)
                {
                    score.YBScoreNumInBRAndRank =YBStatisticScore.Sum(a => a.ScoreNum);
                    score.YBBQTechInfire = YBStatisticScore.Sum(a => a.BQTechInfire);
                    score.YBJXTechInfire = YBStatisticScore.Sum(a => a.JXTechInfire);
                    score.YBJXTechNotInfire = YBStatisticScore.Sum(a => a.JXTechNotInfire);
                    score.YBBeginBar = startIndex / 32 + 1;
                    score.YBBeginPos = startIndex % 32 * 2;
                    score.YBEndBar = (endIndex+1) / 32 + 1;
                    score.YBEndPos = (endIndex+1) % 32 * 2;
                    //score.UnYBBeginDesc = statisticScore.FirstOrDefault().NoteType;
                    //score.UnYBEndDesc = "";
                    score.YBFirstNoteType = YBStatisticScore.FirstOrDefault().NoteType;
                    score.YBIndex = GetBQIndex(startIndex, endIndex+1);
                 
                }
                _analyseBDs.Add(score);
                ////超极限统计
                //var YBStatisticScore = groupData.Where(a => a.CurrentCount >= startIndex && a.CurrentCount <= endIndex + 1);
                //if (YBStatisticScore != null && YBStatisticScore.Count() > 0)
                //{
                //    double YBScore = YBStatisticScore.Sum(a => a.ScoreNum);
                //    score.YBScore = YBScore;
                //    score.YBBeginBar = startIndex / 32 + 1;
                //    score.YBBeginPos = startIndex % 32 * 2;
                //    score.YBBeginEndBar = (endIndex + 1) / 32 + 1;
                //    score.YBBeginEndPos = (endIndex + 1) % 32 * 2;
                //    //score.UnYBBeginDesc = statisticScore.FirstOrDefault().NoteType;
                //    //score.UnYBEndDesc = "";
                //    score.UnYBIndex = GetBQIndex(startIndex, endIndex + 1);
                //    _analyseBDs.Add(score);
                //}
            }
          
        }
        private void SetDataGridColumns(bool isAnalyse)
        {
            if(isAnalyse)
            {
                _mainData.Columns[4].Header = "曝气范围基础分";
                _mainData.Columns[8].Header = "爆气开始的键类型";

            }
         
            else
            {
                _mainData.Columns[4].Header = "ScoreNum(实验室)";
                _mainData.Columns[8].Header = "键类型";
            }
           
        }

        private void txtUnYBAnaylyse_Click(object sender, RoutedEventArgs e)
        {
            if (_analyseBDs != null && _analyseBDs.Count > 0)
            {
                IList<IScoreDetailInfo> details = new List<IScoreDetailInfo>();
                foreach (var item in _analyseBDs.OrderByDescending(a => a.UnYBScoreNumInBRAndRank))
                {
                    IScoreDetailInfo score;
                    if (_mode == 1)
                    {
                        score = new IdolScoreDetailInfo();
                    }
                    else
                    {
                        score = new PinballScoreDetailInfo();
                    }
                    score.Bar = item.UnYBBeginBar;
                    score.Pos = item.UnYBBeginPos;
                    int startCombo = _detailInfos.Where(a => a.CurrentCount == (item.UnYBBeginBar - 1) * 32 +
                 item.UnYBBeginPos / 2).OrderByDescending(a=>a.NoteType).FirstOrDefault().CurrentCombo ;
                    score.CurrentCombo = startCombo;
                    score.EndBar = item.UnYBEndBar;
                    score.EndPos = item.UnYBEndPos;

                 //   int endCombo = _detailInfos.Where(a => a.CurrentCount == (item.UnYBEndBar - 1) * 32 +
               // item.UnYBEndPos / 2).OrderByDescending(a => a.NoteType).FirstOrDefault().CurrentCombo;
                  //  score.EndCombo = endCombo;
                    score.ShowScore = item.UnYBScoreNumInBRAndRank;
                    score.NoteType = item.UnYBFirstNoteType;
                    details.Add(score);
                }
                _mainData.ItemsSource = details;
                SetDataGridColumns(true);
            }
          
        }

        private void txtYBAnaylyse_Click(object sender, RoutedEventArgs e)
        {
            if (_analyseBDs != null && _analyseBDs.Count > 0)
            {
                IList<IScoreDetailInfo> details = new List<IScoreDetailInfo>();
                foreach (var item in _analyseBDs.OrderByDescending(a => a.YBScoreNumInBRAndRank))
                {
                    IScoreDetailInfo score;
                    if (_mode == 1)
                    {
                        score = new IdolScoreDetailInfo();
                    }
                    else
                    {
                        score = new PinballScoreDetailInfo();
                    }
                    score.Bar = item.YBBeginBar;
                    score.Pos = item.YBBeginPos;
                    int startCombo = _detailInfos.Where(a => a.CurrentCount == (item.YBBeginBar - 1) * 32 +
                 item.YBBeginPos / 2).OrderByDescending(a => a.NoteType).FirstOrDefault().CurrentCombo;
                    score.CurrentCombo = startCombo;
                    score.EndBar = item.YBEndBar;
                    score.EndPos = item.YBEndPos;

                    //   int endCombo = _detailInfos.Where(a => a.CurrentCount == (item.UnYBEndBar - 1) * 32 +
                    // item.UnYBEndPos / 2).OrderByDescending(a => a.NoteType).FirstOrDefault().CurrentCombo;
                    //  score.EndCombo = endCombo;
                    score.ShowScore = item.YBScoreNumInBRAndRank;
                    score.NoteType = item.YBFirstNoteType;
                    details.Add(score);
                }
                _mainData.ItemsSource = details;
                SetDataGridColumns(true);
            }
        }

        private void txtCJXAnaylyse_Click(object sender, RoutedEventArgs e)
        {

        }

        private void txtSRAnaylyse_Click(object sender, RoutedEventArgs e)
        {

        }

        private class BDAnalyse
        {

            ///非压爆
            public double UnYBScoreNumInBRAndRank { get; set; }
            public double UnYBBQTechInfire { get; set; }
            public double UnYBJXTechInfire { get; set; }
            public double UnYBJXTechNotInfire { get; set; }
            public int UnYBBeginBar { get; set; }
            public int UnYBBeginPos { get; set; }
            public int UnYBEndBar { get; set; }
            public int UnYBEndPos { get; set; }
            public double UnYBIndex { get; set; }
            public string UnYBBeginDesc { get; set; }
            public string UnYBEndDesc { get; set; }
            public int UnYBFirstNoteType { get; set; }

            ///压爆
            public double YBScoreNumInBRAndRank { get; set; }
            public double YBBQTechInfire { get; set; }
            public double YBJXTechInfire { get; set; }
            public double YBJXTechNotInfire { get; set; }
            public int YBBeginBar { get; set; }
            public int YBBeginPos { get; set; }
            public int YBEndBar { get; set; }
            public int YBEndPos { get; set; }
            public double YBIndex { get; set; }
            public string YBBeginDesc { get; set; }
            public string YBEndDesc { get; set; }
            public int YBFirstNoteType { get; set; }

            ///超极限
            ///压爆
            public double  CJXScoreNumInBRAndRank { get; set; }
            public double  CJXBQTechInfire { get; set; }
            public double CJXJXTechInfire { get; set; }
            public double  CJXJXTechNotInfire { get; set; }
            public int CJXBeginBar { get; set; }
            public int CJXBeginPos { get; set; }
            public int CJXEndBar { get; set; }
            public int CJXEndPos { get; set; }
            public double CJXIndex { get; set; }
            public string CJXBeginDesc { get; set; }
            public string CJXEndDesc { get; set; }
            public int CJXFirstNoteType { get; set; }

        }
    }


}
