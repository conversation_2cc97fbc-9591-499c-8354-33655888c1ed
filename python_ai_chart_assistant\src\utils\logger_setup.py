"""
日志设置工具

配置应用程序的日志系统
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Optional, Dict, Any
import sys


def setup_logging(
    level: str = "INFO",
    format_string: Optional[str] = None,
    log_file: Optional[str] = None,
    max_file_size: str = "10MB",
    backup_count: int = 5,
    console_output: bool = True
):
    """
    设置日志系统
    
    Args:
        level: 日志级别
        format_string: 日志格式字符串
        log_file: 日志文件路径
        max_file_size: 最大文件大小
        backup_count: 备份文件数量
        console_output: 是否输出到控制台
    """
    
    # 设置日志级别
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # 设置日志格式
    if format_string is None:
        format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    formatter = logging.Formatter(format_string)
    
    # 获取根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台处理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 解析文件大小
        max_bytes = _parse_file_size(max_file_size)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # 设置第三方库的日志级别
    _configure_third_party_loggers()
    
    logging.info("日志系统初始化完成")


def _parse_file_size(size_str: str) -> int:
    """
    解析文件大小字符串
    
    Args:
        size_str: 大小字符串，如 "10MB", "1GB"
        
    Returns:
        int: 字节数
    """
    size_str = size_str.upper().strip()
    
    if size_str.endswith('KB'):
        return int(float(size_str[:-2]) * 1024)
    elif size_str.endswith('MB'):
        return int(float(size_str[:-2]) * 1024 * 1024)
    elif size_str.endswith('GB'):
        return int(float(size_str[:-2]) * 1024 * 1024 * 1024)
    else:
        # 假设是字节数
        return int(size_str)


def _configure_third_party_loggers():
    """配置第三方库的日志级别"""
    
    # 设置常见第三方库的日志级别
    third_party_loggers = {
        'urllib3': logging.WARNING,
        'requests': logging.WARNING,
        'matplotlib': logging.WARNING,
        'PIL': logging.WARNING,
        'torch': logging.WARNING,
        'tensorflow': logging.WARNING,
        'librosa': logging.WARNING,
    }
    
    for logger_name, level in third_party_loggers.items():
        logging.getLogger(logger_name).setLevel(level)


def setup_logging_from_config(config: Dict[str, Any]):
    """
    从配置字典设置日志
    
    Args:
        config: 日志配置字典
    """
    logging_config = config.get('logging', {})
    
    setup_logging(
        level=logging_config.get('level', 'INFO'),
        format_string=logging_config.get('format'),
        log_file=logging_config.get('file'),
        max_file_size=logging_config.get('max_file_size', '10MB'),
        backup_count=logging_config.get('backup_count', 5),
        console_output=logging_config.get('console_output', True)
    )


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = (
                self.COLORS[record.levelname] + 
                record.levelname + 
                self.COLORS['RESET']
            )
        
        return super().format(record)


def setup_colored_logging(
    level: str = "INFO",
    format_string: Optional[str] = None,
    log_file: Optional[str] = None
):
    """
    设置彩色日志
    
    Args:
        level: 日志级别
        format_string: 日志格式字符串
        log_file: 日志文件路径
    """
    
    # 设置日志级别
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # 设置日志格式
    if format_string is None:
        format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 获取根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 彩色控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    console_handler.setFormatter(ColoredFormatter(format_string))
    root_logger.addHandler(console_handler)
    
    # 文件处理器（不使用颜色）
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(logging.Formatter(format_string))
        root_logger.addHandler(file_handler)
    
    # 设置第三方库的日志级别
    _configure_third_party_loggers()


class ContextFilter(logging.Filter):
    """上下文过滤器，添加额外的上下文信息"""
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__()
        self.context = context
    
    def filter(self, record):
        # 添加上下文信息到日志记录
        for key, value in self.context.items():
            setattr(record, key, value)
        return True


def add_context_to_logger(logger_name: str, context: Dict[str, Any]):
    """
    为指定日志器添加上下文信息
    
    Args:
        logger_name: 日志器名称
        context: 上下文字典
    """
    logger = logging.getLogger(logger_name)
    context_filter = ContextFilter(context)
    logger.addFilter(context_filter)


def get_logger(name: str, context: Optional[Dict[str, Any]] = None) -> logging.Logger:
    """
    获取带上下文的日志器
    
    Args:
        name: 日志器名称
        context: 上下文信息
        
    Returns:
        logging.Logger: 日志器实例
    """
    logger = logging.getLogger(name)
    
    if context:
        add_context_to_logger(name, context)
    
    return logger


class LogCapture:
    """日志捕获器，用于测试"""
    
    def __init__(self, logger_name: str = None, level: int = logging.DEBUG):
        self.logger_name = logger_name or 'root'
        self.level = level
        self.records = []
        self.handler = None
    
    def __enter__(self):
        # 创建内存处理器
        self.handler = logging.handlers.MemoryHandler(capacity=1000)
        self.handler.setLevel(self.level)
        
        # 添加到日志器
        logger = logging.getLogger(self.logger_name)
        logger.addHandler(self.handler)
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 移除处理器
        if self.handler:
            logger = logging.getLogger(self.logger_name)
            logger.removeHandler(self.handler)
            
            # 获取记录
            self.records = self.handler.buffer.copy()
            self.handler.close()
    
    def get_messages(self, level: Optional[int] = None) -> List[str]:
        """
        获取日志消息
        
        Args:
            level: 过滤的日志级别
            
        Returns:
            List[str]: 日志消息列表
        """
        messages = []
        for record in self.records:
            if level is None or record.levelno >= level:
                messages.append(record.getMessage())
        return messages
    
    def has_message(self, message: str, level: Optional[int] = None) -> bool:
        """
        检查是否包含指定消息
        
        Args:
            message: 要查找的消息
            level: 日志级别
            
        Returns:
            bool: 是否包含消息
        """
        messages = self.get_messages(level)
        return any(message in msg for msg in messages)
