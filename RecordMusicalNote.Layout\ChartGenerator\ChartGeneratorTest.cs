using System;
using System.IO;
using System.Linq;
using MidiSheetMusic;

namespace MyWPF.Layout.ChartGenerator
{
    /// <summary>
    /// 谱面生成器测试类
    /// 用于验证谱面生成功能的正确性
    /// </summary>
    public class ChartGeneratorTest
    {
        /// <summary>
        /// 测试基本的谱面生成功能
        /// </summary>
        /// <param name="midiFilePath">MIDI文件路径</param>
        /// <returns>测试结果</returns>
        public static string TestBasicGeneration(string midiFilePath)
        {
            try
            {
                if (!File.Exists(midiFilePath))
                {
                    return "错误：MIDI文件不存在";
                }

                var generator = new MusicChartGenerator();
                
                // 测试星动模式
                var idolLevel = generator.GenerateChart(midiFilePath, 1, 5, 4);
                var idolNotes = generator.GetIdolNotes();
                
                // 测试弹珠模式
                var pinballLevel = generator.GenerateChart(midiFilePath, 2, 5, 4);
                var pinballNotes = generator.GetPinballNotes();
                
                // 测试泡泡模式
                var bubbleLevel = generator.GenerateChart(midiFilePath, 3, 5, 4);
                var bubbleNotes = generator.GetBubbleNotes();

                // 生成测试报告
                var report = $@"
=== 谱面生成测试报告 ===
MIDI文件: {Path.GetFileName(midiFilePath)}

星动模式:
- 关卡信息: BPM={idolLevel.BPM:F1}, 小节数={idolLevel.BarAmount}, 轨道数={idolLevel.TrackCount}
- 音符数量: {idolNotes.Count}
- 音符类型分布: {GetNoteTypeDistribution(idolNotes.Select(n => n.NoteType))}

弹珠模式:
- 关卡信息: BPM={pinballLevel.BPM:F1}, 小节数={pinballLevel.BarAmount}, 轨道数={pinballLevel.TrackCount}
- 音符数量: {pinballNotes.Count}
- 音符类型分布: {GetNoteTypeDistribution(pinballNotes.Select(n => n.NoteType))}

泡泡模式:
- 关卡信息: BPM={bubbleLevel.BPM:F1}, 小节数={bubbleLevel.BarAmount}, 轨道数={bubbleLevel.TrackCount}
- 音符数量: {bubbleNotes.Count}
- 音符类型分布: {GetBubbleNoteTypeDistribution(bubbleNotes)}

测试结果: 成功
";
                return report;
            }
            catch (Exception ex)
            {
                return $"测试失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 测试不同难度级别的生成效果
        /// </summary>
        /// <param name="midiFilePath">MIDI文件路径</param>
        /// <returns>测试结果</returns>
        public static string TestDifficultyLevels(string midiFilePath)
        {
            try
            {
                if (!File.Exists(midiFilePath))
                {
                    return "错误：MIDI文件不存在";
                }

                var generator = new MusicChartGenerator();
                var report = "=== 难度级别测试报告 ===\n";
                report += $"MIDI文件: {Path.GetFileName(midiFilePath)}\n\n";

                for (int difficulty = 1; difficulty <= 10; difficulty += 2)
                {
                    var level = generator.GenerateChart(midiFilePath, 1, difficulty, 4); // 星动模式
                    var notes = generator.GetIdolNotes();
                    
                    report += $"难度 {difficulty}: {notes.Count} 个音符\n";
                }

                report += "\n测试结果: 成功";
                return report;
            }
            catch (Exception ex)
            {
                return $"测试失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 测试MIDI文件解析功能
        /// </summary>
        /// <param name="midiFilePath">MIDI文件路径</param>
        /// <returns>测试结果</returns>
        public static string TestMidiParsing(string midiFilePath)
        {
            try
            {
                if (!File.Exists(midiFilePath))
                {
                    return "错误：MIDI文件不存在";
                }

                var midiFile = new MidiFile(midiFilePath);
                
                var report = $@"
=== MIDI解析测试报告 ===
文件: {Path.GetFileName(midiFilePath)}
文件大小: {new FileInfo(midiFilePath).Length / 1024.0:F1} KB

基本信息:
- 轨道数量: {midiFile.TotalTracks}
- 时间签名: {midiFile.Time.Numerator}/{midiFile.Time.Denominator}
- 四分音符时长: {midiFile.Time.QuarterNote}
- 每小节时长: {midiFile.Time.Measure}

轨道详情:";

                for (int i = 1; i <= Math.Min(midiFile.TotalTracks, 10); i++)
                {
                    var track = midiFile.GetTrack(i);
                    var firstNote = track.Notes.FirstOrDefault();
                    var lastNote = track.Notes.LastOrDefault();

                    report += $@"
- 轨道 {i}: {track.Notes.Count} 个音符, 乐器: {track.Instrument}
  音符范围: {firstNote?.Number ?? 0} - {lastNote?.Number ?? 0}
  时间范围: {firstNote?.StartTime ?? 0} - {lastNote?.EndTime ?? 0}";
                }

                if (midiFile.TotalTracks > 10)
                {
                    report += $"\n... 还有 {midiFile.TotalTracks - 10} 个轨道";
                }

                report += "\n\n测试结果: 成功";
                return report;
            }
            catch (Exception ex)
            {
                return $"测试失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取音符类型分布统计
        /// </summary>
        private static string GetNoteTypeDistribution(System.Collections.Generic.IEnumerable<string> noteTypes)
        {
            var distribution = noteTypes
                .GroupBy(t => t)
                .ToDictionary(g => g.Key, g => g.Count());

            if (distribution.Count == 0)
                return "无";

            return string.Join(", ", distribution.Select(kvp => $"{kvp.Key}:{kvp.Value}"));
        }

        /// <summary>
        /// 获取泡泡音符类型分布统计
        /// </summary>
        private static string GetBubbleNoteTypeDistribution(System.Collections.Generic.IEnumerable<RecordMusicalNote.DataModel.BubbleNoteInfo> bubbleNotes)
        {
            var distribution = bubbleNotes
                .Where(n => n.Type.HasValue)
                .GroupBy(n => n.Type.Value)
                .ToDictionary(g => g.Key, g => g.Count());

            if (distribution.Count == 0)
                return "无";

            return string.Join(", ", distribution.Select(kvp => $"类型{kvp.Key}:{kvp.Value}"));
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        /// <param name="midiFilePath">MIDI文件路径</param>
        /// <returns>完整测试报告</returns>
        public static string RunAllTests(string midiFilePath)
        {
            var fullReport = "=== 谱面生成器完整测试报告 ===\n";
            fullReport += $"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n";
            fullReport += $"测试文件: {midiFilePath}\n\n";

            // 1. MIDI解析测试
            fullReport += "1. MIDI解析测试\n";
            fullReport += TestMidiParsing(midiFilePath);
            fullReport += "\n\n";

            // 2. 基本生成测试
            fullReport += "2. 基本生成测试\n";
            fullReport += TestBasicGeneration(midiFilePath);
            fullReport += "\n\n";

            // 3. 难度级别测试
            fullReport += "3. 难度级别测试\n";
            fullReport += TestDifficultyLevels(midiFilePath);
            fullReport += "\n\n";

            fullReport += "=== 测试完成 ===";
            return fullReport;
        }

        /// <summary>
        /// 验证生成的谱面数据的合理性
        /// </summary>
        /// <param name="midiFilePath">MIDI文件路径</param>
        /// <returns>验证结果</returns>
        public static string ValidateGeneratedChart(string midiFilePath)
        {
            try
            {
                var generator = new MusicChartGenerator();
                var level = generator.GenerateChart(midiFilePath, 1, 5, 4);
                var notes = generator.GetIdolNotes();

                var issues = new System.Collections.Generic.List<string>();

                // 检查基本数据
                if (level.BPM <= 0)
                    issues.Add("BPM值异常");
                
                if (level.BarAmount <= 0)
                    issues.Add("小节数异常");
                
                if (notes.Count == 0)
                    issues.Add("未生成任何音符");

                // 检查音符数据
                foreach (var note in notes.Take(100)) // 只检查前100个音符
                {
                    if (note.Bar <= 0)
                        issues.Add($"音符小节号异常: {note.Bar}");
                    
                    if (note.Pos < 0 || note.Pos >= 64)
                        issues.Add($"音符位置异常: {note.Pos}");
                    
                    if (string.IsNullOrEmpty(note.NoteType))
                        issues.Add("音符类型为空");
                }

                if (issues.Count == 0)
                {
                    return "验证通过：生成的谱面数据格式正确";
                }
                else
                {
                    return $"验证失败：发现 {issues.Count} 个问题\n" + string.Join("\n", issues);
                }
            }
            catch (Exception ex)
            {
                return $"验证失败: {ex.Message}";
            }
        }
    }
}
