using System;

namespace CommonModel
{
    /// <summary>
    /// 难度设置类
    /// </summary>
    public class DifficultySettings
    {
        // 轨道设置
        public int TrackCount { get; set; } = 4;
        public bool IsFourFinger { get; set; } = false;

        // 难度等级
        public DifficultyLevel DifficultyLevel { get; set; } = DifficultyLevel.Normal;
        public double NoteDensity { get; set; } = 0.7; // 保留70%的音符

        // 音符类型控制
        public bool EnableShortNotes { get; set; } = true;
        public bool EnableLongNotes { get; set; } = true;
        public bool EnableSlipNotes { get; set; } = true;
        public bool EnableCombineNotes { get; set; } = false;
        public double LongNoteMinDuration { get; set; } = 1.0; // 拍

        // 高级设置
        public int MaxSimultaneousNotes { get; set; } = 2;
        public bool SmartTrackAssignment { get; set; } = true;
        public bool AvoidCrossHand { get; set; } = true;

        /// <summary>
        /// 获取轨道名称列表（从左到右的正确顺序）
        /// </summary>
        public string[] GetTrackNames()
        {
            switch (TrackCount)
            {
                case 4:
                    // 四轨从左到右：Left2, Left1, Right1, Right2
                    return new[] { "Left2", "Left1", "Right1", "Right2" };
                case 5:
                    // 五轨从左到右：Left2, Left1, Center, Right1, Right2
                    return new[] { "Left2", "Left1", "Center", "Right1", "Right2" };
                case 6:
                    // 六轨从左到右：Left3, Left2, Left1, Right1, Right2, Right3
                    return new[] { "Left3", "Left2", "Left1", "Right1", "Right2", "Right3" };
                default:
                    return new[] { "Left2", "Left1", "Right1", "Right2" };
            }
        }

        /// <summary>
        /// 根据难度等级调整参数（仅在用户未自定义时应用默认值）
        /// </summary>
        public void ApplyDifficultyAdjustments()
        {
            // 用户自定义设置优先，不再强制限制
            // 只在特定情况下应用难度限制

            switch (DifficultyLevel)
            {
                case DifficultyLevel.Easy:
                    // 简单模式：禁用复杂音符类型，但保留用户的密度设置
                    EnableSlipNotes = false;
                    EnableCombineNotes = false;
                    // 如果用户没有修改同时音符数量，使用默认限制
                    if (MaxSimultaneousNotes > 2)
                        MaxSimultaneousNotes = Math.Min(MaxSimultaneousNotes, 2);
                    break;
                case DifficultyLevel.Normal:
                    // 普通模式：保持用户设置，只限制极端值
                    if (MaxSimultaneousNotes > 3)
                        MaxSimultaneousNotes = Math.Min(MaxSimultaneousNotes, 3);
                    break;
                case DifficultyLevel.Hard:
                    // 困难模式：保持用户设置
                    break;
                case DifficultyLevel.Expert:
                    // 专家模式：完全不限制
                    break;
            }
        }
    }

    /// <summary>
    /// 难度等级枚举
    /// </summary>
    public enum DifficultyLevel
    {
        Easy = 0,    // 简单
        Normal = 1,  // 普通
        Hard = 2,    // 困难
        Expert = 3   // 专家
    }
}
