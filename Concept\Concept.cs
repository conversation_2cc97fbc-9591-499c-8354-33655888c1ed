﻿using Concept;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Concept
{
    public class MoveTrack : ICodedTerm<MoveTrack>
    {
        public int CodeId
        {
            get
            {
                throw new NotImplementedException();
            }
        }

        public string Display
        {
            get
            {
                throw new NotImplementedException();
            }
        }

        public MoveTrack Create()
        {
            throw new NotImplementedException();
        }
    }
    public class FlyTrack : ICodedTerm<MoveTrack>
    {
        public int CodeId
        {
            get
            {
                throw new NotImplementedException();
            }
        }

        public string Display
        {
            get
            {
                throw new NotImplementedException();
            }
        }

        public MoveTrack Create()
        {
            throw new NotImplementedException();
        }
    }
}
