﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote
{
    public class IdolShortNoteInfoModel
    {
        public int Bar { get; set; }
        public int Pos { get; set; }
        public string From_Track { get; set; }
        public string Target_Track { get; set; }
        public string Note_Type { get { return "short"; } }
    }
    public class IdolSlipInfoModel
    {
        public int Bar { get; set; }
        public int Pos { get; set; }
        public string Target_track { get; set; }
        public string End_track { get; set; }
        public string Note_Type { get { return "slip"; } }
    }

    public class IdolLongInfoModel
    {
        public int Bar { get; set; }
        public int Pos { get; set; }
        public string From_Track { get; set; }
        public string Target_Track { get; set; }
        public string Note_Type { get { return "long"; } }
        public int EndBar { get; set; }
        public int EndPos { get; set; }
    }

}
