﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote.Library
{
    public class Score : IScore
    {
        public int Id { get; set; }
        public string SongName { get ; set ; }
        public string Artist { get; set ; }
        public int Mode { get; set ; }
        public int ShowTimeStartBar { get ; set ; }

        public string ModeName
        {
            get
            {
                if (Mode == 1)
                    return "星动";
                else if (Mode == 2)
                    return "弹珠";
                else if (Mode == 3)
                    return "泡泡";
                else
                    return "弦乐";
            }
        }
    }
}
