<UserControl x:Class="MyWPF.Layout.Audio.AudioAnalysisPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 工具栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5" Background="#F5F5F5">
            <Button Name="btnLoadAudio" Content="📁 加载音频" Width="120" Height="35" Margin="5" 
                    Click="BtnLoadAudio_Click"/>
            <Button Name="btnAnalyze" Content="🔍 分析" Width="100" Height="35" Margin="5" 
                    Click="BtnAnalyze_Click" IsEnabled="False"/>
            <Separator Width="10"/>
            <CheckBox Name="chkShowWaveform" Content="波形" VerticalAlignment="Center" Margin="5" 
                      Checked="ChkDisplay_Changed" Unchecked="ChkDisplay_Changed" IsChecked="True"/>
            <CheckBox Name="chkShowEnergy" Content="能量" VerticalAlignment="Center" Margin="5" 
                      Checked="ChkDisplay_Changed" Unchecked="ChkDisplay_Changed" IsChecked="True"/>
            <CheckBox Name="chkShowBeats" Content="节拍" VerticalAlignment="Center" Margin="5" 
                      Checked="ChkDisplay_Changed" Unchecked="ChkDisplay_Changed" IsChecked="True"/>
            <Separator Width="10"/>
            <TextBlock Text="缩放:" VerticalAlignment="Center" Margin="5"/>
            <Slider Name="sliderZoom" Width="100" Minimum="0.1" Maximum="5.0" Value="1.0" 
                    VerticalAlignment="Center" Margin="5" ValueChanged="SliderZoom_ValueChanged"/>
            <TextBlock Name="txtZoomValue" Text="1.0x" VerticalAlignment="Center" Margin="5"/>
        </StackPanel>
        
        <!-- 主显示区域 -->
        <Grid Grid.Row="1" Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 波形显示区域 -->
            <Border Grid.Column="0" BorderBrush="Gray" BorderThickness="1" Background="White">
                <ScrollViewer Name="waveformScrollViewer" HorizontalScrollBarVisibility="Auto" 
                              VerticalScrollBarVisibility="Auto">
                    <Canvas Name="waveformCanvas" Background="White" Height="300" 
                            MouseLeftButtonDown="WaveformCanvas_MouseLeftButtonDown"
                            MouseMove="WaveformCanvas_MouseMove"
                            MouseLeftButtonUp="WaveformCanvas_MouseLeftButtonUp"/>
                </ScrollViewer>
            </Border>
            
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="LightGray"/>
            
            <!-- 分析信息面板 -->
            <Grid Grid.Column="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- 音频信息 -->
                <GroupBox Grid.Row="0" Header="音频信息" Margin="5">
                    <StackPanel>
                        <TextBlock Name="txtFileName" Text="文件: 未加载" Margin="2"/>
                        <TextBlock Name="txtDuration" Text="时长: --:--" Margin="2"/>
                        <TextBlock Name="txtSampleRate" Text="采样率: --" Margin="2"/>
                        <TextBlock Name="txtChannels" Text="声道: --" Margin="2"/>

                        <!-- BPM信息和编辑 -->
                        <StackPanel Margin="2">
                            <TextBlock Name="txtEstimatedBPM" Text="估算BPM: --" FontWeight="Bold"/>
                            <Grid Margin="0,5,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="BPM:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBox Grid.Column="1" Name="txtBPMInput" Width="60" Height="25"
                                        VerticalAlignment="Center" HorizontalAlignment="Left"
                                        Text="120" IsEnabled="False"/>
                                <Button Grid.Column="2" Name="btnApplyBPM" Content="应用" Width="60" Height="25"
                                       Margin="5,0,0,0" Click="BtnApplyBPM_Click" IsEnabled="False"/>
                                <Button Grid.Column="3" Name="btnResetBPM" Content="重置" Width="60" Height="25"
                                       Margin="5,0,0,0" Click="BtnResetBPM_Click" IsEnabled="False"/>
                            </Grid>
                            <TextBlock Name="txtBPMStatus" Text="" Margin="0,2,0,0" FontSize="10"
                                      Foreground="Gray"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
                
                <!-- 分析结果 -->
                <TabControl Grid.Row="1" Margin="5">
                    <TabItem Header="节拍点">
                        <ListBox Name="lstBeats" DisplayMemberPath="TimeDisplay"/>
                    </TabItem>
                    <TabItem Header="能量峰值">
                        <ListBox Name="lstEnergyPeaks" DisplayMemberPath="PeakDisplay"/>
                    </TabItem>
                    <TabItem Header="建议音符">
                        <StackPanel>
                            <Button Name="btnGenerateNotes" Content="生成建议音符" Margin="5" 
                                    Click="BtnGenerateNotes_Click" IsEnabled="False"/>
                            <ListBox Name="lstSuggestedNotes" DisplayMemberPath="NoteDisplay"/>
                        </StackPanel>
                    </TabItem>
                </TabControl>
            </Grid>
        </Grid>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Name="txtStatus" Text="就绪"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <ProgressBar Name="progressBar" Width="200" Height="16" Visibility="Collapsed"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>