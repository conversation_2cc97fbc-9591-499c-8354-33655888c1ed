﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CommonModel
{
    public class MusicXMLInfo
    {
        public string MusicXMLInfoID { get; set; }
        public int Measure { get; set; }//小节数

        public float Divisions { get; set; }
        public int Duration { get; set; }
        public string Step { get; set; }//A-G中的某个音符,r表示休止符
        public int Alter { get; set; }//这个音的升降（-1降调 1 升调）
        public double Beats { get; set; }
        public double BeatType { get; set; }
        public int Octave { get; set; }//这个音所在八度（4标识中央C）

        public int StepNmericalValue//中央C表示48
        {
            get
            {
                if (Step.ToLower() == "c")
                {
                    return Octave * 12 + Alter;
                }

                else if (Step.ToLower() == "d")
                {
                    return Octave * 12 + 1 + Alter;
                }

                else if (Step.ToLower() == "e")
                {
                    return Octave * 12 + 2 + Alter;
                }

                else if (Step.ToLower() == "f")
                {
                    return Octave * 12 + 3 + Alter;
                }

                else if (Step.ToLower() == "g")
                {
                    return Octave * 12 + 4 + Alter;
                }

                else if (Step.ToLower() == "a")
                {
                    return Octave * 12 + 5 + Alter;
                }

                else if (Step.ToLower() == "b")
                {
                    return Octave * 12 + 6 + Alter;
                }
                return 0;

            }
        }

        public string PartName { get; set; }

        public string PartAbbreviationg { get; set; }//乐器名称

        public int MidiProgram { get; set; }
        public double MusicXMLPosition { get; set; }//音符在musicXML的位置
        public double GameBar
        {
            get { return MusicXMLPosition / 64 + 1; }

        }
        public double GamePos
        {
            get { return MusicXMLPosition % 64; }

        }
        /// <summary>
        /// 几分音符
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// 记谱标号
        /// </summary>
        public string ClefSign { get; set; }

    }
}
