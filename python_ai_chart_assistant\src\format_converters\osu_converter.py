"""
osu!格式转换器

支持osu!音游的.osu格式导入导出
"""

from typing import Dict, List, Optional, Any
from pathlib import Path
import logging
import math

from .base_converter import BaseConverter, register_converter, ConversionError
from ..chart_generation.chart_data import ChartData, ChartMetadata, NoteInfo

logger = logging.getLogger(__name__)


@register_converter
class OsuConverter(BaseConverter):
    """osu!格式转换器"""
    
    def __init__(self):
        super().__init__("osu")
        self.supported_extensions = ['.osu']
    
    def export(self, chart_data: ChartData, output_path: str, **kwargs) -> bool:
        """
        导出为osu!格式
        
        Args:
            chart_data: 谱面数据
            output_path: 输出文件路径
            **kwargs: 额外参数
                - approach_rate: 缩圈速度 (默认8)
                - overall_difficulty: 整体难度 (默认7)
                - hp_drain_rate: 掉血速度 (默认6)
                
        Returns:
            bool: 是否成功
        """
        try:
            # 验证数据
            errors = self._validate_chart_data(chart_data)
            if errors:
                raise ConversionError(f"谱面数据验证失败: {'; '.join(errors)}", self.format_name)
            
            # 获取参数
            approach_rate = kwargs.get('approach_rate', 8)
            overall_difficulty = kwargs.get('overall_difficulty', 7)
            hp_drain_rate = kwargs.get('hp_drain_rate', 6)
            
            # 构建osu!文件内容
            osu_content = self._build_osu_content(
                chart_data, approach_rate, overall_difficulty, hp_drain_rate
            )
            
            # 保存文件
            output_path = Path(output_path)
            if output_path.suffix.lower() != '.osu':
                output_path = output_path.with_suffix('.osu')
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(osu_content)
            
            self._log_export_info(chart_data, str(output_path))
            return True
            
        except Exception as e:
            logger.error(f"导出osu!格式失败: {e}")
            return False
    
    def import_chart(self, file_path: str, **kwargs) -> Optional[ChartData]:
        """
        从osu!格式文件导入谱面
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
            
        Returns:
            Optional[ChartData]: 导入的谱面数据
        """
        try:
            if not self.validate_file_extension(file_path):
                raise ConversionError(f"不支持的文件扩展名", self.format_name, file_path)
            
            # 解析osu!文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            chart_data = self._parse_osu_content(content)
            
            self._log_import_info(chart_data, file_path)
            return chart_data
            
        except Exception as e:
            logger.error(f"导入osu!格式失败: {e}")
            return None
    
    def _build_osu_content(
        self, 
        chart_data: ChartData, 
        approach_rate: int, 
        overall_difficulty: int, 
        hp_drain_rate: int
    ) -> str:
        """
        构建osu!文件内容
        
        Args:
            chart_data: 谱面数据
            approach_rate: 缩圈速度
            overall_difficulty: 整体难度
            hp_drain_rate: 掉血速度
            
        Returns:
            str: osu!文件内容
        """
        lines = []
        
        # 文件头
        lines.append("osu file format v14")
        lines.append("")
        
        # [General] 部分
        lines.append("[General]")
        lines.append(f"AudioFilename: {chart_data.metadata.title}.mp3")
        lines.append("AudioLeadIn: 0")
        lines.append("PreviewTime: -1")
        lines.append("Countdown: 0")
        lines.append("SampleSet: Normal")
        lines.append("StackLeniency: 0.7")
        lines.append("Mode: 3")  # osu!mania模式
        lines.append("LetterboxInBreaks: 0")
        lines.append("SpecialStyle: 0")
        lines.append("WidescreenStoryboard: 0")
        lines.append("")
        
        # [Editor] 部分
        lines.append("[Editor]")
        lines.append("Bookmarks: ")
        lines.append("DistanceSpacing: 1")
        lines.append("BeatDivisor: 4")
        lines.append("GridSize: 4")
        lines.append("TimelineZoom: 1")
        lines.append("")
        
        # [Metadata] 部分
        lines.append("[Metadata]")
        lines.append(f"Title:{chart_data.metadata.title}")
        lines.append(f"TitleUnicode:{chart_data.metadata.title}")
        lines.append(f"Artist:{chart_data.metadata.artist}")
        lines.append(f"ArtistUnicode:{chart_data.metadata.artist}")
        lines.append(f"Creator:{chart_data.metadata.creator}")
        lines.append(f"Version:{chart_data.metadata.difficulty}K")
        lines.append("Source:")
        lines.append("Tags:")
        lines.append("BeatmapID:0")
        lines.append("BeatmapSetID:-1")
        lines.append("")
        
        # [Difficulty] 部分
        lines.append("[Difficulty]")
        lines.append(f"HPDrainRate:{hp_drain_rate}")
        lines.append(f"CircleSize:{chart_data.metadata.track_count}")  # 键位数
        lines.append(f"OverallDifficulty:{overall_difficulty}")
        lines.append(f"ApproachRate:{approach_rate}")
        lines.append("SliderMultiplier:1.4")
        lines.append("SliderTickRate:1")
        lines.append("")
        
        # [Events] 部分
        lines.append("[Events]")
        lines.append("//Background and Video events")
        lines.append("//Break Periods")
        lines.append("//Storyboard Layer 0 (Background)")
        lines.append("//Storyboard Layer 1 (Fail)")
        lines.append("//Storyboard Layer 2 (Pass)")
        lines.append("//Storyboard Layer 3 (Foreground)")
        lines.append("//Storyboard Sound Samples")
        lines.append("")
        
        # [TimingPoints] 部分
        lines.append("[TimingPoints]")
        # 添加基本时间点
        beat_length = 60000 / chart_data.metadata.bpm  # 毫秒
        lines.append(f"0,{beat_length:.2f},4,2,0,100,1,0")
        lines.append("")
        
        # [HitObjects] 部分
        lines.append("[HitObjects]")
        
        # 转换音符
        track_width = 512 // chart_data.metadata.track_count
        for note in chart_data.get_all_notes():
            x = note.track * track_width + track_width // 2
            y = 192  # 固定y坐标
            time = int(note.time * 1000)  # 转换为毫秒
            
            if note.note_type == 1:
                # 短音符 (Circle)
                hit_object = f"{x},{y},{time},1,0,0:0:0:0:"
            elif note.note_type == 2:
                # 长音符 (Hold)
                end_time = int((note.time + note.duration) * 1000)
                hit_object = f"{x},{y},{time},128,0,{end_time}:0:0:0:0:"
            else:
                continue
            
            lines.append(hit_object)
        
        return '\n'.join(lines)
    
    def _parse_osu_content(self, content: str) -> ChartData:
        """
        解析osu!文件内容
        
        Args:
            content: 文件内容
            
        Returns:
            ChartData: 解析后的谱面数据
        """
        lines = content.split('\n')
        
        # 初始化变量
        metadata = ChartMetadata()
        current_section = None
        timing_points = []
        hit_objects = []
        
        # 解析各个部分
        for line in lines:
            line = line.strip()
            
            if not line or line.startswith('//'):
                continue
            
            if line.startswith('[') and line.endswith(']'):
                current_section = line[1:-1]
                continue
            
            if current_section == 'Metadata':
                self._parse_metadata_line(line, metadata)
            elif current_section == 'Difficulty':
                self._parse_difficulty_line(line, metadata)
            elif current_section == 'TimingPoints':
                timing_points.append(line)
            elif current_section == 'HitObjects':
                hit_objects.append(line)
        
        # 创建谱面数据
        chart_data = ChartData(metadata)
        
        # 解析时间点获取BPM
        if timing_points:
            first_timing = timing_points[0].split(',')
            if len(first_timing) >= 2:
                beat_length = float(first_timing[1])
                if beat_length > 0:
                    metadata.bpm = 60000 / beat_length
        
        # 解析音符
        for hit_object_line in hit_objects:
            note = self._parse_hit_object(hit_object_line, metadata)
            if note:
                chart_data.add_note(note)
        
        return chart_data
    
    def _parse_metadata_line(self, line: str, metadata: ChartMetadata):
        """解析元数据行"""
        if ':' in line:
            key, value = line.split(':', 1)
            if key == 'Title':
                metadata.title = value
            elif key == 'Artist':
                metadata.artist = value
            elif key == 'Creator':
                metadata.creator = value
    
    def _parse_difficulty_line(self, line: str, metadata: ChartMetadata):
        """解析难度行"""
        if ':' in line:
            key, value = line.split(':', 1)
            if key == 'CircleSize':
                metadata.track_count = int(float(value))
            elif key == 'OverallDifficulty':
                metadata.difficulty = int(float(value))
    
    def _parse_hit_object(self, line: str, metadata: ChartMetadata) -> Optional[NoteInfo]:
        """解析音符对象"""
        try:
            parts = line.split(',')
            if len(parts) < 4:
                return None
            
            x = int(parts[0])
            time = int(parts[2]) / 1000.0  # 转换为秒
            hit_type = int(parts[3])
            
            # 计算轨道
            track_width = 512 // metadata.track_count
            track = min(x // track_width, metadata.track_count - 1)
            
            if hit_type & 1:  # Circle
                return NoteInfo(time=time, track=track, note_type=1, duration=0.0)
            elif hit_type & 128:  # Hold
                if len(parts) >= 6:
                    end_time = int(parts[5].split(':')[0]) / 1000.0
                    duration = max(0.1, end_time - time)
                    return NoteInfo(time=time, track=track, note_type=2, duration=duration)
            
            return None
            
        except (ValueError, IndexError) as e:
            logger.warning(f"解析音符失败: {e}, 行: {line}")
            return None
    
    def get_format_info(self) -> Dict[str, Any]:
        """获取osu!格式信息"""
        info = super().get_format_info()
        info.update({
            'description': 'osu!mania音游格式 (.osu)',
            'features': [
                '支持多键位模式',
                '支持短音符和长音符',
                '支持时间点和BPM变化',
                '完整的元数据支持'
            ],
            'limitations': [
                '主要支持mania模式',
                '不支持复杂的故事板',
                '音频文件需要单独提供'
            ],
            'compatibility': {
                'osu_client': True,
                'mania_mode': True,
                'file_format': 'v14'
            }
        })
        return info
