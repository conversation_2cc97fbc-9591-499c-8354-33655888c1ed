﻿using RecordMusicalNote.DataModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote.Library
{
    public class Pinball : IPinball
    {
        public Pinball()
        {
            _model.PinBallNoteInfoID = -1;
        }
        PinBallNoteInfo _model=new PinBallNoteInfo();
        public Pinball(PinBallNoteInfo model)
        {
            _model = model;
        }
        public int PinBallNoteInfoID
        {
            get
            {
                return _model.PinBallNoteInfoID;
            }
        }
        public int LevelInfoId
        {
            get
            {
                return _model.LevelInfoId.Value;
            }

            set
            {
                _model.LevelInfoId = value;
            }
        }

        public int Bar
        {
            get
            {
                return _model.bar.Value;
            }

            set
            {
                _model.bar = value;
            }
        }

        public string EndArea
        {
            get
            {
                return _model.EndArea;
            }

            set
            {
                _model.EndArea = value;
            }
        }

        public int EndBar
        {
            get
            {
                return _model.EndBar.Value;
            }

            set
            {
                _model.EndBar = value;
            }
        }

        public int EndPos
        {
            get
            {
                return _model.EndPos.Value;
            }

            set
            {
                _model.EndPos = value;
            }
        }

    
        public int MoveTime
        {
            get
            {
                return _model.MoveTime.Value;
            }

            set
            {
                _model.MoveTime = value;
            }
        }

        public string NoteType
        {
            get
            {
                return _model.NoteType;
            }

            set
            {
                _model.NoteType = value;
            }
        }

      
        public int Pos
        {
            get
            {
                return _model.pos.Value;
            }

            set
            {
                _model.pos = value;
            }
        }
        private int _pinballId;
        public int PinballID
        {
            get
            {
                return _pinballId;
            }

            set
            {
                _pinballId = value;
            }
        }

        private string _sonId;
        public string SonId
        {
            get
            {
                return _sonId;
            }

            set
            {
                _sonId = value;
            }
        }
        public override string ToString()
        {
            string str = "";
            if (this.NoteType == "PinballLong")
            {
                str= "<Note ID=\"" + PinballID + "\"  " + "Bar=\"" + Bar + "\"  " + "Pos=\"" + Pos + "\"  " + "EndArea=\"" + EndArea + "\"  " +
                      "Son=\"" + SonId + "\"  " + "note_type=\"" + NoteType + "\"  "
                      + "EndBar=\"" + EndBar + "\"  " + "EndPos=\"" + EndPos + "\"  " + "MoveTime=\"" + MoveTime + "\"  " + "/>";
                return str;
            }
            else if (this.NoteType == "PinballSingle")
            {
                str = "<Note ID=\"" + PinballID + "\"  " + "Bar=\"" + Bar + "\"  "  + "Pos=\"" + Pos + "\"  " + "EndArea=\"" + EndArea + "\"  " +
                    "Son=\"" + SonId + "\"  "  + "note_type=\"" + NoteType + "\"  " + "MoveTime=\"" + MoveTime + "\"  " + "/>";
                return str;
            }
            else if (this.NoteType == "PinballSlip")
            {
                str = "<Note ID=\"" + PinballID + "\"  " + "Bar=\"" + Bar + "\"  " + "Pos=\"" + Pos + "\"  " +
                    "EndArea=\"" + EndArea + "\"  " + "Son=\"" + SonId + "\"  "
                    + "note_type=\"" + NoteType + "\"  " + "MoveTime=\"" + MoveTime + "\"  " + "/>";
                return str;
            }

            else if (NoteType== "PinballSeries")
            {
                str = "<Note ID=\"" + PinballID + "\"  " + "Bar=\"" + Bar + "\"  " + "\"  " + "Pos=\"" + Pos + "\"  " + "EndArea=\"" + EndArea + "\"  " +
                "Son=\"" + SonId + "\"  "+ "note_type=\"" + NoteType + "\"  " + "MoveTime=\"" + MoveTime + "\"  " + "/>";
                return str;
            }
            return "";
        }
    }
}
