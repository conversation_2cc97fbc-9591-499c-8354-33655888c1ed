﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 14
VisualStudioVersion = 14.0.25420.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RecordMusicalNote", "RecordMusicalNote\RecordMusicalNote.csproj", "{7A2C3CF8-3691-4888-97FB-493BCD84F124}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RecordMusicalNote.Library", "RecordMusicalNote.Library\RecordMusicalNote.Library.csproj", "{C3FF4408-7B95-4D21-BBD8-91612362CD8C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RecordMusicalNote.DataModel", "RecordMusicalNote.DataModel\RecordMusicalNote.DataModel.csproj", "{97E616EA-7301-4498-8E26-4981E4312A9B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Concept", "Concept\Concept.csproj", "{0CE41AB4-C6D8-4FC6-9756-20C147EA6CFA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RecordMusicalNote.Layout", "RecordMusicalNote.Layout\RecordMusicalNote.Layout.csproj", "{B217AE65-CB85-496A-8650-D988C82A5E2F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MidiSheetMusic", "MidiSheetMusic\MidiSheetMusic.csproj", "{0B5CBA19-8987-4EBE-8D7F-4683FCAA5A2F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CommonModel", "CommonModel\CommonModel.csproj", "{1B1CC2B6-C6AC-40DB-9D24-C89C89CF024C}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7A2C3CF8-3691-4888-97FB-493BCD84F124}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A2C3CF8-3691-4888-97FB-493BCD84F124}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A2C3CF8-3691-4888-97FB-493BCD84F124}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A2C3CF8-3691-4888-97FB-493BCD84F124}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3FF4408-7B95-4D21-BBD8-91612362CD8C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3FF4408-7B95-4D21-BBD8-91612362CD8C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3FF4408-7B95-4D21-BBD8-91612362CD8C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3FF4408-7B95-4D21-BBD8-91612362CD8C}.Release|Any CPU.Build.0 = Release|Any CPU
		{97E616EA-7301-4498-8E26-4981E4312A9B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{97E616EA-7301-4498-8E26-4981E4312A9B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{97E616EA-7301-4498-8E26-4981E4312A9B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{97E616EA-7301-4498-8E26-4981E4312A9B}.Release|Any CPU.Build.0 = Release|Any CPU
		{0CE41AB4-C6D8-4FC6-9756-20C147EA6CFA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0CE41AB4-C6D8-4FC6-9756-20C147EA6CFA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0CE41AB4-C6D8-4FC6-9756-20C147EA6CFA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0CE41AB4-C6D8-4FC6-9756-20C147EA6CFA}.Release|Any CPU.Build.0 = Release|Any CPU
		{B217AE65-CB85-496A-8650-D988C82A5E2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B217AE65-CB85-496A-8650-D988C82A5E2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B217AE65-CB85-496A-8650-D988C82A5E2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B217AE65-CB85-496A-8650-D988C82A5E2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B5CBA19-8987-4EBE-8D7F-4683FCAA5A2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B5CBA19-8987-4EBE-8D7F-4683FCAA5A2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B5CBA19-8987-4EBE-8D7F-4683FCAA5A2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B5CBA19-8987-4EBE-8D7F-4683FCAA5A2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{1B1CC2B6-C6AC-40DB-9D24-C89C89CF024C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1B1CC2B6-C6AC-40DB-9D24-C89C89CF024C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1B1CC2B6-C6AC-40DB-9D24-C89C89CF024C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1B1CC2B6-C6AC-40DB-9D24-C89C89CF024C}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
