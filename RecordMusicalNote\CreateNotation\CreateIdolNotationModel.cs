﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote.CreateNotation
{
    public static class CreateIdolNotationModel
    {
        private static string CreateShortNote(IdolShortNoteInfoModel model)
        {
            string str = "<Note Bar =\"{0}\" Pos=\"{1}\" from_track=\"{2}\" target_track=\"{3}\" note_type=\"short\"  />";
            str = string.Format(str, model.Bar, model.Pos, model.From_Track, model.Target_Track);
            
            return str;
        }

        private static string CreateLongNote(IdolLongInfoModel model)
        {
            string str = "<Note Bar =\"{0}\" Pos=\"{1}\" from_track=\"{2}\" target_track=\"{3}\" note_type=\"long\" EndBar=\"{4}\" EndPos=\"{5}\" />";
            str = string.Format(str, model.Bar, model.Pos, model.From_Track, model.Target_Track, model.EndBar, model.EndPos);
            return str;
        }
        private static string CreateSlipNote(IdolSlipInfoModel model)
        {
            string str = "<Note Bar =\"{0}\" Pos=\"{1}\"  target_track=\"{2}\" end_track=\"{3}\"  note_type=\"slip\" />";
            str = string.Format(str, model.Bar, model.Pos, model.Target_track, model.End_track);
            return str;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="trackCount">轨数</param>
        /// <param name="startPos">开始的Pos</param>
        /// <param name="totalPos">总共的Pos数目</param>
        /// <returns></returns>
        public static string CreateRadomShortModel(int trackCount, int startPos, int totalPos, bool isMultipleFinger)
        {
            string[] fourTracks = { "Left2", "Left1", "Right1", "Right2" };
            string[] fiveTracks = { "Left2", "Left1", "Middle", "Right1", "Right2" };
            string allStr = "";
            IList<string> UsedTracks = new List<string>();
            Random trackNameRadom = new Random();
            Random getTrackCountRadom = new Random();
            Random randomI = new Random();
            int lastNotetypePos = 0;
            int[] randomIs = { 0, 1, 2, 4, 6, 8, 9 };
            for (int i = startPos; i < totalPos; i++)
            {

                int a = randomI.Next(0, 7);
                if (i - lastNotetypePos > 3)
                {

                    if (lastNotetypePos>0&&i - lastNotetypePos != 7 && randomIs[a] % 2 == 0)//基数
                        continue;
                    int r1 = 0;
                    string selectedTrackName = "";
                    if (!isMultipleFinger)
                    {
                        r1 = getTrackCountRadom.Next(1, 3);


                    }
                    else
                    {
                        r1 = getTrackCountRadom.Next(1, 6);
                    }

                    for (int i1 = 0; i1 < r1; i1++)
                    {
                        if (trackCount == 4)
                            selectedTrackName = fourTracks[trackNameRadom.Next(0, 4)];
                        else
                            selectedTrackName = fiveTracks[trackNameRadom.Next(0, 5)];
                        if (UsedTracks.Contains(selectedTrackName))
                        {
                            i--;
                            break;
                        }
                        UsedTracks.Add(selectedTrackName);
                        IdolShortNoteInfoModel model = new IdolShortNoteInfoModel();
                        model.Bar = i / 32 + 1;
                        model.Pos = i % 32 * 2;
                        model.From_Track = selectedTrackName;
                        model.Target_Track = selectedTrackName;
                        allStr += "\r\n" +CreateShortNote(model) ;
                        lastNotetypePos = i;
                    }

                    UsedTracks.Clear();
                }
            }
            return allStr;

        }



    }
}
