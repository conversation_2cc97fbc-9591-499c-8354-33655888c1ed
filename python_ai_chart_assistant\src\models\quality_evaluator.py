"""
质量评估器

评估生成谱面的质量
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class QualityEvaluator(nn.Module):
    """谱面质量评估模型"""
    
    def __init__(
        self,
        input_dim: int = 32,
        hidden_dim: int = 64,
        num_layers: int = 2,
        dropout: float = 0.3
    ):
        """
        初始化质量评估器
        
        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            num_layers: 网络层数
            dropout: Dropout比例
        """
        super(QualityEvaluator, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # 特征提取网络
        layers = []
        current_dim = input_dim
        
        for i in range(num_layers):
            layers.extend([
                nn.Linear(current_dim, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(dropout)
            ])
            current_dim = hidden_dim
            hidden_dim = max(hidden_dim // 2, 16)
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # 质量评分头
        self.quality_head = nn.Sequential(
            nn.Linear(current_dim, 32),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(32, 1),
            nn.Sigmoid()  # 输出0-1之间的质量分数
        )
        
        # 游戏性评分头
        self.playability_head = nn.Sequential(
            nn.Linear(current_dim, 32),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
        # 音乐匹配度评分头
        self.musicality_head = nn.Sequential(
            nn.Linear(current_dim, 32),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
    
    def forward(self, features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            features: 输入特征 [batch_size, input_dim]
            
        Returns:
            Dict: 评估结果
        """
        # 特征提取
        extracted_features = self.feature_extractor(features)
        
        # 各项评分
        quality_score = self.quality_head(extracted_features)
        playability_score = self.playability_head(extracted_features)
        musicality_score = self.musicality_head(extracted_features)
        
        return {
            'quality_score': quality_score,
            'playability_score': playability_score,
            'musicality_score': musicality_score,
            'overall_score': (quality_score + playability_score + musicality_score) / 3
        }
    
    def evaluate_chart(self, chart_features: np.ndarray) -> Dict[str, float]:
        """
        评估单个谱面
        
        Args:
            chart_features: 谱面特征
            
        Returns:
            Dict: 评估结果
        """
        self.eval()
        
        with torch.no_grad():
            if isinstance(chart_features, np.ndarray):
                chart_features = torch.tensor(chart_features, dtype=torch.float32)
            
            if chart_features.dim() == 1:
                chart_features = chart_features.unsqueeze(0)
            
            outputs = self.forward(chart_features)
            
            return {
                'quality_score': outputs['quality_score'].item(),
                'playability_score': outputs['playability_score'].item(),
                'musicality_score': outputs['musicality_score'].item(),
                'overall_score': outputs['overall_score'].item()
            }


class ChartQualityAnalyzer:
    """谱面质量分析器（基于规则）"""
    
    def __init__(self):
        self.weights = {
            'density_consistency': 0.2,
            'track_balance': 0.2,
            'rhythm_matching': 0.3,
            'playability': 0.3
        }
    
    def analyze_chart_quality(self, chart_data, audio_features: Optional[Dict] = None) -> Dict:
        """
        分析谱面质量
        
        Args:
            chart_data: 谱面数据
            audio_features: 音频特征
            
        Returns:
            Dict: 质量分析结果
        """
        analysis = {}
        
        # 1. 密度一致性分析
        analysis['density_consistency'] = self._analyze_density_consistency(chart_data)
        
        # 2. 轨道平衡分析
        analysis['track_balance'] = self._analyze_track_balance(chart_data)
        
        # 3. 节奏匹配度分析
        analysis['rhythm_matching'] = self._analyze_rhythm_matching(chart_data, audio_features)
        
        # 4. 游戏性分析
        analysis['playability'] = self._analyze_playability(chart_data)
        
        # 5. 计算综合评分
        analysis['overall_score'] = self._calculate_overall_score(analysis)
        
        return analysis
    
    def _analyze_density_consistency(self, chart_data) -> Dict:
        """分析密度一致性"""
        notes = chart_data.get_all_notes()
        if len(notes) < 10:
            return {'score': 0.5, 'reason': '音符数量太少'}
        
        # 计算时间窗口内的密度变化
        window_size = 2.0  # 2秒窗口
        duration = chart_data.metadata.duration
        
        densities = []
        current_time = 0.0
        
        while current_time < duration:
            window_notes = [
                note for note in notes
                if current_time <= note.time < current_time + window_size
            ]
            density = len(window_notes) / window_size
            densities.append(density)
            current_time += window_size / 2  # 50%重叠
        
        if not densities:
            return {'score': 0.5, 'reason': '无法计算密度'}
        
        # 计算密度变化的标准差
        density_std = np.std(densities)
        density_mean = np.mean(densities)
        
        # 变异系数（标准差/均值）
        cv = density_std / (density_mean + 1e-6)
        
        # 一致性评分（变异系数越小越好）
        consistency_score = max(0, 1 - cv)
        
        return {
            'score': consistency_score,
            'density_mean': density_mean,
            'density_std': density_std,
            'coefficient_of_variation': cv
        }
    
    def _analyze_track_balance(self, chart_data) -> Dict:
        """分析轨道平衡"""
        notes = chart_data.get_all_notes()
        if not notes:
            return {'score': 0.0, 'reason': '没有音符'}
        
        # 统计各轨道音符数量
        track_counts = {}
        for note in notes:
            track_counts[note.track] = track_counts.get(note.track, 0) + 1
        
        if len(track_counts) < 2:
            return {'score': 0.5, 'reason': '轨道数量太少'}
        
        counts = list(track_counts.values())
        mean_count = np.mean(counts)
        std_count = np.std(counts)
        
        # 平衡度评分
        if mean_count == 0:
            balance_score = 0.0
        else:
            cv = std_count / mean_count
            balance_score = max(0, 1 - cv)
        
        return {
            'score': balance_score,
            'track_counts': track_counts,
            'mean_count': mean_count,
            'std_count': std_count
        }
    
    def _analyze_rhythm_matching(self, chart_data, audio_features: Optional[Dict]) -> Dict:
        """分析节奏匹配度"""
        notes = chart_data.get_all_notes()
        if not notes:
            return {'score': 0.0, 'reason': '没有音符'}
        
        # 如果没有音频特征，使用简单的节奏规律性分析
        if not audio_features:
            return self._analyze_rhythm_regularity(notes)
        
        # 如果有音频特征，分析与音频的匹配度
        return self._analyze_audio_matching(notes, audio_features)
    
    def _analyze_rhythm_regularity(self, notes: List) -> Dict:
        """分析节奏规律性"""
        if len(notes) < 3:
            return {'score': 0.5, 'reason': '音符太少'}
        
        # 计算音符间隔
        intervals = []
        sorted_notes = sorted(notes, key=lambda x: x.time)
        
        for i in range(1, len(sorted_notes)):
            interval = sorted_notes[i].time - sorted_notes[i-1].time
            intervals.append(interval)
        
        if not intervals:
            return {'score': 0.5, 'reason': '无法计算间隔'}
        
        # 分析间隔的规律性
        interval_std = np.std(intervals)
        interval_mean = np.mean(intervals)
        
        # 规律性评分
        if interval_mean == 0:
            regularity_score = 0.0
        else:
            cv = interval_std / interval_mean
            regularity_score = max(0, 1 - cv)
        
        return {
            'score': regularity_score,
            'interval_mean': interval_mean,
            'interval_std': interval_std,
            'regularity': regularity_score
        }
    
    def _analyze_audio_matching(self, notes: List, audio_features: Dict) -> Dict:
        """分析与音频的匹配度"""
        # 这里可以实现更复杂的音频匹配分析
        # 暂时返回基础的节奏规律性分析
        return self._analyze_rhythm_regularity(notes)
    
    def _analyze_playability(self, chart_data) -> Dict:
        """分析游戏性"""
        notes = chart_data.get_all_notes()
        if not notes:
            return {'score': 0.0, 'reason': '没有音符'}
        
        scores = []
        
        # 1. 音符密度适中性
        duration = chart_data.metadata.duration
        if duration > 0:
            density = len(notes) / duration
            # 理想密度范围：0.5-3.0 音符/秒
            if 0.5 <= density <= 3.0:
                density_score = 1.0
            elif density < 0.5:
                density_score = density / 0.5
            else:
                density_score = max(0, 1 - (density - 3.0) / 3.0)
            scores.append(density_score)
        
        # 2. 避免过于复杂的模式
        complexity_penalty = 0
        
        # 检查连续长音符
        long_note_streak = 0
        max_long_streak = 0
        
        for note in sorted(notes, key=lambda x: x.time):
            if note.note_type == 2:  # 长音符
                long_note_streak += 1
                max_long_streak = max(max_long_streak, long_note_streak)
            else:
                long_note_streak = 0
        
        if max_long_streak > 5:
            complexity_penalty += 0.2
        
        # 检查同时音符数量
        time_groups = {}
        for note in notes:
            time_key = round(note.time, 1)  # 100ms精度
            if time_key not in time_groups:
                time_groups[time_key] = []
            time_groups[time_key].append(note)
        
        max_simultaneous = max(len(group) for group in time_groups.values()) if time_groups else 0
        if max_simultaneous > chart_data.metadata.track_count:
            complexity_penalty += 0.3
        
        complexity_score = max(0, 1 - complexity_penalty)
        scores.append(complexity_score)
        
        # 3. 音符类型分布
        note_types = [note.note_type for note in notes]
        type_counts = {1: 0, 2: 0}  # 短音符、长音符
        
        for note_type in note_types:
            if note_type in type_counts:
                type_counts[note_type] += 1
        
        total_notes = sum(type_counts.values())
        if total_notes > 0:
            short_ratio = type_counts[1] / total_notes
            long_ratio = type_counts[2] / total_notes
            
            # 理想比例：70-90%短音符，10-30%长音符
            if 0.7 <= short_ratio <= 0.9 and 0.1 <= long_ratio <= 0.3:
                distribution_score = 1.0
            else:
                distribution_score = 0.7  # 部分分数
            
            scores.append(distribution_score)
        
        overall_playability = np.mean(scores) if scores else 0.0
        
        return {
            'score': overall_playability,
            'density_score': scores[0] if len(scores) > 0 else 0,
            'complexity_score': scores[1] if len(scores) > 1 else 0,
            'distribution_score': scores[2] if len(scores) > 2 else 0,
            'max_simultaneous_notes': max_simultaneous,
            'max_long_note_streak': max_long_streak
        }
    
    def _calculate_overall_score(self, analysis: Dict) -> float:
        """计算综合评分"""
        total_score = 0.0
        total_weight = 0.0
        
        for component, weight in self.weights.items():
            if component in analysis and 'score' in analysis[component]:
                total_score += analysis[component]['score'] * weight
                total_weight += weight
        
        if total_weight == 0:
            return 0.0
        
        return total_score / total_weight
    
    def get_quality_feedback(self, analysis: Dict) -> List[str]:
        """获取质量反馈建议"""
        feedback = []
        
        # 密度一致性反馈
        if 'density_consistency' in analysis:
            score = analysis['density_consistency']['score']
            if score < 0.6:
                feedback.append("音符密度变化过大，建议平滑密度分布")
        
        # 轨道平衡反馈
        if 'track_balance' in analysis:
            score = analysis['track_balance']['score']
            if score < 0.7:
                feedback.append("轨道音符分布不均衡，建议调整各轨道音符数量")
        
        # 节奏匹配反馈
        if 'rhythm_matching' in analysis:
            score = analysis['rhythm_matching']['score']
            if score < 0.6:
                feedback.append("节奏模式不够规律，建议优化音符时间安排")
        
        # 游戏性反馈
        if 'playability' in analysis:
            playability = analysis['playability']
            if 'max_simultaneous_notes' in playability and playability['max_simultaneous_notes'] > 4:
                feedback.append("同时音符数量过多，可能影响游戏体验")
            
            if 'max_long_note_streak' in playability and playability['max_long_note_streak'] > 5:
                feedback.append("连续长音符过多，建议增加短音符变化")
        
        if not feedback:
            feedback.append("谱面质量良好，无明显问题")
        
        return feedback
