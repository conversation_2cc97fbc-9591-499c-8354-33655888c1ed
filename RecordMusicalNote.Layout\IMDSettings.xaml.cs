﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace MyWPF.Layout
{
    /// <summary>
    /// IMDSettings.xaml 的交互逻辑
    /// </summary>
    public partial class IMDSettings : Window
    {
        public IMDSettings()
        {
            InitializeComponent();
            btnConfrim.Click -= BtnConfrim_Click;
            btnConfrim.Click += BtnConfrim_Click;
            btnCancel.Click -= BtnCancel_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            txtBpm.Text = "";
        }

        private void BtnConfrim_Click(object sender, RoutedEventArgs e)
        {
            float.TryParse(txtBpm.Text,out float bpm);
            BPM = bpm;
            int.TryParse(txtKey.Text, out int key);
            Key = key;
            if (BPM == 0|| Key==0)
            {
                MessageBox.Show("BPM或者key不能为空!");
                return;
            };
            Close();
        }

        public float BPM { get; set; }

        public int Key { get; set; }
    }
}
