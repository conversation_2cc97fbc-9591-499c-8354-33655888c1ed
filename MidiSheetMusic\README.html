<html>
<head><title>Midi Sheet Music Viewer </title></head>
<body>
<font face="arial,helvetica" size="2">

<font size="4"><b>Midi Sheet Music</b></font>
<p>
Version 1.0 - May 12, 2008
<p>
The Midi Sheet Music program displays sheet music from
MIDI music files.  A sample screenshot is shown below:
<p>
<img src="sheetmusic.png" alt="screenshot"></img>
<p>
<b>1. System Requirements </b>
<p>
Microsoft Windows XP or Windows Vista <br>
Microsoft .NET Framework 2.0 or later<br>
2 MB hard drive space<br>
<p>
To determine if you have the Microsoft .NET Framework, check if
the following directory exists:<br>
<font face="courier new">
C:\WINDOWS\Microsoft.NET\Framework\&lt;version&gt;
</font>
<br>
The version should be 2.0 or greater.  If you do not have
the .NET Framework installed, you can download it for free at<br>
<a href="http://msdn.microsoft.com/netframework/downloads/updates/default.aspx">
http://msdn.microsoft.com/netframework/downloads/updates/default.aspx
</a>
<p>
<b>2. Installation </b>
<p>
No installation is required.  Simply download the MidiSheetMusic-1.0.exe
executable, and directly run it.

<p>
<b>3. Features</b>
<p>
The Midi Sheet Music program has the following features:
<ul>
<li> Displaying Sheet Music for MIDI music files
<li> Saving the Sheet Music as PNG images
<li> Print and Print Preview support
<li> Adjusting the zoom level from 60% to 160%
<li> Selecting which Midi Tracks to display
<li> Combining Midi Tracks into two staffs (left hand and right hand)
     for piano songs.
</ul>
<p>

<b>4. Building from source</b>
<p>
The Midi Sheet Music program is open source software, released under
the GNU General Public License version 2. 
<p>
To load the MidiSheetMusic project into Visual Studio or Visual C#
Express, simply select the "Open Project" menu, and open the
MidiSheetMusic.csproj file.
<p>
If you don't have Visual Studio or Visual C# Express, you can still
build the program from the source by using the command line csc.exe
compiler.  To build from the command line, do the following:
<p>
Unzip the MidiSheetMusic-src.zip zipfile. <br>
Edit the make.bat file in Notepad. <br>
Set the PATH variable to the directory containing the C# compiler (csc.exe).<br>
The directory should have a path similar to<br>
<font face="courier new">
C:\WINDOWS\Microsoft.NET\Framework\&lt;version&gt;
</font>
<br>
Open a command prompt, and run the make.bat script<br>.
The MidiSheetMusic.exe executable will be created.
<p>
<b>5. Running the Unit Tests</b>
<p>
The Midi Sheet Music source comes with unit tests, in the file
UnitTest.cs.  To compile and run the unit tests, you must first 
download and install NUnit from http://www.nunit.org/
<p>
Then, open a command prompt and run the script
<font face="courier">
maketest.bat
</font>
This will produce a test file UnitTest.dll.  Run the NUnit GUI executable,
select "Open Project" from the menu, and select the UnitTest.dll.  Once
the UnitTest is loaded, simply click on the "Run" button to run the
unit tests.
<p>
<b>6. Author and License </b>
<p>
The Midi Sheet Music program was written by Madhav Vaidyanathan.
It is released under the GNU General Public License Version 2.

</body></html>

