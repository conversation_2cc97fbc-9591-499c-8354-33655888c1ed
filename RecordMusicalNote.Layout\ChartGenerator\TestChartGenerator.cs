using System;
using System.IO;
using System.Linq;

namespace MyWPF.Layout.ChartGenerator
{
    /// <summary>
    /// MIDI谱面生成器测试类
    /// </summary>
    public class TestChartGenerator
    {
        /// <summary>
        /// 测试星动模式谱面生成
        /// </summary>
        public static void TestIdolGeneration()
        {
            try
            {
                Console.WriteLine("开始测试星动模式谱面生成...");
                
                var generator = new MusicChartGenerator();
                
                // 假设有一个测试MIDI文件
                string testMidiPath = @"test.mid";
                
                // 如果测试文件不存在，创建一个简单的测试
                if (!File.Exists(testMidiPath))
                {
                    Console.WriteLine("测试MIDI文件不存在，跳过实际生成测试");
                    TestTrackNameMapping();
                    return;
                }
                
                // 生成星动模式谱面
                var levelInfo = generator.GenerateChart(testMidiPath, 1, 5, 4, true);
                
                Console.WriteLine($"生成成功！");
                Console.WriteLine($"BPM: {levelInfo.BPM}");
                Console.WriteLine($"小节数: {levelInfo.BarAmount}");
                Console.WriteLine($"轨道数: {levelInfo.TrackCount}");
                
                var idolNotes = generator.GetIdolNotes();
                Console.WriteLine($"音符数量: {idolNotes.Count}");
                
                // 检查轨道名称
                var trackNames = idolNotes.Select(n => n.FromTrack).Distinct().ToList();
                Console.WriteLine($"使用的轨道: {string.Join(", ", trackNames)}");
                
                // 检查音符类型
                var noteTypes = idolNotes.Select(n => n.NoteType).Distinct().ToList();
                Console.WriteLine($"音符类型: {string.Join(", ", noteTypes)}");
                
                // 导出XML测试
                string outputPath = "test_output.xml";
                generator.ExportIdolChartToXml(outputPath, "测试歌曲", "测试艺术家");
                Console.WriteLine($"XML导出成功: {outputPath}");
                
                Console.WriteLine("星动模式测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试轨道名称映射
        /// </summary>
        public static void TestTrackNameMapping()
        {
            Console.WriteLine("测试轨道名称映射...");
            
            var generator = new MusicChartGenerator();
            
            // 使用反射测试私有方法（仅用于测试）
            var method = typeof(MusicChartGenerator).GetMethod("AssignTrackName", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (method != null)
            {
                // 创建模拟MIDI音符
                var mockNote = new MidiSheetMusic.MidiNote
                {
                    Number = 60, // C4
                    StartTime = 0,
                    EndTime = 100
                };
                
                // 测试4轨道模式
                for (int i = 0; i < 12; i++)
                {
                    mockNote.Number = 60 + i;
                    string trackName = (string)method.Invoke(generator, new object[] { mockNote, 4 });
                    Console.WriteLine($"音符 {mockNote.Number} -> 轨道 {trackName}");
                }
            }
            
            Console.WriteLine("轨道名称映射测试完成！");
        }
        
        /// <summary>
        /// 测试BPM计算
        /// </summary>
        public static void TestBPMCalculation()
        {
            Console.WriteLine("测试BPM计算...");
            
            // 这里可以添加BPM计算的单元测试
            // 验证BPM值在合理范围内（60-200）
            
            Console.WriteLine("BPM计算测试完成！");
        }
    }
}
