"""
MIDI文件分析器

提供MIDI文件的解析、分析和特征提取功能
"""

import pretty_midi
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class MidiAnalyzer:
    """MIDI文件分析器"""
    
    def __init__(self):
        self.midi_data = None
        self.analysis_cache = {}
    
    def load_midi(self, midi_path: str) -> bool:
        """
        加载MIDI文件
        
        Args:
            midi_path: MIDI文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            self.midi_data = pretty_midi.PrettyMIDI(midi_path)
            self.analysis_cache.clear()  # 清空缓存
            logger.info(f"成功加载MIDI文件: {midi_path}")
            return True
        except Exception as e:
            logger.error(f"加载MIDI文件失败: {e}")
            return False
    
    def get_basic_info(self) -> Dict:
        """
        获取MIDI文件基本信息
        
        Returns:
            Dict: 包含BPM、时长、轨道数等基本信息
        """
        if self.midi_data is None:
            raise ValueError("请先加载MIDI文件")
        
        if 'basic_info' in self.analysis_cache:
            return self.analysis_cache['basic_info']
        
        # 计算基本信息
        tempo_changes = self.midi_data.get_tempo_changes()
        initial_tempo = tempo_changes[1][0] if len(tempo_changes[1]) > 0 else 120.0
        
        info = {
            'duration': self.midi_data.get_end_time(),
            'initial_bpm': initial_tempo,
            'tempo_changes': len(tempo_changes[0]),
            'time_signature_changes': len(self.midi_data.time_signature_changes),
            'key_signature_changes': len(self.midi_data.key_signature_changes),
            'total_instruments': len(self.midi_data.instruments),
            'total_notes': sum(len(inst.notes) for inst in self.midi_data.instruments if not inst.is_drum),
            'drum_notes': sum(len(inst.notes) for inst in self.midi_data.instruments if inst.is_drum),
        }
        
        self.analysis_cache['basic_info'] = info
        return info
    
    def get_note_sequences(self) -> List[Dict]:
        """
        获取所有乐器的音符序列
        
        Returns:
            List[Dict]: 每个乐器的音符序列信息
        """
        if self.midi_data is None:
            raise ValueError("请先加载MIDI文件")
        
        if 'note_sequences' in self.analysis_cache:
            return self.analysis_cache['note_sequences']
        
        sequences = []
        
        for i, instrument in enumerate(self.midi_data.instruments):
            if instrument.is_drum:
                continue  # 暂时跳过鼓轨
            
            notes = []
            for note in instrument.notes:
                notes.append({
                    'start': note.start,
                    'end': note.end,
                    'pitch': note.pitch,
                    'velocity': note.velocity,
                    'duration': note.end - note.start
                })
            
            # 按开始时间排序
            notes.sort(key=lambda x: x['start'])
            
            sequences.append({
                'instrument_id': i,
                'program': instrument.program,
                'name': instrument.name or f"Instrument_{i}",
                'notes': notes,
                'note_count': len(notes)
            })
        
        # 按音符数量排序，音符最多的在前面
        sequences.sort(key=lambda x: x['note_count'], reverse=True)
        
        self.analysis_cache['note_sequences'] = sequences
        return sequences
    
    def get_main_melody_track(self) -> Optional[Dict]:
        """
        获取主旋律轨道（音符数量最多的非鼓轨）
        
        Returns:
            Optional[Dict]: 主旋律轨道信息，如果没有找到返回None
        """
        sequences = self.get_note_sequences()
        return sequences[0] if sequences else None
    
    def analyze_rhythm_patterns(self) -> Dict:
        """
        分析节奏模式
        
        Returns:
            Dict: 节奏模式分析结果
        """
        if self.midi_data is None:
            raise ValueError("请先加载MIDI文件")
        
        if 'rhythm_patterns' in self.analysis_cache:
            return self.analysis_cache['rhythm_patterns']
        
        main_track = self.get_main_melody_track()
        if not main_track:
            return {'error': '没有找到主旋律轨道'}
        
        notes = main_track['notes']
        if not notes:
            return {'error': '主旋律轨道没有音符'}
        
        # 计算音符间隔
        intervals = []
        for i in range(1, len(notes)):
            interval = notes[i]['start'] - notes[i-1]['start']
            intervals.append(interval)
        
        # 分析常见的节拍模式
        common_intervals = self._find_common_intervals(intervals)
        
        # 计算音符密度（每秒音符数）
        duration = self.get_basic_info()['duration']
        note_density = len(notes) / duration if duration > 0 else 0
        
        result = {
            'note_density': note_density,
            'common_intervals': common_intervals,
            'total_intervals': len(intervals),
            'avg_interval': np.mean(intervals) if intervals else 0,
            'std_interval': np.std(intervals) if intervals else 0,
        }
        
        self.analysis_cache['rhythm_patterns'] = result
        return result
    
    def _find_common_intervals(self, intervals: List[float], tolerance: float = 0.05) -> List[Dict]:
        """
        找出常见的音符间隔模式
        
        Args:
            intervals: 音符间隔列表
            tolerance: 容差范围
            
        Returns:
            List[Dict]: 常见间隔模式
        """
        if not intervals:
            return []
        
        # 将间隔分组
        interval_groups = {}
        for interval in intervals:
            # 找到最接近的组
            found_group = False
            for group_key in interval_groups:
                if abs(interval - group_key) <= tolerance:
                    interval_groups[group_key].append(interval)
                    found_group = True
                    break
            
            if not found_group:
                interval_groups[interval] = [interval]
        
        # 计算每组的统计信息
        common_intervals = []
        for group_key, group_intervals in interval_groups.items():
            if len(group_intervals) >= 3:  # 至少出现3次才算常见模式
                common_intervals.append({
                    'interval': np.mean(group_intervals),
                    'count': len(group_intervals),
                    'frequency': len(group_intervals) / len(intervals),
                    'std': np.std(group_intervals)
                })
        
        # 按频率排序
        common_intervals.sort(key=lambda x: x['frequency'], reverse=True)
        
        return common_intervals[:5]  # 返回前5个最常见的模式
    
    def get_chord_progressions(self) -> List[Dict]:
        """
        分析和弦进行
        
        Returns:
            List[Dict]: 和弦进行信息
        """
        if self.midi_data is None:
            raise ValueError("请先加载MIDI文件")
        
        if 'chord_progressions' in self.analysis_cache:
            return self.analysis_cache['chord_progressions']
        
        # 简化的和弦分析：在每个时间点找同时发声的音符
        all_notes = []
        for instrument in self.midi_data.instruments:
            if not instrument.is_drum:
                for note in instrument.notes:
                    all_notes.append({
                        'start': note.start,
                        'end': note.end,
                        'pitch': note.pitch % 12,  # 转换为音级
                        'octave': note.pitch // 12
                    })
        
        # 按开始时间排序
        all_notes.sort(key=lambda x: x['start'])
        
        # 简单的和弦检测（同时开始的音符）
        chords = []
        current_chord = []
        current_time = None
        
        for note in all_notes:
            if current_time is None or abs(note['start'] - current_time) < 0.1:
                # 同一时间点的音符
                current_chord.append(note['pitch'])
                current_time = note['start']
            else:
                # 新的时间点
                if len(current_chord) >= 2:  # 至少2个音符才算和弦
                    chords.append({
                        'time': current_time,
                        'pitches': sorted(list(set(current_chord))),
                        'chord_type': self._identify_chord_type(current_chord)
                    })
                current_chord = [note['pitch']]
                current_time = note['start']
        
        # 处理最后一个和弦
        if len(current_chord) >= 2:
            chords.append({
                'time': current_time,
                'pitches': sorted(list(set(current_chord))),
                'chord_type': self._identify_chord_type(current_chord)
            })
        
        self.analysis_cache['chord_progressions'] = chords
        return chords
    
    def _identify_chord_type(self, pitches: List[int]) -> str:
        """
        简单的和弦类型识别
        
        Args:
            pitches: 音高列表（0-11）
            
        Returns:
            str: 和弦类型
        """
        unique_pitches = sorted(list(set(pitches)))
        if len(unique_pitches) < 2:
            return "single"
        elif len(unique_pitches) == 2:
            return "interval"
        elif len(unique_pitches) == 3:
            # 简单的三和弦判断
            intervals = [(unique_pitches[i+1] - unique_pitches[i]) % 12 for i in range(len(unique_pitches)-1)]
            if intervals == [4, 3]:  # 大三和弦
                return "major"
            elif intervals == [3, 4]:  # 小三和弦
                return "minor"
            else:
                return "triad"
        else:
            return "complex"
    
    def export_analysis(self) -> Dict:
        """
        导出完整的分析结果
        
        Returns:
            Dict: 完整的MIDI分析结果
        """
        if self.midi_data is None:
            raise ValueError("请先加载MIDI文件")
        
        return {
            'basic_info': self.get_basic_info(),
            'note_sequences': self.get_note_sequences(),
            'main_melody': self.get_main_melody_track(),
            'rhythm_patterns': self.analyze_rhythm_patterns(),
            'chord_progressions': self.get_chord_progressions()
        }
