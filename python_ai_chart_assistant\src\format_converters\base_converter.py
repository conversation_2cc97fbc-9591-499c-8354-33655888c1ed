"""
基础转换器

定义格式转换器的基础接口和通用功能
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
import logging
from pathlib import Path

from ..chart_generation.chart_data import ChartData

logger = logging.getLogger(__name__)


class BaseConverter(ABC):
    """格式转换器基类"""
    
    def __init__(self, format_name: str):
        """
        初始化转换器
        
        Args:
            format_name: 格式名称
        """
        self.format_name = format_name
        self.supported_extensions = []
    
    @abstractmethod
    def export(self, chart_data: ChartData, output_path: str, **kwargs) -> bool:
        """
        导出谱面到指定格式
        
        Args:
            chart_data: 谱面数据
            output_path: 输出文件路径
            **kwargs: 额外参数
            
        Returns:
            bool: 是否成功
        """
        pass
    
    @abstractmethod
    def import_chart(self, file_path: str, **kwargs) -> Optional[ChartData]:
        """
        从文件导入谱面
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
            
        Returns:
            Optional[ChartData]: 导入的谱面数据，失败返回None
        """
        pass
    
    def validate_file_extension(self, file_path: str) -> bool:
        """
        验证文件扩展名
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持该扩展名
        """
        if not self.supported_extensions:
            return True  # 如果没有限制，则支持所有扩展名
        
        extension = Path(file_path).suffix.lower()
        return extension in self.supported_extensions
    
    def get_format_info(self) -> Dict[str, Any]:
        """
        获取格式信息
        
        Returns:
            Dict: 格式信息
        """
        return {
            'name': self.format_name,
            'supported_extensions': self.supported_extensions,
            'description': self.__doc__ or f"{self.format_name} format converter"
        }
    
    def _validate_chart_data(self, chart_data: ChartData) -> List[str]:
        """
        验证谱面数据
        
        Args:
            chart_data: 谱面数据
            
        Returns:
            List[str]: 错误信息列表
        """
        return chart_data.validate()
    
    def _log_export_info(self, chart_data: ChartData, output_path: str):
        """记录导出信息"""
        stats = chart_data.get_statistics()
        logger.info(
            f"导出{self.format_name}格式谱面到 {output_path}: "
            f"{stats['total_notes']}个音符, "
            f"时长{chart_data.metadata.duration:.1f}秒"
        )
    
    def _log_import_info(self, chart_data: ChartData, file_path: str):
        """记录导入信息"""
        stats = chart_data.get_statistics()
        logger.info(
            f"从 {file_path} 导入{self.format_name}格式谱面: "
            f"{stats['total_notes']}个音符, "
            f"时长{chart_data.metadata.duration:.1f}秒"
        )


class ConversionError(Exception):
    """转换错误异常"""
    
    def __init__(self, message: str, format_name: str = "", file_path: str = ""):
        self.message = message
        self.format_name = format_name
        self.file_path = file_path
        super().__init__(self.message)
    
    def __str__(self):
        parts = [self.message]
        if self.format_name:
            parts.append(f"格式: {self.format_name}")
        if self.file_path:
            parts.append(f"文件: {self.file_path}")
        return " | ".join(parts)


def register_converter(converter_class: type) -> type:
    """
    转换器注册装饰器
    
    Args:
        converter_class: 转换器类
        
    Returns:
        type: 注册后的转换器类
    """
    if not hasattr(register_converter, 'converters'):
        register_converter.converters = {}
    
    converter_instance = converter_class()
    register_converter.converters[converter_instance.format_name] = converter_class
    
    logger.info(f"注册转换器: {converter_instance.format_name}")
    return converter_class


def get_available_converters() -> Dict[str, type]:
    """
    获取所有可用的转换器
    
    Returns:
        Dict[str, type]: 转换器字典
    """
    return getattr(register_converter, 'converters', {})


def get_converter(format_name: str) -> Optional[BaseConverter]:
    """
    获取指定格式的转换器实例
    
    Args:
        format_name: 格式名称
        
    Returns:
        Optional[BaseConverter]: 转换器实例
    """
    converters = get_available_converters()
    converter_class = converters.get(format_name)
    
    if converter_class:
        return converter_class()
    else:
        logger.error(f"未找到格式转换器: {format_name}")
        return None


def convert_chart(
    chart_data: ChartData,
    target_format: str,
    output_path: str,
    **kwargs
) -> bool:
    """
    转换谱面到指定格式
    
    Args:
        chart_data: 谱面数据
        target_format: 目标格式
        output_path: 输出路径
        **kwargs: 额外参数
        
    Returns:
        bool: 是否成功
    """
    converter = get_converter(target_format)
    if not converter:
        return False
    
    try:
        return converter.export(chart_data, output_path, **kwargs)
    except Exception as e:
        logger.error(f"转换失败: {e}")
        return False


def import_chart_from_file(file_path: str, format_name: Optional[str] = None) -> Optional[ChartData]:
    """
    从文件导入谱面
    
    Args:
        file_path: 文件路径
        format_name: 格式名称，None表示自动检测
        
    Returns:
        Optional[ChartData]: 导入的谱面数据
    """
    if format_name:
        # 使用指定格式
        converter = get_converter(format_name)
        if converter:
            return converter.import_chart(file_path)
    else:
        # 自动检测格式
        file_extension = Path(file_path).suffix.lower()
        
        for converter_class in get_available_converters().values():
            converter = converter_class()
            if converter.validate_file_extension(file_path):
                try:
                    result = converter.import_chart(file_path)
                    if result:
                        return result
                except Exception as e:
                    logger.debug(f"尝试{converter.format_name}格式失败: {e}")
                    continue
    
    logger.error(f"无法导入文件: {file_path}")
    return None
