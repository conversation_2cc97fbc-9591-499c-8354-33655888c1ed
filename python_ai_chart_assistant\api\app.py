"""
Flask API应用

提供REST API服务
"""

import os
import tempfile
from pathlib import Path
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from werkzeug.utils import secure_filename
import logging

from ..src.chart_generator import AIChartGenerator
from ..src.utils.config_manager import ConfigManager
from ..src.utils.logger_setup import setup_logging

logger = logging.getLogger(__name__)


def create_app(config_path: str = None) -> Flask:
    """
    创建Flask应用
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Flask: Flask应用实例
    """
    app = Flask(__name__)
    
    # 加载配置
    config_manager = ConfigManager(config_path)
    api_config = config_manager.get_api_config()
    
    # 设置日志
    setup_logging(**config_manager.get_logging_config())
    
    # CORS配置
    CORS(app, origins=api_config.get('cors_origins', ['*']))
    
    # 文件上传配置
    app.config['MAX_CONTENT_LENGTH'] = _parse_file_size(
        api_config.get('max_file_size', '50MB')
    )
    
    # 初始化AI生成器
    model_config = config_manager.get_model_config()
    generator = AIChartGenerator(
        model_path=model_config.get('generation_model_path'),
        device=model_config.get('device', 'cpu')
    )
    
    # 加载难度预测器
    predictor_path = model_config.get('difficulty_predictor_path')
    if predictor_path and Path(predictor_path).exists():
        generator.load_difficulty_predictor(predictor_path)
    
    @app.route('/health', methods=['GET'])
    def health_check():
        """健康检查"""
        return jsonify({
            'status': 'healthy',
            'service': 'AI Chart Assistant API',
            'version': '1.0.0'
        })
    
    @app.route('/formats', methods=['GET'])
    def get_formats():
        """获取支持的格式"""
        try:
            formats = generator.get_supported_formats()
            format_info = {}
            
            for fmt in formats:
                info = generator.get_format_info(fmt)
                if info:
                    format_info[fmt] = info
            
            return jsonify({
                'success': True,
                'formats': formats,
                'format_info': format_info
            })
            
        except Exception as e:
            logger.error(f"获取格式信息失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/styles', methods=['GET'])
    def get_styles():
        """获取生成风格"""
        try:
            styles = generator.get_generation_styles()
            style_descriptions = {}
            
            for style in styles:
                style_descriptions[style] = generator.get_style_description(style)
            
            return jsonify({
                'success': True,
                'styles': styles,
                'descriptions': style_descriptions
            })
            
        except Exception as e:
            logger.error(f"获取风格信息失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/analyze', methods=['POST'])
    def analyze_midi():
        """分析MIDI文件"""
        try:
            if 'file' not in request.files:
                return jsonify({
                    'success': False,
                    'error': '没有上传文件'
                }), 400
            
            file = request.files['file']
            if file.filename == '':
                return jsonify({
                    'success': False,
                    'error': '文件名为空'
                }), 400
            
            # 验证文件类型
            if not _is_allowed_file(file.filename, ['.mid', '.midi']):
                return jsonify({
                    'success': False,
                    'error': '不支持的文件类型'
                }), 400
            
            # 保存临时文件
            with tempfile.NamedTemporaryFile(suffix='.mid', delete=False) as temp_file:
                file.save(temp_file.name)
                temp_path = temp_file.name
            
            try:
                # 分析MIDI文件
                analysis = generator.analyze_midi(temp_path)
                
                if analysis:
                    return jsonify({
                        'success': True,
                        'analysis': analysis
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': 'MIDI分析失败'
                    }), 500
                    
            finally:
                # 清理临时文件
                os.unlink(temp_path)
                
        except Exception as e:
            logger.error(f"MIDI分析失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/predict_difficulty', methods=['POST'])
    def predict_difficulty():
        """预测难度"""
        try:
            if 'file' not in request.files:
                return jsonify({
                    'success': False,
                    'error': '没有上传文件'
                }), 400
            
            file = request.files['file']
            if not _is_allowed_file(file.filename, ['.mid', '.midi']):
                return jsonify({
                    'success': False,
                    'error': '不支持的文件类型'
                }), 400
            
            # 保存临时文件
            with tempfile.NamedTemporaryFile(suffix='.mid', delete=False) as temp_file:
                file.save(temp_file.name)
                temp_path = temp_file.name
            
            try:
                # 预测难度
                result = generator.predict_difficulty(temp_path)
                
                return jsonify({
                    'success': True,
                    'prediction': result
                })
                
            finally:
                os.unlink(temp_path)
                
        except Exception as e:
            logger.error(f"难度预测失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/generate', methods=['POST'])
    def generate_chart():
        """生成谱面"""
        try:
            if 'file' not in request.files:
                return jsonify({
                    'success': False,
                    'error': '没有上传文件'
                }), 400
            
            file = request.files['file']
            if not _is_allowed_file(file.filename, ['.mid', '.midi']):
                return jsonify({
                    'success': False,
                    'error': '不支持的文件类型'
                }), 400
            
            # 获取参数
            format_name = request.form.get('format', 'malody')
            difficulty = int(request.form.get('difficulty', 5))
            track_count = int(request.form.get('track_count', 4))
            style = request.form.get('style', 'balanced')
            title = request.form.get('title', file.filename)
            artist = request.form.get('artist', 'Unknown')
            
            # 验证参数
            if format_name not in generator.get_supported_formats():
                return jsonify({
                    'success': False,
                    'error': f'不支持的格式: {format_name}'
                }), 400
            
            if not (1 <= difficulty <= 10):
                return jsonify({
                    'success': False,
                    'error': '难度必须在1-10之间'
                }), 400
            
            # 保存临时文件
            with tempfile.NamedTemporaryFile(suffix='.mid', delete=False) as temp_midi:
                file.save(temp_midi.name)
                midi_path = temp_midi.name
            
            # 创建输出文件
            output_ext = '.mc' if format_name == 'malody' else '.imd'
            with tempfile.NamedTemporaryFile(suffix=output_ext, delete=False) as temp_output:
                output_path = temp_output.name
            
            try:
                # 生成谱面
                success = generator.generate_from_midi(
                    midi_path=midi_path,
                    output_path=output_path,
                    format_name=format_name,
                    difficulty=difficulty,
                    track_count=track_count,
                    style=style,
                    title=title,
                    artist=artist
                )
                
                if success:
                    # 返回生成的文件
                    return send_file(
                        output_path,
                        as_attachment=True,
                        download_name=f"{Path(title).stem}_{difficulty}_{format_name}{output_ext}"
                    )
                else:
                    return jsonify({
                        'success': False,
                        'error': '谱面生成失败'
                    }), 500
                    
            finally:
                # 清理临时文件
                if os.path.exists(midi_path):
                    os.unlink(midi_path)
                if os.path.exists(output_path):
                    os.unlink(output_path)
                
        except Exception as e:
            logger.error(f"谱面生成失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/convert', methods=['POST'])
    def convert_chart():
        """转换谱面格式"""
        try:
            if 'file' not in request.files:
                return jsonify({
                    'success': False,
                    'error': '没有上传文件'
                }), 400
            
            file = request.files['file']
            target_format = request.form.get('target_format')
            
            if not target_format:
                return jsonify({
                    'success': False,
                    'error': '必须指定目标格式'
                }), 400
            
            if target_format not in generator.get_supported_formats():
                return jsonify({
                    'success': False,
                    'error': f'不支持的目标格式: {target_format}'
                }), 400
            
            # 保存输入文件
            with tempfile.NamedTemporaryFile(delete=False) as temp_input:
                file.save(temp_input.name)
                input_path = temp_input.name
            
            # 创建输出文件
            output_ext = '.mc' if target_format == 'malody' else '.imd'
            with tempfile.NamedTemporaryFile(suffix=output_ext, delete=False) as temp_output:
                output_path = temp_output.name
            
            try:
                # 转换格式
                success = generator.convert_chart(
                    input_path=input_path,
                    output_path=output_path,
                    target_format=target_format
                )
                
                if success:
                    return send_file(
                        output_path,
                        as_attachment=True,
                        download_name=f"converted_{target_format}{output_ext}"
                    )
                else:
                    return jsonify({
                        'success': False,
                        'error': '格式转换失败'
                    }), 500
                    
            finally:
                if os.path.exists(input_path):
                    os.unlink(input_path)
                if os.path.exists(output_path):
                    os.unlink(output_path)
                
        except Exception as e:
            logger.error(f"格式转换失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.errorhandler(413)
    def file_too_large(error):
        """文件过大错误处理"""
        return jsonify({
            'success': False,
            'error': '文件过大'
        }), 413
    
    @app.errorhandler(500)
    def internal_error(error):
        """内部错误处理"""
        logger.error(f"内部错误: {error}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误'
        }), 500
    
    return app


def _parse_file_size(size_str: str) -> int:
    """解析文件大小字符串"""
    size_str = size_str.upper().strip()
    
    if size_str.endswith('KB'):
        return int(float(size_str[:-2]) * 1024)
    elif size_str.endswith('MB'):
        return int(float(size_str[:-2]) * 1024 * 1024)
    elif size_str.endswith('GB'):
        return int(float(size_str[:-2]) * 1024 * 1024 * 1024)
    else:
        return int(size_str)


def _is_allowed_file(filename: str, allowed_extensions: list) -> bool:
    """检查文件扩展名是否允许"""
    if not filename:
        return False
    
    ext = Path(filename).suffix.lower()
    return ext in allowed_extensions


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='AI Chart Assistant API Server')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--host', default='127.0.0.1', help='服务器地址')
    parser.add_argument('--port', type=int, default=5000, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    # 创建应用
    app = create_app(args.config)
    
    # 运行服务器
    app.run(
        host=args.host,
        port=args.port,
        debug=args.debug
    )


if __name__ == '__main__':
    main()
