#!/usr/bin/env python3
"""
启动Streamlit UI界面的脚本

使用方法:
    python run_ui.py
    或
    python run_ui.py --port 8502 --host 0.0.0.0
"""

import sys
import subprocess
import argparse
import os
from pathlib import Path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动AI音游写谱助手UI界面")
    parser.add_argument('--host', default='localhost', help='服务器地址')
    parser.add_argument('--port', type=int, default=8501, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version >= (3, 13):
        print(f"⚠️ 检测到Python {python_version.major}.{python_version.minor}，某些依赖可能需要特殊处理")

    # 检查Streamlit是否安装
    try:
        import streamlit
        print(f"✅ Streamlit版本: {streamlit.__version__}")
    except ImportError:
        print("❌ Streamlit未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit>=1.28.0"])
            print("✅ Streamlit安装完成")
        except subprocess.CalledProcessError:
            print("❌ Streamlit安装失败，请手动安装:")
            print("   pip install streamlit>=1.28.0")
            print("或运行智能安装脚本:")
            print("   python install_deps.py")
            sys.exit(1)

    # 检查其他关键依赖
    missing_deps = []
    optional_deps = []

    try:
        import plotly
    except ImportError:
        missing_deps.append("plotly>=5.15.0")

    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas>=1.3.0")

    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy>=1.21.0")

    # 检查可选依赖
    try:
        import streamlit_option_menu
    except ImportError:
        optional_deps.append("streamlit-option-menu>=0.3.6")

    # 安装缺失的关键依赖
    if missing_deps:
        print(f"⚠️ 缺少关键依赖: {', '.join(missing_deps)}")
        print("正在安装...")
        for dep in missing_deps:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            except subprocess.CalledProcessError:
                print(f"❌ {dep} 安装失败")
                print("建议运行完整安装脚本:")
                print("   python install_deps.py")
                sys.exit(1)
        print("✅ 关键依赖安装完成")

    # 安装可选依赖（失败不影响运行）
    if optional_deps:
        print(f"💡 安装可选依赖: {', '.join(optional_deps)}")
        for dep in optional_deps:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            except subprocess.CalledProcessError:
                print(f"⚠️ {dep} 安装失败，某些UI功能可能受限")
    
    # 构建Streamlit命令
    ui_script = Path(__file__).parent / "ui" / "streamlit_app.py"
    
    if not ui_script.exists():
        print(f"❌ UI脚本不存在: {ui_script}")
        sys.exit(1)
    
    cmd = [
        "streamlit", "run", str(ui_script),
        "--server.address", args.host,
        "--server.port", str(args.port),
        "--theme.base", "light",
        "--theme.primaryColor", "#1f77b4",
        "--theme.backgroundColor", "#ffffff",
        "--theme.secondaryBackgroundColor", "#f0f2f6",
        "--server.headless", "true",
        "--browser.gatherUsageStats", "false"
    ]
    
    if args.debug:
        cmd.extend(["--logger.level", "debug"])
    
    # 设置环境变量，跳过Streamlit欢迎信息
    os.environ['STREAMLIT_SERVER_HEADLESS'] = 'true'
    os.environ['STREAMLIT_BROWSER_GATHER_USAGE_STATS'] = 'false'

    print(f"🚀 启动UI界面...")
    print(f"📍 地址: http://{args.host}:{args.port}")
    print(f"🎵 AI音游写谱助手UI界面")
    print("=" * 50)
    print("💡 提示: 按 Ctrl+C 可以停止服务")
    print()

    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n👋 UI界面已关闭")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
