﻿using CommonModel;
using RecordMusicalNote;
using RecordMusicalNote.Library;
using RecordMusicalNote.Library.Repository;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace MyWPF.Layout
{
    public class IMDHelper
    {
        public static void ToIdol(float bpm,int keyNum, List<IMDTxtDataModel> iMDTxtDataModels)
        {
            IList<IdolTrack> idolTracks = CommonModel.Common.GetIdolTracks(4);
            string fileName = "E:\\IMDTxtToXML.txt";
            if (File.Exists(fileName))
                File.Delete(fileName);
            FileStream fs = new FileStream(fileName, FileMode.OpenOrCreate);
            StreamWriter sw = new StreamWriter(fs);
            IdolRepository repository = new IdolRepository();
            sw.WriteLine("<Normal>");
            float oneBarUseTime = 60 / bpm * 4 * 1000;
            float oneCellUseTime = 60 / bpm * 4 / 64 * 1000;
            foreach (var item in iMDTxtDataModels)
            {
                IIdol idol = repository.CreateNewIdol();
                if (item.折线 == "开始")
                {
                    sw.WriteLine("<CombineNote>");
                }
                if (keyNum == 4)
                {
                    if (item.键位 == 0)
                    {
                        idol.FromTrack = "Left2";
                        idol.TargetTrack = "Left2";
                    }
                    else if (item.键位 == 1)
                    {
                        idol.FromTrack = "Left1";
                        idol.TargetTrack = "Left1";
                    }
                    else if (item.键位 == 2)
                    {
                        idol.FromTrack = "Right1";
                        idol.TargetTrack = "Right1";
                    }
                    else if (item.键位 == 3)
                    {
                        idol.FromTrack = "Right2";
                        idol.TargetTrack = "Right2";
                    }
                }
                else if(keyNum == 5)
                {
                    if (item.键位 == 0)
                    {
                        idol.FromTrack = "Left2";
                        idol.TargetTrack = "Left2";
                    }
                    else if (item.键位 == 1)
                    {
                        idol.FromTrack = "Left1";
                        idol.TargetTrack = "Left1";
                    }
                    else if (item.键位 == 2)
                    {
                        idol.FromTrack = "Middle";
                        idol.TargetTrack = "Middle";
                    }
                    else if (item.键位 == 3)
                    {
                        idol.FromTrack = "Right1";
                        idol.TargetTrack = "Right1";
                    }
                    else if (item.键位 == 4)
                    {
                        idol.FromTrack = "Right2";
                        idol.TargetTrack = "Right2";
                    }
                }
                if (item.类型 == "按")
                {
                    idol.NoteType = "long";
                    idol.Bar = int.Parse((Math.Floor(item.时间点 / oneBarUseTime) + 1).ToString());
                    idol.Pos = Math.Ceiling(item.时间点 / oneCellUseTime % 64);
                    idol.EndBar = int.Parse((Math.Floor((item.时间点 + item.参数) / oneBarUseTime) + 1).ToString());
                    idol.EndPos = Math.Ceiling((item.时间点 + item.参数) / oneCellUseTime % 64);
                }
                else if (item.类型 == "点")
                {
                    idol.NoteType = "short";
                    idol.Bar = int.Parse((Math.Floor(item.时间点 / oneBarUseTime) + 1).ToString());
                    idol.Pos = Math.Ceiling(item.时间点 / oneCellUseTime % 64);
                }
                else if (item.类型 == "滑")
                {
                    idol.NoteType = "slip";
                    idol.Bar = int.Parse((Math.Floor(item.时间点 / oneBarUseTime) + 1).ToString());
                    idol.Pos = Math.Ceiling(item.时间点 / oneCellUseTime % 64);
                    int endTrackPos = item.键位 + item.参数;
                    if (keyNum == 4)
                    {
                        if (endTrackPos == 0)
                            idol.EndTrack = "Left2";
                        else if (endTrackPos == 1)
                            idol.EndTrack = "Left1";
                        else if (endTrackPos == 2)
                            idol.EndTrack = "Right1";
                        else if (endTrackPos == 3)
                            idol.EndTrack = "Right2";
                    }
                    else if(keyNum == 5)
                    {
                        if (endTrackPos == 0)
                            idol.EndTrack = "Left2";
                        else if (endTrackPos == 1)
                            idol.EndTrack = "Left1";
                        else if (endTrackPos == 2)
                            idol.EndTrack = "Middle";
                        else if (endTrackPos == 3)
                            idol.EndTrack = "Right1";
                        else if (endTrackPos == 4)
                            idol.EndTrack = "Right2";
                    }
                }
                sw.WriteLine(idol.ToString());
                if (item.折线 == "结束")
                {
                    sw.WriteLine("</CombineNote>");
                }
            }
            sw.WriteLine("</Normal>");
            sw.Close();
            MessageBox.Show("已创建");
        }
    }
}
