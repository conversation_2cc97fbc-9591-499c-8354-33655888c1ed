﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CommonModel
{
    public class MidiTrackList
    {
        public int MidiTrackListID { get; set; }

        public int MidiId { get; set; }
        public int MidiTrackID { get; set; }


        public int Channel { get; set; }

        public int Duration { get; set; }
        /// <summary>
        /// 代表音名数字
        /// </summary>
        public int SyllabelNumber { get; set; }
     
        private IList<SyllabelAndNum> _syllabelAndNum;
        /// <summary>
        /// 定义60=C4
        /// </summary>
        /// 
        public string Syllabel {

            get
            {
                string syllabel = "";
                if (_syllabelAndNum == null)
                    _syllabelAndNum = Common.GetSyllabelAndNum();
                int bs = SyllabelNumber / 12-1;
                int cmNum = SyllabelNumber % 12+1;
               IList<string> Syllabels= _syllabelAndNum.Where(a => a.CM == cmNum).Select(a => a.Syllabel).ToList();
                foreach (var item in Syllabels)
                {
                    if(item.IndexOf('+')>0)
                    {
                        string a = item.Substring(item.IndexOf('+'));
                        string b = item.Substring(0,item.IndexOf('+'));
                        syllabel += b + bs+a + "  ";
                    }
                    else if (item.IndexOf('-') > 0)
                    {
                        string a = item.Substring(item.IndexOf('-'));
                        string b = item.Substring(0, item.IndexOf('-'));
                        syllabel += b + bs + a + "  ";
                    }
                    else
                    syllabel += item+ bs + "  ";
                }
                return syllabel;
            }

        }
        public int StartTime { get; set; }

        public int EndTime { get; set; }

       
    }
}
