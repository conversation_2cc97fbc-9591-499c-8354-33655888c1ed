"""
特征提取器

从MIDI数据中提取用于机器学习的特征
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from .midi_analyzer import MidiAnalyzer

logger = logging.getLogger(__name__)


class FeatureExtractor:
    """MIDI特征提取器"""
    
    def __init__(self, time_resolution: float = 0.125):
        """
        初始化特征提取器
        
        Args:
            time_resolution: 时间分辨率（秒），默认1/8秒
        """
        self.time_resolution = time_resolution
        self.analyzer = MidiAnalyzer()
    
    def extract_features(self, midi_path: str) -> Dict:
        """
        从MIDI文件提取特征
        
        Args:
            midi_path: MIDI文件路径
            
        Returns:
            Dict: 提取的特征数据
        """
        if not self.analyzer.load_midi(midi_path):
            raise ValueError(f"无法加载MIDI文件: {midi_path}")
        
        logger.info(f"开始提取特征: {midi_path}")
        
        # 获取基本分析结果
        basic_info = self.analyzer.get_basic_info()
        main_melody = self.analyzer.get_main_melody_track()
        rhythm_patterns = self.analyzer.analyze_rhythm_patterns()
        
        if not main_melody:
            raise ValueError("没有找到主旋律轨道")
        
        # 提取各种特征
        features = {
            'basic_info': basic_info,
            'time_series': self._extract_time_series_features(main_melody),
            'statistical': self._extract_statistical_features(main_melody),
            'rhythmic': self._extract_rhythmic_features(rhythm_patterns),
            'melodic': self._extract_melodic_features(main_melody),
            'structural': self._extract_structural_features(),
        }
        
        logger.info("特征提取完成")
        return features
    
    def _extract_time_series_features(self, main_melody: Dict) -> Dict:
        """
        提取时间序列特征
        
        Args:
            main_melody: 主旋律轨道数据
            
        Returns:
            Dict: 时间序列特征
        """
        notes = main_melody['notes']
        if not notes:
            return {'error': '没有音符数据'}
        
        # 计算时间网格
        duration = self.analyzer.get_basic_info()['duration']
        time_steps = int(duration / self.time_resolution) + 1
        
        # 初始化特征矩阵
        pitch_sequence = np.zeros(time_steps)
        velocity_sequence = np.zeros(time_steps)
        note_density = np.zeros(time_steps)
        
        # 填充特征
        for note in notes:
            start_idx = int(note['start'] / self.time_resolution)
            end_idx = int(note['end'] / self.time_resolution)
            
            # 确保索引在范围内
            start_idx = max(0, min(start_idx, time_steps - 1))
            end_idx = max(0, min(end_idx, time_steps - 1))
            
            # 音高序列（使用MIDI音符号）
            for i in range(start_idx, end_idx + 1):
                if pitch_sequence[i] == 0:  # 如果该时间点还没有音符
                    pitch_sequence[i] = note['pitch']
                    velocity_sequence[i] = note['velocity']
            
            # 音符密度
            note_density[start_idx] += 1
        
        return {
            'time_steps': time_steps,
            'time_resolution': self.time_resolution,
            'pitch_sequence': pitch_sequence.tolist(),
            'velocity_sequence': velocity_sequence.tolist(),
            'note_density': note_density.tolist(),
            'duration': duration
        }
    
    def _extract_statistical_features(self, main_melody: Dict) -> Dict:
        """
        提取统计特征
        
        Args:
            main_melody: 主旋律轨道数据
            
        Returns:
            Dict: 统计特征
        """
        notes = main_melody['notes']
        if not notes:
            return {'error': '没有音符数据'}
        
        # 提取各种数值特征
        pitches = [note['pitch'] for note in notes]
        velocities = [note['velocity'] for note in notes]
        durations = [note['duration'] for note in notes]
        intervals = [notes[i]['start'] - notes[i-1]['start'] for i in range(1, len(notes))]
        
        return {
            # 音高统计
            'pitch_mean': np.mean(pitches),
            'pitch_std': np.std(pitches),
            'pitch_min': np.min(pitches),
            'pitch_max': np.max(pitches),
            'pitch_range': np.max(pitches) - np.min(pitches),
            
            # 力度统计
            'velocity_mean': np.mean(velocities),
            'velocity_std': np.std(velocities),
            'velocity_min': np.min(velocities),
            'velocity_max': np.max(velocities),
            
            # 时值统计
            'duration_mean': np.mean(durations),
            'duration_std': np.std(durations),
            'duration_min': np.min(durations),
            'duration_max': np.max(durations),
            
            # 间隔统计
            'interval_mean': np.mean(intervals) if intervals else 0,
            'interval_std': np.std(intervals) if intervals else 0,
            'interval_min': np.min(intervals) if intervals else 0,
            'interval_max': np.max(intervals) if intervals else 0,
            
            # 其他统计
            'total_notes': len(notes),
            'unique_pitches': len(set(pitches)),
            'note_density_per_second': len(notes) / self.analyzer.get_basic_info()['duration']
        }
    
    def _extract_rhythmic_features(self, rhythm_patterns: Dict) -> Dict:
        """
        提取节奏特征
        
        Args:
            rhythm_patterns: 节奏模式分析结果
            
        Returns:
            Dict: 节奏特征
        """
        if 'error' in rhythm_patterns:
            return rhythm_patterns
        
        features = {
            'note_density': rhythm_patterns['note_density'],
            'avg_interval': rhythm_patterns['avg_interval'],
            'interval_stability': 1.0 / (1.0 + rhythm_patterns['std_interval']),  # 间隔稳定性
            'rhythmic_complexity': len(rhythm_patterns['common_intervals']),
        }
        
        # 添加常见间隔模式的特征
        common_intervals = rhythm_patterns['common_intervals']
        if common_intervals:
            features['dominant_interval'] = common_intervals[0]['interval']
            features['dominant_interval_frequency'] = common_intervals[0]['frequency']
            features['rhythmic_diversity'] = len([x for x in common_intervals if x['frequency'] > 0.1])
        else:
            features['dominant_interval'] = 0
            features['dominant_interval_frequency'] = 0
            features['rhythmic_diversity'] = 0
        
        return features
    
    def _extract_melodic_features(self, main_melody: Dict) -> Dict:
        """
        提取旋律特征
        
        Args:
            main_melody: 主旋律轨道数据
            
        Returns:
            Dict: 旋律特征
        """
        notes = main_melody['notes']
        if not notes:
            return {'error': '没有音符数据'}
        
        pitches = [note['pitch'] for note in notes]
        
        # 计算音程
        intervals = [pitches[i] - pitches[i-1] for i in range(1, len(pitches))]
        
        # 计算方向变化
        directions = []
        for i in range(1, len(intervals)):
            if intervals[i] > 0 and intervals[i-1] <= 0:
                directions.append(1)  # 上行
            elif intervals[i] < 0 and intervals[i-1] >= 0:
                directions.append(-1)  # 下行
            else:
                directions.append(0)  # 保持
        
        features = {
            # 音程特征
            'avg_interval': np.mean(np.abs(intervals)) if intervals else 0,
            'max_interval': np.max(np.abs(intervals)) if intervals else 0,
            'interval_variety': len(set(intervals)) if intervals else 0,
            
            # 方向特征
            'direction_changes': len([d for d in directions if d != 0]),
            'upward_tendency': len([i for i in intervals if i > 0]) / len(intervals) if intervals else 0,
            'downward_tendency': len([i for i in intervals if i < 0]) / len(intervals) if intervals else 0,
            
            # 音域特征
            'pitch_range': np.max(pitches) - np.min(pitches),
            'pitch_center': np.mean(pitches),
            
            # 重复性特征
            'pitch_repetition': 1.0 - (len(set(pitches)) / len(pitches)),
        }
        
        return features
    
    def _extract_structural_features(self) -> Dict:
        """
        提取结构特征
        
        Returns:
            Dict: 结构特征
        """
        basic_info = self.analyzer.get_basic_info()
        
        # 简单的结构分析
        duration = basic_info['duration']
        
        # 根据时长推测结构
        if duration < 60:
            structure_type = "short"
        elif duration < 180:
            structure_type = "medium"
        elif duration < 300:
            structure_type = "long"
        else:
            structure_type = "extended"
        
        return {
            'duration': duration,
            'structure_type': structure_type,
            'tempo_changes': basic_info['tempo_changes'],
            'time_signature_changes': basic_info['time_signature_changes'],
            'complexity_score': (
                basic_info['tempo_changes'] * 0.3 +
                basic_info['time_signature_changes'] * 0.5 +
                (duration / 60) * 0.2
            )
        }
    
    def extract_training_features(self, midi_path: str, target_difficulty: int = 5) -> np.ndarray:
        """
        提取用于训练的特征向量
        
        Args:
            midi_path: MIDI文件路径
            target_difficulty: 目标难度（1-10）
            
        Returns:
            np.ndarray: 特征向量
        """
        features = self.extract_features(midi_path)
        
        # 将特征转换为固定长度的向量
        feature_vector = []
        
        # 基本信息特征
        basic = features['basic_info']
        feature_vector.extend([
            basic['duration'],
            basic['initial_bpm'],
            basic['total_notes'],
            basic['total_instruments'],
        ])
        
        # 统计特征
        stats = features['statistical']
        if 'error' not in stats:
            feature_vector.extend([
                stats['pitch_mean'],
                stats['pitch_std'],
                stats['pitch_range'],
                stats['velocity_mean'],
                stats['velocity_std'],
                stats['duration_mean'],
                stats['duration_std'],
                stats['interval_mean'],
                stats['interval_std'],
                stats['note_density_per_second'],
            ])
        else:
            feature_vector.extend([0] * 10)
        
        # 节奏特征
        rhythmic = features['rhythmic']
        if 'error' not in rhythmic:
            feature_vector.extend([
                rhythmic['note_density'],
                rhythmic['avg_interval'],
                rhythmic['interval_stability'],
                rhythmic['rhythmic_complexity'],
                rhythmic['dominant_interval_frequency'],
            ])
        else:
            feature_vector.extend([0] * 5)
        
        # 旋律特征
        melodic = features['melodic']
        if 'error' not in melodic:
            feature_vector.extend([
                melodic['avg_interval'],
                melodic['max_interval'],
                melodic['upward_tendency'],
                melodic['downward_tendency'],
                melodic['pitch_range'],
                melodic['pitch_repetition'],
            ])
        else:
            feature_vector.extend([0] * 6)
        
        # 结构特征
        structural = features['structural']
        feature_vector.extend([
            structural['duration'],
            structural['complexity_score'],
            target_difficulty,  # 目标难度作为输入特征
        ])
        
        return np.array(feature_vector, dtype=np.float32)
