<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="4a42a017733446158515536c4c1de14a" Name="Diagram1">
        <EntityTypeShape EntityType="MusicModel.BubbleNoteInfo" Width="1.5" PointX="3" PointY="1.375" IsExpanded="true" />
        <EntityTypeShape EntityType="MusicModel.IdolNoteInfo" Width="1.5" PointX="3" PointY="6.25" IsExpanded="true" />
        <EntityTypeShape EntityType="MusicModel.LevelInfo" Width="1.5" PointX="0.75" PointY="5.25" IsExpanded="true" />
        <EntityTypeShape EntityType="MusicModel.PinBallNoteInfo" Width="1.5" PointX="3" PointY="10.25" IsExpanded="true" />
        <AssociationConnector Association="MusicModel.foreign_BubbleLevelInfoId" ManuallyRouted="false" />
        <AssociationConnector Association="MusicModel.foreign_IdolLevelInfoId" ManuallyRouted="false" />
        <AssociationConnector Association="MusicModel.foreign_PinballLevelInfoId" ManuallyRouted="false" />
      </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>