# AI聊天制谱功能使用说明

## 🎵 功能概述

AI聊天制谱功能是一个智能的音游谱面制作助手，通过自然语言对话的方式帮助用户创建和优化音游谱面。

## 🚀 快速开始

### 1. 启动AI聊天
- 在音符编辑器中点击 `💬 AI对话` 按钮
- 系统会自动分析当前谱面状态并提供个性化欢迎消息

### 2. 配置API密钥
- 首次使用需要配置AI服务商的API密钥
- 支持 DeepSeek、OpenAI、Claude 等主流AI服务
- 在设置中输入有效的API密钥

## 💬 对话示例

### 基础生成
```
用户: 帮我写第5-10小节的铺面
AI: 好的！我会根据当前谱面的风格为第5-10小节生成合适的音符...
```

### 风格调整
```
用户: 让第8小节更有挑战性
AI: 我来为第8小节增加一些技巧性音符，包括滑动音符组合...
```

### 分析优化
```
用户: 分析一下当前谱面的特点
AI: 你的谱面有以下特点：
- 总音符数: 156个
- 难度分布: 中等偏上
- 音符类型: 60%短音符, 25%长音符, 15%滑动音符
- 建议优化: ...
```

## 📝 常用模板

系统提供了丰富的预设模板，分为以下几类：

### 基础生成
- **继续写谱**: 根据当前风格生成后续小节
- **指定小节生成**: 为特定小节范围生成音符
- **填充空白小节**: 为空白区域添加内容

### 风格调整
- **增加节奏感**: 添加更多短音符和滑动音符
- **增加长音符**: 提升谱面表现力
- **滑动音符重点**: 设计技巧性滑动组合

### 难度调整
- **降低难度**: 简化谱面，适合新手
- **提高难度**: 增加挑战性
- **平衡难度**: 调整难度分布

### 分析优化
- **谱面分析**: 全面分析谱面特点
- **优化手感**: 改善手部动作流畅性
- **轨道平衡**: 平衡各轨道音符分布

### 特殊效果
- **高潮部分**: 为音乐高潮设计震撼效果
- **过渡段落**: 设计平滑过渡
- **结尾设计**: 创造有仪式感的结尾

### 修复问题
- **修复冲突**: 解决音符位置冲突
- **清理音符**: 移除不合理音符
- **间距调整**: 优化音符间距

## 🎯 智能意图识别

AI助手能够理解多种表达方式：

### 小节范围识别
- "第5-10小节" / "5到10小节"
- "第3小节" / "3小节"
- "后面几个小节" / "接下来的部分"

### 操作类型识别
- **生成**: "写"、"生成"、"创建"、"帮我做"
- **分析**: "分析"、"看看"、"检查"、"怎么样"
- **优化**: "优化"、"改进"、"调整"、"修改"
- **删除**: "删除"、"移除"、"清除"、"去掉"

### 音符类型偏好
- **滑动音符**: "滑动"、"slip"、"滑"
- **长音符**: "长按"、"long"、"长"
- **短音符**: "短"、"点击"、"short"、"tap"

### 难度提示
- **简单**: "简单"、"容易"、"新手"、"初级"
- **困难**: "困难"、"挑战"、"高难"、"专家"
- **中等**: "中等"、"适中"、"平衡"

## 🔧 高级功能

### 上下文感知
- AI会分析当前谱面的音符分布、风格特点
- 根据已有音符推断合适的生成策略
- 保持谱面整体的一致性和连贯性

### 智能验证
- 自动验证生成的音符是否合理
- 检查轨道冲突、时间位置等问题
- 确保滑动音符的轨道相邻性

### 批量操作
- 支持一次性生成多个小节
- 可以同时调整多个方面（难度、风格、类型）
- 提供批量优化建议

## 💡 使用技巧

### 1. 明确描述需求
```
❌ 不好的例子: "帮我改一下"
✅ 好的例子: "帮我把第5-8小节改得更有节奏感，多加一些短音符"
```

### 2. 指定具体范围
```
❌ 不好的例子: "后面的部分"
✅ 好的例子: "第10-15小节"
```

### 3. 描述期望效果
```
❌ 不好的例子: "生成一些音符"
✅ 好的例子: "生成一些适合新手的简单音符，主要用短音符"
```

### 4. 利用模板快速开始
- 使用预设模板可以快速表达常见需求
- 可以在模板基础上进行个性化修改
- 模板会自动计算合适的小节范围

## 🎨 最佳实践

### 渐进式制谱
1. 先生成基础框架（主要节拍点）
2. 逐步添加细节和变化
3. 最后进行整体优化和平衡

### 分段制作
1. 按音乐段落分别制作（intro、verse、chorus等）
2. 每个段落使用不同的风格和难度
3. 注意段落之间的过渡

### 迭代优化
1. 先生成初版谱面
2. 通过对话不断调整和优化
3. 利用分析功能检查整体效果

## ⚠️ 注意事项

### API使用
- 确保API密钥有效且有足够余额
- 网络连接稳定，避免请求超时
- 合理使用，避免频繁请求

### 生成质量
- AI生成的音符需要人工审核
- 复杂的创意设计仍需人工参与
- 建议将AI作为辅助工具，而非完全替代

### 数据安全
- API密钥会保存在本地设置中
- 谱面数据不会上传到云端
- 仅发送必要的上下文信息给AI服务

## 🔮 未来功能

- [ ] 支持更多AI服务商
- [ ] 音频文件直接分析
- [ ] 谱面风格学习和模仿
- [ ] 多人协作制谱
- [ ] 自动难度评估
- [ ] 谱面质量评分

---

**提示**: 如果遇到问题，可以尝试重新描述需求或使用预设模板。AI助手会不断学习和改进，为你提供更好的制谱体验！
