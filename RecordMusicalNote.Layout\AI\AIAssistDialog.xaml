<Window x:Class="MyWPF.Layout.AI.AIAssistDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="AI辅助写谱" Height="600" Width="800" 
        WindowStartupLocation="CenterOwner"
        Style="{StaticResource MaterialDesignWindow}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="AI辅助写谱" 
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- 主要内容 -->
        <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}">
            
            <!-- 基础设置 -->
            <TabItem Header="基础设置">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        
                        <!-- API配置 -->
                        <GroupBox Header="API配置" Style="{StaticResource MaterialDesignCardGroupBox}" Margin="0,0,0,20">
                            <StackPanel Margin="10">
                                <TextBox Name="txtApiKey" 
                                         materialDesign:HintAssist.Hint="API密钥" 
                                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                         Margin="0,0,0,15"/>
                                
                                <ComboBox Name="cmbProvider" 
                                          materialDesign:HintAssist.Hint="AI服务商"
                                          Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                                          Margin="0,0,0,15">
                                    <ComboBoxItem Content="DeepSeek" IsSelected="True"/>
                                    <ComboBoxItem Content="OpenAI"/>
                                    <ComboBoxItem Content="Claude"/>
                                </ComboBox>
                            </StackPanel>
                        </GroupBox>

                        <!-- 生成参数 -->
                        <GroupBox Header="生成参数" Style="{StaticResource MaterialDesignCardGroupBox}" Margin="0,0,0,20">
                            <StackPanel Margin="10">
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                        <TextBlock Text="难度等级" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,5"/>
                                        <Slider Name="sliderDifficulty" Minimum="1" Maximum="10" Value="5" 
                                                TickFrequency="1" IsSnapToTickEnabled="True"
                                                Style="{StaticResource MaterialDesignSlider}"/>
                                        <TextBlock Name="lblDifficulty" Text="5" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                        <TextBlock Text="音符密度" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,5"/>
                                        <Slider Name="sliderDensity" Minimum="0.1" Maximum="1.0" Value="0.7" 
                                                TickFrequency="0.1" IsSnapToTickEnabled="True"
                                                Style="{StaticResource MaterialDesignSlider}"/>
                                        <TextBlock Name="lblDensity" Text="70%" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Grid>

                                <ComboBox Name="cmbStyle" 
                                          materialDesign:HintAssist.Hint="生成风格"
                                          Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                                          Margin="0,15,0,15">
                                    <ComboBoxItem Content="节奏型 (Rhythmic)" IsSelected="True"/>
                                    <ComboBoxItem Content="旋律型 (Melodic)"/>
                                    <ComboBoxItem Content="混合型 (Hybrid)"/>
                                </ComboBox>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBox Name="txtMaxNotesPerSecond" 
                                             Grid.Column="0"
                                             Text="8"
                                             materialDesign:HintAssist.Hint="最大音符/秒" 
                                             Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                             Margin="0,0,10,0"/>
                                    
                                    <TextBox Name="txtMaxNotes" 
                                             Grid.Column="1"
                                             Text="50"
                                             materialDesign:HintAssist.Hint="最大生成数量" 
                                             Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                             Margin="10,0,0,0"/>
                                </Grid>
                            </StackPanel>
                        </GroupBox>

                        <!-- 时间范围 -->
                        <GroupBox Header="时间范围" Style="{StaticResource MaterialDesignCardGroupBox}">
                            <StackPanel Margin="10">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBox Name="txtStartTime" 
                                             Grid.Column="0"
                                             Text="0"
                                             materialDesign:HintAssist.Hint="开始时间(秒)" 
                                             Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                             Margin="0,0,10,0"/>
                                    
                                    <TextBox Name="txtEndTime" 
                                             Grid.Column="1"
                                             Text="60"
                                             materialDesign:HintAssist.Hint="结束时间(秒)" 
                                             Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                             Margin="10,0,0,0"/>
                                </Grid>
                            </StackPanel>
                        </GroupBox>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 高级选项 -->
            <TabItem Header="高级选项">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        
                        <GroupBox Header="生成偏好" Style="{StaticResource MaterialDesignCardGroupBox}" Margin="0,0,0,20">
                            <StackPanel Margin="10">
                                <CheckBox Name="chkFollowMelody" Content="跟随主旋律" IsChecked="True" Margin="0,5"/>
                                <CheckBox Name="chkTrackBalance" Content="轨道平衡" IsChecked="True" Margin="0,5"/>
                                <CheckBox Name="chkAvoidComplexPatterns" Content="避免复杂模式" IsChecked="False" Margin="0,5"/>
                                <CheckBox Name="chkOptimizeHandMovement" Content="优化手部动作" IsChecked="True" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="音符类型偏好" Style="{StaticResource MaterialDesignCardGroupBox}" Margin="0,0,0,20">
                            <StackPanel Margin="10">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="短音符比例" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,5"/>
                                        <Slider Name="sliderShortNotes" Minimum="0" Maximum="1" Value="0.6" 
                                                Style="{StaticResource MaterialDesignSlider}"/>
                                        <TextBlock Name="lblShortNotes" Text="60%" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="1" Margin="10,0">
                                        <TextBlock Text="长音符比例" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,5"/>
                                        <Slider Name="sliderLongNotes" Minimum="0" Maximum="1" Value="0.25" 
                                                Style="{StaticResource MaterialDesignSlider}"/>
                                        <TextBlock Name="lblLongNotes" Text="25%" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="滑动音符比例" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,5"/>
                                        <Slider Name="sliderSlipNotes" Minimum="0" Maximum="1" Value="0.15" 
                                                Style="{StaticResource MaterialDesignSlider}"/>
                                        <TextBlock Name="lblSlipNotes" Text="15%" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="提示词模板" Style="{StaticResource MaterialDesignCardGroupBox}">
                            <StackPanel Margin="10">
                                <ComboBox Name="cmbPromptTemplate" 
                                          materialDesign:HintAssist.Hint="选择提示词模板"
                                          Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                                          Margin="0,0,0,15">
                                    <ComboBoxItem Content="默认模板" IsSelected="True"/>
                                    <ComboBoxItem Content="节奏重点模板"/>
                                    <ComboBoxItem Content="旋律重点模板"/>
                                    <ComboBoxItem Content="技巧重点模板"/>
                                </ComboBox>
                                
                                <TextBox Name="txtCustomPrompt" 
                                         materialDesign:HintAssist.Hint="自定义提示词 (可选)"
                                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                         AcceptsReturn="True"
                                         TextWrapping="Wrap"
                                         Height="100"/>
                            </StackPanel>
                        </GroupBox>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 生成结果 -->
            <TabItem Header="生成结果">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 状态信息 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Name="lblGenerationStatus" Text="等待生成..." Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                        <ProgressBar Name="progressGeneration" Width="200" Height="4" Margin="10,0,0,0" IsIndeterminate="False"/>
                    </StackPanel>

                    <!-- 结果显示 -->
                    <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}">
                        <TabItem Header="生成的音符">
                            <DataGrid Name="dgGeneratedNotes" 
                                      Style="{StaticResource MaterialDesignDataGrid}"
                                      AutoGenerateColumns="False"
                                      IsReadOnly="True">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="小节" Binding="{Binding Bar}" Width="60"/>
                                    <DataGridTextColumn Header="位置" Binding="{Binding Pos}" Width="60"/>
                                    <DataGridTextColumn Header="轨道" Binding="{Binding TargetTrack}" Width="80"/>
                                    <DataGridTextColumn Header="类型" Binding="{Binding NoteType}" Width="80"/>
                                    <DataGridTextColumn Header="结束轨道" Binding="{Binding EndTrack}" Width="80"/>
                                    <DataGridTextColumn Header="结束小节" Binding="{Binding EndBar}" Width="80"/>
                                    <DataGridTextColumn Header="结束位置" Binding="{Binding EndPos}" Width="80"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </TabItem>
                        
                        <TabItem Header="统计信息">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <StackPanel Margin="10">
                                    <TextBlock Name="lblStats" Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                </StackPanel>
                            </ScrollViewer>
                        </TabItem>
                        
                        <TabItem Header="原始响应">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <TextBox Name="txtRawResponse" 
                                         Style="{StaticResource MaterialDesignTextBox}"
                                         AcceptsReturn="True"
                                         TextWrapping="Wrap"
                                         IsReadOnly="True"
                                         FontFamily="Consolas"/>
                            </ScrollViewer>
                        </TabItem>
                    </TabControl>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
                        <Button Name="btnExportNotes" Content="导出音符" 
                                Style="{StaticResource MaterialDesignOutlinedButton}" 
                                Margin="0,0,10,0" Click="BtnExportNotes_Click"/>
                        <Button Name="btnCopyToClipboard" Content="复制到剪贴板" 
                                Style="{StaticResource MaterialDesignOutlinedButton}" 
                                Margin="0,0,10,0" Click="BtnCopyToClipboard_Click"/>
                    </StackPanel>
                </Grid>
            </TabItem>

        </TabControl>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Name="btnGenerate" Content="开始生成" 
                    Style="{StaticResource MaterialDesignRaisedButton}" 
                    Margin="0,0,10,0" Click="BtnGenerate_Click"/>
            <Button Name="btnCancel" Content="取消" 
                    Style="{StaticResource MaterialDesignOutlinedButton}" 
                    Click="BtnCancel_Click"/>
        </StackPanel>

    </Grid>
</Window>
