using System;
using System.Windows;
using CommonModel;

namespace MyWPF.Layout.BackGround
{
    /// <summary>
    /// 谱面难度设置窗口
    /// </summary>
    public partial class DifficultySettingsWindow : Window
    {
        public DifficultySettings Settings { get; private set; }
        public bool IsConfirmed { get; private set; } = false;

        public DifficultySettingsWindow()
        {
            InitializeComponent();
            InitializeSettings();
            SetupEventHandlers();
        }

        public DifficultySettingsWindow(DifficultySettings existingSettings) : this()
        {
            LoadSettings(existingSettings);    
        }

        /// <summary>
        /// 初始化默认设置
        /// </summary>
        private void InitializeSettings()
        {
            Settings = new DifficultySettings();
            LoadSettings(Settings);
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 滑块值变化事件
            sliderNoteDensity.ValueChanged += (s, e) =>
            {
                txtNoteDensityValue.Text = $"{(int)(e.NewValue * 100)}% - 保留{(int)(e.NewValue * 100)}%的音符";
            };

            sliderLongNoteMinDuration.ValueChanged += (s, e) =>
            {
                txtLongNoteDurationValue.Text = $"{e.NewValue:F1} 拍";
            };

            sliderMaxSimultaneousNotes.ValueChanged += (s, e) =>
            {
                txtMaxSimultaneousValue.Text = $"最多{(int)e.NewValue}个同时音符";
            };
        }

        /// <summary>
        /// 加载设置到界面
        /// </summary>
        private void LoadSettings(DifficultySettings settings)
        {
            // 轨道设置
            rbTrack4.IsChecked = settings.TrackCount == 4;
            rbTrack5.IsChecked = settings.TrackCount == 5;
            rbTrack6.IsChecked = settings.TrackCount == 6;
            cbIsFourFinger.IsChecked = settings.IsFourFinger;

            // 难度等级
            cmbDifficultyLevel.SelectedIndex = (int)settings.DifficultyLevel;
            sliderNoteDensity.Value = settings.NoteDensity;

            // 音符类型
            cbEnableShortNotes.IsChecked = settings.EnableShortNotes;
            cbEnableLongNotes.IsChecked = settings.EnableLongNotes;
            cbEnableSlipNotes.IsChecked = settings.EnableSlipNotes;
            cbEnableCombineNotes.IsChecked = settings.EnableCombineNotes;
            sliderLongNoteMinDuration.Value = settings.LongNoteMinDuration;

            // 高级设置
            sliderMaxSimultaneousNotes.Value = settings.MaxSimultaneousNotes;
            cbSmartTrackAssignment.IsChecked = settings.SmartTrackAssignment;
            cbAvoidCrossHand.IsChecked = settings.AvoidCrossHand;
        }

        /// <summary>
        /// 从界面保存设置
        /// </summary>
        private void SaveSettings()
        {
            Settings = new DifficultySettings();

            // 轨道设置
            if (rbTrack4.IsChecked == true) Settings.TrackCount = 4;
            else if (rbTrack5.IsChecked == true) Settings.TrackCount = 5;
            else if (rbTrack6.IsChecked == true) Settings.TrackCount = 6;
            Settings.IsFourFinger = cbIsFourFinger.IsChecked == true;

            // 难度等级
            Settings.DifficultyLevel = (DifficultyLevel)cmbDifficultyLevel.SelectedIndex;
            Settings.NoteDensity = sliderNoteDensity.Value;



            // 音符类型
            Settings.EnableShortNotes = cbEnableShortNotes.IsChecked == true;
            Settings.EnableLongNotes = cbEnableLongNotes.IsChecked == true;
            Settings.EnableSlipNotes = cbEnableSlipNotes.IsChecked == true;
            Settings.EnableCombineNotes = cbEnableCombineNotes.IsChecked == true;
            Settings.LongNoteMinDuration = sliderLongNoteMinDuration.Value;

            // 高级设置
            Settings.MaxSimultaneousNotes = (int)sliderMaxSimultaneousNotes.Value;
            Settings.SmartTrackAssignment = cbSmartTrackAssignment.IsChecked == true;
            Settings.AvoidCrossHand = cbAvoidCrossHand.IsChecked == true;

            // 调试信息：显示保存的设置
            System.Diagnostics.Debug.WriteLine($"=== 用户设置保存 ===");
            System.Diagnostics.Debug.WriteLine($"难度等级: {Settings.DifficultyLevel}");
            System.Diagnostics.Debug.WriteLine($"音符密度: {Settings.NoteDensity:P0}");
            System.Diagnostics.Debug.WriteLine($"最大同时音符: {Settings.MaxSimultaneousNotes}");
            System.Diagnostics.Debug.WriteLine($"启用滑动音符: {Settings.EnableSlipNotes}");
            System.Diagnostics.Debug.WriteLine($"==================");
        }

        private void btnReset_Click(object sender, RoutedEventArgs e)
        {
            LoadSettings(new DifficultySettings()); // 加载默认设置
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            IsConfirmed = false;
            DialogResult = false; // 设置DialogResult为false
            Close();
        }

        private void btnOK_Click(object sender, RoutedEventArgs e)
        {
            SaveSettings();
            IsConfirmed = true;
            DialogResult = true; // 设置DialogResult为true
            Close();
        }
    }
}
