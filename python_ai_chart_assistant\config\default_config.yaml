# AI音游写谱助手默认配置

# 模型配置
model:
  # 预训练模型路径
  generation_model_path: "models/chart_generation_model.pth"
  difficulty_predictor_path: "models/difficulty_predictor.pth"
  
  # 计算设备
  device: "cpu"  # "cpu" 或 "cuda"
  
  # 模型参数
  input_dim: 28
  hidden_dim: 256
  num_layers: 3
  max_sequence_length: 1000
  dropout: 0.2

# 音频分析配置
audio_analysis:
  # 时间分辨率（秒）
  time_resolution: 0.125
  
  # MIDI分析参数
  midi_analysis:
    # 最小音符时长（秒）
    min_note_duration: 0.05
    # 音符合并容差（秒）
    merge_tolerance: 0.02
  
  # 节拍检测参数
  beat_detection:
    # 节拍检测窗口大小
    window_size: 0.25
    # 节拍强度阈值
    strength_threshold: 0.3

# 谱面生成配置
chart_generation:
  # 默认参数
  default_difficulty: 5
  default_track_count: 4
  default_style: "balanced"
  
  # 风格参数
  styles:
    balanced:
      note_density: 0.3
      long_note_ratio: 0.2
      track_balance: true
      rhythm_emphasis: 0.5
    
    dense:
      note_density: 0.45
      long_note_ratio: 0.1
      track_balance: false
      rhythm_emphasis: 0.3
    
    sparse:
      note_density: 0.18
      long_note_ratio: 0.3
      track_balance: true
      rhythm_emphasis: 0.7
    
    rhythmic:
      note_density: 0.3
      long_note_ratio: 0.15
      track_balance: true
      rhythm_emphasis: 0.9
  
  # 后处理参数
  post_processing:
    # 音符冲突检测
    conflict_detection: true
    conflict_resolution_distance: 0.1
    
    # 音符密度平滑
    density_smoothing: true
    smoothing_window: 2.0
    
    # 轨道平衡
    track_balancing: true
    balance_tolerance: 0.3

# 格式转换配置
format_converters:
  # Malody格式
  malody:
    default_key_count: 4
    subdivision_precision: 16
    
  # 节奏大师格式
  rhythm_master:
    beat_precision: 64
    xml_encoding: "utf-8"
    
  # osu!格式
  osu:
    approach_rate: 8
    overall_difficulty: 7
    hp_drain_rate: 6

# 训练配置
training:
  # 数据路径
  data_paths:
    midi_dir: "data/midi"
    charts_dir: "data/charts"
    processed_dir: "data/processed"
  
  # 训练参数
  batch_size: 32
  learning_rate: 0.001
  num_epochs: 100
  validation_split: 0.2
  
  # 数据增强
  data_augmentation:
    tempo_variation: 0.1
    pitch_shift: 2
    time_stretch: 0.1
  
  # 模型保存
  save_interval: 10
  best_model_metric: "validation_loss"

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/ai_chart_assistant.log"
  max_file_size: "10MB"
  backup_count: 5

# API服务配置
api:
  host: "127.0.0.1"
  port: 5000
  debug: false
  
  # 文件上传限制
  max_file_size: "50MB"
  allowed_extensions: [".mid", ".midi", ".wav", ".mp3"]
  
  # 请求限制
  rate_limit: "100/hour"
  
  # CORS设置
  cors_origins: ["http://localhost:3000"]

# 缓存配置
cache:
  # 特征提取缓存
  feature_cache_size: 1000
  feature_cache_ttl: 3600  # 秒
  
  # 模型预测缓存
  prediction_cache_size: 500
  prediction_cache_ttl: 1800

# 性能配置
performance:
  # 并行处理
  max_workers: 4
  
  # 内存限制
  max_memory_usage: "2GB"
  
  # 批处理大小
  batch_processing_size: 10

# 输出配置
output:
  # 默认输出目录
  default_output_dir: "output"
  
  # 文件命名模式
  filename_pattern: "{title}_{difficulty}_{format}"
  
  # 元数据包含
  include_metadata: true
  include_statistics: true
