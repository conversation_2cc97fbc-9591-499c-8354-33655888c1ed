#!/usr/bin/env python3
"""
AI音游写谱助手 - Streamlit UI界面

提供用户友好的Web界面来使用AI谱面生成功能
"""

import streamlit as st
import sys
import os
from pathlib import Path
import tempfile
import json
import time
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.chart_generator import AIChartGenerator
    from src.utils.config_manager import ConfigManager
    from src.training.train import ModelTrainer
    from src.audio_analysis import FeatureExtractor
except ImportError as e:
    st.error(f"导入模块失败: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="🎵 AI音游写谱助手",
    page_icon="🎵",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.5rem;
        color: #ff7f0e;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .success-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    .error-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    .info-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
    }
</style>
""", unsafe_allow_html=True)

# 初始化session state
if 'generator' not in st.session_state:
    st.session_state.generator = None
if 'training_status' not in st.session_state:
    st.session_state.training_status = None

def init_generator():
    """初始化AI谱面生成器"""
    try:
        if st.session_state.generator is None:
            st.session_state.generator = AIChartGenerator()
        return True
    except Exception as e:
        st.error(f"初始化生成器失败: {e}")
        return False

def main():
    """主函数"""
    # 标题
    st.markdown('<h1 class="main-header">🎵 AI音游写谱助手</h1>', unsafe_allow_html=True)
    
    # 侧边栏导航
    with st.sidebar:
        st.markdown("## 🎮 功能导航")
        page = st.selectbox(
            "选择功能",
            [
                "🎵 谱面生成",
                "🔍 MIDI分析", 
                "🎯 难度预测",
                "🔄 格式转换",
                "🚀 模型训练",
                "⚙️ 系统设置"
            ]
        )
        
        st.markdown("---")
        st.markdown("### 📊 系统状态")
        
        # 检查生成器状态
        if init_generator():
            st.success("✅ 生成器已就绪")
        else:
            st.error("❌ 生成器未就绪")
        
        # 显示配置信息
        try:
            config_manager = ConfigManager()
            config = config_manager.get_config()
            device = config.get('model', {}).get('device', 'cpu')
            st.info(f"🖥️ 计算设备: {device}")
        except:
            st.warning("⚠️ 配置加载失败")
    
    # 根据选择显示不同页面
    if page == "🎵 谱面生成":
        show_generation_page()
    elif page == "🔍 MIDI分析":
        show_analysis_page()
    elif page == "🎯 难度预测":
        show_prediction_page()
    elif page == "🔄 格式转换":
        show_conversion_page()
    elif page == "🚀 模型训练":
        show_training_page()
    elif page == "⚙️ 系统设置":
        show_settings_page()

def show_generation_page():
    """显示谱面生成页面"""
    st.markdown('<h2 class="section-header">🎵 谱面生成</h2>', unsafe_allow_html=True)
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("### 📁 文件上传")
        uploaded_file = st.file_uploader(
            "选择MIDI文件",
            type=['mid', 'midi'],
            help="支持标准MIDI格式文件"
        )
        
        if uploaded_file is not None:
            # 保存上传的文件到临时目录
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mid') as tmp_file:
                tmp_file.write(uploaded_file.getvalue())
                temp_midi_path = tmp_file.name
            
            st.success(f"✅ 文件上传成功: {uploaded_file.name}")
            
            # 显示文件信息
            file_size = len(uploaded_file.getvalue())
            st.info(f"📊 文件大小: {file_size / 1024:.1f} KB")
    
    with col2:
        st.markdown("### ⚙️ 生成参数")
        
        # 输出格式
        output_format = st.selectbox(
            "输出格式",
            ["rhythm_master", "malody", "osu"],
            index=0,
            help="选择目标音游格式"
        )
        
        # 难度设置
        difficulty = st.slider(
            "难度等级",
            min_value=1,
            max_value=10,
            value=5,
            help="1=简单, 10=困难"
        )
        
        # 生成风格
        style = st.selectbox(
            "生成风格",
            ["balanced", "dense", "sparse", "rhythmic"],
            index=0,
            help="不同风格的音符分布特点"
        )
        
        # 歌曲信息
        st.markdown("### 🎼 歌曲信息")
        title = st.text_input("歌曲标题", placeholder="请输入歌曲名称")
        artist = st.text_input("艺术家", placeholder="请输入艺术家名称")
    
    # 生成按钮
    if uploaded_file is not None:
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("🚀 开始生成谱面", type="primary", use_container_width=True):
                generate_chart(temp_midi_path, output_format, difficulty, style, title, artist)

def generate_chart(midi_path: str, output_format: str, difficulty: int, style: str, title: str, artist: str):
    """生成谱面"""
    if not init_generator():
        return
    
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    try:
        # 步骤1: 分析MIDI
        status_text.text("🔍 正在分析MIDI文件...")
        progress_bar.progress(20)
        
        analysis = st.session_state.generator.analyze_midi(midi_path)
        if not analysis:
            st.error("❌ MIDI文件分析失败")
            return
        
        # 步骤2: 生成谱面数据
        status_text.text("🎵 正在生成谱面...")
        progress_bar.progress(60)
        
        # 创建临时输出文件
        output_ext = {
            'rhythm_master': '.imd',
            'malody': '.mc',
            'osu': '.osu'
        }.get(output_format, '.imd')
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=output_ext) as tmp_output:
            temp_output_path = tmp_output.name
        
        # 生成谱面
        success = st.session_state.generator.generate_from_midi(
            midi_path=midi_path,
            output_path=temp_output_path,
            format_name=output_format,
            difficulty=difficulty,
            style=style,
            title=title or "Unknown",
            artist=artist or "Unknown"
        )
        
        progress_bar.progress(100)
        
        if success:
            status_text.text("✅ 谱面生成完成！")
            
            # 显示结果
            st.markdown('<div class="success-box">🎉 谱面生成成功！</div>', unsafe_allow_html=True)
            
            # 提供下载
            with open(temp_output_path, 'rb') as f:
                file_data = f.read()
            
            filename = f"{title or 'generated_chart'}_{difficulty}_{style}{output_ext}"
            st.download_button(
                label="📥 下载谱面文件",
                data=file_data,
                file_name=filename,
                mime="application/octet-stream"
            )
            
            # 显示基本信息
            show_generation_results(analysis, difficulty, style)
            
        else:
            st.error("❌ 谱面生成失败")
            
    except Exception as e:
        st.error(f"❌ 生成过程中出现错误: {e}")
    finally:
        # 清理临时文件
        try:
            os.unlink(midi_path)
            if 'temp_output_path' in locals():
                os.unlink(temp_output_path)
        except:
            pass

def show_generation_results(analysis: Dict[str, Any], difficulty: int, style: str):
    """显示生成结果信息"""
    st.markdown("### 📊 生成结果")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("难度等级", difficulty)
        st.metric("生成风格", style)
    
    with col2:
        basic_info = analysis.get('basic_info', {})
        duration = basic_info.get('duration', 0)
        bpm = basic_info.get('initial_bpm', 0)
        st.metric("歌曲时长", f"{duration:.1f}s")
        st.metric("BPM", f"{bpm:.0f}")
    
    with col3:
        total_notes = basic_info.get('total_notes', 0)
        st.metric("原始音符数", total_notes)
        estimated_chart_notes = int(total_notes * 0.3)  # 估算
        st.metric("预估谱面音符", estimated_chart_notes)

def show_analysis_page():
    """显示MIDI分析页面"""
    st.markdown('<h2 class="section-header">🔍 MIDI分析</h2>', unsafe_allow_html=True)

    uploaded_file = st.file_uploader(
        "选择MIDI文件进行分析",
        type=['mid', 'midi'],
        key="analysis_upload"
    )

    if uploaded_file is not None:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.mid') as tmp_file:
            tmp_file.write(uploaded_file.getvalue())
            temp_midi_path = tmp_file.name

        if st.button("🔍 开始分析", type="primary"):
            analyze_midi_file(temp_midi_path)

def analyze_midi_file(midi_path: str):
    """分析MIDI文件"""
    if not init_generator():
        return

    with st.spinner("正在分析MIDI文件..."):
        try:
            analysis = st.session_state.generator.analyze_midi(midi_path)

            if not analysis:
                st.error("❌ 分析失败")
                return

            # 显示分析结果
            st.success("✅ 分析完成！")

            # 基本信息
            basic_info = analysis.get('basic_info', {})
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("时长", f"{basic_info.get('duration', 0):.1f}s")
            with col2:
                st.metric("BPM", f"{basic_info.get('initial_bpm', 0):.1f}")
            with col3:
                st.metric("总音符数", basic_info.get('total_notes', 0))
            with col4:
                st.metric("轨道数", basic_info.get('total_instruments', 0))

            # 详细信息
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("### 🎵 主旋律信息")
                main_melody = analysis.get('main_melody', {})
                if main_melody:
                    st.write(f"**轨道名称**: {main_melody.get('name', 'Unknown')}")
                    st.write(f"**音符数量**: {main_melody.get('note_count', 0)}")
                    st.write(f"**音域范围**: {main_melody.get('pitch_range', 'N/A')}")
                else:
                    st.info("未检测到明显的主旋律轨道")

            with col2:
                st.markdown("### 🥁 节奏信息")
                rhythm = analysis.get('rhythm_patterns', {})
                if rhythm:
                    st.write(f"**音符密度**: {rhythm.get('note_density', 0):.2f} 音符/秒")
                    st.write(f"**平均间隔**: {rhythm.get('avg_interval', 0):.3f} 秒")
                    st.write(f"**节奏复杂度**: {rhythm.get('complexity', 'N/A')}")
                else:
                    st.info("节奏信息分析中...")

            # 和弦进行
            chords = analysis.get('chord_progressions', [])
            if chords:
                st.markdown("### 🎹 和弦进行")
                chord_data = []
                for chord in chords[:10]:  # 显示前10个和弦
                    chord_data.append({
                        "时间": f"{chord.get('time', 0):.1f}s",
                        "和弦类型": chord.get('chord_type', 'unknown'),
                        "根音": chord.get('root', 'N/A')
                    })

                if chord_data:
                    st.table(chord_data)

                if len(chords) > 10:
                    st.info(f"还有 {len(chords) - 10} 个和弦未显示")

        except Exception as e:
            st.error(f"❌ 分析过程中出现错误: {e}")
        finally:
            try:
                os.unlink(midi_path)
            except:
                pass

def show_prediction_page():
    """显示难度预测页面"""
    st.markdown('<h2 class="section-header">🎯 难度预测</h2>', unsafe_allow_html=True)

    st.info("💡 使用AI模型预测MIDI文件的游戏难度")

    uploaded_file = st.file_uploader(
        "选择MIDI文件进行难度预测",
        type=['mid', 'midi'],
        key="prediction_upload"
    )

    if uploaded_file is not None:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.mid') as tmp_file:
            tmp_file.write(uploaded_file.getvalue())
            temp_midi_path = tmp_file.name

        if st.button("🎯 预测难度", type="primary"):
            predict_difficulty(temp_midi_path)

def predict_difficulty(midi_path: str):
    """预测难度"""
    if not init_generator():
        return

    with st.spinner("正在预测难度..."):
        try:
            result = st.session_state.generator.predict_difficulty(midi_path)

            if not result:
                st.error("❌ 难度预测失败")
                return

            st.success("✅ 预测完成！")

            # 显示预测结果
            col1, col2, col3 = st.columns(3)

            with col1:
                difficulty = result.get('difficulty', 0)
                st.metric("预测难度", f"{difficulty:.1f}/10")

                # 难度等级描述
                if difficulty <= 2:
                    level_desc = "🟢 简单"
                elif difficulty <= 4:
                    level_desc = "🟡 普通"
                elif difficulty <= 6:
                    level_desc = "🟠 中等"
                elif difficulty <= 8:
                    level_desc = "🔴 困难"
                else:
                    level_desc = "🟣 专家"

                st.write(f"**难度等级**: {level_desc}")

            with col2:
                confidence = result.get('confidence', 0)
                st.metric("置信度", f"{confidence:.2%}")

                if confidence > 0.8:
                    conf_desc = "🟢 高置信度"
                elif confidence > 0.6:
                    conf_desc = "🟡 中等置信度"
                else:
                    conf_desc = "🔴 低置信度"

                st.write(f"**置信度评级**: {conf_desc}")

            with col3:
                playability = result.get('playability', 0)
                st.metric("游戏性评分", f"{playability:.2f}/10")

                if playability > 7:
                    play_desc = "🟢 优秀"
                elif playability > 5:
                    play_desc = "🟡 良好"
                else:
                    play_desc = "🔴 需要优化"

                st.write(f"**游戏性评级**: {play_desc}")

            # 音符分布
            if 'note_distribution' in result:
                st.markdown("### 📊 音符分布预测")
                dist = result['note_distribution']

                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("空白比例", f"{dist[0]:.1%}")
                with col2:
                    st.metric("短音符比例", f"{dist[1]:.1%}")
                with col3:
                    st.metric("长音符比例", f"{dist[2]:.1%}")

        except Exception as e:
            st.error(f"❌ 预测过程中出现错误: {e}")
        finally:
            try:
                os.unlink(midi_path)
            except:
                pass

def show_conversion_page():
    """显示格式转换页面"""
    st.markdown('<h2 class="section-header">🔄 格式转换</h2>', unsafe_allow_html=True)

    st.info("💡 在不同音游格式之间转换谱面文件")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### 📁 输入文件")
        uploaded_file = st.file_uploader(
            "选择谱面文件",
            type=['json', 'mc', 'imd', 'xml', 'osu'],
            key="conversion_upload"
        )

        if uploaded_file:
            st.success(f"✅ 文件: {uploaded_file.name}")

            # 检测输入格式
            file_ext = Path(uploaded_file.name).suffix.lower()
            input_format = {
                '.json': 'json',
                '.mc': 'malody',
                '.imd': 'rhythm_master',
                '.xml': 'rhythm_master',
                '.osu': 'osu'
            }.get(file_ext, 'unknown')

            st.info(f"🔍 检测到格式: {input_format}")

    with col2:
        st.markdown("### ⚙️ 转换设置")
        target_format = st.selectbox(
            "目标格式",
            ["rhythm_master", "malody", "osu"],
            help="选择要转换到的目标格式"
        )

        # 格式特定选项
        if target_format == "rhythm_master":
            beat_precision = st.slider("节拍精度", 16, 128, 64, step=16)
        elif target_format == "malody":
            key_count = st.selectbox("键位数", [4, 5, 6, 7], index=0)

        preserve_metadata = st.checkbox("保留元数据", value=True)

    if uploaded_file is not None:
        if st.button("🔄 开始转换", type="primary"):
            convert_chart_format(uploaded_file, target_format)

def convert_chart_format(uploaded_file, target_format: str):
    """转换谱面格式"""
    if not init_generator():
        return

    # 保存上传文件
    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(uploaded_file.name).suffix) as tmp_input:
        tmp_input.write(uploaded_file.getvalue())
        temp_input_path = tmp_input.name

    # 创建输出文件
    output_ext = {
        'rhythm_master': '.imd',
        'malody': '.mc',
        'osu': '.osu'
    }.get(target_format, '.imd')

    with tempfile.NamedTemporaryFile(delete=False, suffix=output_ext) as tmp_output:
        temp_output_path = tmp_output.name

    with st.spinner("正在转换格式..."):
        try:
            success = st.session_state.generator.convert_chart(
                input_path=temp_input_path,
                output_path=temp_output_path,
                target_format=target_format
            )

            if success:
                st.success("✅ 格式转换成功！")

                # 提供下载
                with open(temp_output_path, 'rb') as f:
                    file_data = f.read()

                base_name = Path(uploaded_file.name).stem
                output_filename = f"{base_name}_converted{output_ext}"

                st.download_button(
                    label="📥 下载转换后的文件",
                    data=file_data,
                    file_name=output_filename,
                    mime="application/octet-stream"
                )

            else:
                st.error("❌ 格式转换失败")

        except Exception as e:
            st.error(f"❌ 转换过程中出现错误: {e}")
        finally:
            try:
                os.unlink(temp_input_path)
                os.unlink(temp_output_path)
            except:
                pass

def show_training_page():
    """显示模型训练页面"""
    st.markdown('<h2 class="section-header">🚀 模型训练</h2>', unsafe_allow_html=True)

    st.warning("⚠️ 模型训练需要大量的MIDI和谱面数据对，请确保数据已准备就绪")

    # 训练参数设置
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### 📊 数据设置")

        data_dir = st.text_input(
            "训练数据目录",
            value="data",
            help="包含midi和charts子目录的数据根目录"
        )

        validation_split = st.slider(
            "验证集比例",
            min_value=0.1,
            max_value=0.4,
            value=0.2,
            step=0.05
        )

        batch_size = st.selectbox(
            "批次大小",
            [8, 16, 32, 64],
            index=2
        )

    with col2:
        st.markdown("### ⚙️ 训练参数")

        model_type = st.selectbox(
            "模型类型",
            ["generation", "difficulty"],
            help="generation: 谱面生成模型, difficulty: 难度预测模型"
        )

        epochs = st.number_input(
            "训练轮数",
            min_value=1,
            max_value=1000,
            value=100,
            step=10
        )

        learning_rate = st.selectbox(
            "学习率",
            [0.0001, 0.001, 0.01],
            index=1,
            format_func=lambda x: f"{x:.4f}"
        )

        device = st.selectbox(
            "计算设备",
            ["cpu", "cuda"],
            help="如果有GPU可选择cuda"
        )

    # 高级设置
    with st.expander("🔧 高级设置"):
        save_dir = st.text_input("模型保存目录", value="models")
        resume_checkpoint = st.text_input("恢复检查点路径", placeholder="可选：从检查点恢复训练")

        enable_tensorboard = st.checkbox("启用TensorBoard", value=True)
        save_interval = st.number_input("保存间隔（轮数）", min_value=1, max_value=50, value=10)

    # 数据验证
    st.markdown("### 📋 数据验证")
    if st.button("🔍 检查数据"):
        check_training_data(data_dir)

    # 开始训练
    if st.button("🚀 开始训练", type="primary"):
        start_training(
            data_dir, model_type, epochs, batch_size,
            learning_rate, device, save_dir, resume_checkpoint
        )

def check_training_data(data_dir: str):
    """检查训练数据"""
    try:
        data_path = Path(data_dir)
        midi_dir = data_path / "midi"
        charts_dir = data_path / "charts"

        if not data_path.exists():
            st.error(f"❌ 数据目录不存在: {data_dir}")
            return

        if not midi_dir.exists():
            st.error(f"❌ MIDI目录不存在: {midi_dir}")
            return

        if not charts_dir.exists():
            st.error(f"❌ 谱面目录不存在: {charts_dir}")
            return

        # 统计文件数量
        midi_files = list(midi_dir.glob("*.mid")) + list(midi_dir.glob("*.midi"))
        chart_files = list(charts_dir.glob("*.imd")) + list(charts_dir.glob("*.xml")) + list(charts_dir.glob("*.json"))

        st.success("✅ 数据目录结构正确")

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("MIDI文件数", len(midi_files))
        with col2:
            st.metric("谱面文件数", len(chart_files))
        with col3:
            # 计算匹配的文件对数量
            matched_pairs = 0
            for midi_file in midi_files:
                base_name = midi_file.stem
                for chart_file in chart_files:
                    if chart_file.stem == base_name:
                        matched_pairs += 1
                        break
            st.metric("匹配的数据对", matched_pairs)

        if matched_pairs < 10:
            st.warning("⚠️ 匹配的数据对较少，建议至少准备100对以上的数据")
        elif matched_pairs < 100:
            st.info("💡 数据量适中，可以开始训练")
        else:
            st.success("🎉 数据量充足，训练效果会更好")

    except Exception as e:
        st.error(f"❌ 数据检查失败: {e}")

def start_training(data_dir: str, model_type: str, epochs: int, batch_size: int,
                  learning_rate: float, device: str, save_dir: str, resume_checkpoint: str):
    """启动模型训练"""
    try:
        st.info("🚀 正在初始化训练...")

        # 创建训练器
        trainer = ModelTrainer(device=device)
        trainer.setup_model(model_type)
        trainer.setup_data_loader(data_dir)
        trainer.setup_optimizer()

        # 设置TensorBoard
        log_dir = Path(save_dir) / 'tensorboard_logs'
        trainer.setup_tensorboard(str(log_dir))

        # 恢复检查点
        if resume_checkpoint and Path(resume_checkpoint).exists():
            trainer.load_checkpoint(resume_checkpoint)
            st.info(f"✅ 已加载检查点: {resume_checkpoint}")

        st.success("✅ 训练器初始化完成")
        st.info("⚠️ 训练将在后台进行，请不要关闭浏览器")

        # 显示训练信息
        st.markdown("### 📊 训练配置")
        config_data = {
            "数据目录": data_dir,
            "模型类型": model_type,
            "训练轮数": epochs,
            "批次大小": batch_size,
            "学习率": learning_rate,
            "计算设备": device,
            "保存目录": save_dir
        }

        for key, value in config_data.items():
            st.write(f"**{key}**: {value}")

        # 注意：实际的训练过程需要在后台运行
        # 这里只是演示界面，真实训练需要异步处理
        st.warning("💡 实际训练需要在命令行中运行，或者实现异步训练功能")

        # 提供命令行命令
        st.markdown("### 🖥️ 等效命令行命令")
        cmd = f"""python main.py train \\
    --data-dir {data_dir} \\
    --model-type {model_type} \\
    --epochs {epochs} \\
    --device {device} \\
    --save-dir {save_dir}"""

        if resume_checkpoint:
            cmd += f" \\\n    --resume {resume_checkpoint}"

        st.code(cmd, language="bash")

    except Exception as e:
        st.error(f"❌ 训练启动失败: {e}")

def show_settings_page():
    """显示系统设置页面"""
    st.markdown('<h2 class="section-header">⚙️ 系统设置</h2>', unsafe_allow_html=True)

    # 模型设置
    st.markdown("### 🤖 模型设置")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 生成模型")
        gen_model_path = st.text_input(
            "生成模型路径",
            value="models/chart_generation_model.pth",
            help="AI谱面生成模型文件路径"
        )

        if Path(gen_model_path).exists():
            st.success("✅ 模型文件存在")
        else:
            st.warning("⚠️ 模型文件不存在")

    with col2:
        st.markdown("#### 难度预测模型")
        diff_model_path = st.text_input(
            "难度预测模型路径",
            value="models/difficulty_predictor.pth",
            help="难度预测模型文件路径"
        )

        if Path(diff_model_path).exists():
            st.success("✅ 模型文件存在")
        else:
            st.warning("⚠️ 模型文件不存在")

    # 计算设置
    st.markdown("### 🖥️ 计算设置")

    col1, col2 = st.columns(2)

    with col1:
        device = st.selectbox(
            "计算设备",
            ["cpu", "cuda"],
            help="选择用于AI推理的计算设备"
        )

        max_workers = st.slider(
            "最大工作线程",
            min_value=1,
            max_value=8,
            value=4,
            help="并行处理的最大线程数"
        )

    with col2:
        memory_limit = st.selectbox(
            "内存限制",
            ["1GB", "2GB", "4GB", "8GB"],
            index=1,
            help="最大内存使用限制"
        )

        batch_size = st.slider(
            "批处理大小",
            min_value=1,
            max_value=64,
            value=10,
            help="批量处理时的批次大小"
        )

    # 输出设置
    st.markdown("### 📁 输出设置")

    col1, col2 = st.columns(2)

    with col1:
        output_dir = st.text_input(
            "默认输出目录",
            value="output",
            help="生成文件的默认保存目录"
        )

        filename_pattern = st.text_input(
            "文件命名模式",
            value="{title}_{difficulty}_{format}",
            help="输出文件的命名模式"
        )

    with col2:
        include_metadata = st.checkbox("包含元数据", value=True)
        include_statistics = st.checkbox("包含统计信息", value=True)
        auto_backup = st.checkbox("自动备份", value=False)

    # 日志设置
    st.markdown("### 📝 日志设置")

    col1, col2 = st.columns(2)

    with col1:
        log_level = st.selectbox(
            "日志级别",
            ["DEBUG", "INFO", "WARNING", "ERROR"],
            index=1
        )

        log_file = st.text_input(
            "日志文件路径",
            value="logs/ai_chart_assistant.log"
        )

    with col2:
        max_log_size = st.selectbox(
            "最大日志文件大小",
            ["1MB", "5MB", "10MB", "50MB"],
            index=2
        )

        backup_count = st.number_input(
            "日志备份数量",
            min_value=1,
            max_value=20,
            value=5
        )

    # 保存设置
    if st.button("💾 保存设置", type="primary"):
        save_settings({
            'model': {
                'generation_model_path': gen_model_path,
                'difficulty_predictor_path': diff_model_path,
                'device': device
            },
            'performance': {
                'max_workers': max_workers,
                'max_memory_usage': memory_limit,
                'batch_processing_size': batch_size
            },
            'output': {
                'default_output_dir': output_dir,
                'filename_pattern': filename_pattern,
                'include_metadata': include_metadata,
                'include_statistics': include_statistics,
                'auto_backup': auto_backup
            },
            'logging': {
                'level': log_level,
                'file': log_file,
                'max_file_size': max_log_size,
                'backup_count': backup_count
            }
        })

    # 系统信息
    st.markdown("### 📊 系统信息")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Python版本", f"{sys.version_info.major}.{sys.version_info.minor}")

    with col2:
        try:
            import torch
            st.metric("PyTorch版本", torch.__version__)
        except:
            st.metric("PyTorch版本", "未安装")

    with col3:
        try:
            import tensorflow as tf
            st.metric("TensorFlow版本", tf.__version__)
        except:
            st.metric("TensorFlow版本", "未安装")

def save_settings(settings: Dict[str, Any]):
    """保存设置到配置文件"""
    try:
        config_path = Path("config/user_config.yaml")
        config_path.parent.mkdir(exist_ok=True)

        import yaml
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(settings, f, default_flow_style=False, allow_unicode=True)

        st.success("✅ 设置已保存")
        st.info(f"💾 配置文件: {config_path}")

    except Exception as e:
        st.error(f"❌ 保存设置失败: {e}")

if __name__ == "__main__":
    main()
