using System.Configuration;

namespace RecordMusicalNote.Layout.Properties
{
    /// <summary>
    /// 应用程序设置
    /// </summary>
    public sealed partial class Settings : ApplicationSettingsBase
    {
        private static Settings defaultInstance = ((Settings)(ApplicationSettingsBase.Synchronized(new Settings())));

        public static Settings Default
        {
            get
            {
                return defaultInstance;
            }
        }

        [UserScopedSetting()]
        [DefaultSettingValue("")]
        public string AI<PERSON><PERSON><PERSON>ey
        {
            get
            {
                return ((string)(this["AIApiKey"]));
            }
            set
            {
                this["AIApiKey"] = value;
            }
        }

        [UserScopedSetting()]
        [DefaultSettingValue("DeepSeek")]
        public string AIProvider
        {
            get
            {
                return ((string)(this["AIProvider"]));
            }
            set
            {
                this["AIProvider"] = value;
            }
        }

        [UserScopedSetting()]
        [DefaultSettingValue("5")]
        public int DefaultDifficulty
        {
            get
            {
                return ((int)(this["DefaultDifficulty"]));
            }
            set
            {
                this["DefaultDifficulty"] = value;
            }
        }

        [UserScopedSetting()]
        [DefaultSettingValue("0.7")]
        public double DefaultNoteDensity
        {
            get
            {
                return ((double)(this["DefaultNoteDensity"]));
            }
            set
            {
                this["DefaultNoteDensity"] = value;
            }
        }
    }
}
