"""
AI谱面生成器主接口

提供简单易用的API接口
"""

import logging
from typing import Dict, List, Optional, Union
from pathlib import Path

from .chart_generation import ChartGenerator as CoreChartGenerator
from .chart_generation.chart_data import ChartData
from .format_converters import get_converter, get_available_converters

logger = logging.getLogger(__name__)


class AIChartGenerator:
    """AI谱面生成器主接口"""
    
    def __init__(
        self,
        model_path: Optional[str] = None,
        device: str = 'cpu',
        time_resolution: float = 0.125
    ):
        """
        初始化AI谱面生成器
        
        Args:
            model_path: 预训练模型路径
            device: 计算设备 ('cpu' 或 'cuda')
            time_resolution: 时间分辨率（秒）
        """
        self.core_generator = CoreChartGenerator(model_path, device, time_resolution)
        self.available_formats = list(get_available_converters().keys())
        
        logger.info(f"AI谱面生成器初始化完成")
        logger.info(f"支持的格式: {', '.join(self.available_formats)}")
    
    def generate_from_midi(
        self,
        midi_path: str,
        output_path: str,
        format_name: str = "malody",
        difficulty: int = 5,
        track_count: int = 4,
        style: str = "balanced",
        title: Optional[str] = None,
        artist: Optional[str] = None,
        **format_kwargs
    ) -> bool:
        """
        从MIDI文件生成谱面并保存
        
        Args:
            midi_path: MIDI文件路径
            output_path: 输出文件路径
            format_name: 输出格式 ("malody", "rhythm_master", "osu")
            difficulty: 目标难度 (1-10)
            track_count: 轨道数量
            style: 生成风格 ("balanced", "dense", "sparse", "rhythmic")
            title: 歌曲标题
            artist: 艺术家
            **format_kwargs: 格式特定参数
            
        Returns:
            bool: 是否成功
        """
        try:
            # 验证输入
            if not Path(midi_path).exists():
                logger.error(f"MIDI文件不存在: {midi_path}")
                return False
            
            if format_name not in self.available_formats:
                logger.error(f"不支持的格式: {format_name}")
                return False
            
            # 生成谱面数据
            logger.info(f"开始生成谱面: {midi_path}")
            chart_data = self.core_generator.generate_from_midi(
                midi_path=midi_path,
                difficulty=difficulty,
                track_count=track_count,
                style=style,
                title=title,
                artist=artist
            )
            
            # 导出到指定格式
            converter = get_converter(format_name)
            if not converter:
                logger.error(f"无法获取转换器: {format_name}")
                return False
            
            success = converter.export(chart_data, output_path, **format_kwargs)
            
            if success:
                logger.info(f"谱面生成成功: {output_path}")
            else:
                logger.error("谱面导出失败")
            
            return success
            
        except Exception as e:
            logger.error(f"生成谱面失败: {e}")
            return False
    
    def generate_chart_data(
        self,
        midi_path: str,
        difficulty: int = 5,
        track_count: int = 4,
        style: str = "balanced",
        title: Optional[str] = None,
        artist: Optional[str] = None
    ) -> Optional[ChartData]:
        """
        从MIDI文件生成谱面数据（不保存文件）
        
        Args:
            midi_path: MIDI文件路径
            difficulty: 目标难度 (1-10)
            track_count: 轨道数量
            style: 生成风格
            title: 歌曲标题
            artist: 艺术家
            
        Returns:
            Optional[ChartData]: 生成的谱面数据
        """
        try:
            if not Path(midi_path).exists():
                logger.error(f"MIDI文件不存在: {midi_path}")
                return None
            
            chart_data = self.core_generator.generate_from_midi(
                midi_path=midi_path,
                difficulty=difficulty,
                track_count=track_count,
                style=style,
                title=title,
                artist=artist
            )
            
            return chart_data
            
        except Exception as e:
            logger.error(f"生成谱面数据失败: {e}")
            return None
    
    def predict_difficulty(self, midi_path: str) -> Dict:
        """
        预测MIDI文件的难度
        
        Args:
            midi_path: MIDI文件路径
            
        Returns:
            Dict: 难度预测结果
        """
        try:
            if not Path(midi_path).exists():
                logger.error(f"MIDI文件不存在: {midi_path}")
                return {'difficulty': 5.0, 'confidence': 0.0}
            
            return self.core_generator.predict_difficulty(midi_path)
            
        except Exception as e:
            logger.error(f"难度预测失败: {e}")
            return {'difficulty': 5.0, 'confidence': 0.0}
    
    def convert_chart(
        self,
        input_path: str,
        output_path: str,
        target_format: str,
        source_format: Optional[str] = None,
        **format_kwargs
    ) -> bool:
        """
        转换谱面格式
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            target_format: 目标格式
            source_format: 源格式（None表示自动检测）
            **format_kwargs: 格式特定参数
            
        Returns:
            bool: 是否成功
        """
        try:
            if not Path(input_path).exists():
                logger.error(f"输入文件不存在: {input_path}")
                return False
            
            if target_format not in self.available_formats:
                logger.error(f"不支持的目标格式: {target_format}")
                return False
            
            # 导入谱面数据
            if source_format:
                source_converter = get_converter(source_format)
                if not source_converter:
                    logger.error(f"无法获取源格式转换器: {source_format}")
                    return False
                chart_data = source_converter.import_chart(input_path)
            else:
                # 自动检测格式
                from .format_converters.base_converter import import_chart_from_file
                chart_data = import_chart_from_file(input_path)
            
            if not chart_data:
                logger.error("无法导入谱面数据")
                return False
            
            # 导出到目标格式
            target_converter = get_converter(target_format)
            if not target_converter:
                logger.error(f"无法获取目标格式转换器: {target_format}")
                return False
            
            success = target_converter.export(chart_data, output_path, **format_kwargs)
            
            if success:
                logger.info(f"格式转换成功: {input_path} -> {output_path}")
            else:
                logger.error("格式转换失败")
            
            return success
            
        except Exception as e:
            logger.error(f"格式转换失败: {e}")
            return False
    
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的格式列表
        
        Returns:
            List[str]: 支持的格式名称列表
        """
        return self.available_formats.copy()
    
    def get_format_info(self, format_name: str) -> Optional[Dict]:
        """
        获取格式信息
        
        Args:
            format_name: 格式名称
            
        Returns:
            Optional[Dict]: 格式信息
        """
        converter = get_converter(format_name)
        if converter:
            return converter.get_format_info()
        return None
    
    def load_model(self, model_path: str) -> bool:
        """
        加载预训练模型
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            self.core_generator.load_model(model_path)
            return True
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            return False
    
    def load_difficulty_predictor(self, predictor_path: str) -> bool:
        """
        加载难度预测器
        
        Args:
            predictor_path: 预测器文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            self.core_generator.load_difficulty_predictor(predictor_path)
            return True
        except Exception as e:
            logger.error(f"加载难度预测器失败: {e}")
            return False
    
    def analyze_midi(self, midi_path: str) -> Optional[Dict]:
        """
        分析MIDI文件
        
        Args:
            midi_path: MIDI文件路径
            
        Returns:
            Optional[Dict]: 分析结果
        """
        try:
            if not Path(midi_path).exists():
                logger.error(f"MIDI文件不存在: {midi_path}")
                return None
            
            # 使用内部分析器
            analyzer = self.core_generator.midi_analyzer
            if analyzer.load_midi(midi_path):
                return analyzer.export_analysis()
            else:
                return None
                
        except Exception as e:
            logger.error(f"MIDI分析失败: {e}")
            return None
    
    def get_generation_styles(self) -> List[str]:
        """
        获取可用的生成风格
        
        Returns:
            List[str]: 风格名称列表
        """
        return ["balanced", "dense", "sparse", "rhythmic"]
    
    def get_style_description(self, style: str) -> str:
        """
        获取风格描述
        
        Args:
            style: 风格名称
            
        Returns:
            str: 风格描述
        """
        descriptions = {
            "balanced": "平衡风格 - 音符密度适中，适合大多数歌曲",
            "dense": "密集风格 - 音符密度较高，适合快节奏歌曲",
            "sparse": "稀疏风格 - 音符密度较低，适合慢节奏或抒情歌曲",
            "rhythmic": "节奏风格 - 强调节拍，跟随音乐节奏"
        }
        return descriptions.get(style, "未知风格")


# 便捷函数
def generate_chart(
    midi_path: str,
    output_path: str,
    format_name: str = "malody",
    difficulty: int = 5,
    **kwargs
) -> bool:
    """
    便捷的谱面生成函数
    
    Args:
        midi_path: MIDI文件路径
        output_path: 输出文件路径
        format_name: 输出格式
        difficulty: 难度等级
        **kwargs: 其他参数
        
    Returns:
        bool: 是否成功
    """
    generator = AIChartGenerator()
    return generator.generate_from_midi(
        midi_path=midi_path,
        output_path=output_path,
        format_name=format_name,
        difficulty=difficulty,
        **kwargs
    )
