using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml;
using RecordMusicalNote.DataModel;

namespace MyWPF.Layout.Converter
{
    /// <summary>
    /// 星动转弹珠转换器 - 新版本
    /// 支持多种转换策略和难度调整
    /// </summary>
    public class IdolToPinballConverter
    {
        #region 转换设置

        public class ConversionSettings
        {
            /// <summary>难度系数 0.5-2.0</summary>
            public float DifficultyFactor { get; set; } = 1.0f;
            
            /// <summary>轨道0使用率 0-0.2</summary>
            public float Track0Rate { get; set; } = 0.1f;
            
            /// <summary>是否保留拐弯效果</summary>
            public bool PreserveCurves { get; set; } = true;
            
            /// <summary>最大连击长度</summary>
            public int MaxComboLength { get; set; } = 4;
            
            /// <summary>左右平衡 0.5-0.8 (偏向1|2的概率)</summary>
            public float LeftRightBalance { get; set; } = 0.6f;
            
            /// <summary>拐弯处理策略</summary>
            public CurveHandlingStrategy CurveStrategy { get; set; } = CurveHandlingStrategy.SplitToMultiple;
        }

        public enum CurveHandlingStrategy
        {
            SplitToMultiple,    // 拆分为多个音符
            SimplifyToLine,     // 简化为直线
            ConvertToCombo      // 转换为连击序列
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 星动转弹珠 - 新版本
        /// </summary>
        /// <param name="inputFilePath">输入的星动XML文件路径</param>
        /// <param name="settings">转换设置</param>
        public static void ConvertIdolToPinball(string inputFilePath, ConversionSettings settings = null)
        {
            if (settings == null)
                settings = new ConversionSettings();

            try
            {
                // 1. 解析星动XML
                var idolNotes = ParseIdolXml(inputFilePath);

                // 2. 转换为弹珠音符
                var pinballNotes = ConvertNotes(idolNotes, settings);

                // 3. 应用难度调整
                pinballNotes = ApplyDifficultyAdjustment(pinballNotes, settings);

                // 4. 生成SonId连接
                AssignSonIds(pinballNotes, settings);

                // 5. 生成输出文件名（使用pinball开头）
                string fileName = System.IO.Path.GetFileNameWithoutExtension(inputFilePath);
                string directory = System.IO.Path.GetDirectoryName(inputFilePath);
                string outputPath = System.IO.Path.Combine(directory, $"pinball_{fileName}.xml");

                // 6. 保存弹珠XML（保留原文件结构，只替换NoteInfo部分）
                SavePinballXmlWithOriginalStructure(inputFilePath, outputPath, pinballNotes);
            }
            catch (Exception ex)
            {
                throw new Exception($"转换失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 解析星动XML文件
        /// </summary>
        private static List<IdolNoteInfo> ParseIdolXml(string filePath)
        {
            var notes = new List<IdolNoteInfo>();
            var doc = new XmlDocument();
            doc.Load(filePath);

            var noteNodes = doc.SelectNodes("//Note");
            foreach (XmlNode node in noteNodes)
            {
                var note = new IdolNoteInfo
                {
                    Bar = int.Parse(node.Attributes["Bar"]?.Value ?? "1"),
                    Pos = double.Parse(node.Attributes["Pos"]?.Value ?? "0"),
                    FromTrack = node.Attributes["from_track"]?.Value ?? "1",
                    TargetTrack = node.Attributes["target_track"]?.Value ?? "1",
                    EndTrack = node.Attributes["target_track"]?.Value ?? "1", // 对于简单音符，EndTrack等于TargetTrack
                    NoteType = node.Attributes["note_type"]?.Value ?? "short",
                    EndBar = int.Parse(node.Attributes["EndBar"]?.Value ?? node.Attributes["Bar"]?.Value ?? "1"),
                    EndPos = double.Parse(node.Attributes["EndPos"]?.Value ?? node.Attributes["Pos"]?.Value ?? "0")
                };
                Console.WriteLine($"[DEBUG] 解析音符: Bar={note.Bar}, Pos={note.Pos}, Type={note.NoteType}, FromTrack={note.FromTrack}, TargetTrack={note.TargetTrack}");
                notes.Add(note);
            }

            return notes.OrderBy(n => n.Bar).ThenBy(n => n.Pos).ToList();
        }

        /// <summary>
        /// 解析关卡信息
        /// </summary>
        private static LevelInfo ParseLevelInfo(string filePath)
        {
            var doc = new XmlDocument();
            doc.Load(filePath);

            var levelNode = doc.SelectSingleNode("//LevelInfo");
            return new LevelInfo
            {
                BPM = double.Parse(levelNode?.Attributes["BPM"]?.Value ?? "120"),
                BeatPerBar = int.Parse(levelNode?.Attributes["BeatPerBar"]?.Value ?? "4"),
                BeatLen = int.Parse(levelNode?.Attributes["BeatLen"]?.Value ?? "16"),
                TrackCount = 3, // 弹珠模式固定3轨道
                SongName = levelNode?.Attributes["SongName"]?.Value ?? "未知歌曲",
                Artist = levelNode?.Attributes["Artist"]?.Value ?? "未知艺术家"
            };
        }

        /// <summary>
        /// 转换音符
        /// </summary>
        private static List<PinBallNoteInfo> ConvertNotes(List<IdolNoteInfo> idolNotes, ConversionSettings settings)
        {
            var pinballNotes = new List<PinBallNoteInfo>();
            var random = new Random(42); // 固定种子确保一致性

            // 按时间分组处理，避免重叠
            var timeGroups = idolNotes.GroupBy(n => new { n.Bar, Pos = (int)n.Pos })
                                     .OrderBy(g => g.Key.Bar)
                                     .ThenBy(g => g.Key.Pos);

            foreach (var timeGroup in timeGroups)
            {
                var notesAtSameTime = timeGroup.ToList();
                var convertedNotes = ConvertSimultaneousNotes(notesAtSameTime, settings, random);
                pinballNotes.AddRange(convertedNotes);
            }

            return pinballNotes;
        }

        /// <summary>
        /// 转换同时出现的音符，处理重叠冲突
        /// </summary>
        private static List<PinBallNoteInfo> ConvertSimultaneousNotes(List<IdolNoteInfo> simultaneousNotes, ConversionSettings settings, Random random)
        {
            var result = new List<PinBallNoteInfo>();
            var usedPositions = new HashSet<int>(); // 记录已使用的位置

            // 按优先级排序：拐弯音符 > 长音符 > 短音符
            var sortedNotes = simultaneousNotes.OrderByDescending(n => GetNotePriority(n)).ToList();

            foreach (var idolNote in sortedNotes)
            {
                if (IsSimpleNote(idolNote))
                {
                    var pinballNote = ConvertSimpleNoteWithConflictCheck(idolNote, settings, random, usedPositions);
                    if (pinballNote != null)
                    {
                        result.Add(pinballNote);
                        usedPositions.Add(GetNotePosition(pinballNote));
                    }
                }
                else
                {
                    var convertedNotes = ConvertCurveNoteWithConflictCheck(idolNote, settings, random, usedPositions);
                    foreach (var note in convertedNotes)
                    {
                        if (!usedPositions.Contains(GetNotePosition(note)))
                        {
                            result.Add(note);
                            usedPositions.Add(GetNotePosition(note));
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 获取音符优先级
        /// </summary>
        private static int GetNotePriority(IdolNoteInfo note)
        {
            if (!IsSimpleNote(note)) return 3; // 拐弯音符最高优先级
            if (note.NoteType == "长条" || note.NoteType == "long") return 2; // 长音符中等优先级
            return 1; // 短音符最低优先级
        }

        /// <summary>
        /// 获取弹珠音符的位置标识
        /// </summary>
        private static int GetNotePosition(PinBallNoteInfo note)
        {
            // 根据EndArea确定位置区域
            switch (note.EndArea)
            {
                case "": return 0; // 轨道0
                case "1|2": return 1; // 区域1|2
                case "3|4": return 2; // 区域3|4
                default: return -1;
            }
        }

        /// <summary>
        /// 判断是否为简单音符（不拐弯）
        /// </summary>
        private static bool IsSimpleNote(IdolNoteInfo note)
        {
            return note.FromTrack == note.TargetTrack && note.TargetTrack == note.EndTrack;
        }

        /// <summary>
        /// 转换简单音符
        /// </summary>
        private static PinBallNoteInfo ConvertSimpleNote(IdolNoteInfo idolNote, ConversionSettings settings, Random random)
        {
            int originalTrack = ConvertTrackNameToNumber(idolNote.FromTrack);
            int newPosition = MapToTwoTrackSystem(originalTrack, 4, settings, random); // 星动是4轨道

            var pinballNote = new PinBallNoteInfo
            {
                bar = idolNote.Bar,
                pos = (int)idolNote.Pos,
                NoteType = ConvertNoteType(idolNote.NoteType),
                EndArea = GetEndArea(newPosition),
                MoveTime = 3
            };

            // 如果是长音符，设置EndBar和EndPos
            if (ConvertNoteType(idolNote.NoteType) == "3") // PinballLong
            {
                pinballNote.EndBar = idolNote.EndBar ?? idolNote.Bar;
                pinballNote.EndPos = (int)(idolNote.EndPos ?? idolNote.Pos);
            }

            return pinballNote;
        }

        /// <summary>
        /// 转换简单音符（带冲突检测）
        /// </summary>
        private static PinBallNoteInfo ConvertSimpleNoteWithConflictCheck(IdolNoteInfo idolNote, ConversionSettings settings, Random random, HashSet<int> usedPositions)
        {
            int originalTrack = ConvertTrackNameToNumber(idolNote.FromTrack);

            // 尝试多个位置，避免冲突
            var candidatePositions = GetCandidatePositions(originalTrack, 6, settings, random);

            foreach (int position in candidatePositions)
            {
                int positionId = GetPositionId(position);
                if (!usedPositions.Contains(positionId))
                {
                    var pinballNote = new PinBallNoteInfo
                    {
                        bar = idolNote.Bar,
                        pos = (int)idolNote.Pos,
                        NoteType = ConvertNoteType(idolNote.NoteType),
                        EndArea = GetEndArea(position),
                        MoveTime = 3
                    };

                    // 如果是长音符，设置EndBar和EndPos
                    if (ConvertNoteType(idolNote.NoteType) == "3") // PinballLong
                    {
                        pinballNote.EndBar = idolNote.EndBar ?? idolNote.Bar;
                        pinballNote.EndPos = (int)(idolNote.EndPos ?? idolNote.Pos);
                    }

                    return pinballNote;
                }
            }

            // 如果所有位置都冲突，则丢弃这个音符（或延迟处理）
            return null;
        }

        /// <summary>
        /// 转换拐弯音符（带冲突检测）
        /// </summary>
        private static List<PinBallNoteInfo> ConvertCurveNoteWithConflictCheck(IdolNoteInfo idolNote, ConversionSettings settings, Random random, HashSet<int> usedPositions)
        {
            var result = new List<PinBallNoteInfo>();

            switch (settings.CurveStrategy)
            {
                case CurveHandlingStrategy.SplitToMultiple:
                    result.AddRange(SplitCurveToMultipleWithConflictCheck(idolNote, settings, random, usedPositions));
                    break;
                case CurveHandlingStrategy.SimplifyToLine:
                    var simplified = SimplifyCurveToLineWithConflictCheck(idolNote, settings, random, usedPositions);
                    if (simplified != null) result.Add(simplified);
                    break;
                case CurveHandlingStrategy.ConvertToCombo:
                    result.AddRange(ConvertCurveToComboWithConflictCheck(idolNote, settings, random, usedPositions));
                    break;
            }

            return result;
        }

        /// <summary>
        /// 获取候选位置列表（按优先级排序）
        /// </summary>
        private static List<int> GetCandidatePositions(int originalTrack, int totalTracks, ConversionSettings settings, Random random)
        {
            var candidates = new List<int>();

            // 首选位置：基于原轨道的映射
            int primaryPosition = MapToTwoTrackSystemDeterministic(originalTrack, totalTracks);
            candidates.Add(primaryPosition);

            // 备选位置：同区域的另一个轨道
            if (primaryPosition == 1) candidates.Add(2);
            else if (primaryPosition == 2) candidates.Add(1);
            else if (primaryPosition == 3) candidates.Add(4);
            else if (primaryPosition == 4) candidates.Add(3);

            // 最后选择：轨道0
            if (random.NextDouble() < settings.Track0Rate * 2) // 冲突时增加轨道0使用率
            {
                candidates.Add(0);
            }

            // 紧急备选：其他区域
            if (primaryPosition <= 2)
            {
                candidates.AddRange(new[] { 3, 4 });
            }
            else
            {
                candidates.AddRange(new[] { 1, 2 });
            }

            return candidates.Distinct().ToList();
        }

        /// <summary>
        /// 确定性的轨道映射（不使用随机）
        /// </summary>
        private static int MapToTwoTrackSystemDeterministic(int originalTrack, int totalTracks)
        {
            if (originalTrack <= totalTracks / 2)
            {
                return originalTrack % 2 == 1 ? 1 : 2; // 奇数→1，偶数→2
            }
            else
            {
                return originalTrack % 2 == 1 ? 3 : 4; // 奇数→3，偶数→4
            }
        }

        /// <summary>
        /// 获取位置ID（用于冲突检测）
        /// </summary>
        private static int GetPositionId(int position)
        {
            switch (position)
            {
                case 0: return 0; // 轨道0
                case 1:
                case 2: return 1; // 区域1|2
                case 3:
                case 4: return 2; // 区域3|4
                default: return -1;
            }
        }

        /// <summary>
        /// 转换拐弯音符
        /// </summary>
        private static List<PinBallNoteInfo> ConvertCurveNote(IdolNoteInfo idolNote, ConversionSettings settings, Random random)
        {
            var result = new List<PinBallNoteInfo>();

            switch (settings.CurveStrategy)
            {
                case CurveHandlingStrategy.SplitToMultiple:
                    result.AddRange(SplitCurveToMultiple(idolNote, settings, random));
                    break;
                case CurveHandlingStrategy.SimplifyToLine:
                    result.Add(SimplifyCurveToLine(idolNote, settings, random));
                    break;
                case CurveHandlingStrategy.ConvertToCombo:
                    result.AddRange(ConvertCurveToCombo(idolNote, settings, random));
                    break;
            }

            return result;
        }

        /// <summary>
        /// 策略A：拆分拐弯音符为多个音符（带冲突检测）
        /// </summary>
        private static List<PinBallNoteInfo> SplitCurveToMultipleWithConflictCheck(IdolNoteInfo idolNote, ConversionSettings settings, Random random, HashSet<int> usedPositions)
        {
            var notes = new List<PinBallNoteInfo>();

            int fromTrack = ConvertTrackNameToNumber(idolNote.FromTrack);
            int endTrack = ConvertTrackNameToNumber(idolNote.EndTrack);

            // 获取起始和结束位置的候选
            var startCandidates = GetCandidatePositions(fromTrack, 6, settings, random);
            var endCandidates = GetCandidatePositions(endTrack, 6, settings, random);

            // 选择不冲突的位置
            int startPos = startCandidates.FirstOrDefault(p => !usedPositions.Contains(GetPositionId(p)));
            int endPos = endCandidates.FirstOrDefault(p => !usedPositions.Contains(GetPositionId(p)) && GetPositionId(p) != GetPositionId(startPos));

            if (startPos != 0 || endPos != 0) // 至少有一个有效位置
            {
                // 起始音符 - 滑动弹珠
                var startNote = new PinBallNoteInfo
                {
                    bar = idolNote.Bar,
                    pos = (int)idolNote.Pos,
                    NoteType = "2", // PinballSlip
                    EndArea = GetEndArea(startPos),
                    MoveTime = 3
                };
                notes.Add(startNote);

                // 结束音符 - 单点弹珠
                if (endPos != 0)
                {
                    var endNote = new PinBallNoteInfo
                    {
                        bar = idolNote.EndBar ?? idolNote.Bar,
                        pos = (int)(idolNote.EndPos ?? idolNote.Pos),
                        NoteType = "1", // PinballSingle
                        EndArea = GetEndArea(endPos),
                        MoveTime = 3
                    };
                    notes.Add(endNote);
                }
            }

            return notes;
        }

        /// <summary>
        /// 策略B：简化拐弯为直线（带冲突检测）
        /// </summary>
        private static PinBallNoteInfo SimplifyCurveToLineWithConflictCheck(IdolNoteInfo idolNote, ConversionSettings settings, Random random, HashSet<int> usedPositions)
        {
            int fromTrack = ConvertTrackNameToNumber(idolNote.FromTrack);
            var candidates = GetCandidatePositions(fromTrack, 6, settings, random);

            int position = candidates.FirstOrDefault(p => !usedPositions.Contains(GetPositionId(p)));

            if (position != 0)
            {
                return new PinBallNoteInfo
                {
                    bar = idolNote.Bar,
                    pos = (int)idolNote.Pos,
                    NoteType = "2", // PinballSlip
                    EndArea = GetEndArea(position),
                    MoveTime = 3
                };
            }

            return null;
        }

        /// <summary>
        /// 策略C：转换为连击序列（带冲突检测）
        /// </summary>
        private static List<PinBallNoteInfo> ConvertCurveToComboWithConflictCheck(IdolNoteInfo idolNote, ConversionSettings settings, Random random, HashSet<int> usedPositions)
        {
            var notes = new List<PinBallNoteInfo>();
            int comboLength = Math.Min(settings.MaxComboLength, 3);
            var availablePositions = new List<int> { 1, 2, 3, 4, 0 }.Where(p => !usedPositions.Contains(GetPositionId(p))).ToList();

            for (int i = 0; i < Math.Min(comboLength, availablePositions.Count); i++)
            {
                var note = new PinBallNoteInfo
                {
                    bar = idolNote.Bar,
                    pos = (int)(idolNote.Pos + i * 8), // 间隔8个位置
                    NoteType = i == 0 ? "2" : "1", // 第一个是滑动，其他是单点
                    EndArea = GetEndArea(availablePositions[i]),
                    MoveTime = 3
                };
                notes.Add(note);
            }

            return notes;
        }

        /// <summary>
        /// 策略A：拆分拐弯音符为多个音符
        /// </summary>
        private static List<PinBallNoteInfo> SplitCurveToMultiple(IdolNoteInfo idolNote, ConversionSettings settings, Random random)
        {
            var notes = new List<PinBallNoteInfo>();

            int fromTrack = ConvertTrackNameToNumber(idolNote.FromTrack);
            int targetTrack = ConvertTrackNameToNumber(idolNote.TargetTrack);
            int endTrack = ConvertTrackNameToNumber(idolNote.EndTrack ?? idolNote.TargetTrack);

            // 起始音符 - 滑动弹珠
            var startNote = new PinBallNoteInfo
            {
                bar = idolNote.Bar,
                pos = (int)idolNote.Pos,
                NoteType = "2", // PinballSlip
                EndArea = GetEndArea(MapToTwoTrackSystem(fromTrack, 4, settings, random)),
                MoveTime = 3
            };
            notes.Add(startNote);

            // 中间过渡 - 使用轨道0
            if (ShouldUseTrack0(settings, random))
            {
                var middleNote = new PinBallNoteInfo
                {
                    bar = idolNote.Bar,
                    pos = (int)(idolNote.Pos + 16), // 稍后一点
                    NoteType = "1", // PinballSingle
                    EndArea = "",
                    MoveTime = 3
                };
                notes.Add(middleNote);
            }

            // 结束音符 - 单点弹珠
            var endNote = new PinBallNoteInfo
            {
                bar = idolNote.EndBar ?? idolNote.Bar,
                pos = (int)(idolNote.EndPos ?? idolNote.Pos),
                NoteType = "1", // PinballSingle
                EndArea = GetEndArea(MapToTwoTrackSystem(endTrack, 4, settings, random)),
                MoveTime = 3
            };
            notes.Add(endNote);

            return notes;
        }

        /// <summary>
        /// 策略B：简化拐弯为直线
        /// </summary>
        private static PinBallNoteInfo SimplifyCurveToLine(IdolNoteInfo idolNote, ConversionSettings settings, Random random)
        {
            int fromTrack = ConvertTrackNameToNumber(idolNote.FromTrack);
            int newPosition = MapToTwoTrackSystem(fromTrack, 4, settings, random);

            return new PinBallNoteInfo
            {
                bar = idolNote.Bar,
                pos = (int)idolNote.Pos,
                NoteType = "2", // PinballSlip
                EndArea = GetEndArea(newPosition),
                MoveTime = 3
            };
        }

        /// <summary>
        /// 策略C：转换为连击序列
        /// </summary>
        private static List<PinBallNoteInfo> ConvertCurveToCombo(IdolNoteInfo idolNote, ConversionSettings settings, Random random)
        {
            var notes = new List<PinBallNoteInfo>();
            int comboLength = Math.Min(settings.MaxComboLength, 3);

            for (int i = 0; i < comboLength; i++)
            {
                var note = new PinBallNoteInfo
                {
                    bar = idolNote.Bar,
                    pos = (int)(idolNote.Pos + i * 8), // 间隔8个位置
                    NoteType = i == 0 ? "2" : "1", // 第一个是滑动，其他是单点
                    EndArea = GetEndArea(GetRandomPosition(settings, random)),
                    MoveTime = 3
                };
                notes.Add(note);
            }

            return notes;
        }

        /// <summary>
        /// 将轨道名称转换为数字
        /// </summary>
        private static int ConvertTrackNameToNumber(string trackName)
        {
            switch (trackName)
            {
                case "Left1":
                    return 1;
                case "Left2":
                    return 2;
                case "Right1":
                    return 3;
                case "Right2":
                    return 4;
                default:
                    return 1; // 默认值
            }
        }

        /// <summary>
        /// 轨道映射到2轨道系统
        /// </summary>
        private static int MapToTwoTrackSystem(int originalTrack, int totalTracks, ConversionSettings settings, Random random)
        {
            // 检查是否使用轨道0
            if (ShouldUseTrack0(settings, random))
            {
                return 0;
            }

            // 基于左右平衡设置进行映射
            bool isLeftTrack = originalTrack <= 2; // 1,2是左轨道，3,4是右轨道

            // 根据左右平衡设置决定是否保持原有左右分布
            if (random.NextDouble() < settings.LeftRightBalance)
            {
                // 保持原有左右分布
                if (isLeftTrack)
                {
                    return random.Next(0, 2) == 0 ? 1 : 2; // 左区域
                }
                else
                {
                    return random.Next(0, 2) == 0 ? 3 : 4; // 右区域
                }
            }
            else
            {
                // 随机分配到任意区域
                return random.Next(1, 5); // 1-4随机
            }
        }

        /// <summary>
        /// 判断是否使用轨道0
        /// </summary>
        private static bool ShouldUseTrack0(ConversionSettings settings, Random random)
        {
            return random.NextDouble() < settings.Track0Rate;
        }

        /// <summary>
        /// 获取随机位置（考虑左右平衡）
        /// </summary>
        private static int GetRandomPosition(ConversionSettings settings, Random random)
        {
            if (random.NextDouble() < settings.Track0Rate)
            {
                return 0; // 轨道0
            }

            if (random.NextDouble() < settings.LeftRightBalance)
            {
                return random.Next(0, 2) == 0 ? 1 : 2; // 偏向1|2
            }
            else
            {
                return random.Next(0, 2) == 0 ? 3 : 4; // 偏向3|4
            }
        }

        /// <summary>
        /// 转换音符类型
        /// </summary>
        private static string ConvertNoteType(string idolNoteType)
        {
            string result;
            switch (idolNoteType)
            {
                case "点":
                case "short":
                    result = "1"; // PinballSingle
                    break;
                case "长条":
                case "long":
                    result = "3"; // PinballLong
                    Console.WriteLine($"[DEBUG] 发现长音符: {idolNoteType} -> {result}");
                    break;
                case "箭头":
                case "slip":
                    result = "2"; // PinballSlip
                    break;
                default:
                    result = "1";
                    break;
            }
            return result;
        }

        /// <summary>
        /// 获取EndArea
        /// </summary>
        private static string GetEndArea(int position)
        {
            switch (position)
            {
                case 0:
                    return ""; // 轨道0独立
                case 1:
                case 2:
                    return "1|2"; // 区域1|2
                case 3:
                case 4:
                    return "3|4"; // 区域3|4
                default:
                    return "";
            }
        }

        /// <summary>
        /// 应用难度调整
        /// </summary>
        private static List<PinBallNoteInfo> ApplyDifficultyAdjustment(List<PinBallNoteInfo> notes, ConversionSettings settings)
        {
            if (Math.Abs(settings.DifficultyFactor - 1.0f) < 0.01f)
                return notes; // 无需调整

            var adjustedNotes = new List<PinBallNoteInfo>();
            var random = new Random(123);

            if (settings.DifficultyFactor < 1.0f)
            {
                // 降低难度：减少音符
                int keepCount = (int)(notes.Count * settings.DifficultyFactor);
                var indicesToKeep = Enumerable.Range(0, notes.Count)
                    .OrderBy(x => random.Next())
                    .Take(keepCount)
                    .OrderBy(x => x)
                    .ToList();

                foreach (int index in indicesToKeep)
                {
                    adjustedNotes.Add(notes[index]);
                }
            }
            else
            {
                // 提高难度：保持原有音符，可能增加一些
                adjustedNotes.AddRange(notes);

                if (settings.DifficultyFactor > 1.2f)
                {
                    // 在空隙中添加额外音符
                    AddExtraNotes(adjustedNotes, settings, random);
                }
            }

            return adjustedNotes.OrderBy(n => n.bar).ThenBy(n => n.pos).ToList();
        }

        /// <summary>
        /// 添加额外音符（提高难度）
        /// </summary>
        private static void AddExtraNotes(List<PinBallNoteInfo> notes, ConversionSettings settings, Random random)
        {
            var extraNotes = new List<PinBallNoteInfo>();

            for (int i = 0; i < notes.Count - 1; i++)
            {
                var current = notes[i];
                var next = notes[i + 1];

                // 如果两个音符间隔较大，添加中间音符
                if (next.bar > current.bar || (next.bar == current.bar && next.pos - current.pos > 16))
                {
                    var middleNote = new PinBallNoteInfo
                    {
                        bar = current.bar,
                        pos = current.pos + 8,
                        NoteType = "1", // PinballSingle
                        EndArea = GetEndArea(GetRandomPosition(settings, random)),
                        MoveTime = 3
                    };
                    extraNotes.Add(middleNote);
                }
            }

            notes.AddRange(extraNotes);
        }

        /// <summary>
        /// 分配SonId连接
        /// </summary>
        private static void AssignSonIds(List<PinBallNoteInfo> notes, ConversionSettings settings)
        {
            var slipNotes = notes.Where((n, i) => n.NoteType == "2").Select((n, i) => new { Note = n, Index = i }).ToList();

            foreach (var slip in slipNotes)
            {
                // 寻找后续的单点弹珠作为目标
                var targetNote = notes.Skip(slip.Index + 1)
                    .FirstOrDefault(n => n.NoteType == "1" &&
                                        (n.bar > slip.Note.bar ||
                                         (n.bar == slip.Note.bar && n.pos > slip.Note.pos)));

                if (targetNote != null)
                {
                    int targetIndex = notes.IndexOf(targetNote);
                    slip.Note.son = targetIndex + 1; // SonId从1开始
                }
            }
        }

        /// <summary>
        /// 保存弹珠XML
        /// </summary>
        private static void SavePinballXml(string outputPath, List<PinBallNoteInfo> notes, LevelInfo levelInfo)
        {
            var doc = new XmlDocument();
            var root = doc.CreateElement("Level");
            doc.AppendChild(root);

            // 添加关卡信息
            var levelInfoElement = doc.CreateElement("LevelInfo");
            levelInfoElement.SetAttribute("BPM", levelInfo.BPM.ToString());
            levelInfoElement.SetAttribute("BeatPerBar", levelInfo.BeatPerBar.ToString());
            levelInfoElement.SetAttribute("BeatLen", levelInfo.BeatLen.ToString());
            levelInfoElement.SetAttribute("TrackCount", "3");
            levelInfoElement.SetAttribute("SongName", levelInfo.SongName);
            levelInfoElement.SetAttribute("Artist", levelInfo.Artist);
            root.AppendChild(levelInfoElement);

            // 添加音符信息
            var noteInfoElement = doc.CreateElement("NoteInfo");
            var normalElement = doc.CreateElement("Normal");
            normalElement.SetAttribute("PosNum", "64");
            noteInfoElement.AppendChild(normalElement);
            root.AppendChild(noteInfoElement);

            foreach (var note in notes)
            {
                var noteElement = doc.CreateElement("Note");
                noteElement.SetAttribute("Bar", note.bar.ToString());
                noteElement.SetAttribute("Pos", note.pos.ToString());
                noteElement.SetAttribute("NoteType", note.NoteType);

                if (!string.IsNullOrEmpty(note.EndArea))
                    noteElement.SetAttribute("EndArea", note.EndArea);
                if (note.son.HasValue)
                    noteElement.SetAttribute("SonId", note.son.ToString());
                if (note.MoveTime.HasValue)
                    noteElement.SetAttribute("MoveTime", note.MoveTime.ToString());

                normalElement.AppendChild(noteElement);
            }

            doc.Save(outputPath);
        }

        /// <summary>
        /// 保存弹珠XML（保留原文件结构，只替换NoteInfo部分）
        /// </summary>
        private static void SavePinballXmlWithOriginalStructure(string inputFilePath, string outputPath, List<PinBallNoteInfo> notes)
        {
            var doc = new XmlDocument();
            doc.Load(inputFilePath);

            // 找到NoteInfo节点
            var noteInfoNode = doc.SelectSingleNode("//NoteInfo");
            if (noteInfoNode != null)
            {
                // 清空NoteInfo的内容
                noteInfoNode.RemoveAll();

                // 创建新的Normal节点（不添加PosNum属性）
                var normalElement = doc.CreateElement("Normal");
                noteInfoNode.AppendChild(normalElement);

                // 添加转换后的弹珠音符
                int noteId = 1;
                foreach (var note in notes)
                {
                    var noteElement = doc.CreateElement("Note");
                    noteElement.SetAttribute("ID", noteId.ToString());
                    noteElement.SetAttribute("note_type", ConvertToCorrectNoteType(note.NoteType));
                    noteElement.SetAttribute("Bar", note.bar.ToString());
                    noteElement.SetAttribute("Pos", note.pos.ToString());

                    // 处理长音符的EndBar和EndPos
                    if (ConvertToCorrectNoteType(note.NoteType) == "PinballLong")
                    {
                        if (note.EndBar.HasValue)
                            noteElement.SetAttribute("EndBar", note.EndBar.ToString());
                        if (note.EndPos.HasValue)
                            noteElement.SetAttribute("EndPos", note.EndPos.ToString());
                    }

                    if (note.son.HasValue)
                        noteElement.SetAttribute("Son", note.son.ToString());
                    else
                        noteElement.SetAttribute("Son", "");

                    if (!string.IsNullOrEmpty(note.EndArea))
                        noteElement.SetAttribute("EndArea", note.EndArea);
                    if (note.MoveTime.HasValue)
                        noteElement.SetAttribute("MoveTime", note.MoveTime.ToString());

                    normalElement.AppendChild(noteElement);
                    noteId++;
                }
            }

            // 保存到新文件
            doc.Save(outputPath);
        }

        /// <summary>
        /// 转换为正确的弹珠音符类型
        /// </summary>
        private static string ConvertToCorrectNoteType(string noteType)
        {
            switch (noteType)
            {
                case "1":
                    return "PinballSingle";
                case "2":
                    return "PinballSlip";
                case "3":
                    return "PinballLong";
                default:
                    return "PinballSingle";
            }
        }

        #endregion
    }
}
