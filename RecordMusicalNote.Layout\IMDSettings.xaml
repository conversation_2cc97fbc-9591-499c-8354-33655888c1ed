﻿<Window x:Class="MyWPF.Layout.IMDSettings"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MyWPF.Layout"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="IMD设置" Height="300" Width="400" MinHeight="250" MinWidth="350"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <materialDesign:DialogHost Identifier="IMDSettingsDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 顶部标题栏 -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
                <DockPanel>
                    <materialDesign:PackIcon Kind="Settings" Width="20" Height="20" VerticalAlignment="Center" DockPanel.Dock="Left"/>
                    <TextBlock Text="IMD设置" FontSize="18" FontWeight="Medium" VerticalAlignment="Center" Margin="12,0,0,0"/>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- 主要内容区域 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="24">
                <materialDesign:Card Style="{StaticResource MaterialCard}">
                    <StackPanel>
                        <TextBlock Text="参数配置" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,24"/>

                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBox Grid.Row="0" Name="txtBpm"
                                   materialDesign:HintAssist.Hint="BPM (每分钟节拍数)"
                                   materialDesign:HintAssist.IsFloating="True"
                                   Style="{StaticResource MaterialTextBox}"
                                   Margin="0,0,0,16">
                                <TextBox.ToolTip>
                                    <ToolTip Content="设置音乐的BPM值，通常在60-200之间"/>
                                </TextBox.ToolTip>
                            </TextBox>

                            <TextBox Grid.Row="1" Name="txtKey"
                                   materialDesign:HintAssist.Hint="键位数量"
                                   materialDesign:HintAssist.IsFloating="True"
                                   Style="{StaticResource MaterialTextBox}">
                                <TextBox.ToolTip>
                                    <ToolTip Content="设置游戏的键位数量，通常为4或5"/>
                                </TextBox.ToolTip>
                            </TextBox>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
            </ScrollViewer>

            <!-- 底部按钮区域 -->
            <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Name="btnCancel" Content="取消"
                          Style="{StaticResource MaterialDesignFlatButton}"
                          Margin="0,0,8,0" MinWidth="80">
                        <Button.ToolTip>
                            <ToolTip Content="取消设置并关闭窗口"/>
                        </Button.ToolTip>
                    </Button>

                    <Button Name="btnConfrim" Content="确定"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          MinWidth="80">
                        <Button.ToolTip>
                            <ToolTip Content="保存设置并关闭窗口"/>
                        </Button.ToolTip>
                    </Button>
                </StackPanel>
            </materialDesign:ColorZone>
        </Grid>
    </materialDesign:DialogHost>
</Window>
