﻿<Window x:Class="RecordMusicalNote.SetBPMMultiple"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:RecordMusicalNote"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="设置BPM倍数" Height="200" Width="350"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        FontFamily="{DynamicResource MaterialDesignFont}"
        WindowStartupLocation="CenterOwner">

    <materialDesign:Card Margin="16" Padding="24">
        <StackPanel>
            <!-- 标题 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                <materialDesign:PackIcon Kind="Speedometer" Width="24" Height="24" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Text="设置BPM倍数" Style="{StaticResource MaterialDesignHeadline6TextBlock}" VerticalAlignment="Center"/>
            </StackPanel>

            <!-- 输入区域 -->
            <TextBox Name="txtBPMMultiple"
                   materialDesign:HintAssist.Hint="请输入BPM倍数"
                   materialDesign:HintAssist.IsFloating="True"
                   Style="{StaticResource MaterialDesignTextBox}"
                   Margin="0,0,0,24"/>

            <!-- 按钮区域 -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Name="btnClear" Content="清空" Click="btnClear_Click"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Margin="0,0,8,0" Width="80"/>
                <Button Name="btnConfirm" Content="确定" Click="btnConfirm_Click"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Width="80"/>
            </StackPanel>
        </StackPanel>
    </materialDesign:Card>
</Window>
