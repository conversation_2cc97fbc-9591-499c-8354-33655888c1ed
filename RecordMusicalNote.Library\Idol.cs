﻿using RecordMusicalNote;
using RecordMusicalNote.DataModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote.Library
{
    public class Idol : IIdol
    {
        public Idol()
        {
            _model.IdolNoteInfoID = -1;
        }
        IdolNoteInfo _model = new IdolNoteInfo();
        public Idol(IdolNoteInfo model)
        {
            _model = model;
        }
        public int IdolNoteInfoID
        {
            get
            {
                return _model.IdolNoteInfoID;
            }
        }

        public int LevelInfoId
        {
            get
            {
                if (_model.LevelInfoId == null)
                    return -1;
                return _model.LevelInfoId.Value;
            }

            set
            {
                _model.LevelInfoId = value;
            }
        }

        public int Bar
        {
            get
            {
                return _model.Bar.Value;
            }

            set
            {
                _model.Bar = value;
            }
        }

        public int CombineNoteNum
        {
            get
            {
                if (_model.CombineNoteNum == null)
                    return -1;
                return _model.CombineNoteNum.Value;
            }

            set
            {
                _model.CombineNoteNum = value;
            }
        }

        public int EndBar
        {
            get
            {
                if (_model.EndPos == null) return 0;
                return _model.EndBar.Value;
            }

            set
            {
                _model.EndBar = value;
            }
        }

        public double EndPos
        {
            get
            {
                if (_model.EndPos == null) return 0;
                return _model.EndPos.Value;
            }

            set
            {
                _model.EndPos = value;
            }
        }

        public string EndTrack
        {
            get
            {
                return _model.EndTrack;
            }

            set
            {
                _model.EndTrack = value;
            }
        }

        public string FromTrack
        {
            get
            {
                return _model.FromTrack;
            }

            set
            {
                _model.FromTrack = value;
            }
        }


        public string NoteType
        {
            get
            {
                return _model.NoteType;
            }

            set
            {
                _model.NoteType = value;
            }
        }

        public double Pos
        {
            get
            {
                return _model.Pos.Value;
            }

            set
            {
                _model.Pos = value;
            }
        }

        public string TargetTrack
        {
            get
            {
                return _model.TargetTrack;
            }

            set
            {
                _model.TargetTrack = value;
            }
        }
        public override string ToString()
        {
           if(this.NoteType== "long")
            {
                return "<note Bar=\"" + Bar + "\"  " + "Pos=\"" + Pos + "\"  " + "from_track=\"" + FromTrack + "\"  " +
                      "target_track=\"" + TargetTrack + "\"  " + "note_type=\"" + NoteType + "\"  "
                      + "EndBar=\"" + EndBar + "\"  " + "EndPos=\"" + EndPos + "\"  " + "/>";
            }
           else if(this.NoteType == "short")
            {
                return "<note Bar=\"" + Bar + "\"  " + "Pos=\"" + Pos + "\"  " + "from_track=\"" + FromTrack + "\"  " +
                    "target_track=\"" + TargetTrack + "\"  " + "note_type=\"" + NoteType + "\"  " + "/>";
            }
            else if (this.NoteType == "slip")
            {
                return "<note Bar=\"" + Bar + "\"  " + "Pos=\"" + Pos + "\"  " +
                    "target_track=\"" + TargetTrack + "\"  " + "end_track=\"" + EndTrack + "\"  " 
                    + "note_type=\"" + NoteType + "\"  " + "/>";
            }
            return "";
        }
    }
}
