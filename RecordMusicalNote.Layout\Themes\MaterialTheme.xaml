<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- 自定义颜色 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#673AB7"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="#9C27B0"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#512DA8"/>
    <SolidColorBrush x:Key="AccentBrush" Color="#CDDC39"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>

    <!-- 卡片样式 -->
    <Style x:Key="MaterialCard" TargetType="materialDesign:Card">
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
        <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
    </Style>

    <!-- 大卡片样式 -->
    <Style x:Key="MaterialLargeCard" TargetType="materialDesign:Card" BasedOn="{StaticResource MaterialCard}">
        <Setter Property="Margin" Value="16"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth3"/>
    </Style>

    <!-- 按钮样式 -->
    <Style x:Key="MaterialRaisedButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Height" Value="36"/>
        <Setter Property="MinWidth" Value="88"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <Style x:Key="MaterialOutlinedButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Height" Value="36"/>
        <Setter Property="MinWidth" Value="88"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <Style x:Key="MaterialFlatButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Height" Value="36"/>
        <Setter Property="MinWidth" Value="88"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 文本框样式 -->
    <Style x:Key="MaterialTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
    </Style>

    <Style x:Key="MaterialFilledTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignFilledTextBox}">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
    </Style>

    <!-- 标签样式 -->
    <Style x:Key="MaterialLabel" TargetType="Label">
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="4"/>
    </Style>

    <!-- 芯片样式 -->
    <Style x:Key="MaterialChip" TargetType="materialDesign:Chip">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 键位芯片样式 -->
    <Style x:Key="KeyChip" TargetType="materialDesign:Chip" BasedOn="{StaticResource MaterialChip}">
        <Setter Property="Background" Value="{StaticResource MaterialDesignChipBackground}"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="MinWidth" Value="60"/>
    </Style>

    <!-- 活跃键位芯片样式 -->
    <Style x:Key="ActiveKeyChip" TargetType="materialDesign:Chip" BasedOn="{StaticResource KeyChip}">
        <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <!-- 滑块样式 -->
    <Style x:Key="MaterialSlider" TargetType="Slider" BasedOn="{StaticResource MaterialDesignSlider}">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Height" Value="20"/>
    </Style>

    <!-- 单选按钮样式 -->
    <Style x:Key="MaterialRadioButton" TargetType="RadioButton" BasedOn="{StaticResource MaterialDesignRadioButton}">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 复选框样式 -->
    <Style x:Key="MaterialCheckBox" TargetType="CheckBox" BasedOn="{StaticResource MaterialDesignCheckBox}">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 工具栏按钮样式 -->
    <Style x:Key="MaterialToolButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignToolButton}">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
    </Style>

    <!-- 菜单项样式 -->
    <Style x:Key="MaterialDesignMenuItem" TargetType="MenuItem" BasedOn="{StaticResource MaterialDesignMenuItem}">
        <Setter Property="Margin" Value="2"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Height" Value="36"/>
    </Style>

    <!-- 状态指示器样式 -->
    <Style x:Key="StatusIndicator" TargetType="Border">
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Width" Value="24"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="Margin" Value="4"/>
    </Style>

    <!-- 成功状态 -->
    <Style x:Key="SuccessIndicator" TargetType="Border" BasedOn="{StaticResource StatusIndicator}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
    </Style>

    <!-- 警告状态 -->
    <Style x:Key="WarningIndicator" TargetType="Border" BasedOn="{StaticResource StatusIndicator}">
        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
    </Style>

    <!-- 错误状态 -->
    <Style x:Key="ErrorIndicator" TargetType="Border" BasedOn="{StaticResource StatusIndicator}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
    </Style>

</ResourceDictionary>
