# 核心依赖
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# MIDI处理
pretty_midi>=0.2.9
mido>=1.2.10
music21>=8.1.0

# 机器学习
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0
scikit-learn>=1.1.0
tensorflow>=2.9.0

# 音频处理
librosa>=0.9.0
soundfile>=0.10.0
audioread>=2.1.9

# 数据处理和可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Web框架和API
flask>=2.2.0
flask-cors>=3.0.10
requests>=2.28.0

# 配置和工具
pyyaml>=6.0
tqdm>=4.64.0
click>=8.1.0
colorama>=0.4.5

# 开发工具
pytest>=7.1.0
black>=22.0.0
flake8>=5.0.0
mypy>=0.971

# 可选：GPU加速
# torch-audio  # 如果需要GPU音频处理
# cupy-cuda11x  # 如果有NVIDIA GPU

# 数据科学
jupyter>=1.0.0
ipykernel>=6.15.0
notebook>=6.4.0
