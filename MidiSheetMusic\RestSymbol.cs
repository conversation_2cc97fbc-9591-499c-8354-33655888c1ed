/*
 * Copyright (c) 2007-2008 <PERSON><PERSON><PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License version 2.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace MidiSheetMusic {


/* 
 * A Rest symbol represents a rest - whole, half, quarter, or eighth.
 * The Rest symbol has a starttime and a duration, just like a regular
 * note.
 */
public class RestSymbol : MusicSymbol {
    int starttime;
    NoteDuration duration;
    int width;


    public RestSymbol(int start, NoteDuration dur) {
        starttime = start;
        duration = dur; 
        width = MinWidth;
    }

    public override int StartTime { 
        get { return starttime; }
    }

    public override int Width {
        get { return width; }
        set { width = value; }
    }

    public override int MinWidth {
        get { return 2 * SheetMusic.NoteHeight + 
              SheetMusic.NoteHeight/2;
        }
    }

    public override int AboveStaff { 
        get { return 0; }
    }

    public override int BelowStaff { 
        get { return 0; }
    }

    public override 
    void Draw(Graphics g, Pen pen, int ytop) {
        g.TranslateTransform(Width - MinWidth, 0);
        g.TranslateTransform(SheetMusic.NoteHeight/2, 0);

        if (duration == NoteDuration.Whole) {
            DrawWhole(g, pen, ytop);
        }
        else if (duration == NoteDuration.Half) {
            DrawHalf(g, pen, ytop);
        }
        else if (duration == NoteDuration.Quarter) {
            DrawQuarter(g, pen, ytop);
        }
        else if (duration == NoteDuration.Eighth) {
            DrawEighth(g, pen, ytop);
        }
        g.TranslateTransform(-SheetMusic.NoteHeight/2, 0);
        g.TranslateTransform(-(Width - MinWidth), 0);
    }


    /* Draw a whole rest symbol, a rectangle below a staff line. */
    public void DrawWhole(Graphics g, Pen pen, int ytop) {
        int y = ytop + SheetMusic.NoteHeight;

        g.FillRectangle(Brushes.Black, 0, y, 
                        SheetMusic.NoteWidth, SheetMusic.NoteHeight/2);
    }

    /* Draw a half rest symbol, a rectangle above a staff line. */
    public void DrawHalf(Graphics g, Pen pen, int ytop) {
        int y = ytop + SheetMusic.NoteHeight + SheetMusic.NoteHeight/2;

        g.FillRectangle(Brushes.Black, 0, y, 
                        SheetMusic.NoteWidth, SheetMusic.NoteHeight/2);
    }

    /* Draw a quarter rest symbol.  */
    public void DrawQuarter(Graphics g, Pen pen, int ytop) {
        pen.EndCap = LineCap.Flat;

        int y = ytop + SheetMusic.NoteHeight/2;
        int x = 2;
        int xend = x + 2*SheetMusic.NoteHeight/3;
        pen.Width = 1;
        g.DrawLine(pen, x, y, xend-1, y + SheetMusic.NoteHeight-1);

        pen.Width = SheetMusic.LineSpace/2;
        y  = ytop + SheetMusic.NoteHeight + 1;
        g.DrawLine(pen, xend-2, y, x, y + SheetMusic.NoteHeight);

        pen.Width = 1;
        y = ytop + SheetMusic.NoteHeight*2 - 1;
        g.DrawLine(pen, 0, y, xend+2, y + SheetMusic.NoteHeight); 

        pen.Width = SheetMusic.LineSpace/2;
        g.DrawLine(pen, xend, y + 3*SheetMusic.NoteHeight/4,
                        x/2, y + 3*SheetMusic.NoteHeight/4);
        pen.Width = 1;
        g.DrawLine(pen, 0, y + 2*SheetMusic.NoteHeight/3 + 1, 
                        xend - 1, y + 3*SheetMusic.NoteHeight/2);
    }

    /* Draw an eighth rest symbol */
    public void DrawEighth(Graphics g, Pen pen, int ytop) {
        int y = ytop + SheetMusic.NoteHeight - 1;
        g.FillEllipse(Brushes.Black, 0, y+1, 
                      SheetMusic.LineSpace-1, SheetMusic.LineSpace-1);
        pen.Width = 1;
        g.DrawLine(pen, (SheetMusic.LineSpace-2)/2, y + SheetMusic.LineSpace-1,
                        3*SheetMusic.LineSpace/2, y + SheetMusic.LineSpace/2);
        g.DrawLine(pen, 3*SheetMusic.LineSpace/2, y + SheetMusic.LineSpace/2,
                        3*SheetMusic.LineSpace/4, y + SheetMusic.NoteHeight*2);
    }

    public override string ToString() {
        return string.Format("RestSymbol starttime={0} duration={1} width={2}",
                             starttime, duration, width);
    }

}


}

