﻿using Concept;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote
{
    public interface IBubble
    {
        int BubbleNoteInfoID { get; }
        int LevelInfoId { get; set; }
        int Bar { get; set; }
        int BeatPos { get; set; }
        int Track { get; set; }
        int Type { get; set; }
        int EndBar { get; set; }
        int EndPod { get; set; }
        int ID { get; set; }//非主键
        MoveTrack MoveTrack { get; set; }
        int MoveTrackDegree { get; set; }
        float ScreenPosX { get; set; }
        float ScreenPosY { get; set; }
        FlyTrack FlyTrack { get; set; }
        int FlyTrackDegree { get; set; }
    }
}
