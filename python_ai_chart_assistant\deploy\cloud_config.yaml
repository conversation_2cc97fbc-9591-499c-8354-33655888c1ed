# AI音游写谱助手 - 云端配置文件

# 模型配置 (云端优化)
model:
  # 使用GPU加速
  device: "cuda"  # 云端通常有GPU
  
  # 模型参数 (可以设置更大的模型)
  input_dim: 28
  hidden_dim: 512  # 增大隐藏层
  num_layers: 4    # 增加层数
  max_sequence_length: 2000  # 增加序列长度
  dropout: 0.3
  
  # 批处理大小 (GPU内存充足时可以增大)
  batch_size: 64

# 训练配置 (云端优化)
training:
  # 数据路径
  data_paths:
    midi_dir: "/data/midi"
    charts_dir: "/data/charts"
    processed_dir: "/data/processed"
  
  # 训练参数 (GPU加速)
  batch_size: 64        # 增大批次
  learning_rate: 0.001
  num_epochs: 200       # 增加训练轮数
  validation_split: 0.2
  
  # GPU优化
  num_workers: 4        # 数据加载并行度
  pin_memory: true      # GPU内存优化
  mixed_precision: true # 混合精度训练
  
  # 模型保存
  save_interval: 5      # 更频繁保存
  best_model_metric: "validation_loss"
  early_stopping_patience: 20

# 性能配置 (云端优化)
performance:
  # 并行处理 (云端CPU核心多)
  max_workers: 8
  
  # 内存限制 (云端内存充足)
  max_memory_usage: "16GB"
  
  # 批处理大小
  batch_processing_size: 50
  
  # GPU设置
  gpu_memory_fraction: 0.9  # 使用90%的GPU内存
  allow_growth: true        # 动态分配GPU内存

# API服务配置 (云端部署)
api:
  host: "0.0.0.0"      # 监听所有接口
  port: 5000
  debug: false
  
  # 文件上传限制 (云端带宽好)
  max_file_size: "100MB"
  allowed_extensions: [".mid", ".midi", ".wav", ".mp3", ".flac"]
  
  # 请求限制 (云端性能好)
  rate_limit: "1000/hour"
  
  # 跨域设置
  cors_origins: ["*"]  # 允许所有来源

# Streamlit配置 (云端部署)
streamlit:
  server:
    address: "0.0.0.0"
    port: 8501
    headless: true
    enableCORS: false
  
  theme:
    base: "light"
    primaryColor: "#1f77b4"

# 缓存配置 (云端优化)
cache:
  # 特征提取缓存 (云端存储充足)
  feature_cache_size: 5000
  feature_cache_ttl: 7200
  
  # 模型预测缓存
  prediction_cache_size: 2000
  prediction_cache_ttl: 3600
  
  # 磁盘缓存路径
  cache_dir: "/tmp/ai_chart_cache"

# 日志配置 (云端监控)
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "/logs/ai_chart_assistant.log"
  max_file_size: "50MB"
  backup_count: 10
  
  # 云端日志
  remote_logging: true
  log_server: "localhost:9200"  # 如果有ELK stack

# 数据配置 (云端存储)
data:
  # 数据存储路径
  base_dir: "/data"
  
  # 自动备份
  auto_backup: true
  backup_interval: 3600  # 每小时备份
  backup_dir: "/backup"
  
  # 数据预处理
  preprocessing:
    parallel: true
    num_processes: 8
    chunk_size: 1000

# 监控配置 (云端监控)
monitoring:
  # 系统监控
  enable_system_monitor: true
  monitor_interval: 60  # 秒
  
  # GPU监控
  enable_gpu_monitor: true
  gpu_monitor_interval: 30
  
  # 模型性能监控
  enable_model_monitor: true
  
  # 告警设置
  alerts:
    gpu_memory_threshold: 0.9
    cpu_usage_threshold: 0.8
    disk_usage_threshold: 0.9
