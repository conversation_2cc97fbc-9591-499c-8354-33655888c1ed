using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using RecordMusicalNote;
using Newtonsoft.Json;
using System.Text.RegularExpressions;

namespace MyWPF.Layout.AI
{
    /// <summary>
    /// AI聊天对话框
    /// </summary>
    public partial class AIChatDialog : Window
    {
        private AIChartGenerationService _aiService;
        private string _midiFilePath;
        private List<IIdol> _currentNotes;
        private List<ChatMessage> _chatHistory;
        private bool _isProcessing = false;
        private AudioAnalysisData _audioAnalysis;
        private string _xmlContent;

        public AIChatDialog(string midiFilePath, List<IIdol> currentNotes, AudioAnalysisData audioAnalysis = null)
        {
            InitializeComponent();
            _midiFilePath = midiFilePath;
            _currentNotes = currentNotes ?? new List<IIdol>();
            _audioAnalysis = audioAnalysis;
            _xmlContent = GetXmlContent(midiFilePath);
            _chatHistory = new List<ChatMessage>();

            InitializeAIService();
            InitializeUI();
        }

        /// <summary>
        /// 获取XML文件内容
        /// </summary>
        private string GetXmlContent(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !System.IO.File.Exists(filePath))
                    return null;

                return System.IO.File.ReadAllText(filePath, System.Text.Encoding.UTF8);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取XML文件失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取生成的音符
        /// </summary>
        public List<IIdol> GeneratedNotes { get; private set; } = new List<IIdol>();

        /// <summary>
        /// 初始化AI服务
        /// </summary>
        private void InitializeAIService()
        {
            try
            {
                var config = AIConfigManager.LoadConfig();

                if (!AIConfigManager.IsConfigValid(config))
                {
                    lblStatus.Text = "● 配置无效";
                    lblStatus.Foreground = Brushes.Orange;
                    AddSystemMessage("AI配置无效，请先在设置中配置正确的API信息。");
                    btnSend.IsEnabled = false;
                    return;
                }

                _aiService = new AIChartGenerationService(config);
                lblStatus.Text = "● 已连接";
                lblStatus.Foreground = Brushes.Green;
            }
            catch (Exception ex)
            {
                lblStatus.Text = "● 连接失败";
                lblStatus.Foreground = Brushes.Red;
                MessageBox.Show($"AI服务初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            txtInput.Focus();

            // 初始化模板
            InitializeTemplates();

            // 添加智能欢迎消息
            AddWelcomeMessage();

            // 检查AI配置
            var config = AIConfigManager.LoadConfig();
            if (!AIConfigManager.IsConfigValid(config))
            {
                AddSystemMessage("AI配置无效，请先配置正确的API信息才能使用聊天功能。");
                btnSend.IsEnabled = false;
            }
        }

        /// <summary>
        /// 添加智能欢迎消息
        /// </summary>
        private void AddWelcomeMessage()
        {
            var noteCount = _currentNotes.Count;
            var maxBar = _currentNotes.Any() ? _currentNotes.Max(n => n.Bar) : 0;

            string welcomeMessage;
            string audioAnalysisInfo = GetAudioAnalysisInfo();

            if (noteCount == 0)
            {
                welcomeMessage = $@"你好！我是AI制谱助手。🎵

{audioAnalysisInfo}

我发现你还没有任何音符，我可以帮你：
• 从头开始创建谱面
• 分析MIDI文件并生成基础谱面
• 根据音乐风格设计合适的音符

试试说：""帮我写前4小节的基础谱面""";
            }
            else if (noteCount < 20)
            {
                welcomeMessage = $@"你好！我是AI制谱助手。🎵

{audioAnalysisInfo}

我看到你已经有了 {noteCount} 个音符（共 {maxBar} 小节），我可以帮你：
• 继续写后面的小节
• 优化现有谱面的难度
• 添加更多音符类型

试试说：""根据当前风格继续写第{maxBar + 1}-{maxBar + 4}小节""";
            }
            else
            {
                var noteTypes = _currentNotes.GroupBy(n => n.NoteType).ToDictionary(g => g.Key, g => g.Count());
                var dominantType = noteTypes.OrderByDescending(kvp => kvp.Value).First().Key;

                welcomeMessage = $@"你好！我是AI制谱助手。🎵

{audioAnalysisInfo}

你的谱面已经很丰富了！{noteCount} 个音符，{maxBar} 小节，主要使用 {dominantType} 音符。

我可以帮你：
• 分析谱面特点和难度分布
• 优化特定小节的设计
• 调整音符类型平衡

试试说：""分析一下当前谱面的特点""";
            }

            AddAIMessage(welcomeMessage);
        }

        /// <summary>
        /// 获取音频分析信息
        /// </summary>
        private string GetAudioAnalysisInfo()
        {
            var info = new List<string>();

            // 音频分析信息
            if (_audioAnalysis != null)
            {
                info.Add($"📊 音频分析结果：");
                info.Add($"• BPM: {_audioAnalysis.BPM:F1} (置信度: {_audioAnalysis.BPMConfidence:P1})");
                info.Add($"• 时长: {_audioAnalysis.Duration:mm\\:ss}");
                info.Add($"• 节拍点: {_audioAnalysis.BeatTimes?.Count ?? 0} 个");
                info.Add($"• 能量峰值: {_audioAnalysis.EnergyPeaks?.Count ?? 0} 个");
            }
            else
            {
                info.Add("📊 音频分析：未提供音频分析数据");
            }

            // 谱面基本信息
            if (_currentNotes?.Any() == true)
            {
                info.Add("");
                info.Add("📈 谱面基本信息：");
                info.Add($"• 总音符: {_currentNotes.Count} 个");
                info.Add($"• 总小节数: {(_currentNotes.Any() ? _currentNotes.Max(n => n.Bar) : 0)} 小节");

                // 音符类型统计
                var noteTypeCount = _currentNotes.GroupBy(n => n.NoteType).ToDictionary(g => g.Key, g => g.Count());
                info.Add($"• 音符类型: {string.Join(", ", noteTypeCount.Select(kvp => $"{kvp.Key}({kvp.Value})"))}");

                // 轨道分布统计
                var trackDistribution = _currentNotes.GroupBy(n => n.TargetTrack).ToDictionary(g => g.Key, g => g.Count());
                info.Add($"• 轨道分布: {string.Join(", ", trackDistribution.Select(kvp => $"{kvp.Key}({kvp.Value})"))}");
            }

            return string.Join("\n", info);
        }

        /// <summary>
        /// 获取音频分析提示词信息
        /// </summary>
        private string GetAudioAnalysisPromptInfo(ChatContext context)
        {
            var info = new List<string>();

            // 详细音频分析信息
            if (context.AudioAnalysis != null)
            {
                var audioAnalysis = context.AudioAnalysis;
                info.Add("音频分析数据：");
                info.Add($"- BPM: {audioAnalysis.BPM:F1} (置信度: {audioAnalysis.BPMConfidence:P1})");
                info.Add($"- 时长: {audioAnalysis.Duration:mm\\:ss}");
                info.Add($"- 节拍点: {audioAnalysis.BeatTimes?.Count ?? 0} 个");
                info.Add($"- 能量峰值: {audioAnalysis.EnergyPeaks?.Count ?? 0} 个");

                // 添加所有节拍时间点（分组显示）
                if (audioAnalysis.BeatTimes?.Count > 0)
                {
                    info.Add("- 完整节拍时间序列：");
                    var beatGroups = audioAnalysis.BeatTimes.Select((beat, index) => new { beat, index })
                        .GroupBy(x => x.index / 20) // 每20个一组
                        .Select(g => string.Join(", ", g.Select(x => $"{x.beat:F1}s")));

                    foreach (var group in beatGroups)
                    {
                        info.Add($"  {group}");
                    }
                }

                // 添加详细能量峰值信息
                if (audioAnalysis.EnergyPeaks?.Count > 0)
                {
                    info.Add("- 主要能量峰值：");
                    var topPeaks = audioAnalysis.EnergyPeaks
                        .OrderByDescending(p => p.Energy)
                        .Take(30) // 显示前30个最强峰值
                        .OrderBy(p => p.Time);

                    var peakGroups = topPeaks.Select((peak, index) => new { peak, index })
                        .GroupBy(x => x.index / 10) // 每10个一组
                        .Select(g => string.Join(", ", g.Select(x => $"{x.peak.Time:F1}s({x.peak.Energy:F2})")));

                    foreach (var group in peakGroups)
                    {
                        info.Add($"  {group}");
                    }
                }

                // 添加详细分析信息
                if (audioAnalysis.DetailedAnalysis != null)
                {
                    var detailed = audioAnalysis.DetailedAnalysis;

                    // 节拍段落信息
                    if (detailed.BeatSegments?.Count > 0)
                    {
                        info.Add("- 节拍段落分析：");
                        foreach (var segment in detailed.BeatSegments.Take(10))
                        {
                            info.Add($"  {segment.StartTime:F0}s-{segment.EndTime:F0}s: {segment.AverageBPM:F1}BPM, {segment.Characteristics}");
                        }
                    }

                    // 能量段落信息
                    if (detailed.EnergySegments?.Count > 0)
                    {
                        info.Add("- 能量段落分析：");
                        foreach (var segment in detailed.EnergySegments.Take(10))
                        {
                            info.Add($"  {segment.StartTime:F0}s-{segment.EndTime:F0}s: {segment.EnergyLevel}能量, 峰值{segment.PeaksInSegment?.Count ?? 0}个");
                        }
                    }

                    // 音频结构信息
                    if (detailed.StructureAnalysis?.Sections?.Count > 0)
                    {
                        info.Add("- 音频结构分析：");
                        foreach (var section in detailed.StructureAnalysis.Sections)
                        {
                            info.Add($"  {section.Name}({section.StartTime:F0}s-{section.EndTime:F0}s): {section.Characteristics}");
                        }
                    }
                }
            }
            else
            {
                info.Add("音频分析：未提供音频分析数据");
            }

            return string.Join("\n", info);
        }

        /// <summary>
        /// 初始化模板
        /// </summary>
        private void InitializeTemplates()
        {
            // 加载模板分类
            var categories = ChatTemplates.GetAllCategories();
            cmbTemplateCategory.ItemsSource = categories;

            // 默认选择第一个分类
            if (categories.Any())
            {
                cmbTemplateCategory.SelectedIndex = 0;
            }
        }

        /// <summary>
        /// 发送按钮点击
        /// </summary>
        private async void BtnSend_Click(object sender, RoutedEventArgs e)
        {
            await SendMessage();
        }

        /// <summary>
        /// 输入框按键事件
        /// </summary>
        private async void TxtInput_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && Keyboard.Modifiers == ModifierKeys.Control)
            {
                await SendMessage();
                e.Handled = true;
            }
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        private async Task SendMessage()
        {
            var message = txtInput.Text.Trim();
            if (string.IsNullOrEmpty(message) || _isProcessing)
                return;

            try
            {
                _isProcessing = true;
                ShowLoading(true);
                
                // 添加用户消息到聊天界面
                AddUserMessage(message);
                txtInput.Clear();

                // 解析用户意图并生成响应
                var response = await ProcessUserMessage(message);
                
                // 添加AI响应到聊天界面
                AddAIMessage(response.Message);
                
                // 如果生成了音符，更新结果
                if (response.GeneratedNotes.Any())
                {
                    GeneratedNotes = response.GeneratedNotes;
                    AddSystemMessage($"已生成 {response.GeneratedNotes.Count} 个音符，关闭对话框后可以选择应用到谱面中。");
                }
            }
            catch (Exception ex)
            {
                AddSystemMessage($"处理失败: {ex.Message}");
            }
            finally
            {
                _isProcessing = false;
                ShowLoading(false);
            }
        }

        /// <summary>
        /// 处理用户消息
        /// </summary>
        private async Task<ChatResponse> ProcessUserMessage(string message)
        {
            // 解析用户意图
            var intent = ParseUserIntent(message);
            
            // 构建上下文信息
            var context = BuildChatContext(intent, message);
            
            // 调用AI服务
            var prompt = BuildChatPrompt(context, message);
            var aiResponse = await CallAIForChat(prompt);
            
            // 解析AI响应
            return ParseChatResponse(aiResponse, intent);
        }

        /// <summary>
        /// 解析用户意图
        /// </summary>
        private UserIntent ParseUserIntent(string message)
        {
            var intent = new UserIntent { OriginalMessage = message };
            
            // 检测小节范围
            var barRangeMatch = Regex.Match(message, @"第?(\d+)[-到至](\d+)小节|(\d+)[-到至](\d+)小节");
            if (barRangeMatch.Success)
            {
                intent.StartBar = int.Parse(barRangeMatch.Groups[1].Value.Length > 0 ? barRangeMatch.Groups[1].Value : barRangeMatch.Groups[3].Value);
                intent.EndBar = int.Parse(barRangeMatch.Groups[2].Value.Length > 0 ? barRangeMatch.Groups[2].Value : barRangeMatch.Groups[4].Value);
                intent.HasBarRange = true;
            }
            else
            {
                var singleBarMatch = Regex.Match(message, @"第?(\d+)小节");
                if (singleBarMatch.Success)
                {
                    intent.StartBar = intent.EndBar = int.Parse(singleBarMatch.Groups[1].Value);
                    intent.HasBarRange = true;
                }
            }
            
            // 检测操作类型
            if (message.Contains("写") || message.Contains("生成") || message.Contains("创建"))
                intent.Action = "generate";
            else if (message.Contains("分析") || message.Contains("看看") || message.Contains("检查"))
                intent.Action = "analyze";
            else if (message.Contains("优化") || message.Contains("改进") || message.Contains("调整"))
                intent.Action = "optimize";
            else if (message.Contains("删除") || message.Contains("移除") || message.Contains("清除"))
                intent.Action = "delete";
            else
                intent.Action = "generate"; // 默认为生成
            
            // 检测音符类型偏好
            if (message.Contains("滑动") || message.Contains("slip"))
                intent.PreferredNoteTypes.Add("slip");
            if (message.Contains("长按") || message.Contains("long"))
                intent.PreferredNoteTypes.Add("long");
            if (message.Contains("短") || message.Contains("点击") || message.Contains("short"))
                intent.PreferredNoteTypes.Add("short");
            
            // 检测难度要求
            if (message.Contains("简单") || message.Contains("容易") || message.Contains("新手"))
                intent.DifficultyHint = "easy";
            else if (message.Contains("困难") || message.Contains("挑战") || message.Contains("高难"))
                intent.DifficultyHint = "hard";
            else if (message.Contains("中等") || message.Contains("适中"))
                intent.DifficultyHint = "medium";
            
            return intent;
        }

        /// <summary>
        /// 构建聊天上下文
        /// </summary>
        private ChatContext BuildChatContext(UserIntent intent, string message)
        {
            var context = new ChatContext
            {
                CurrentNotes = _currentNotes,
                TotalBars = _currentNotes.Any() ? _currentNotes.Max(n => n.Bar) : 0,
                ChatHistory = _chatHistory.Skip(Math.Max(0, _chatHistory.Count - 5)).ToList(), // 只保留最近5条对话
                MusicInfo = GetMusicInfo(),
                AudioAnalysis = _audioAnalysis,
                XmlContent = _xmlContent, // 直接传递XML内容
                Intent = intent
            };

            // 如果指定了小节范围，提取相关音符
            if (intent.HasBarRange)
            {
                context.RelevantNotes = _currentNotes
                    .Where(n => n.Bar >= intent.StartBar && n.Bar <= intent.EndBar)
                    .ToList();
            }

            return context;
        }

        /// <summary>
        /// 构建聊天提示词
        /// </summary>
        private string BuildChatPrompt(ChatContext context, string userMessage)
        {
            var audioAnalysisInfo = GetAudioAnalysisPromptInfo(context);
            var fileName = string.IsNullOrEmpty(_midiFilePath) ? "未保存" : System.IO.Path.GetFileName(_midiFilePath);

            var prompt = $@"
你是一个专业的音游制谱AI助手。用户正在制作音游谱面，需要你的帮助。

文件信息：
- 谱面文件: {fileName}
- 音乐文件: {System.IO.Path.GetFileName(_midiFilePath)}

{audioAnalysisInfo}

完整XML谱面文件内容：
{context.XmlContent ?? "未提供XML内容"}

用户需求: {userMessage}

请基于以上完整的音频分析数据和XML谱面文件内容，提供专业的制谱建议。特别注意：
1. 分析XML文件中的现有音符结构和模式
2. 结合音频的BPM和节拍点进行音符放置建议
3. 根据能量峰值推荐音符类型和强度
4. 基于现有谱面风格进行续写或优化
5. 保持谱面的可玩性和音乐性平衡

解析的意图:
- 操作类型: {context.Intent.Action}
- 小节范围: {(context.Intent.HasBarRange ? $"{context.Intent.StartBar}-{context.Intent.EndBar}" : "未指定")}
- 偏好音符类型: {string.Join(", ", context.Intent.PreferredNoteTypes)}
- 难度提示: {context.Intent.DifficultyHint ?? "未指定"}

{(context.RelevantNotes.Any() ? $@"
相关小节的现有音符:
{string.Join("\n", context.RelevantNotes.Take(10).Select(n => $"小节{n.Bar} 位置{n.Pos} {n.TargetTrack}轨道 {n.NoteType}音符"))}" : "")}

请根据用户需求提供帮助。如果需要生成音符，请按以下JSON格式输出：
{{
  ""message"": ""你的回复消息"",
  ""action"": ""generate|analyze|optimize|delete"",
  ""notes"": [
    {{
      ""bar"": 1,
      ""pos"": 0,
      ""fromTrack"": ""Left1"",
      ""targetTrack"": ""Left1"",
      ""endTrack"": ""Left1"",
      ""noteType"": ""short"",
      ""endBar"": 1,
      ""endPos"": 0
    }}
  ]
}}

如果不需要生成音符，只需要回复消息，格式如下：
{{
  ""message"": ""你的回复消息"",
  ""action"": ""analyze""
}}
";
            
            return prompt;
        }

        /// <summary>
        /// 调用AI进行聊天
        /// </summary>
        private async Task<string> CallAIForChat(string prompt)
        {
            var config = AIConfigManager.LoadConfig();

            var requestBody = new
            {
                model = config.Model,
                messages = new[]
                {
                    new { role = "system", content = "你是一个专业的音游制谱AI助手，擅长理解用户需求并生成高质量的音符。请始终以JSON格式回复。" },
                    new { role = "user", content = prompt }
                },
                temperature = config.Temperature,
                max_tokens = config.MaxTokens,
                response_format = new { type = "json_object" }
            };

            var json = JsonConvert.SerializeObject(requestBody);
            var content = new System.Net.Http.StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var httpClient = new System.Net.Http.HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {config.ApiKey}");
            httpClient.Timeout = TimeSpan.FromSeconds(config.TimeoutSeconds);

            var response = await httpClient.PostAsync(config.BaseUrl, content);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<DeepSeekResponse>(responseContent);

            return result.choices[0].message.content;
        }

        /// <summary>
        /// 解析聊天响应
        /// </summary>
        private ChatResponse ParseChatResponse(string aiResponse, UserIntent intent)
        {
            try
            {
                var response = JsonConvert.DeserializeObject<AIChatResponse>(aiResponse);
                
                var chatResponse = new ChatResponse
                {
                    Message = response.message,
                    Action = response.action,
                    GeneratedNotes = new List<IIdol>()
                };

                // 如果有音符数据，转换为IIdol对象
                if (response.notes != null)
                {
                    foreach (var noteData in response.notes)
                    {
                        var idol = new RecordMusicalNote.Library.Idol();
                        idol.Bar = noteData.bar;
                        idol.Pos = noteData.pos;
                        idol.FromTrack = noteData.fromTrack;
                        idol.TargetTrack = noteData.targetTrack;
                        idol.EndTrack = noteData.endTrack ?? noteData.targetTrack;
                        idol.NoteType = noteData.noteType;
                        idol.EndBar = noteData.endBar ?? noteData.bar;
                        idol.EndPos = noteData.endPos ?? noteData.pos;
                        
                        chatResponse.GeneratedNotes.Add(idol);
                    }
                }

                return chatResponse;
            }
            catch (Exception ex)
            {
                return new ChatResponse
                {
                    Message = $"抱歉，我在处理你的请求时遇到了问题: {ex.Message}",
                    Action = "error",
                    GeneratedNotes = new List<IIdol>()
                };
            }
        }

        /// <summary>
        /// 添加用户消息到聊天界面
        /// </summary>
        private void AddUserMessage(string message)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                CornerRadius = new CornerRadius(15),
                Padding = new Thickness(15, 10, 15, 10),
                Margin = new Thickness(50, 5, 0, 5),
                HorizontalAlignment = HorizontalAlignment.Right
            };

            var stackPanel = new StackPanel();
            
            var nameBlock = new TextBlock
            {
                Text = "你",
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                Margin = new Thickness(0, 0, 0, 5)
            };
            
            var messageBlock = new TextBlock
            {
                Text = message,
                TextWrapping = TextWrapping.Wrap,
                Foreground = Brushes.White
            };

            stackPanel.Children.Add(nameBlock);
            stackPanel.Children.Add(messageBlock);
            border.Child = stackPanel;

            chatPanel.Children.Add(border);
            
            // 记录到聊天历史
            _chatHistory.Add(new ChatMessage { Role = "user", Content = message, Timestamp = DateTime.Now });
            
            ScrollToBottom();
        }

        /// <summary>
        /// 添加AI消息到聊天界面
        /// </summary>
        private void AddAIMessage(string message)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(245, 245, 245)),
                CornerRadius = new CornerRadius(15),
                Padding = new Thickness(15, 10, 15, 10),
                Margin = new Thickness(0, 5, 50, 5),
                HorizontalAlignment = HorizontalAlignment.Left
            };

            var stackPanel = new StackPanel();
            
            var nameBlock = new TextBlock
            {
                Text = "AI助手",
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                Margin = new Thickness(0, 0, 0, 5)
            };
            
            var messageBlock = new TextBlock
            {
                Text = message,
                TextWrapping = TextWrapping.Wrap,
                Foreground = Brushes.Black
            };

            stackPanel.Children.Add(nameBlock);
            stackPanel.Children.Add(messageBlock);
            border.Child = stackPanel;

            chatPanel.Children.Add(border);
            
            // 记录到聊天历史
            _chatHistory.Add(new ChatMessage { Role = "assistant", Content = message, Timestamp = DateTime.Now });
            
            ScrollToBottom();
        }

        /// <summary>
        /// 添加系统消息到聊天界面
        /// </summary>
        private void AddSystemMessage(string message)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(255, 193, 7)),
                CornerRadius = new CornerRadius(15),
                Padding = new Thickness(15, 10, 15, 10),
                Margin = new Thickness(25, 5, 25, 5),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var messageBlock = new TextBlock
            {
                Text = message,
                TextWrapping = TextWrapping.Wrap,
                Foreground = Brushes.Black,
                TextAlignment = TextAlignment.Center
            };

            border.Child = messageBlock;
            chatPanel.Children.Add(border);
            
            ScrollToBottom();
        }

        /// <summary>
        /// 滚动到底部
        /// </summary>
        private void ScrollToBottom()
        {
            chatScrollViewer.ScrollToEnd();
        }

        /// <summary>
        /// 显示/隐藏加载指示器
        /// </summary>
        private void ShowLoading(bool show)
        {
            loadingOverlay.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            btnSend.IsEnabled = !show;
        }

        /// <summary>
        /// 获取音乐信息
        /// </summary>
        private string GetMusicInfo()
        {
            return $"文件: {System.IO.Path.GetFileName(_midiFilePath)}";
        }

        // 快捷操作按钮事件
        private async void BtnQuickGenerate_Click(object sender, RoutedEventArgs e)
        {
            txtInput.Text = "根据当前谱面的风格，帮我继续写后面的小节";
            await SendMessage();
        }

        private async void BtnAnalyzeChart_Click(object sender, RoutedEventArgs e)
        {
            txtInput.Text = "分析一下当前谱面的特点和难度分布";
            await SendMessage();
        }

        private async void BtnOptimize_Click(object sender, RoutedEventArgs e)
        {
            txtInput.Text = "帮我优化当前谱面的难度和可玩性";
            await SendMessage();
        }

        private void BtnTemplates_Click(object sender, RoutedEventArgs e)
        {
            templateExpander.IsExpanded = !templateExpander.IsExpanded;
        }

        /// <summary>
        /// 模板分类选择变化
        /// </summary>
        private void CmbTemplateCategory_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbTemplateCategory.SelectedItem is string category)
            {
                var templates = ChatTemplates.GetTemplatesByCategory(category);
                lstTemplates.ItemsSource = templates;
            }
        }

        /// <summary>
        /// 模板选择变化
        /// </summary>
        private void LstTemplates_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (lstTemplates.SelectedItem is ChatTemplate template)
            {
                // 计算当前谱面的小节范围
                var maxBar = _currentNotes.Any() ? _currentNotes.Max(n => n.Bar) : 4;
                var nextBar = maxBar + 1;
                var endBar = nextBar + 3; // 默认生成4个小节

                // 应用模板参数
                var message = template.ApplyParameters(nextBar, endBar, nextBar);
                txtInput.Text = message;

                // 收起模板选择器
                templateExpander.IsExpanded = false;

                // 聚焦到输入框
                txtInput.Focus();
                txtInput.SelectAll();
            }
        }

        private void BtnAttach_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现附件功能，比如选择特定的音符范围
            MessageBox.Show("附件功能开发中...", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnSettings_Click(object sender, RoutedEventArgs e)
        {
            var settingsDialog = new AIAssistDialog(_midiFilePath, _currentNotes);
            settingsDialog.Owner = this;
            settingsDialog.ShowDialog();
        }

        /// <summary>
        /// 输入框获得焦点时清除提示文本
        /// </summary>
        private void TxtInput_GotFocus(object sender, RoutedEventArgs e)
        {
            if (txtInput.Text == "输入你的需求，比如：帮我写第5-10小节的铺面")
            {
                txtInput.Text = "";
                txtInput.Foreground = System.Windows.Media.Brushes.Black;
            }
        }

        /// <summary>
        /// 输入框失去焦点时恢复提示文本
        /// </summary>
        private void TxtInput_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtInput.Text))
            {
                txtInput.Text = "输入你的需求，比如：帮我写第5-10小节的铺面";
                txtInput.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _aiService?.Dispose();
            base.OnClosed(e);
        }
    }
}
