﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B217AE65-CB85-496A-8650-D988C82A5E2F}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>MyWPF.Layout</RootNamespace>
    <AssemblyName>MyWPF.Layout</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="MaterialDesignColors, Version=2.1.4.0, Culture=neutral, PublicKeyToken=df2a72020bd7962a, processorArchitecture=MSIL">
      <HintPath>..\packages\MaterialDesignColors.2.1.4\lib\net462\MaterialDesignColors.dll</HintPath>
    </Reference>
    <Reference Include="MaterialDesignThemes.Wpf, Version=4.9.0.0, Culture=neutral, PublicKeyToken=df2a72020bd7962a, processorArchitecture=MSIL">
      <HintPath>..\packages\MaterialDesignThemes.4.9.0\lib\net462\MaterialDesignThemes.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Xaml.Behaviors, Version=1.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Xaml.Behaviors.Wpf.1.1.39\lib\net45\Microsoft.Xaml.Behaviors.dll</HintPath>
    </Reference>
    <Reference Include="NAudio, Version=2.2.1.0, Culture=neutral, PublicKeyToken=e279aa5131008a41, processorArchitecture=MSIL">
      <HintPath>..\packages\NAudio.2.2.1\lib\net472\NAudio.dll</HintPath>
    </Reference>
    <Reference Include="NAudio.Core, Version=2.2.1.0, Culture=neutral, PublicKeyToken=e279aa5131008a41, processorArchitecture=MSIL">
      <HintPath>..\packages\NAudio.Core.2.2.1\lib\netstandard2.0\NAudio.Core.dll</HintPath>
    </Reference>
    <Reference Include="NAudio.Wasapi, Version=2.2.1.0, Culture=neutral, PublicKeyToken=e279aa5131008a41, processorArchitecture=MSIL">
      <HintPath>..\packages\NAudio.Wasapi.2.2.1\lib\netstandard2.0\NAudio.Wasapi.dll</HintPath>
    </Reference>
    <Reference Include="NAudio.WinMM, Version=2.2.1.0, Culture=neutral, PublicKeyToken=e279aa5131008a41, processorArchitecture=MSIL">
      <HintPath>..\packages\NAudio.WinMM.2.2.1\lib\netstandard2.0\NAudio.WinMM.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.7\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.9.0.7\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.9.0.7\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.9.0.7\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="AI\AIAssistDialog.xaml.cs">
      <DependentUpon>AIAssistDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="AI\AIChartGenerationService.cs" />
    <Compile Include="AI\AIChatDialog.xaml.cs">
      <DependentUpon>AIChatDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="AI\AIConfigManager.cs" />
    <Compile Include="AI\AIConfigWindow.xaml.cs">
      <DependentUpon>AIConfigWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="AI\AIDataModels.cs" />
    <Compile Include="AI\ChatTemplates.cs" />
    <Compile Include="BackGround\AdvancedMidiToGameConverter.cs" />
    <Compile Include="BackGround\DifficultySettingsWindow.xaml.cs" />
    <Compile Include="BackGround\ForeCommonMethod.cs" />
    <Compile Include="BackGround\MidiTimbreMeter .cs" />
    <Compile Include="BackGround\ReadMusicXMLInfo.xaml.cs">
      <DependentUpon>ReadMusicXMLInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="BackGround\ReadMidiInfo.xaml.cs">
      <DependentUpon>ReadMidiInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="CalculateScore\SaveScoreInfo.xaml.cs">
      <DependentUpon>SaveScoreInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="CellInfo.cs" />
    <Compile Include="Converter\PinballToIdolConverter.cs" />
    <Compile Include="IMDHelper.cs" />
    <Compile Include="IMDSettings.xaml.cs">
      <DependentUpon>IMDSettings.xaml</DependentUpon>
    </Compile>
    <Compile Include="Scene.cs" />
    <Compile Include="CalculateScore\ScoreHelper.cs" />
    <Compile Include="CalculateScore\ScoreInfoAnalyse.xaml.cs">
      <DependentUpon>ScoreInfoAnalyse.xaml</DependentUpon>
    </Compile>
    <Compile Include="SetBPMMultiple.xaml.cs">
      <DependentUpon>SetBPMMultiple.xaml</DependentUpon>
    </Compile>
    <Compile Include="ChartGenerator\MusicChartGenerator.cs" />
    <Compile Include="ChartGenerator\ChartGeneratorWindow.xaml.cs">
      <DependentUpon>ChartGeneratorWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="ChartGenerator\ChartGeneratorTest.cs" />
    <Compile Include="Converter\IdolToPinballConverter.cs" />
    <Compile Include="Converter\ConversionSettingsWindow.xaml.cs">
      <DependentUpon>ConversionSettingsWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="NoteEditor.xaml.cs">
      <DependentUpon>NoteEditor.xaml</DependentUpon>
    </Compile>
    <Compile Include="CopyNotesDialog.xaml.cs">
      <DependentUpon>CopyNotesDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Audio\Mp3Analyzer.cs" />
    <Compile Include="Audio\AudioAnalysisPanel.xaml.cs">
      <DependentUpon>AudioAnalysisPanel.xaml</DependentUpon>
    </Compile>
    <Page Include="AI\AIAssistDialog.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="AI\AIChatDialog.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="AI\AIConfigWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="BackGround\DifficultySettingsWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="BackGround\Idol_Main.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="BackGround\ReadMusicXMLInfo.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="BackGround\ReadMidiInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="CalculateScore\SaveScoreInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ClassicalTrans.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="DrawNote.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="IMDSettings.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="BackGround\Idol_Main.xaml.cs">
      <DependentUpon>Idol_Main.xaml</DependentUpon>
    </Compile>
    <Compile Include="ClassicalTrans.xaml.cs">
      <DependentUpon>ClassicalTrans.xaml</DependentUpon>
    </Compile>
    <Compile Include="DrawNote.xaml.cs">
      <DependentUpon>DrawNote.xaml</DependentUpon>
    </Compile>
    <Compile Include="Helper.cs" />
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="CalculateScore\ScoreInfoAnalyse.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SetBPMMultiple.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ChartGenerator\ChartGeneratorWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Converter\ConversionSettingsWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="NoteEditor.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="CopyNotesDialog.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Audio\AudioAnalysisPanel.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="AI\AI_Features_Summary.md" />
    <None Include="AI\README_AI_Chat.md" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="1.ico" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CommonModel\CommonModel.csproj">
      <Project>{1b1cc2b6-c6ac-40db-9d24-c89c89cf024c}</Project>
      <Name>CommonModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\MidiSheetMusic\MidiSheetMusic.csproj">
      <Project>{0b5cba19-8987-4ebe-8d7f-4683fcaa5a2f}</Project>
      <Name>MidiSheetMusic</Name>
    </ProjectReference>
    <ProjectReference Include="..\RecordMusicalNote.DataModel\RecordMusicalNote.DataModel.csproj">
      <Project>{97E616EA-7301-4498-8E26-4981E4312A9B}</Project>
      <Name>RecordMusicalNote.DataModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\RecordMusicalNote.Library\RecordMusicalNote.Library.csproj">
      <Project>{C3FF4408-7B95-4D21-BBD8-91612362CD8C}</Project>
      <Name>RecordMusicalNote.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\RecordMusicalNote\RecordMusicalNote.csproj">
      <Project>{7A2C3CF8-3691-4888-97FB-493BCD84F124}</Project>
      <Name>RecordMusicalNote</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Template\a_idol.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Themes\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>