# 🎵 AI音游写谱助手使用指南

## 📋 目录
- [快速开始](#快速开始)
- [命令行使用](#命令行使用)
- [Python API使用](#python-api使用)
- [配置说明](#配置说明)
- [训练自定义模型](#训练自定义模型)
- [常见问题](#常见问题)

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd python_ai_chart_assistant

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 运行演示
```bash
# 运行基础演示
python main.py demo

# 或者直接运行示例
python examples/basic_usage.py
```

### 3. 生成你的第一个谱面
```bash
# 从MIDI文件生成Malody格式谱面
python main.py generate input.mid output.mc --difficulty 6 --style balanced

# 分析MIDI文件
python main.py analyze input.mid

# 预测难度
python main.py predict input.mid
```

## 💻 命令行使用

### 生成谱面
```bash
# 基础用法
python main.py generate input.mid output.mc

# 指定参数
python main.py generate input.mid output.mc \
    --format malody \
    --difficulty 7 \
    --style dense \
    --title "我的歌曲" \
    --artist "艺术家名称"

# 使用自定义模型
python main.py generate input.mid output.mc \
    --model models/my_model.pth \
    --device cuda
```

### 分析MIDI文件
```bash
# 分析MIDI文件结构
python main.py analyze song.mid

# 输出示例:
# 📊 基本信息:
#   时长: 180.5 秒
#   BPM: 128.0
#   总音符数: 1250
#   轨道数: 4
# 
# 🎵 主旋律轨道:
#   轨道名称: Piano
#   音符数量: 450
```

### 预测难度
```bash
# 预测MIDI文件的难度
python main.py predict song.mid

# 使用自定义预测器
python main.py predict song.mid --predictor models/difficulty_predictor.pth
```

### 格式转换
```bash
# 转换谱面格式
python main.py convert input.json output.mc --format malody

# 支持的格式转换:
# JSON → Malody (.mc)
# JSON → 节奏大师 (.imd)
# Malody → JSON
# 节奏大师 → JSON
```

### 启动API服务
```bash
# 启动本地API服务
python main.py api

# 指定地址和端口
python main.py api --host 0.0.0.0 --port 8080

# 调试模式
python main.py api --debug
```

## 🐍 Python API使用

### 基础使用
```python
from src.chart_generator import AIChartGenerator

# 初始化生成器
generator = AIChartGenerator()

# 生成谱面
success = generator.generate_from_midi(
    midi_path="song.mid",
    output_path="output.mc",
    format_name="malody",
    difficulty=7,
    style="balanced"
)

if success:
    print("✅ 谱面生成成功！")
```

### 高级使用
```python
# 加载自定义模型
generator = AIChartGenerator(
    model_path="models/my_model.pth",
    device="cuda"
)

# 生成谱面数据（不保存文件）
chart_data = generator.generate_chart_data(
    midi_path="song.mid",
    difficulty=8,
    style="rhythmic"
)

# 分析谱面质量
if chart_data:
    stats = chart_data.get_statistics()
    print(f"总音符数: {stats['total_notes']}")
    print(f"轨道分布: {stats['track_distribution']}")
```

### 批量处理
```python
from pathlib import Path

# 批量生成谱面
midi_dir = Path("midi_files")
output_dir = Path("output")

for midi_file in midi_dir.glob("*.mid"):
    output_file = output_dir / f"{midi_file.stem}.mc"
    
    success = generator.generate_from_midi(
        midi_path=str(midi_file),
        output_path=str(output_file),
        format_name="malody",
        difficulty=6
    )
    
    print(f"{'✅' if success else '❌'} {midi_file.name}")
```

## ⚙️ 配置说明

### 配置文件结构
```yaml
# config/user_config.yaml
model:
  device: "cpu"  # 或 "cuda"
  generation_model_path: "models/chart_generation_model.pth"
  difficulty_predictor_path: "models/difficulty_predictor.pth"

chart_generation:
  default_difficulty: 6
  default_style: "balanced"
  
  styles:
    balanced:
      note_density: 0.3
      long_note_ratio: 0.2
    dense:
      note_density: 0.45
      long_note_ratio: 0.1

logging:
  level: "INFO"
  console_output: true
  file: "logs/ai_chart_assistant.log"
```

### 使用自定义配置
```bash
# 使用自定义配置文件
python main.py --config config/my_config.yaml generate input.mid output.mc
```

## 🎓 训练自定义模型

### 准备训练数据
```bash
# 数据目录结构
data/
├── midi/           # MIDI文件
│   ├── song1.mid
│   ├── song2.mid
│   └── ...
├── charts/         # 对应的谱面文件
│   ├── song1.json
│   ├── song2.mc
│   └── ...
└── processed/      # 预处理后的数据（自动生成）
```

### 开始训练
```bash
# 训练谱面生成模型
python main.py train \
    --data-dir data \
    --model-type generation \
    --epochs 100 \
    --device cuda \
    --save-dir models

# 训练难度预测器
python main.py train \
    --data-dir data \
    --model-type difficulty \
    --epochs 50 \
    --device cuda
```

### 监控训练过程
```bash
# 启动TensorBoard
tensorboard --logdir models/tensorboard_logs

# 在浏览器中打开 http://localhost:6006
```

## 🔧 REST API使用

### 启动API服务
```bash
python main.py api --host 0.0.0.0 --port 5000
```

### API端点

#### 生成谱面
```bash
curl -X POST http://localhost:5000/generate \
  -F "file=@song.mid" \
  -F "format=malody" \
  -F "difficulty=7" \
  -F "style=balanced" \
  -F "title=My Song" \
  -o output.mc
```

#### 分析MIDI
```bash
curl -X POST http://localhost:5000/analyze \
  -F "file=@song.mid" \
  | jq .
```

#### 预测难度
```bash
curl -X POST http://localhost:5000/predict_difficulty \
  -F "file=@song.mid" \
  | jq .
```

#### 获取支持的格式
```bash
curl http://localhost:5000/formats | jq .
```

## ❓ 常见问题

### Q: 支持哪些MIDI文件格式？
A: 支持标准的.mid和.midi文件格式，建议使用Type 1 MIDI文件以获得最佳效果。

### Q: 生成的谱面质量如何？
A: 谱面质量取决于：
- 输入MIDI文件的质量
- 选择的难度和风格参数
- 使用的AI模型（如果有训练好的模型会更好）

### Q: 可以生成多少轨道的谱面？
A: 默认支持4轨道，可以通过参数调整为1-8轨道。

### Q: 如何提高生成速度？
A: 
- 使用GPU加速（设置device="cuda"）
- 减少MIDI文件长度
- 使用较低的时间分辨率

### Q: 训练需要多少数据？
A: 建议至少：
- 1000首MIDI文件
- 3000个高质量谱面
- 训练时间：CPU约24-48小时，GPU约6-12小时

### Q: 如何与现有的C#项目集成？
A: 
1. 使用REST API接口
2. 通过命令行调用
3. 使用共享的XML数据格式

### Q: 遇到错误怎么办？
A: 
1. 检查日志文件：`logs/ai_chart_assistant.log`
2. 使用`--verbose`参数获取详细信息
3. 确保MIDI文件格式正确
4. 检查依赖是否正确安装

## 📚 更多资源

- [项目README](README.md) - 项目概述和架构
- [API文档](api/README.md) - 详细的API说明
- [开发指南](DEVELOPMENT.md) - 开发和贡献指南
- [示例代码](examples/) - 更多使用示例

## 🆘 获取帮助

如果遇到问题，可以：
1. 查看[常见问题](#常见问题)部分
2. 检查GitHub Issues
3. 运行`python main.py --help`查看命令帮助
4. 查看日志文件获取错误详情
