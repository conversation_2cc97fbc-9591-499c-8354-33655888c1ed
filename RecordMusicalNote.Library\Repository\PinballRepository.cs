﻿using RecordMusicalNote.IRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote.Library.Repository
{
    public class PinballRepository : IPinballRepository
    {
        public ILevelInfo CreateNewLevelInfo()
        {
            return new LevelInfo();
        }

        public IPinball CreatenewPinball()
        {
            return new Pinball();
        }

        public IList<IPinball> GetInfo(int levelInfoId)
        {
            throw new NotImplementedException();
        }

        public IList<ILevelInfo> GetInfo(int cmbIndex, string content)
        {
            return new IdolRepository().GetInfo(cmbIndex, content);
        }

        public bool SaveInfo(IList<IPinball> modes, ILevelInfo levelInfo,out string msg)
        {
            throw new NotImplementedException();
        }
    }
}
