﻿using CommonModel;
using Helper;
using RecordMusicalNote.Library;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote.IRepository
{
    public class MidiRepository
    {
        public IList<Midi> GetMifiInfo(int selectedIndex, string text)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return new List<Midi>();
            }

            string sql = "";
            switch (selectedIndex)
            {
                case 0: // 歌名
                    sql = "select * from midi where SongName like @searchText";
                    break;
                case 1: // 歌手 - 注意：Midi表中没有Artist字段，这里可能需要调整
                    sql = "select * from midi where SongName like @searchText"; // 暂时用歌名搜索
                    break;
                case 2: // BPM - 使用QuarternotetTime字段
                    if (int.TryParse(text, out int bpm))
                    {
                        sql = "select * from midi where QuarternotetTime = @searchValue";
                    }
                    else
                    {
                        return new List<Midi>(); // 如果不是数字，返回空列表
                    }
                    break;
                default:
                    return new List<Midi>();
            }

            try
            {
                SqlHelper helper = new SqlHelper();
                System.Data.SqlClient.SqlParameter[] parameters;

                if (selectedIndex == 2) // BPM搜索
                {
                    parameters = new System.Data.SqlClient.SqlParameter[]
                    {
                        new System.Data.SqlClient.SqlParameter("@searchValue", int.Parse(text))
                    };
                }
                else // 文本搜索
                {
                    parameters = new System.Data.SqlClient.SqlParameter[]
                    {
                        new System.Data.SqlClient.SqlParameter("@searchText", "%" + text + "%")
                    };
                }

                DataSet ds = helper.ExecuteDataSet(sql, parameters);
                if (ds == null || ds.Tables.Count == 0) return new List<Midi>();

                DataTable dt = ds.Tables[0];
                IList<Midi> midis = new List<Midi>();
                foreach (DataRow dr in dt.Rows)
                {
                    Midi midi = new Midi();
                    midi.MidiID = (int)dr[0];
                    midi.SongName = dr[1].ToString();
                    midi.Meter = dr[2].ToString();
                    midi.TrackCount = (int)dr[3];
                    midi.KeySignature = dr[4].ToString();
                    midi.QuarternotetTime = (int)dr[5];
                    midis.Add(midi);
                }
                return midis;
            }
            catch (Exception ex)
            {
                // 记录错误或抛出更具体的异常
                throw new Exception($"查询MIDI信息时发生错误：{ex.Message}", ex);
            }
        }

        public IList<MidiTrack> GetMidiTrack(int midiID)
        {
            try
            {
                string sql = "select * from MidiTrack where MidiID=@midiID";
                SqlHelper helper = new SqlHelper();
                System.Data.SqlClient.SqlParameter[] parameters = new System.Data.SqlClient.SqlParameter[]
                {
                    new System.Data.SqlClient.SqlParameter("@midiID", midiID)
                };

                DataSet ds = helper.ExecuteDataSet(sql, parameters);
                if (ds == null || ds.Tables.Count == 0) return new List<MidiTrack>();

                DataTable dt = ds.Tables[0];
                IList<MidiTrack> MidiTracks = new List<MidiTrack>();
                foreach (DataRow dr in dt.Rows)
                {
                    MidiTrack MidiTrack = new MidiTrack();
                    MidiTrack.MidiTrackID = (int)dr[0];
                    MidiTrack.MidiID = (int)dr[1];
                    MidiTrack.Number = (int)dr[2];
                    MidiTrack.Instrument = dr[3].ToString();

                    // 根据乐器名称智能推测轨道类型
                    MidiTrack.TrackType = GuessTrackTypeFromInstrument(MidiTrack.Instrument);

                    MidiTracks.Add(MidiTrack);
                }
                return MidiTracks;
            }
            catch (Exception ex)
            {
                throw new Exception($"查询MIDI轨道信息时发生错误：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据乐器名称智能推测轨道类型
        /// </summary>
        private TrackType GuessTrackTypeFromInstrument(string instrument)
        {
            if (string.IsNullOrEmpty(instrument))
                return TrackType.Unknown;

            string inst = instrument.ToLower();

            // 鼓类
            if (inst.Contains("drum") || inst.Contains("percussion") || inst.Contains("kit"))
                return TrackType.Drums;

            // 贝斯类
            if (inst.Contains("bass"))
                return TrackType.Bass;

            // 钢琴类 (通常是主旋律或和弦)
            if (inst.Contains("piano") || inst.Contains("keyboard"))
                return TrackType.MainMelody;

            // 弦乐类 (通常是旋律或和声)
            if (inst.Contains("string") || inst.Contains("violin") || inst.Contains("viola") || inst.Contains("cello"))
                return TrackType.SubMelody;

            // 吉他类
            if (inst.Contains("guitar"))
                return TrackType.Chord;

            // 管乐类 (通常是旋律)
            if (inst.Contains("trumpet") || inst.Contains("sax") || inst.Contains("flute") || inst.Contains("clarinet"))
                return TrackType.MainMelody;

            // 合成器类 (通常是背景或特效)
            if (inst.Contains("synth") || inst.Contains("pad") || inst.Contains("lead"))
                return TrackType.Background;

            return TrackType.Unknown;
        }

        public IList<MidiTrackList> MidiTrackList(int midiID)
        {
            try
            {
                string sql = "select * from MidiTrackList where MidiID=@midiID";
                SqlHelper helper = new SqlHelper();
                System.Data.SqlClient.SqlParameter[] parameters = new System.Data.SqlClient.SqlParameter[]
                {
                    new System.Data.SqlClient.SqlParameter("@midiID", midiID)
                };

                DataSet ds = helper.ExecuteDataSet(sql, parameters);
                if (ds == null || ds.Tables.Count == 0) return new List<MidiTrackList>();

                DataTable dt = ds.Tables[0];
                IList<MidiTrackList> MidiTracks = new List<MidiTrackList>();
                foreach (DataRow dr in dt.Rows)
                {
                    MidiTrackList MidiTrackList = new MidiTrackList();
                    MidiTrackList.MidiTrackListID = (int)dr[0];
                    MidiTrackList.MidiTrackID = (int)dr[1];
                    MidiTrackList.Channel = (int)dr[2];
                    MidiTrackList.Duration = (int)dr[3];
                    MidiTrackList.SyllabelNumber = (int)dr[4];
                    MidiTrackList.StartTime = (int)dr[5];
                    MidiTrackList.EndTime = (int)dr[6];
                    MidiTrackList.MidiId = (int)dr[7];
                    MidiTracks.Add(MidiTrackList);
                }
                return MidiTracks;
            }
            catch (Exception ex)
            {
                throw new Exception($"查询MIDI轨道列表信息时发生错误：{ex.Message}", ex);
            }
        }
    }
}
