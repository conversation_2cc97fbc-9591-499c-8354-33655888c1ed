﻿<Window x:Class="MyWPF.Layout.BackGround.ReadMusicXMLInfo"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MyWPF.Layout.BackGround"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="MusicXML信息管理" Height="700" Width="1200" MinHeight="600" MinWidth="1000"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <Style x:Key="contentCenterStyle" TargetType="{x:Type TextBlock}">
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
    </Window.Resources>

    <materialDesign:DialogHost Identifier="MusicXMLInfoDialog">
        <Grid Name="_grid">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 顶部标题栏 -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
                <DockPanel>
                    <materialDesign:PackIcon Kind="FileMusic" Width="24" Height="24" VerticalAlignment="Center" DockPanel.Dock="Left"/>
                    <TextBlock Text="MusicXML信息管理" FontSize="20" FontWeight="Medium" VerticalAlignment="Center" Margin="16,0,0,0" DockPanel.Dock="Left"/>

                    <!-- 操作按钮 -->
                    <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                        <Button Name="openMusicXML" Content="打开MusicXML文件" Click="openMusicXML_Click"
                              Style="{StaticResource MaterialDesignRaisedLightButton}" Margin="4">
                            <Button.ToolTip>
                                <ToolTip Content="选择并打开MusicXML文件"/>
                            </Button.ToolTip>
                        </Button>
                    </StackPanel>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- 主要内容区域 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 设置面板 -->
                    <materialDesign:Card Grid.Row="0" Style="{StaticResource MaterialCard}" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="生成设置" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- 难度模式选择 -->
                                <StackPanel Grid.Column="0" Margin="0,0,16,0">
                                    <TextBlock Text="难度模式" Style="{StaticResource MaterialDesignSubtitle2TextBlock}" Margin="0,0,0,8"/>
                                    <ComboBox Name="cmMode" SelectedIndex="2"
                                            materialDesign:HintAssist.Hint="选择难度"
                                            Style="{StaticResource MaterialDesignComboBox}">
                                        <ComboBoxItem Name="cbModeEasy">简单</ComboBoxItem>
                                        <ComboBoxItem Name="cbModeCommon">一般</ComboBoxItem>
                                        <ComboBoxItem Name="cbModeHard">困难</ComboBoxItem>
                                        <ComboBoxItem Name="cbModeVeryHard">变态</ComboBoxItem>
                                    </ComboBox>
                                </StackPanel>

                                <!-- 游戏模式选择 -->
                                <StackPanel Grid.Column="1" Margin="0,0,16,0">
                                    <TextBlock Text="游戏模式" Style="{StaticResource MaterialDesignSubtitle2TextBlock}" Margin="0,0,0,8"/>
                                    <ComboBox Name="cmGameMode" SelectedIndex="0"
                                            materialDesign:HintAssist.Hint="选择游戏模式"
                                            Style="{StaticResource MaterialDesignComboBox}">
                                        <ComboBoxItem Name="cbGameModeIdol4">星动4轨</ComboBoxItem>
                                        <ComboBoxItem Name="cbGameModeIdol5">星动5轨</ComboBoxItem>
                                        <ComboBoxItem Name="cbGameModeCommon">弹珠</ComboBoxItem>
                                        <ComboBoxItem Name="cbGameModeHard">泡泡</ComboBoxItem>
                                        <ComboBoxItem Name="cbGameModeVeryHard">弦月</ComboBoxItem>
                                    </ComboBox>
                                </StackPanel>

                                <!-- 操作按钮 -->
                                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Bottom">
                                    <Button Name="btnCreateSingleChannelNote" Content="创建单轨" Click="BtnCreateSingleChannelNote_Click"
                                          Style="{StaticResource MaterialRaisedButton}" Margin="0,0,8,0">
                                        <Button.ToolTip>
                                            <ToolTip Content="创建单轨道谱面"/>
                                        </Button.ToolTip>
                                    </Button>

                                    <Button Name="btnCreateAllNote" Content="创建完整" Click="BtnCreateAllNote_Click"
                                          Style="{StaticResource MaterialRaisedButton}">
                                        <Button.ToolTip>
                                            <ToolTip Content="创建完整谱面"/>
                                        </Button.ToolTip>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 乐器信息显示 -->
                    <materialDesign:Card Grid.Row="1" Style="{StaticResource MaterialCard}">
                        <StackPanel>
                            <TextBlock Text="乐器信息" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                            <DataGrid Name="_instrumentData"
                                    AutoGenerateColumns="False"
                                    IsReadOnly="True"
                                    materialDesign:DataGridAssist.CellPadding="8"
                                    materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                                    CanUserAddRows="False"
                                    CanUserDeleteRows="False"
                                    CanUserReorderColumns="False"
                                    CanUserResizeRows="False"
                                    SelectionMode="Single"
                                    GridLinesVisibility="Horizontal"
                                    HorizontalGridLinesBrush="{DynamicResource MaterialDesignDivider}"
                                    MinHeight="300">

                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="ID" Visibility="Collapsed" Binding="{Binding MidiTrackID}"/>
                                    <DataGridTextColumn Header="乐器名称" Width="*" Binding="{Binding Instrument}"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </materialDesign:Card>
                </Grid>
            </ScrollViewer>
        </Grid>
    </materialDesign:DialogHost>
</Window>
