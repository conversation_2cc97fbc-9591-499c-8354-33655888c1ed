"""
难度预测器

预测谱面的难度等级和游戏性评分
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class DifficultyPredictor(nn.Module):
    """难度预测模型"""
    
    def __init__(
        self,
        input_dim: int = 28,
        hidden_dim: int = 128,
        num_layers: int = 2,
        dropout: float = 0.3
    ):
        """
        初始化难度预测器
        
        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            num_layers: 网络层数
            dropout: Dropout比例
        """
        super(DifficultyPredictor, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # 特征提取网络
        layers = []
        current_dim = input_dim
        
        for i in range(num_layers):
            layers.extend([
                nn.Linear(current_dim, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(dropout)
            ])
            current_dim = hidden_dim
            hidden_dim = max(hidden_dim // 2, 32)  # 逐层减少维度
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # 难度回归头
        self.difficulty_head = nn.Sequential(
            nn.Linear(current_dim, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),  # 输出难度值 (1-10)
            nn.Sigmoid()  # 确保输出在0-1之间，后续会缩放到1-10
        )
        
        # 游戏性评分头
        self.playability_head = nn.Sequential(
            nn.Linear(current_dim, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),  # 输出游戏性评分 (0-1)
            nn.Sigmoid()
        )
        
        # 音符类型分布预测头
        self.note_distribution_head = nn.Sequential(
            nn.Linear(current_dim, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 3),  # 3种音符类型的分布
            nn.Softmax(dim=-1)
        )
        
    def forward(self, features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            features: 输入特征 [batch_size, input_dim]
            
        Returns:
            Dict: 预测结果
        """
        # 特征提取
        extracted_features = self.feature_extractor(features)
        
        # 各种预测
        difficulty = self.difficulty_head(extracted_features)
        playability = self.playability_head(extracted_features)
        note_distribution = self.note_distribution_head(extracted_features)
        
        # 将难度从0-1缩放到1-10
        difficulty_scaled = difficulty * 9 + 1
        
        return {
            'difficulty': difficulty_scaled,
            'playability': playability,
            'note_distribution': note_distribution,
            'features': extracted_features
        }
    
    def predict_difficulty(self, features: np.ndarray) -> Dict[str, float]:
        """
        预测单个样本的难度
        
        Args:
            features: 特征向量
            
        Returns:
            Dict: 预测结果
        """
        self.eval()
        
        with torch.no_grad():
            if isinstance(features, np.ndarray):
                features = torch.tensor(features, dtype=torch.float32)
            
            if features.dim() == 1:
                features = features.unsqueeze(0)  # 添加batch维度
            
            outputs = self.forward(features)
            
            return {
                'difficulty': outputs['difficulty'].item(),
                'playability': outputs['playability'].item(),
                'note_distribution': outputs['note_distribution'].squeeze().tolist()
            }
    
    def calculate_loss(
        self,
        predictions: Dict[str, torch.Tensor],
        targets: Dict[str, torch.Tensor],
        weights: Optional[Dict[str, float]] = None
    ) -> torch.Tensor:
        """
        计算损失函数
        
        Args:
            predictions: 模型预测结果
            targets: 目标值
            weights: 各项损失权重
            
        Returns:
            torch.Tensor: 总损失
        """
        if weights is None:
            weights = {
                'difficulty': 1.0,
                'playability': 0.5,
                'note_distribution': 0.3
            }
        
        total_loss = 0.0
        
        # 难度损失 (MSE)
        if 'difficulty' in targets:
            difficulty_loss = F.mse_loss(predictions['difficulty'], targets['difficulty'])
            total_loss += weights.get('difficulty', 1.0) * difficulty_loss
        
        # 游戏性损失 (MSE)
        if 'playability' in targets:
            playability_loss = F.mse_loss(predictions['playability'], targets['playability'])
            total_loss += weights.get('playability', 0.5) * playability_loss
        
        # 音符分布损失 (KL散度)
        if 'note_distribution' in targets:
            note_dist_loss = F.kl_div(
                torch.log(predictions['note_distribution'] + 1e-8),
                targets['note_distribution'],
                reduction='batchmean'
            )
            total_loss += weights.get('note_distribution', 0.3) * note_dist_loss
        
        return total_loss


class ChartQualityAnalyzer:
    """谱面质量分析器"""
    
    def __init__(self):
        self.difficulty_predictor = None
    
    def load_predictor(self, model_path: str):
        """加载难度预测模型"""
        self.difficulty_predictor = DifficultyPredictor.load_model(model_path)
    
    def analyze_chart_quality(self, chart_data: Dict, features: np.ndarray) -> Dict:
        """
        分析谱面质量
        
        Args:
            chart_data: 谱面数据
            features: 音乐特征
            
        Returns:
            Dict: 质量分析结果
        """
        analysis = {}
        
        # 1. 基础统计分析
        analysis['basic_stats'] = self._analyze_basic_stats(chart_data)
        
        # 2. 难度一致性分析
        analysis['difficulty_consistency'] = self._analyze_difficulty_consistency(chart_data)
        
        # 3. 音符分布分析
        analysis['note_distribution'] = self._analyze_note_distribution(chart_data)
        
        # 4. 节奏匹配度分析
        analysis['rhythm_matching'] = self._analyze_rhythm_matching(chart_data, features)
        
        # 5. 游戏性评分
        analysis['playability'] = self._calculate_playability_score(chart_data)
        
        # 6. 如果有预测模型，使用模型预测
        if self.difficulty_predictor:
            model_prediction = self.difficulty_predictor.predict_difficulty(features)
            analysis['model_prediction'] = model_prediction
        
        # 7. 综合评分
        analysis['overall_score'] = self._calculate_overall_score(analysis)
        
        return analysis
    
    def _analyze_basic_stats(self, chart_data: Dict) -> Dict:
        """分析基础统计信息"""
        total_notes = 0
        track_distribution = {}
        
        for track_name, notes in chart_data.items():
            if track_name.startswith('track_'):
                note_count = sum(1 for note in notes if note > 0)
                total_notes += note_count
                track_distribution[track_name] = note_count
        
        return {
            'total_notes': total_notes,
            'track_distribution': track_distribution,
            'avg_notes_per_track': total_notes / len(track_distribution) if track_distribution else 0
        }
    
    def _analyze_difficulty_consistency(self, chart_data: Dict) -> Dict:
        """分析难度一致性"""
        # 计算音符密度变化
        density_variations = []
        
        for track_name, notes in chart_data.items():
            if track_name.startswith('track_'):
                # 计算每个时间窗口的音符密度
                window_size = 20  # 20个时间步为一个窗口
                densities = []
                
                for i in range(0, len(notes), window_size):
                    window = notes[i:i+window_size]
                    density = sum(1 for note in window if note > 0) / len(window)
                    densities.append(density)
                
                if len(densities) > 1:
                    variation = np.std(densities)
                    density_variations.append(variation)
        
        avg_variation = np.mean(density_variations) if density_variations else 0
        
        return {
            'density_variation': avg_variation,
            'consistency_score': max(0, 1 - avg_variation * 2)  # 变化越小，一致性越高
        }
    
    def _analyze_note_distribution(self, chart_data: Dict) -> Dict:
        """分析音符类型分布"""
        note_type_counts = {0: 0, 1: 0, 2: 0}  # 无音符、短音符、长音符
        
        for track_name, notes in chart_data.items():
            if track_name.startswith('track_'):
                for note in notes:
                    note_type_counts[note] += 1
        
        total = sum(note_type_counts.values())
        if total == 0:
            return {'error': '没有音符数据'}
        
        distribution = {
            'no_note_ratio': note_type_counts[0] / total,
            'short_note_ratio': note_type_counts[1] / total,
            'long_note_ratio': note_type_counts[2] / total
        }
        
        # 评估分布合理性
        # 理想情况：60-80%空白，15-30%短音符，5-15%长音符
        ideal_ranges = {
            'no_note_ratio': (0.6, 0.8),
            'short_note_ratio': (0.15, 0.3),
            'long_note_ratio': (0.05, 0.15)
        }
        
        distribution_score = 0
        for key, (min_val, max_val) in ideal_ranges.items():
            actual_val = distribution[key]
            if min_val <= actual_val <= max_val:
                distribution_score += 1
            else:
                # 计算偏离程度
                if actual_val < min_val:
                    deviation = (min_val - actual_val) / min_val
                else:
                    deviation = (actual_val - max_val) / max_val
                distribution_score += max(0, 1 - deviation)
        
        distribution['distribution_score'] = distribution_score / 3
        
        return distribution
    
    def _analyze_rhythm_matching(self, chart_data: Dict, features: np.ndarray) -> Dict:
        """分析节奏匹配度"""
        # 这里简化处理，实际应该与音频特征进行对比
        # 计算音符的节奏规律性
        
        rhythm_scores = []
        
        for track_name, notes in chart_data.items():
            if track_name.startswith('track_'):
                # 找出音符位置
                note_positions = [i for i, note in enumerate(notes) if note > 0]
                
                if len(note_positions) < 3:
                    continue
                
                # 计算音符间隔
                intervals = [note_positions[i] - note_positions[i-1] for i in range(1, len(note_positions))]
                
                # 计算间隔的规律性（标准差越小越规律）
                if intervals:
                    interval_std = np.std(intervals)
                    regularity = 1.0 / (1.0 + interval_std / 10)  # 归一化
                    rhythm_scores.append(regularity)
        
        avg_rhythm_score = np.mean(rhythm_scores) if rhythm_scores else 0
        
        return {
            'rhythm_regularity': avg_rhythm_score,
            'rhythm_score': avg_rhythm_score
        }
    
    def _calculate_playability_score(self, chart_data: Dict) -> Dict:
        """计算游戏性评分"""
        scores = []
        
        # 1. 音符分布均匀性
        track_note_counts = []
        for track_name, notes in chart_data.items():
            if track_name.startswith('track_'):
                count = sum(1 for note in notes if note > 0)
                track_note_counts.append(count)
        
        if track_note_counts:
            balance_score = 1.0 - (np.std(track_note_counts) / (np.mean(track_note_counts) + 1))
            scores.append(balance_score)
        
        # 2. 音符密度适中性
        total_notes = sum(track_note_counts) if track_note_counts else 0
        total_positions = sum(len(notes) for track_name, notes in chart_data.items() 
                            if track_name.startswith('track_'))
        
        if total_positions > 0:
            density = total_notes / total_positions
            # 理想密度在0.1-0.3之间
            if 0.1 <= density <= 0.3:
                density_score = 1.0
            else:
                density_score = max(0, 1 - abs(density - 0.2) / 0.2)
            scores.append(density_score)
        
        # 3. 避免过于复杂的模式
        complexity_penalty = 0
        for track_name, notes in chart_data.items():
            if track_name.startswith('track_'):
                # 检查连续长音符
                consecutive_long = 0
                max_consecutive = 0
                for note in notes:
                    if note == 2:  # 长音符
                        consecutive_long += 1
                        max_consecutive = max(max_consecutive, consecutive_long)
                    else:
                        consecutive_long = 0
                
                if max_consecutive > 5:  # 超过5个连续长音符
                    complexity_penalty += 0.1
        
        complexity_score = max(0, 1 - complexity_penalty)
        scores.append(complexity_score)
        
        overall_playability = np.mean(scores) if scores else 0
        
        return {
            'balance_score': scores[0] if len(scores) > 0 else 0,
            'density_score': scores[1] if len(scores) > 1 else 0,
            'complexity_score': scores[2] if len(scores) > 2 else 0,
            'overall_playability': overall_playability
        }
    
    def _calculate_overall_score(self, analysis: Dict) -> float:
        """计算综合评分"""
        scores = []
        weights = []
        
        # 收集各项评分
        if 'difficulty_consistency' in analysis:
            scores.append(analysis['difficulty_consistency']['consistency_score'])
            weights.append(0.2)
        
        if 'note_distribution' in analysis and 'distribution_score' in analysis['note_distribution']:
            scores.append(analysis['note_distribution']['distribution_score'])
            weights.append(0.2)
        
        if 'rhythm_matching' in analysis:
            scores.append(analysis['rhythm_matching']['rhythm_score'])
            weights.append(0.3)
        
        if 'playability' in analysis:
            scores.append(analysis['playability']['overall_playability'])
            weights.append(0.3)
        
        if scores and weights:
            weighted_score = sum(s * w for s, w in zip(scores, weights)) / sum(weights)
            return weighted_score
        
        return 0.0
