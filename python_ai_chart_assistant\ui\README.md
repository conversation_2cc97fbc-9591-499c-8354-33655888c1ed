# 🎵 AI音游写谱助手 - UI界面

基于Streamlit的现代化Web用户界面，提供直观易用的AI谱面生成功能。

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装Streamlit相关依赖
pip install streamlit>=1.28.0 streamlit-option-menu>=0.3.6 plotly>=5.15.0

# 或者安装完整依赖
pip install -r requirements.txt
```

### 2. 启动UI界面

```bash
# 方式1: 使用启动脚本（推荐）
python run_ui.py

# 方式2: 直接运行Streamlit
streamlit run ui/streamlit_app.py

# 方式3: 指定端口和地址
python run_ui.py --host 0.0.0.0 --port 8502
```

### 3. 访问界面

打开浏览器访问: http://localhost:8501

## 🎮 功能介绍

### 🎵 谱面生成
- 上传MIDI文件
- 选择输出格式（节奏大师/Malody/osu!）
- 设置难度等级和生成风格
- 一键生成并下载谱面

### 🔍 MIDI分析
- 分析MIDI文件的音乐特征
- 显示BPM、时长、音符数等信息
- 检测主旋律和和弦进行
- 分析节奏模式

### 🎯 难度预测
- AI预测MIDI文件的游戏难度
- 显示置信度和游戏性评分
- 预测音符分布情况

### 🔄 格式转换
- 在不同音游格式间转换谱面
- 支持节奏大师、Malody、osu!格式
- 保留原始元数据

### 🚀 模型训练
- 可视化训练参数设置
- 数据验证和检查
- 训练进度监控
- 生成等效命令行命令

### ⚙️ 系统设置
- 模型路径配置
- 计算设备选择
- 输出参数设置
- 日志级别调整

## 📊 界面特色

### 🎨 现代化设计
- 响应式布局，支持各种屏幕尺寸
- 直观的图标和颜色系统
- 清晰的信息层次结构

### 🔧 易于使用
- 拖拽上传文件
- 实时参数预览
- 一键下载结果
- 详细的帮助提示

### 📈 可视化展示
- 实时进度条
- 数据图表展示
- 训练过程可视化
- 系统状态监控

## 🛠️ 技术架构

### 前端框架
- **Streamlit**: 主要UI框架
- **Plotly**: 数据可视化
- **CSS**: 自定义样式

### 后端集成
- 直接调用项目核心模块
- 异步文件处理
- 临时文件管理
- 错误处理和日志

### 数据流程
```
用户上传 → 临时存储 → AI处理 → 结果展示 → 文件下载
```

## 🔧 配置说明

### 环境变量
```bash
# 可选：设置默认配置
export AI_CHART_CONFIG_PATH="config/default_config.yaml"
export AI_CHART_MODEL_PATH="models/"
export AI_CHART_DATA_PATH="data/"
```

### 配置文件
UI界面会自动读取 `config/default_config.yaml` 中的配置，也可以通过界面进行修改。

## 🐛 故障排除

### 常见问题

1. **模块导入失败**
   ```bash
   # 确保在项目根目录运行
   cd python_ai_chart_assistant
   python run_ui.py
   ```

2. **端口被占用**
   ```bash
   # 使用不同端口
   python run_ui.py --port 8502
   ```

3. **依赖包缺失**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt
   ```

4. **模型文件不存在**
   - 检查模型文件路径
   - 先进行模型训练
   - 或下载预训练模型

### 性能优化

1. **内存使用**
   - 在系统设置中调整批处理大小
   - 限制最大内存使用

2. **处理速度**
   - 使用GPU加速（如果可用）
   - 调整工作线程数量

3. **文件大小**
   - 大文件会自动分块处理
   - 建议MIDI文件小于10MB

## 📝 更新日志

### v1.0.0
- ✅ 基础UI框架搭建
- ✅ 谱面生成功能
- ✅ MIDI分析功能
- ✅ 难度预测功能
- ✅ 格式转换功能
- ✅ 模型训练界面
- ✅ 系统设置页面

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进UI界面！

### 开发环境
```bash
# 克隆项目
git clone <repository>
cd python_ai_chart_assistant

# 安装开发依赖
pip install -r requirements.txt

# 启动开发服务器
streamlit run ui/streamlit_app.py --logger.level debug
```

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。
