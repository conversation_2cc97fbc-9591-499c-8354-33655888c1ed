﻿using RecordMusicalNote.IRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote.Library
{


    /// <summary>
    /// 用来记录每个时刻(pos)的分数
    /// </summary>
    public class IdolScoreDetailInfo: IIdolScoreDetailInfo
    {
        public int Id { get; set; }
        public double ScoreNumInXXAndLab
        {
            get
            {
                if (CurrentCombo < 20 && (NoteType == 1 || NoteType == 2))
                    return 2600;
                else if (CurrentCombo < 50 && (NoteType == 1 || NoteType == 2))
                    return 2860;
                else if (CurrentCombo < 100 && (NoteType == 1 || NoteType == 2))
                    return 2989;
                else if (CurrentCombo >=100 && (NoteType == 1 || NoteType == 2))
                    return 3120;
                else if (CurrentCombo < 20 && NoteType == 3)
                    return 780;
                else if (CurrentCombo < 50 && NoteType == 3)
                    return 858;
                else if (CurrentCombo < 100 && NoteType == 3)
                    return 896;
                else if (CurrentCombo >=100 && NoteType == 3)
                    return 936;
                return 0;
            }

        }
        public double ScoreNumInBRAndRank
        {

            get
            {
                if (CurrentCombo >= 50 && CurrentCombo < 100)
                    return ScoreNumInXXAndLab + 1;
                else
                    return ScoreNumInXXAndLab;
            }

        }
        public int Bar { get; set; }

        public int Pos { get; set; }

        public int EndBar { get; set; }

        public int EndPos { get; set; }

        public int CurrentCount { get; set; }

        public int EndCount { get; set; }
        public int CurrentCombo { get; set; }

        public int StartCombo { get; set; }
        public int EndCombo { get; set; }

        /// <summary>
        /// 爆气时候所获得的总分数
        /// </summary>
        public double ScoreNumBQTechAndInFire
        {
            get
            {
                if (CurrentCombo < 20 && (NoteType == 1 || NoteType == 2))
                    return 7020;
                else if (CurrentCombo < 50 && (NoteType == 1 || NoteType == 2))
                    return 7722;
                else if (CurrentCombo < 100 && (NoteType == 1 || NoteType == 2))
                    return 8073;
                else if (CurrentCombo >= 100 && (NoteType == 1 || NoteType == 2))
                    return 8424;
                else if (CurrentCombo < 20 && NoteType == 3)
                    return 2106;
                else if (CurrentCombo < 50 && NoteType == 3)
                    return 2316;
                else if (CurrentCombo < 100 && NoteType == 3)
                    return 2421;
                else if (CurrentCombo >= 100 && NoteType == 3)
                    return 2527;
                return 0;
            }
        }

        public double ScoreNumJXTechAndNotInFire
        {
            get
            {
                if (CurrentCombo < 20 && (NoteType == 1 || NoteType == 2))
                    return 2886;
                else if (CurrentCombo < 50 && (NoteType == 1 || NoteType == 2))
                    return 3174;
                else if (CurrentCombo < 100 && (NoteType == 1 || NoteType == 2))
                    return 3318;
                else if (CurrentCombo >= 100 && (NoteType == 1 || NoteType == 2))
                    return 3463;
                else if (CurrentCombo < 20 && NoteType == 3)
                    return 865;
                else if (CurrentCombo < 50 && NoteType == 3)
                    return 952;
                else if (CurrentCombo < 100 && NoteType == 3)
                    return 995;
                else if (CurrentCombo >= 100 && NoteType == 3)
                    return 1038;
                return 0;
            }
        }

        public double ScoreNumJXTechAndInFire
        {
            get
            {
                if (CurrentCombo < 20 && (NoteType == 1 || NoteType == 2))
                    return 4329;
                else if (CurrentCombo < 50 && (NoteType == 1 || NoteType == 2))
                    return 4761;
                else if (CurrentCombo < 100 && (NoteType == 1 || NoteType == 2))
                    return 4977;
                else if (CurrentCombo >= 100 && (NoteType == 1 || NoteType == 2))
                    return 5194;
                else if (CurrentCombo < 20 && NoteType == 3)
                    return 1297;
                else if (CurrentCombo < 50 && NoteType == 3)
                    return 1428;
                else if (CurrentCombo < 100 && NoteType == 3)
                    return 1492;
                else if (CurrentCombo >= 100 && NoteType == 3)
                    return 1557;
                return 0;
            }
        }

    
       

        public int NoteType {get; set ; }
        public double ShowScore { get ; set; }

        public string NoteTypeStr
        {
            get
            {
                if (NoteType == 1)
                    return "单点";
                else if (NoteType == 2)
                    return "滑键";
               else if (NoteType == 3)
                    return "长条";
                return "";
            }
        }

        public int SonId { get; set; }
        public int XMLNoteId { get ; set ; }
    }

    public class PinballScoreDetailInfo : IPinballScoreDetailInfo
    {
        public int Id { get; set; }
        public double ScoreNumInXXAndLab
        {
            get
            {
                if (CurrentCombo < 20 && (NoteType == 1 || NoteType == 3))//1 pinballSingle 3 series
                    return 2600;
                else if (CurrentCombo < 50 && (NoteType == 1 || NoteType == 3))
                    return 2860;
                else if (CurrentCombo < 100 && (NoteType == 1 || NoteType == 3))
                    return 2990;
                else if (CurrentCombo >= 100 && (NoteType == 1 || NoteType == 3))
                    return 3120;
                else if (CurrentCombo < 20 && NoteType == 4)
                    return 780;
                else if (CurrentCombo < 50 && NoteType == 4)
                    return 858;
                else if (CurrentCombo < 100 && NoteType == 4)
                    return 897;
                else if (CurrentCombo >= 100 && NoteType == 4)
                    return 936;
                else if (CurrentCombo < 20 && NoteType == 2)
                {
                    if (SonId > 0)
                        return 2600;
                     return 5200;
                }
                else if (CurrentCombo < 50 && NoteType == 2)
                {
                    if (SonId > 0)
                     return 2860;
                     return 5720;
                }
                else if (CurrentCombo < 100 && NoteType == 2)
                {
                    if (SonId > 0)
                      return 2990;
                     return 5980;
                }
                else if (CurrentCombo >= 100 && NoteType == 2)
                {
                    if (SonId > 0)
                        return 3120;
                     return 6240;
                }
                return 0;
            }
                
         }
        public double ScoreNumInBRAndRank
        {

            get
            {
               // if (CurrentCombo >= 50 && CurrentCombo < 100)
                  //  return ScoreNumInXXAndLab + 1;
              //  else
                    return ScoreNumInXXAndLab;
            }

        }
        public int Bar { get; set; }

        public int Pos { get; set; }

        public int EndBar { get; set; }

        public int EndPos { get; set; }

        public int CurrentCount { get; set; }

        public int EndCount { get; set; }
        public int CurrentCombo { get; set; }

        public int StartCombo { get; set; }
        public int EndCombo { get; set; }

        /// <summary>
        /// 爆气时候所获得的总分数
        /// </summary>
        public double ScoreNumBQTechAndInFire
        {
            get
            {
                if (CurrentCombo < 20 && (NoteType == 1 || NoteType == 3))
                    return 7020;
                else if (CurrentCombo < 50 && (NoteType == 1 || NoteType == 3))
                    return 7722;
                else if (CurrentCombo < 100 && (NoteType == 1 || NoteType == 3))
                    return 8073;
                else if (CurrentCombo >=100 && (NoteType == 1 || NoteType == 3))
                    return 8424;
                else if (CurrentCombo < 20 && NoteType == 4)
                    return 2106;
                else if (CurrentCombo < 50 && NoteType == 4)
                    return 2316;
                else if (CurrentCombo < 100 && NoteType == 4)
                    return 2421;
                else if (CurrentCombo>=100 && NoteType == 4)
                    return 2527;
                else if (CurrentCombo < 20 && NoteType == 2)
                {
                    if (SonId > 0)
                    return 7020;
                    return 14040;
                }
                
                else if (CurrentCombo < 50 && NoteType == 2)
                {
                    if (SonId > 0)
                      return 7722;
                    return 15444;
                }
                else if (CurrentCombo < 100 && NoteType == 2)
                {
                    if (SonId > 0)
                        return 8073;
                    return 16146;
                }

                else if (CurrentCombo >=100 && NoteType == 2)
                {
                    if (SonId > 0)
                        return 8424;
                        return 16848;
                }
                 
                else return 0;
            }
        }

        public double ScoreNumJXTechAndNotInFire
        {
            get
            {
                if (CurrentCombo < 20 && (NoteType == 1 || NoteType == 3))
                    return 2886;
                else if (CurrentCombo < 50 && (NoteType == 1 || NoteType == 3))
                    return 3174;
                else if (CurrentCombo < 100 && (NoteType == 1 || NoteType == 3))
                    return 3318;
                else if (CurrentCombo >= 100 && (NoteType == 1 || NoteType == 3))
                    return 3463;
                else if (CurrentCombo < 20 && NoteType == 4)
                    return 865;
                else if (CurrentCombo < 50 && NoteType == 4)
                    return 952;
                else if (CurrentCombo < 100 && NoteType == 4)
                    return 995;
                else if (CurrentCombo >= 100 && NoteType == 4)
                    return 1038;
                else if (CurrentCombo < 20 && NoteType == 2)
                {
                    if (SonId > 0)
                        return 2886;
                    return 5772;
                }
                else if (CurrentCombo < 50 && NoteType == 2)
                {
                    if (SonId > 0)
                        return 3174;
                    return 6349;
                }
                else if (CurrentCombo < 100 && NoteType == 2)
                {
                    if (SonId > 0)

                    return 3318;
                    return 6637;
                }
                else if (CurrentCombo >=100 && NoteType == 2)
                {
                    if (SonId > 0)
                      return 3463;
                    return 6926;
                }
                else return 0;
            }
        }

        public double ScoreNumJXTechAndInFire
        {
            get
            {
                if (CurrentCombo < 20 && (NoteType == 1 || NoteType == 3))
                    return 4329;
                else if (CurrentCombo < 50 && (NoteType == 1 || NoteType == 3))
                    return 4761;
                else if (CurrentCombo < 100 && (NoteType == 1 || NoteType == 3))
                    return 4977;
                else if (CurrentCombo >= 100 && (NoteType == 1 || NoteType == 3))
                    return 5194;
                else if (CurrentCombo < 20 && NoteType == 4)
                    return 1297;
                else if (CurrentCombo < 50 && NoteType == 4)
                    return 1428;
                else if (CurrentCombo < 100 && NoteType == 4)
                    return 1492;
                else if (CurrentCombo >= 100 && NoteType == 4)
                    return 1557;
                else if (CurrentCombo < 20 && NoteType == 2)
                {
                    if (SonId > 0)
                        return 4392;
                    return 8658;
                }
                else if (CurrentCombo < 50 && NoteType == 2)
                {
                    if (SonId > 0)
                        return 4761;
                    return 9523;
                }
                else if (CurrentCombo < 100 && NoteType == 2)
                {
                    if (SonId > 0)
                        return 4977;
                    return 9955;
                }
                else if (CurrentCombo >=100 && NoteType == 2)
                {
                    if (SonId > 0)
                        return 5194;
                    return 10389;
                }
                else return 0;
            }
        }


        public int NoteType { get; set; }
        public int SonId { get ; set ; }
        public double ShowScore { get; set; }
        public string NoteTypeStr
        {
            get
            {
                if (NoteType == 1)
                    return "单点";
                else if (NoteType == 2)
                    return "滑键";
                else if (NoteType == 3)
                    return "连点单点";
                else if (NoteType == 4)
                    return "长条";
                return "";
            }
        }

        public int XMLNoteId { get ; set; }
    }

    public class BubbleScoreDetailInfo : IBubbleScoreDetailInfo
    {
        public int Id { get; set; }
        public double ScoreNumInXXAndLab
        {
            get
            {
                if (CurrentCombo < 20 && (NoteType == 1 || NoteType == 2))//1 单点 2蓝条（滑动）
                    return 2600;
                else if (CurrentCombo < 50 && (NoteType == 1 || NoteType == 2))
                    return 2860;
                else if (CurrentCombo < 100 && (NoteType == 1 || NoteType == 2))
                    return 2990;
                else if (CurrentCombo >= 100 && (NoteType == 1 || NoteType == 2))
                    return 3120;
                else if (CurrentCombo < 20 && NoteType == 3)
                    return 780;
                else if (CurrentCombo < 50 && NoteType == 3)
                    return 858;
                else if (CurrentCombo < 100 && NoteType == 3)
                    return 897;
                else if (CurrentCombo >= 100 && NoteType == 3)
                    return 936;
                return 0;
            }

        }
        public double ScoreNumInBRAndRank
        {

            get
            {
                //if (CurrentCombo >= 50 && CurrentCombo < 100)
                //    return ScoreNumInXXAndLab + 1;
                //else
                    return ScoreNumInXXAndLab;
            }

        }
        public int Bar { get; set; }

        public int Pos { get; set; }

        public int EndBar { get; set; }

        public int EndPos { get; set; }

        public int CurrentCount { get; set; }

        public int EndCount { get; set; }
        public int CurrentCombo { get; set; }

        public int StartCombo { get; set; }
        public int EndCombo { get; set; }

        /// <summary>
        /// 爆气时候所获得的总分数
        /// </summary>
        public double ScoreNumBQTechAndInFire
        {
            get
            {
                if (CurrentCombo < 20 && (NoteType == 1 || NoteType == 2))
                    return 7020;
                else if (CurrentCombo < 50 && (NoteType == 1 || NoteType == 2))
                    return 7722;
                else if (CurrentCombo < 100 && (NoteType == 1 || NoteType == 2))
                    return 8073;
                else if (CurrentCombo >= 100 && (NoteType == 1 || NoteType == 2))
                    return 8424;
                else if (CurrentCombo < 20 && NoteType == 3)
                    return 2106;
                else if (CurrentCombo < 50 && NoteType == 3)
                    return 2316;
                else if (CurrentCombo < 100 && NoteType == 3)
                    return 2421;
                else if (CurrentCombo >= 100 && NoteType == 3)
                    return 2527;
                return 0;
            }
        }

        public double ScoreNumJXTechAndNotInFire
        {
            get
            {
                if (CurrentCombo < 20 && (NoteType == 1 || NoteType == 2))
                    return 2886;
                else if (CurrentCombo < 50 && (NoteType == 1 || NoteType == 2))
                    return 3174;
                else if (CurrentCombo < 100 && (NoteType == 1 || NoteType == 2))
                    return 3318;
                else if (CurrentCombo >= 100 && (NoteType == 1 || NoteType == 2))
                    return 3463;
                else if (CurrentCombo < 20 && NoteType == 3)
                    return 865;
                else if (CurrentCombo < 50 && NoteType == 3)
                    return 952;
                else if (CurrentCombo < 100 && NoteType == 3)
                    return 995;
                else if (CurrentCombo >= 100 && NoteType == 3)
                    return 1038;
                return 0;
            }
        }

        public double ScoreNumJXTechAndInFire
        {
            get
            {
                if (CurrentCombo < 20 && (NoteType == 1 || NoteType == 2))
                    return 4329;
                else if (CurrentCombo < 50 && (NoteType == 1 || NoteType == 2))
                    return 4761;
                else if (CurrentCombo < 100 && (NoteType == 1 || NoteType == 2))
                    return 4977;
                else if (CurrentCombo >= 100 && (NoteType == 1 || NoteType == 2))
                    return 5194;
                else if (CurrentCombo < 20 && NoteType == 3)
                    return 1297;
                else if (CurrentCombo < 50 && NoteType == 3)
                    return 1428;
                else if (CurrentCombo < 100 && NoteType == 3)
                    return 1492;
                else if (CurrentCombo >= 100 && NoteType == 3)
                    return 1557;
                return 0;
            }
        }




        public int NoteType { get; set; }
        public double ShowScore { get; set; }

        public string NoteTypeStr
        {
            get
            {
                if (NoteType == 1)
                    return "单点";
                else if (NoteType == 2)
                    return "滑键";
                else if (NoteType == 3)
                    return "长条";
                return "";
            }
        }

        public int SonId { get; set; }
        public int XMLNoteId { get; set; }
    }
}
