#!/usr/bin/env python3
"""
智能依赖安装脚本

根据Python版本和系统环境自动选择合适的依赖包版本
"""

import sys
import subprocess
import platform
from pathlib import Path

def get_python_version():
    """获取Python版本信息"""
    return sys.version_info

def install_package(package, upgrade=False):
    """安装单个包"""
    cmd = [sys.executable, "-m", "pip", "install"]
    if upgrade:
        cmd.append("--upgrade")
    cmd.append(package)
    
    try:
        print(f"正在安装: {package}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {package} 安装成功")
            return True
        else:
            print(f"❌ {package} 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安装 {package} 时出错: {e}")
        return False

def install_core_deps():
    """安装核心依赖"""
    core_packages = [
        "numpy>=1.21.0",
        "pandas>=1.3.0", 
        "scipy>=1.7.0",
        "pretty_midi>=0.2.9",
        "mido>=1.2.10",
        "matplotlib>=3.5.0",
        "plotly>=5.0.0",
        "flask>=2.2.0",
        "flask-cors>=3.0.10",
        "requests>=2.28.0",
        "pyyaml>=6.0",
        "tqdm>=4.64.0",
        "click>=8.1.0",
        "colorama>=0.4.5"
    ]
    
    print("📦 安装核心依赖包...")
    success_count = 0
    for package in core_packages:
        if install_package(package):
            success_count += 1
    
    print(f"核心依赖安装完成: {success_count}/{len(core_packages)}")
    return success_count == len(core_packages)

def install_ml_deps():
    """安装机器学习依赖"""
    python_version = get_python_version()
    
    print("🤖 安装机器学习依赖...")
    
    # PyTorch (通常兼容性较好)
    torch_packages = [
        "torch>=1.12.0",
        "torchvision>=0.13.0", 
        "torchaudio>=0.12.0",
        "scikit-learn>=1.1.0"
    ]
    
    torch_success = True
    for package in torch_packages:
        if not install_package(package):
            torch_success = False
    
    # TensorFlow (版本兼容性处理)
    tf_success = False
    if python_version >= (3, 13):
        print("⚠️ Python 3.13检测到，尝试安装TensorFlow预发布版本...")
        tf_packages = [
            "tensorflow>=2.20.0rc0",
            "tf-nightly"  # 备选方案
        ]
        for tf_package in tf_packages:
            if install_package(tf_package):
                tf_success = True
                break
    else:
        print("安装标准TensorFlow版本...")
        tf_success = install_package("tensorflow>=2.9.0")
    
    if not tf_success:
        print("⚠️ TensorFlow安装失败，将使用仅PyTorch模式")
        print("💡 项目仍可正常运行，但某些功能可能受限")
    
    return torch_success

def install_audio_deps():
    """安装音频处理依赖"""
    print("🎵 安装音频处理依赖...")
    
    audio_packages = [
        "librosa>=0.9.0",
        "soundfile>=0.10.0",
        "audioread>=2.1.9"
    ]
    
    # music21需要特殊处理
    music21_success = install_package("music21>=8.1.0")
    
    audio_success = True
    for package in audio_packages:
        if not install_package(package):
            print(f"⚠️ {package} 安装失败，某些音频功能可能不可用")
            audio_success = False
    
    return music21_success and audio_success

def install_ui_deps():
    """安装UI界面依赖"""
    print("🖥️ 安装UI界面依赖...")
    
    ui_packages = [
        "streamlit>=1.28.0",
        "streamlit-option-menu>=0.3.6"
    ]
    
    # 可选的UI增强包
    optional_ui_packages = [
        "streamlit-aggrid>=0.3.4",
        "streamlit-plotly>=0.1.0"
    ]
    
    ui_success = True
    for package in ui_packages:
        if not install_package(package):
            ui_success = False
    
    # 安装可选包
    for package in optional_ui_packages:
        install_package(package)  # 失败也不影响主要功能
    
    return ui_success

def install_dev_deps():
    """安装开发依赖"""
    print("🔧 安装开发工具...")
    
    dev_packages = [
        "pytest>=7.1.0",
        "black>=22.0.0",
        "flake8>=5.0.0"
    ]
    
    for package in dev_packages:
        install_package(package)

def main():
    """主安装流程"""
    print("🎵 AI音游写谱助手 - 智能依赖安装")
    print("=" * 50)
    
    python_version = get_python_version()
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print()
    
    # 升级pip
    print("📦 升级pip...")
    install_package("pip", upgrade=True)
    print()
    
    # 安装各类依赖
    core_ok = install_core_deps()
    print()
    
    ml_ok = install_ml_deps()
    print()
    
    audio_ok = install_audio_deps()
    print()
    
    ui_ok = install_ui_deps()
    print()
    
    # 可选：安装开发依赖
    if "--dev" in sys.argv:
        install_dev_deps()
        print()
    
    # 安装总结
    print("📋 安装总结")
    print("=" * 30)
    print(f"✅ 核心功能: {'正常' if core_ok else '异常'}")
    print(f"🤖 机器学习: {'正常' if ml_ok else '部分功能受限'}")
    print(f"🎵 音频处理: {'正常' if audio_ok else '部分功能受限'}")
    print(f"🖥️ UI界面: {'正常' if ui_ok else '异常'}")
    
    if core_ok and ui_ok:
        print("\n🎉 安装完成！可以运行以下命令启动UI:")
        print("   python run_ui.py")
    else:
        print("\n⚠️ 部分依赖安装失败，请检查错误信息")
        print("💡 你也可以尝试手动安装:")
        print("   pip install -r requirements-minimal.txt")

if __name__ == "__main__":
    main()
