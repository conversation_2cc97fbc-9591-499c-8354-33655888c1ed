/*
 * Copyright (c) 2007-2008 <PERSON><PERSON><PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License version 2.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Drawing;

using NUnit.Framework;
using MidiSheetMusic;


/* Test cases for MidiFileReader class */
[TestFixture]
public class MidiFileReaderTest {

    const string testfile = "test.txt";

    static int varlen(byte b1, byte b2, byte b3, byte b4) {
        int result = ((b1 & 0x7F) << 21) |
                     ((b2 & 0x7F) << 14) | 
                     ((b3 & 0x7F) << 7)  |
                     (b4 & 0x7F);
        return result;
    }

    static void WriteTestFile(byte[] data) {
        FileStream fileout = File.Open(testfile, FileMode.Create,
                                   FileAccess.Write);
        fileout.Write(data, 0, data.Length);
        fileout.Close();
    }

    [Test]
    /* Test that MidiFileReader.ReadByte() returns the correct
     * byte, and that the file offset is incremented by 2.
     */
    public void TestByte() {
        byte[] data = new byte[] { 10, 20, 30, 40, 50 };
        WriteTestFile(data);
        MidiFileReader reader = new MidiFileReader(testfile);

        int offset = 0;
        foreach (byte b in data) {
            Assert.AreEqual(reader.GetOffset(), offset);
            Assert.AreEqual(reader.Peek(), b);
            Assert.AreEqual(reader.ReadByte(), b);
            offset++;
        }
        reader.Close();
        File.Delete(testfile);
    }

    /* Test that MidiFileReader.ReadShort() returns the correct
     * unsigned short, and that the file offset is incremented by 2.
     */
    [Test]
    public void TestShort() {
        ushort[] nums = new ushort[] { 200, 3000, 10000, 40000 };
        byte[] data = new byte[nums.Length * 2];
        int index = 0; 
        for (int i = 0; i < nums.Length; i++) {
            data[index]   = (byte)( (nums[i] >> 8) & 0xFF );
            data[index+1] = (byte)( nums[i] & 0xFF );
            index += 2;
        }
        WriteTestFile(data);
        MidiFileReader reader = new MidiFileReader(testfile);

        int offset = 0;
        foreach (ushort u in nums) {
            Assert.AreEqual(reader.GetOffset(), offset);
            Assert.AreEqual(reader.ReadShort(), u);
            offset += 2;
        }
        reader.Close();
        File.Delete(testfile);
    }
 
    /* Test that MidiFileReader.ReadInt() returns the correct
     * int, and that the file offset is incremented by 4.
     */
    [Test]
    public void TestInt() {
        int[] nums = new int[] { 200, 10000, 80000, 999888777 };
        byte[] data = new byte[nums.Length * 4];
        int index = 0; 
        for (int i = 0; i < nums.Length; i++) {
            data[index]   = (byte)( (nums[i] >> 24) & 0xFF );
            data[index+1] = (byte)( (nums[i] >> 16) & 0xFF );
            data[index+2] = (byte)( (nums[i] >> 8) & 0xFF );
            data[index+3] = (byte)(  nums[i] & 0xFF );
            index += 4;
        }
        WriteTestFile(data);
        MidiFileReader reader = new MidiFileReader(testfile);

        int offset = 0;
        foreach (int x in nums) {
            Assert.AreEqual(reader.GetOffset(), offset);
            Assert.AreEqual(reader.ReadInt(), x);
            offset += 4;
        }
        reader.Close();
        File.Delete(testfile);
    }

    /* Test that MidiFileReader.ReadVarlen() correctly parses variable
     * length integers.  A variable length int ends when the byte is
     * less than 0x80 (128). 
     */
    [Test]
    public void TestVarlen() {
        byte[] data = new byte[12];

        data[0] = 0x40;

        data[1] = 0x90; 
        data[2] = 0x30;

        data[3] = 0x81;
        data[4] = 0xA5;
        data[5] = 0x10;

        data[6] = 0x81;
        data[7] = 0x84;
        data[8] = 0xBF;
        data[9] = 0x05;

        WriteTestFile(data);
        MidiFileReader reader = new MidiFileReader(testfile);

        int len = varlen(0, 0, 0, data[0]);
        Assert.AreEqual(reader.GetOffset(), 0);
        Assert.AreEqual(reader.ReadVarlen(), len);
        Assert.AreEqual(reader.GetOffset(), 1);

        len = varlen(0, 0, data[1], data[2]);
        Assert.AreEqual(reader.ReadVarlen(), len);
        Assert.AreEqual(reader.GetOffset(), 3);

        len = varlen(0, data[3], data[4], data[5]);
        Assert.AreEqual(reader.ReadVarlen(), len);
        Assert.AreEqual(reader.GetOffset(), 6);

        len = varlen(data[6], data[7], data[8], data[9]);
        Assert.AreEqual(reader.ReadVarlen(), len);
        Assert.AreEqual(reader.GetOffset(), 10);
        
        reader.Close();
        File.Delete(testfile);
    }

    [Test]
    public void TestAscii() {
        byte[] data = new byte[] { 65, 66, 67, 68, 69, 70 };
        WriteTestFile(data);
        MidiFileReader reader = new MidiFileReader(testfile);
        Assert.AreEqual(reader.GetOffset(), 0);
        Assert.AreEqual(reader.ReadAscii(3), "ABC");
        Assert.AreEqual(reader.GetOffset(), 3);
        Assert.AreEqual(reader.ReadAscii(3), "DEF");
        Assert.AreEqual(reader.GetOffset(), 6);
        reader.Close();
        File.Delete(testfile);
    }

    [Test]
    public void TestSkip() {
        byte[] data = new byte[] { 65, 66, 67, 68, 69, 70, 71 };
        WriteTestFile(data);
        MidiFileReader reader = new MidiFileReader(testfile);
        Assert.AreEqual(reader.GetOffset(), 0);
        reader.Skip(3);
        Assert.AreEqual(reader.GetOffset(), 3);
        Assert.AreEqual(reader.ReadByte(), 68);
        reader.Skip(2);
        Assert.AreEqual(reader.GetOffset(), 6);
        Assert.AreEqual(reader.ReadByte(), 71);
        Assert.AreEqual(reader.GetOffset(), 7);
        reader.Close();
        File.Delete(testfile);
    }
}


[TestFixture]
public class MidiFileTest {

    const string testfile = "test.mid";

    /* The list of Midi Events */
    const byte EventNoteOff         = 0x80;
    const byte EventNoteOn          = 0x90;
    const byte EventKeyPressure     = 0xA0;
    const byte EventControlChange   = 0xB0;
    const byte EventProgramChange   = 0xC0;
    const byte EventChannelPressure = 0xD0;
    const byte EventPitchBend       = 0xE0;
    const byte SysexEvent1          = 0xF0;
    const byte SysexEvent2          = 0xF7;
    const byte MetaEvent            = 0xFF;

    /* The list of Meta Events */
    const byte MetaEventSequence    = 0x0;
    const byte MetaEventKeySignature = 0x59;


    static void WriteTestFile(byte[] data) {
        FileStream fileout = File.Open(testfile, FileMode.Create,
                                   FileAccess.Write);
        fileout.Write(data, 0, data.Length);
        fileout.Close();
    }

    [Test]
    public void TestSequentialNotes() {
        const byte notenum = 60;
        const byte quarternote = 240;
        const byte numtracks = 1;
        const byte velocity = 80;

        byte[] data = new byte[] {
            77, 84, 104, 100,        // MThd ascii header 
            0, 0, 0, 6,              // length of header in bytes
            0, 1,                    // one or more simultaneous tracks
            0, numtracks, 
            0, quarternote,  
            77, 84, 114, 107,        // MTrk ascii header
            0, 0, 0, 24,             // Length of track, in bytes

            // time_interval, NoteEvent, note number, velocity
            0,  EventNoteOn,  notenum,   velocity,
            60, EventNoteOff, notenum,   0,
            0,  EventNoteOn,  notenum+1, velocity,
            30, EventNoteOff, notenum+1, 0,
            0,  EventNoteOn,  notenum+2, velocity,
            90, EventNoteOff, notenum+2, 0
        };

        WriteTestFile(data);
        MidiFile midifile = new MidiFile(testfile);
        File.Delete(testfile);

        Assert.AreEqual(midifile.TotalTracks, 1);
        Assert.AreEqual(midifile.Time.Numerator, 4);
        Assert.AreEqual(midifile.Time.Denominator, 4);
        Assert.AreEqual(midifile.Time.QuarterNote, quarternote);
        Assert.AreEqual(midifile.Time.Measure, quarternote * 4);

        MidiTrack track = midifile.GetTrack(1);
        List<MidiNote> notes = track.Notes;
        Assert.AreEqual(notes.Count, 3);

        Assert.AreEqual(notes[0].StartTime, 0);
        Assert.AreEqual(notes[0].Number, notenum);
        Assert.AreEqual(notes[0].Duration, 60);

        Assert.AreEqual(notes[1].StartTime, 60);
        Assert.AreEqual(notes[1].Number, notenum+1);
        Assert.AreEqual(notes[1].Duration, 30);

        Assert.AreEqual(notes[2].StartTime, 90);
        Assert.AreEqual(notes[2].Number, notenum+2);
        Assert.AreEqual(notes[2].Duration, 90);

    }


    [Test]
    public void TestOverlappingNotes() {
        const byte notenum = 60;
        const byte quarternote = 240;
        const byte numtracks = 1;
        const byte velocity = 80;

        byte[] data = new byte[] {
            77, 84, 104, 100,        // MThd ascii header 
            0, 0, 0, 6,              // length of header in bytes
            0, 1,                    // one or more simultaneous tracks
            0, numtracks, 
            0, quarternote,  
            77, 84, 114, 107,        // MTrk ascii header
            0, 0, 0, 24,             // Length of track, in bytes
            // time_interval, NoteEvent, note number, velocity
            0,  EventNoteOn,  notenum,   velocity,
            30, EventNoteOn,  notenum+1, velocity,
            30, EventNoteOn,  notenum+2, velocity,
            30, EventNoteOff, notenum+1, 0,
            30, EventNoteOff, notenum,   0,
            30, EventNoteOff, notenum+2, 0
        };

        WriteTestFile(data);
        MidiFile midifile = new MidiFile(testfile);
        File.Delete(testfile);

        Assert.AreEqual(midifile.TotalTracks, 1);
        Assert.AreEqual(midifile.Time.Numerator, 4);
        Assert.AreEqual(midifile.Time.Denominator, 4);
        Assert.AreEqual(midifile.Time.QuarterNote, quarternote);
        Assert.AreEqual(midifile.Time.Measure, quarternote * 4);

        MidiTrack track = midifile.GetTrack(1);

        List<MidiNote> notes = track.Notes;
        Assert.AreEqual(notes.Count, 3);

        Assert.AreEqual(notes[0].StartTime, 0);
        Assert.AreEqual(notes[0].Number, notenum);
        Assert.AreEqual(notes[0].Duration, 120);

        Assert.AreEqual(notes[1].StartTime, 30);
        Assert.AreEqual(notes[1].Number, notenum+1);
        Assert.AreEqual(notes[1].Duration, 60);

        Assert.AreEqual(notes[2].StartTime, 60);
        Assert.AreEqual(notes[2].Number, notenum+2);
        Assert.AreEqual(notes[2].Duration, 90);
    }

    [Test]
    public void TestMissingEventCode() {
        const byte notenum = 60;
        const byte quarternote = 240;
        const byte numtracks = 1;
        const byte velocity = 80;

        byte[] data = new byte[] {
            77, 84, 104, 100,        // MThd ascii header 
            0, 0, 0, 6,              // length of header in bytes
            0, 1,                    // one or more simultaneous tracks
            0, numtracks, 
            0, quarternote,  
            77, 84, 114, 107,        // MTrk ascii header
            0, 0, 0, 24,             // Length of track, in bytes
            // time_interval, NoteEvent, note number, velocity
            0,  EventNoteOn,  notenum,   velocity,
            30,               notenum+1, velocity,
            30,               notenum+2, velocity,
            30, EventNoteOff, notenum+1, 0,
            30,               notenum,   0,
            30,               notenum+2, 0
        };

        WriteTestFile(data);
        MidiFile midifile = new MidiFile(testfile);
        File.Delete(testfile);

        Assert.AreEqual(midifile.TotalTracks, 1);
        Assert.AreEqual(midifile.Time.Numerator, 4);
        Assert.AreEqual(midifile.Time.Denominator, 4);
        Assert.AreEqual(midifile.Time.QuarterNote, quarternote);
        Assert.AreEqual(midifile.Time.Measure, quarternote * 4);

        MidiTrack track = midifile.GetTrack(1);

        List<MidiNote> notes = track.Notes;
        Assert.AreEqual(notes.Count, 3);

        Assert.AreEqual(notes[0].StartTime, 0);
        Assert.AreEqual(notes[0].Number, notenum);
        Assert.AreEqual(notes[0].Duration, 120);

        Assert.AreEqual(notes[1].StartTime, 30);
        Assert.AreEqual(notes[1].Number, notenum+1);
        Assert.AreEqual(notes[1].Duration, 60);

        Assert.AreEqual(notes[2].StartTime, 60);
        Assert.AreEqual(notes[2].Number, notenum+2);
        Assert.AreEqual(notes[2].Duration, 90);
    }

    [Test]
    public void TestVariousEvents() {
        const byte notenum = 60;
        const byte quarternote = 240;
        const byte numtracks = 1;
        const byte velocity = 80;

        byte[] data = new byte[] {
            77, 84, 104, 100,        // MThd ascii header 
            0, 0, 0, 6,              // length of header in bytes
            0, 1,                    // one or more simultaneous tracks
            0, numtracks, 
            0, quarternote,  
            77, 84, 114, 107,        // MTrk ascii header
            0, 0, 0, 39,             // Length of track, in bytes

            // time_interval, NoteEvent, note number, velocity
            0,  EventNoteOn,  notenum,   velocity,
            60, EventNoteOff, notenum,   0,
            0,  EventKeyPressure, notenum, 10,
            0,  EventControlChange, 10, 10,
            0,  EventNoteOn,  notenum+1, velocity,
            30, EventNoteOff, notenum+1, 0,
            0,  EventProgramChange, 10,
            0,  EventPitchBend, 0, 0,
            0,  EventNoteOn,  notenum+2, velocity,
            90, EventNoteOff, notenum+2, 0
        };

        WriteTestFile(data);
        MidiFile midifile = new MidiFile(testfile);
        File.Delete(testfile);

        Assert.AreEqual(midifile.TotalTracks, 1);
        Assert.AreEqual(midifile.Time.Numerator, 4);
        Assert.AreEqual(midifile.Time.Denominator, 4);
        Assert.AreEqual(midifile.Time.QuarterNote, quarternote);
        Assert.AreEqual(midifile.Time.Measure, quarternote * 4);

        MidiTrack track = midifile.GetTrack(1);
        List<MidiNote> notes = track.Notes;
        Assert.AreEqual(notes.Count, 3);

        Assert.AreEqual(notes[0].StartTime, 0);
        Assert.AreEqual(notes[0].Number, notenum);
        Assert.AreEqual(notes[0].Duration, 60);

        Assert.AreEqual(notes[1].StartTime, 60);
        Assert.AreEqual(notes[1].Number, notenum+1);
        Assert.AreEqual(notes[1].Duration, 30);

        Assert.AreEqual(notes[2].StartTime, 90);
        Assert.AreEqual(notes[2].Number, notenum+2);
        Assert.AreEqual(notes[2].Duration, 90);

    }

    [Test]
    public void TestMetaEvents() {
        const byte notenum = 60;
        const byte quarternote = 240;
        const byte numtracks = 1;
        const byte velocity = 80;

        byte[] data = new byte[] {
            77, 84, 104, 100,        // MThd ascii header 
            0, 0, 0, 6,              // length of header in bytes
            0, 1,                    // one or more simultaneous tracks
            0, numtracks, 
            0, quarternote,  
            77, 84, 114, 107,        // MTrk ascii header
            0, 0, 0, 36,             // Length of track, in bytes

            // time_interval, NoteEvent, note number, velocity
            0,  EventNoteOn,  notenum,   velocity,
            60, EventNoteOff, notenum,   0,
            0,  MetaEvent, MetaEventSequence, 2, 0, 6,
            0,  EventNoteOn,  notenum+1, velocity,
            30, EventNoteOff, notenum+1, 0,
            0,  MetaEvent, MetaEventKeySignature, 2, 3, 0,
            0,  EventNoteOn,  notenum+2, velocity,
            90, EventNoteOff, notenum+2, 0
        };

        WriteTestFile(data);
        MidiFile midifile = new MidiFile(testfile);
        File.Delete(testfile);

        Assert.AreEqual(midifile.TotalTracks, 1);
        Assert.AreEqual(midifile.Time.Numerator, 4);
        Assert.AreEqual(midifile.Time.Denominator, 4);
        Assert.AreEqual(midifile.Time.QuarterNote, quarternote);
        Assert.AreEqual(midifile.Time.Measure, quarternote * 4);

        MidiTrack track = midifile.GetTrack(1);
        List<MidiNote> notes = track.Notes;
        Assert.AreEqual(notes.Count, 3);

        Assert.AreEqual(notes[0].StartTime, 0);
        Assert.AreEqual(notes[0].Number, notenum);
        Assert.AreEqual(notes[0].Duration, 60);

        Assert.AreEqual(notes[1].StartTime, 60);
        Assert.AreEqual(notes[1].Number, notenum+1);
        Assert.AreEqual(notes[1].Duration, 30);

        Assert.AreEqual(notes[2].StartTime, 90);
        Assert.AreEqual(notes[2].Number, notenum+2);
        Assert.AreEqual(notes[2].Duration, 90);

    }




    [Test]
    public void TestMultipleTracks() {
        const byte notenum = 60;
        const byte quarternote = 240;
        const byte numtracks = 3;
        const byte velocity = 80;

        byte[] data = new byte[] {
            77, 84, 104, 100,        // MThd ascii header 
            0, 0, 0, 6,              // length of header in bytes
            0, 1,                    // one or more simultaneous tracks
            0, numtracks, 
            0, quarternote, 
 
            77, 84, 114, 107,        // MTrk ascii header
            0, 0, 0, 24,             // Length of track, in bytes
            // time_interval, NoteEvent, note number, velocity
            0,  EventNoteOn,  notenum,   velocity,
            60, EventNoteOff, notenum,   0,
            0,  EventNoteOn,  notenum+1, velocity,
            30, EventNoteOff, notenum+1, 0,
            0,  EventNoteOn,  notenum+2, velocity,
            90, EventNoteOff, notenum+2, 0,

            77, 84, 114, 107,        // MTrk ascii header
            0, 0, 0, 24,             // Length of track, in bytes
            // time_interval, NoteEvent, note number, velocity
            0,  EventNoteOn,  notenum+1, velocity,
            60, EventNoteOff, notenum+1, 0,
            0,  EventNoteOn,  notenum+2, velocity,
            30, EventNoteOff, notenum+2, 0,
            0,  EventNoteOn,  notenum+3, velocity,
            90, EventNoteOff, notenum+3, 0,

            77, 84, 114, 107,        // MTrk ascii header
            0, 0, 0, 24,             // Length of track, in bytes
            // time_interval, NoteEvent, note number, velocity
            0,  EventNoteOn,  notenum+2, velocity,
            60, EventNoteOff, notenum+2, 0,
            0,  EventNoteOn,  notenum+3, velocity,
            30, EventNoteOff, notenum+3, 0,
            0,  EventNoteOn,  notenum+4, velocity,
            90, EventNoteOff, notenum+4, 0,


        };

        WriteTestFile(data);
        MidiFile midifile = new MidiFile(testfile);
        File.Delete(testfile);

        Assert.AreEqual(midifile.TotalTracks, numtracks);
        Assert.AreEqual(midifile.Time.Numerator, 4);
        Assert.AreEqual(midifile.Time.Denominator, 4);
        Assert.AreEqual(midifile.Time.QuarterNote, quarternote);
        Assert.AreEqual(midifile.Time.Measure, quarternote * 4);


        for (int tracknum = 0; tracknum < numtracks; tracknum++) {
            MidiTrack track = midifile.GetTrack(tracknum+1);
            List<MidiNote> notes = track.Notes;
            Assert.AreEqual(notes.Count, 3);

            Assert.AreEqual(notes[0].StartTime, 0);
            Assert.AreEqual(notes[0].Number, notenum + tracknum);
            Assert.AreEqual(notes[0].Duration, 60);

            Assert.AreEqual(notes[1].StartTime, 60);
            Assert.AreEqual(notes[1].Number, notenum + tracknum + 1);
            Assert.AreEqual(notes[1].Duration, 30);

            Assert.AreEqual(notes[2].StartTime, 90);
            Assert.AreEqual(notes[2].Number, notenum + tracknum + 2);
            Assert.AreEqual(notes[2].Duration, 90);
        }
    }

    [Test]
    public void TestRoundStartTimes() {
        const byte notenum = 20;
        const byte quarternote = 210;
        const byte numtracks = 2;
        const byte velocity = 80;

        byte[] data = new byte[] {
            77, 84, 104, 100,        // MThd ascii header 
            0, 0, 0, 6,              // length of header in bytes
            0, 1,                    // one or more simultaneous tracks
            0, numtracks, 
            0, quarternote, 
 
            77, 84, 114, 107,        // MTrk ascii header
            0, 0, 0, 40,             // Length of track, in bytes
            // time_interval, NoteEvent, note number, velocity
            0,  EventNoteOn,  notenum,   velocity,
            3,  EventNoteOn,  notenum+1, velocity,
            12, EventNoteOn,  notenum+2, velocity,
            7,  EventNoteOn,  notenum+3, velocity,
            40, EventNoteOn,  notenum+4, velocity,
            60, EventNoteOff, notenum,   0,
            0,  EventNoteOff, notenum+1, 0,
            0,  EventNoteOff, notenum+2, 0,
            0,  EventNoteOff, notenum+3, 0,
            0,  EventNoteOff, notenum+4, 0,

            77, 84, 114, 107,        // MTrk ascii header
            0, 0, 0, 40,             // Length of track, in bytes
            // time_interval, NoteEvent, note number, velocity
            2,  EventNoteOn,  notenum+10,   velocity,
            8,  EventNoteOn,  notenum+11, velocity,
            10,  EventNoteOn,  notenum+12, velocity,
            15,  EventNoteOn,  notenum+13, velocity,
            1,  EventNoteOn,  notenum+14, velocity,
            10, EventNoteOff, notenum+10, 0,
            0,  EventNoteOff, notenum+11, 0,
            0,  EventNoteOff, notenum+12, 0,
            0,  EventNoteOff, notenum+13, 0,
            0,  EventNoteOff, notenum+14, 0
        };

        WriteTestFile(data);
        MidiFile midifile = new MidiFile(testfile);
        File.Delete(testfile);

        Assert.AreEqual(midifile.TotalTracks, numtracks);
        Assert.AreEqual(midifile.Time.Numerator, 4);
        Assert.AreEqual(midifile.Time.Denominator, 4);
        Assert.AreEqual(midifile.Time.QuarterNote, quarternote);
        Assert.AreEqual(midifile.Time.Measure, quarternote * 4);


        MidiTrack track1 = midifile.GetTrack(1);
        MidiTrack track2 = midifile.GetTrack(2);
        List<MidiNote> notes1 = track1.Notes;
        List<MidiNote> notes2 = track2.Notes;
        Assert.AreEqual(notes1.Count, 5);
        Assert.AreEqual(notes2.Count, 5);

        Assert.AreEqual(notes1[0].Number, notenum);
        Assert.AreEqual(notes1[1].Number, notenum+1);
        Assert.AreEqual(notes1[2].Number, notenum+2);
        Assert.AreEqual(notes1[3].Number, notenum+3);
        Assert.AreEqual(notes1[4].Number, notenum+4);

        Assert.AreEqual(notes2[0].Number, notenum+10);
        Assert.AreEqual(notes2[1].Number, notenum+11);
        Assert.AreEqual(notes2[2].Number, notenum+12);
        Assert.AreEqual(notes2[3].Number, notenum+13);
        Assert.AreEqual(notes2[4].Number, notenum+14);

        Assert.AreEqual(notes1[0].StartTime, 0);
        Assert.AreEqual(notes1[1].StartTime, 3);
        Assert.AreEqual(notes1[2].StartTime, 15);
        Assert.AreEqual(notes1[3].StartTime, 22);
        Assert.AreEqual(notes1[4].StartTime, 62);

        Assert.AreEqual(notes2[0].StartTime, 2);
        Assert.AreEqual(notes2[1].StartTime, 10);
        Assert.AreEqual(notes2[2].StartTime, 20);
        Assert.AreEqual(notes2[3].StartTime, 35);
        Assert.AreEqual(notes2[4].StartTime, 36);

        /* quarternote/14 = 15, so notes within 15 pulses
         * should be grouped together.  Therefore:
         * 0, 2, 3, 10, 15 are grouped to starttime 0
         * 20, 22, 35      are grouped to starttime 20
         * 36              is still 36
         * 62              is still 62
         */

        List<MidiTrack> tracks = new List<MidiTrack>();
        tracks.Add(track1);
        tracks.Add(track2);

        tracks = MidiFile.RoundStartTimes(tracks, 210);
        notes1 = tracks[0].Notes;
        notes2 = tracks[1].Notes;
        Assert.AreEqual(notes1.Count, 5);
        Assert.AreEqual(notes2.Count, 5);

        Assert.AreEqual(notes1[0].Number, notenum);
        Assert.AreEqual(notes1[1].Number, notenum+1);
        Assert.AreEqual(notes1[2].Number, notenum+2);
        Assert.AreEqual(notes1[3].Number, notenum+3);
        Assert.AreEqual(notes1[4].Number, notenum+4);

        Assert.AreEqual(notes2[0].Number, notenum+10);
        Assert.AreEqual(notes2[1].Number, notenum+11);
        Assert.AreEqual(notes2[2].Number, notenum+12);
        Assert.AreEqual(notes2[3].Number, notenum+13);
        Assert.AreEqual(notes2[4].Number, notenum+14);


        Assert.AreEqual(notes1[0].StartTime, 0);
        Assert.AreEqual(notes1[1].StartTime, 0);
        Assert.AreEqual(notes1[2].StartTime, 0);
        Assert.AreEqual(notes1[3].StartTime, 20);
        Assert.AreEqual(notes1[3].StartTime, 20);
        Assert.AreEqual(notes1[4].StartTime, 62);

        Assert.AreEqual(notes2[0].StartTime, 0);
        Assert.AreEqual(notes2[1].StartTime, 0);
        Assert.AreEqual(notes2[2].StartTime, 20);
        Assert.AreEqual(notes2[3].StartTime, 20);
        Assert.AreEqual(notes2[4].StartTime, 36);
    }
}

[TestFixture]
public class KeySignatureTest {

    [Test]
    public void TestGetSymbols() {
        KeySignature k;
        AccidSymbol[] symbols1, symbols2;

        k = new KeySignature(0, 0);
        symbols1 = k.GetSymbols(Clef.Treble);
        symbols2 = k.GetSymbols(Clef.Bass);
        Assert.AreEqual(symbols1.Length, 0);
        Assert.AreEqual(symbols2.Length, 0);

        int[] sharps = new int[] {
            WhiteNote.F, WhiteNote.C, WhiteNote.G, WhiteNote.D,
            WhiteNote.A, WhiteNote.E
        };

        for (int sharp = 1; sharp < 7; sharp++) {
            k = new KeySignature(sharp, 0);
            symbols1 = k.GetSymbols(Clef.Treble);
            symbols2 = k.GetSymbols(Clef.Bass);
            for (int i = 0; i < sharp; i++) {
                Assert.AreEqual(symbols1[i].Note.Letter, sharps[i]);
                Assert.AreEqual(symbols2[i].Note.Letter, sharps[i]);
            }
        }

        int[] flats = new int[] {
            WhiteNote.B, WhiteNote.E, WhiteNote.A, WhiteNote.D,
            WhiteNote.G
        }; 

        for (int flat = 1; flat < 6; flat++) {
            k = new KeySignature(0, flat);
            symbols1 = k.GetSymbols(Clef.Treble);
            symbols2 = k.GetSymbols(Clef.Bass);
            for (int i = 0; i < flat; i++) {
                Assert.AreEqual(symbols1[i].Note.Letter, flats[i]);
                Assert.AreEqual(symbols2[i].Note.Letter, flats[i]);
            }
        }
    }

    [Test]
    public void TestGetAccidental() {

        int measure = 1;
        KeySignature k;
        Accid[] expected = new Accid[12];
        for (int i = 0; i < 12; i++) {
            expected[i] = Accid.None;
        }
        expected[NoteScale.Asharp] = Accid.Sharp;
        expected[NoteScale.Csharp] = Accid.Sharp;
        expected[NoteScale.Dsharp] = Accid.Sharp;
        expected[NoteScale.Fsharp] = Accid.Sharp;
        expected[NoteScale.Gsharp] = Accid.Sharp;

        /* Test C Major */
        k = new KeySignature(0, 0);
        measure = 1;
        for (int note = 1; note < 128; note++) {
            int notescale = NoteScale.FromNumber(note);
            Assert.AreEqual(expected[notescale], 
                          k.GetAccidental(note, measure));
            measure++;
        }

        /* Test G major, F# */
        k = new KeySignature(1, 0);
        measure = 1;
        expected[NoteScale.Fsharp] = Accid.None;
        expected[NoteScale.F] = Accid.Natural;
        for (int note = 1; note < 128; note++) {
            int notescale = NoteScale.FromNumber(note);
            Assert.AreEqual(expected[notescale], 
                          k.GetAccidental(note, measure));
            measure++;
        }

        /* Test D major, F#, C# */
        k = new KeySignature(2, 0);
        measure = 1;
        expected[NoteScale.Csharp] = Accid.None;
        expected[NoteScale.C] = Accid.Natural;
        for (int note = 1; note < 128; note++) {
            int notescale = NoteScale.FromNumber(note);
            Assert.AreEqual(expected[notescale], 
                          k.GetAccidental(note, measure));
            measure++;
        }

        /* Test A major, F#, C#, G# */
        k = new KeySignature(3, 0);
        measure = 1;
        expected[NoteScale.Gsharp] = Accid.None;
        expected[NoteScale.G] = Accid.Natural;
        for (int note = 1; note < 128; note++) {
            int notescale = NoteScale.FromNumber(note);
            Assert.AreEqual(expected[notescale], 
                          k.GetAccidental(note, measure));
            measure++;
        }

        /* Test E major, F#, C#, G#, D# */
        k = new KeySignature(4, 0);
        measure = 1;
        expected[NoteScale.Dsharp] = Accid.None;
        expected[NoteScale.D] = Accid.Natural;
        for (int note = 1; note < 128; note++) {
            int notescale = NoteScale.FromNumber(note);
            Assert.AreEqual(expected[notescale], 
                          k.GetAccidental(note, measure));
            measure++;
        }

        /* Test B major, F#, C#, G#, D#, A# */
        k = new KeySignature(5, 0);
        measure = 1;
        expected[NoteScale.Asharp] = Accid.None;
        expected[NoteScale.A] = Accid.Natural;
        for (int note = 1; note < 128; note++) {
            int notescale = NoteScale.FromNumber(note);
            Assert.AreEqual(expected[notescale], 
                          k.GetAccidental(note, measure));
            measure++;
        }

        for (int i = 0; i < 12; i++) {
            expected[i] = Accid.None;
        }
        expected[NoteScale.Aflat] = Accid.Flat;
        expected[NoteScale.Bflat] = Accid.Flat;
        expected[NoteScale.Dflat] = Accid.Flat;
        expected[NoteScale.Eflat] = Accid.Flat;
        expected[NoteScale.Gflat] = Accid.Flat;

        /* Test F major, Bflat */
        k = new KeySignature(0, 1);
        measure = 1;
        expected[NoteScale.Bflat] = Accid.None;
        expected[NoteScale.B] = Accid.Natural;
        for (int note = 1; note < 128; note++) {
            int notescale = NoteScale.FromNumber(note);
            Assert.AreEqual(expected[notescale], 
                          k.GetAccidental(note, measure));
            measure++;
        }

        /* Test Bflat major, Bflat, Eflat */
        k = new KeySignature(0, 2);
        measure = 1;
        expected[NoteScale.Eflat] = Accid.None;
        expected[NoteScale.E] = Accid.Natural;
        for (int note = 1; note < 128; note++) {
            int notescale = NoteScale.FromNumber(note);
            Assert.AreEqual(expected[notescale], 
                          k.GetAccidental(note, measure));
            measure++;
        }

        /* Test Eflat major, Bflat, Eflat, Afat */
        k = new KeySignature(0, 3);
        measure = 1;
        expected[NoteScale.Aflat] = Accid.None;
        expected[NoteScale.A] = Accid.Natural;
        for (int note = 1; note < 128; note++) {
            int notescale = NoteScale.FromNumber(note);
            Assert.AreEqual(expected[notescale], 
                          k.GetAccidental(note, measure));
            measure++;
        }

        /* Test Aflat major, Bflat, Eflat, Aflat, Dflat */
        k = new KeySignature(0, 4);
        measure = 1;
        expected[NoteScale.Dflat] = Accid.None;
        expected[NoteScale.D] = Accid.Natural;
        for (int note = 1; note < 128; note++) {
            int notescale = NoteScale.FromNumber(note);
            Assert.AreEqual(expected[notescale], 
                          k.GetAccidental(note, measure));
            measure++;
        }

        /* Test Dflat major, Bflat, Eflat, Aflat, Dflat, Gflat */
        k = new KeySignature(0, 5);
        measure = 1;
        expected[NoteScale.Gflat] = Accid.None;
        expected[NoteScale.G] = Accid.Natural;
        for (int note = 1; note < 128; note++) {
            int notescale = NoteScale.FromNumber(note);
            Assert.AreEqual(expected[notescale], 
                          k.GetAccidental(note, measure));
            measure++;
        }
    }

    [Test]
    public void TestGetAccidentalSameMeasure() {
        KeySignature k;

        /* G Major, F# */
        k = new KeySignature(1, 0);
        int note = NoteScale.ToNumber(NoteScale.C, 1);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.None);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.None);
        note = NoteScale.ToNumber(NoteScale.Fsharp, 1);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.None);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.None);
        note = NoteScale.ToNumber(NoteScale.F, 1);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.Natural);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.None);
        note = NoteScale.ToNumber(NoteScale.Fsharp, 1);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.Sharp);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.None);

        /* F Major, Bflat */
        k = new KeySignature(0, 1);
        note = NoteScale.ToNumber(NoteScale.G, 1);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.None);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.None);
        note = NoteScale.ToNumber(NoteScale.Bflat, 1);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.None);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.None);
        note = NoteScale.ToNumber(NoteScale.B, 1);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.Natural);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.None);
        note = NoteScale.ToNumber(NoteScale.Bflat, 1);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.Flat);
        Assert.AreEqual(k.GetAccidental(note, 1), Accid.None);
    }

    [Test]
    public void TestGuess() {
        List<int> notes = new List<int>();

        /* C major */
        int octave = 0;
        for (int i = 0; i < 100; i++) {
            notes.Add( NoteScale.ToNumber(NoteScale.A, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.B, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.C, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.D, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.E, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.F, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.G, octave));
            octave = (octave + 1) % 7;
        }
        for (int i = 0; i < 10; i++) {
            notes.Add( NoteScale.ToNumber(NoteScale.Fsharp, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.Dsharp, octave));
        }
        Assert.AreEqual(KeySignature.Guess(notes).ToString(), "C major");

        /* A Major, F#, C#, G# */
        notes.Clear();
        octave = 0;
        for (int i = 0; i < 100; i++) {
            notes.Add( NoteScale.ToNumber(NoteScale.A, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.B, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.Csharp, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.D, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.E, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.Fsharp, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.Gsharp, octave));
            octave = (octave + 1) % 7;
        }
        for (int i = 0; i < 10; i++) {
            notes.Add( NoteScale.ToNumber(NoteScale.F, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.Dsharp, octave));
        }
        Assert.AreEqual(KeySignature.Guess(notes).ToString(), "A major");

        /* Eflat Major, Bflat, Eflat, Aflat */
        notes.Clear();
        octave = 0;
        for (int i = 0; i < 100; i++) {
            notes.Add( NoteScale.ToNumber(NoteScale.Aflat, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.Bflat, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.C, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.D, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.Eflat, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.F, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.G, octave));
            octave = (octave + 1) % 7;
        }
        for (int i = 0; i < 10; i++) {
            notes.Add( NoteScale.ToNumber(NoteScale.Dflat, octave));
            notes.Add( NoteScale.ToNumber(NoteScale.B, octave));
        }
        Assert.AreEqual(KeySignature.Guess(notes).ToString(), "E-flat major");
    }
}


public class TestSymbol : MusicSymbol {
    int starttime;
    int width;

    public TestSymbol(int starttime, int width) {
        this.starttime = starttime;
        this.width = width;
    }

    public override int StartTime { 
        get { return starttime; } 
    }
    public override int MinWidth {
        get { return width; } 
    }
    public override int Width {
        get { return width; }
        set { width = value; }
    }
    public override int AboveStaff {
        get { return 0; } 
    }
    public override int BelowStaff {
        get { return 0; } 
    }
    public override void Draw(Graphics g, Pen pen, int ytop) {}
}

[TestFixture]
public class SymbolWidthsTest {

    [Test]
    public void TestStartTimes() {
        List<MusicSymbol>[] tracks = new List<MusicSymbol>[3];
        for (int i = 0; i < 3; i++) {
            List<MusicSymbol> symbols = new List<MusicSymbol>();
            for (int j = 0; j < 5; j++) {
                symbols.Add(new TestSymbol(i*10 + j, 10));
            }
            tracks[i] = symbols;
        }
        SymbolWidths s = new SymbolWidths(tracks);
        int[] starttimes = s.StartTimes;
        int index = 0;
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 5; j++) {
                Assert.AreEqual(starttimes[index], i*10 + j);
                index++;
            }
        }
    }

    [Test]
    public void TestGetExtraWidth() {
        List<MusicSymbol>[] tracks = new List<MusicSymbol>[3];
        for (int i = 0; i < 3; i++) {
            List<MusicSymbol> symbols = new List<MusicSymbol>();
            symbols.Add(new TestSymbol(100, i*4));
            tracks[i] = symbols;
        }
        SymbolWidths s = new SymbolWidths(tracks);
        int extra = s.GetExtraWidth(0, 100);
        Assert.AreEqual(extra, 8); 
        extra = s.GetExtraWidth(1, 100);
        Assert.AreEqual(extra, 4); 
        extra = s.GetExtraWidth(2, 100);
        Assert.AreEqual(extra, 0); 

        tracks[0].Add(new TestSymbol(200, 6));
        s = new SymbolWidths(tracks);
        extra = s.GetExtraWidth(0, 200);
        Assert.AreEqual(extra, 0); 
        extra = s.GetExtraWidth(1, 200);
        Assert.AreEqual(extra, 6); 
        extra = s.GetExtraWidth(2, 200);
        Assert.AreEqual(extra, 6); 
    }

    [Test]
    public void TestGetWidthBetween() {
        List<MusicSymbol>[] tracks = new List<MusicSymbol>[1];
        List<MusicSymbol> symbols = new List<MusicSymbol>();
        symbols.Add(new TestSymbol(0,  5));
        symbols.Add(new TestSymbol(10, 20));
        symbols.Add(new TestSymbol(20, 10));
        symbols.Add(new TestSymbol(30, 70));
        symbols.Add(new TestSymbol(40, 30));
        tracks[0] = symbols;

        SymbolWidths s = new SymbolWidths(tracks);
        Assert.AreEqual(s.GetWidthBetween(0, 9), 0);
        Assert.AreEqual(s.GetWidthBetween(0, 10), 20);
        Assert.AreEqual(s.GetWidthBetween(0, 19), 20);
        Assert.AreEqual(s.GetWidthBetween(0, 20), 20+10);
        Assert.AreEqual(s.GetWidthBetween(10, 20), 10);
        Assert.AreEqual(s.GetWidthBetween(30, 45), 30);

    }

}


