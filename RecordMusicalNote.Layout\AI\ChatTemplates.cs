using System.Collections.Generic;

namespace MyWPF.Layout.AI
{
    /// <summary>
    /// AI聊天模板管理器
    /// </summary>
    public static class ChatTemplates
    {
        /// <summary>
        /// 获取所有聊天模板
        /// </summary>
        public static List<ChatTemplate> GetAllTemplates()
        {
            return new List<ChatTemplate>
            {
                // 基础生成模板
                new ChatTemplate
                {
                    Category = "基础生成",
                    Title = "继续写谱",
                    Description = "根据当前谱面风格继续生成后续小节",
                    Template = "根据当前谱面的风格和节奏，帮我继续写第{nextBar}-{endBar}小节的铺面",
                    Keywords = new List<string> { "继续", "生成", "后续" }
                },
                
                new ChatTemplate
                {
                    Category = "基础生成",
                    Title = "指定小节生成",
                    Description = "为指定的小节范围生成音符",
                    Template = "帮我写第{startBar}-{endBar}小节的铺面，难度适中",
                    Keywords = new List<string> { "指定", "小节", "生成" }
                },
                
                new ChatTemplate
                {
                    Category = "基础生成",
                    Title = "填充空白小节",
                    Description = "为没有音符的小节生成内容",
                    Template = "我发现第{startBar}-{endBar}小节是空的，帮我根据音乐节奏填充一些音符",
                    Keywords = new List<string> { "填充", "空白", "补充" }
                },

                // 风格调整模板
                new ChatTemplate
                {
                    Category = "风格调整",
                    Title = "增加节奏感",
                    Description = "让谱面更有节奏感和动感",
                    Template = "把第{startBar}-{endBar}小节改得更有节奏感，多加一些短音符和滑动音符",
                    Keywords = new List<string> { "节奏", "动感", "短音符" }
                },
                
                new ChatTemplate
                {
                    Category = "风格调整",
                    Title = "增加长音符",
                    Description = "添加更多长按音符增加表现力",
                    Template = "在第{startBar}-{endBar}小节中添加一些长音符，让谱面更有表现力",
                    Keywords = new List<string> { "长音符", "表现力", "长按" }
                },
                
                new ChatTemplate
                {
                    Category = "风格调整",
                    Title = "滑动音符重点",
                    Description = "重点使用滑动音符增加技巧性",
                    Template = "帮我在第{startBar}-{endBar}小节设计一些有趣的滑动音符组合，增加技巧性",
                    Keywords = new List<string> { "滑动", "技巧", "组合" }
                },

                // 难度调整模板
                new ChatTemplate
                {
                    Category = "难度调整",
                    Title = "降低难度",
                    Description = "简化谱面，适合新手玩家",
                    Template = "第{startBar}-{endBar}小节太难了，帮我简化一下，减少音符密度，适合新手",
                    Keywords = new List<string> { "简化", "新手", "降低难度" }
                },
                
                new ChatTemplate
                {
                    Category = "难度调整",
                    Title = "提高难度",
                    Description = "增加谱面挑战性",
                    Template = "第{startBar}-{endBar}小节太简单了，帮我增加一些挑战性，提高难度",
                    Keywords = new List<string> { "挑战", "提高难度", "复杂" }
                },
                
                new ChatTemplate
                {
                    Category = "难度调整",
                    Title = "平衡难度",
                    Description = "调整谱面难度分布",
                    Template = "帮我分析第{startBar}-{endBar}小节的难度分布，并进行平衡调整",
                    Keywords = new List<string> { "平衡", "分布", "调整" }
                },

                // 分析优化模板
                new ChatTemplate
                {
                    Category = "分析优化",
                    Title = "谱面分析",
                    Description = "分析当前谱面的特点和问题",
                    Template = "帮我分析一下当前谱面的特点，包括难度分布、音符类型比例和可玩性",
                    Keywords = new List<string> { "分析", "特点", "可玩性" }
                },
                
                new ChatTemplate
                {
                    Category = "分析优化",
                    Title = "优化手感",
                    Description = "优化谱面的手部动作流畅性",
                    Template = "第{startBar}-{endBar}小节的手感不太好，帮我优化一下手部动作的流畅性",
                    Keywords = new List<string> { "手感", "流畅", "优化" }
                },
                
                new ChatTemplate
                {
                    Category = "分析优化",
                    Title = "轨道平衡",
                    Description = "平衡各轨道的音符分布",
                    Template = "检查第{startBar}-{endBar}小节的轨道分布是否平衡，如果不平衡请帮我调整",
                    Keywords = new List<string> { "轨道", "平衡", "分布" }
                },

                // 特殊效果模板
                new ChatTemplate
                {
                    Category = "特殊效果",
                    Title = "高潮部分",
                    Description = "为音乐高潮部分设计特殊谱面",
                    Template = "第{startBar}-{endBar}小节是音乐的高潮部分，帮我设计一个震撼的谱面效果",
                    Keywords = new List<string> { "高潮", "震撼", "特殊" }
                },
                
                new ChatTemplate
                {
                    Category = "特殊效果",
                    Title = "过渡段落",
                    Description = "设计平滑的过渡段落",
                    Template = "第{startBar}-{endBar}小节是过渡段落，帮我设计一个平滑的过渡效果",
                    Keywords = new List<string> { "过渡", "平滑", "连接" }
                },
                
                new ChatTemplate
                {
                    Category = "特殊效果",
                    Title = "结尾设计",
                    Description = "设计有仪式感的结尾",
                    Template = "第{startBar}-{endBar}小节是歌曲结尾，帮我设计一个有仪式感的结尾谱面",
                    Keywords = new List<string> { "结尾", "仪式感", "完结" }
                },

                // 修复问题模板
                new ChatTemplate
                {
                    Category = "修复问题",
                    Title = "修复冲突",
                    Description = "修复音符位置冲突问题",
                    Template = "第{startBar}-{endBar}小节有一些音符位置冲突，帮我修复一下",
                    Keywords = new List<string> { "冲突", "修复", "位置" }
                },
                
                new ChatTemplate
                {
                    Category = "修复问题",
                    Title = "清理音符",
                    Description = "清理不合理的音符",
                    Template = "第{startBar}-{endBar}小节有一些不合理的音符，帮我清理和重新设计",
                    Keywords = new List<string> { "清理", "不合理", "重新设计" }
                },
                
                new ChatTemplate
                {
                    Category = "修复问题",
                    Title = "间距调整",
                    Description = "调整音符间距",
                    Template = "第{startBar}-{endBar}小节的音符间距不太合适，帮我调整一下",
                    Keywords = new List<string> { "间距", "调整", "合适" }
                }
            };
        }

        /// <summary>
        /// 根据关键词搜索模板
        /// </summary>
        public static List<ChatTemplate> SearchTemplates(string keyword)
        {
            var allTemplates = GetAllTemplates();
            var results = new List<ChatTemplate>();

            foreach (var template in allTemplates)
            {
                if (template.Title.Contains(keyword) || 
                    template.Description.Contains(keyword) ||
                    template.Keywords.Contains(keyword))
                {
                    results.Add(template);
                }
            }

            return results;
        }

        /// <summary>
        /// 根据分类获取模板
        /// </summary>
        public static List<ChatTemplate> GetTemplatesByCategory(string category)
        {
            var allTemplates = GetAllTemplates();
            var results = new List<ChatTemplate>();

            foreach (var template in allTemplates)
            {
                if (template.Category == category)
                {
                    results.Add(template);
                }
            }

            return results;
        }

        /// <summary>
        /// 获取所有分类
        /// </summary>
        public static List<string> GetAllCategories()
        {
            return new List<string>
            {
                "基础生成",
                "风格调整", 
                "难度调整",
                "分析优化",
                "特殊效果",
                "修复问题"
            };
        }
    }

    /// <summary>
    /// 聊天模板
    /// </summary>
    public class ChatTemplate
    {
        public string Category { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Template { get; set; }
        public List<string> Keywords { get; set; } = new List<string>();

        /// <summary>
        /// 应用参数到模板
        /// </summary>
        public string ApplyParameters(int startBar = 1, int endBar = 4, int nextBar = 1)
        {
            return Template
                .Replace("{startBar}", startBar.ToString())
                .Replace("{endBar}", endBar.ToString())
                .Replace("{nextBar}", nextBar.ToString());
        }
    }
}
