#!/usr/bin/env python3
"""
快速安装PyTorch脚本

专门用于解决"No module named 'torch'"问题
"""

import sys
import subprocess
import platform

def install_pytorch():
    """安装PyTorch"""
    print("🔥 PyTorch快速安装脚本")
    print("=" * 30)
    
    # 检测系统信息
    system = platform.system()
    python_version = sys.version_info
    
    print(f"系统: {system}")
    print(f"Python版本: {python_version.major}.{python_version.minor}")
    print()
    
    # 选择安装命令
    if system == "Windows":
        print("🪟 Windows系统检测到")
        cmd = [
            sys.executable, "-m", "pip", "install", 
            "torch", "torchvision", "torchaudio",
            "--index-url", "https://download.pytorch.org/whl/cpu"
        ]
    else:
        print("🐧 Linux/Mac系统检测到")
        cmd = [
            sys.executable, "-m", "pip", "install",
            "torch", "torchvision", "torchaudio"
        ]
    
    print("📦 正在安装PyTorch...")
    print("⏳ 这可能需要几分钟时间，请耐心等待...")
    print()
    
    try:
        # 执行安装
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ PyTorch安装成功！")
        
        # 验证安装
        print("\n🔍 验证安装...")
        try:
            import torch
            print(f"✅ PyTorch版本: {torch.__version__}")
            print(f"✅ CUDA可用: {torch.cuda.is_available()}")
        except ImportError:
            print("❌ 验证失败，请重试")
            return False
            
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        print("\n💡 尝试其他方案:")
        print("1. 手动安装: pip install torch torchvision torchaudio")
        print("2. 使用conda: conda install pytorch torchvision torchaudio -c pytorch")
        print("3. 运行完整安装: python install_deps.py")
        return False

def install_basic_deps():
    """安装基础依赖"""
    basic_packages = [
        "numpy>=1.21.0",
        "pandas>=1.3.0",
        "matplotlib>=3.5.0",
        "streamlit>=1.28.0"
    ]
    
    print("\n📦 安装基础依赖...")
    for package in basic_packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True)
            print(f"✅ {package.split('>=')[0]}")
        except subprocess.CalledProcessError:
            print(f"⚠️ {package.split('>=')[0]} 安装失败")

def main():
    """主函数"""
    print("🎵 AI音游写谱助手 - PyTorch快速安装")
    print()
    
    # 检查是否已安装
    try:
        import torch
        print(f"✅ PyTorch已安装，版本: {torch.__version__}")
        print("无需重复安装")
        return
    except ImportError:
        pass
    
    # 安装PyTorch
    if install_pytorch():
        print("\n🎉 安装完成！")
        print("现在可以运行UI界面:")
        print("   python run_ui.py")
    else:
        print("\n❌ 安装失败")
        print("请尝试手动安装或查看错误信息")
    
    # 询问是否安装其他依赖
    try:
        choice = input("\n是否安装其他基础依赖？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            install_basic_deps()
            print("\n✅ 基础依赖安装完成")
    except KeyboardInterrupt:
        print("\n👋 安装中断")

if __name__ == "__main__":
    main()
