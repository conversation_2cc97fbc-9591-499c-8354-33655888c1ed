//------------------------------------------------------------------------------
// <auto-generated>
//     此代码已从模板生成。
//
//     手动更改此文件可能导致应用程序出现意外的行为。
//     如果重新生成代码，将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace RecordMusicalNote.DataModel
{
    using System;
    using System.Collections.Generic;
    
    public partial class LevelInfo
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public LevelInfo()
        {
            this.BubbleNoteInfo = new HashSet<BubbleNoteInfo>();
            this.IdolNoteInfo = new HashSet<IdolNoteInfo>();
            this.PinBallNoteInfo = new HashSet<PinBallNoteInfo>();
        }
    
        public int LevelInfoId { get; set; }
        public Nullable<double> BPM { get; set; }
        public Nullable<int> BeatPerBar { get; set; }
        public Nullable<int> BeatLen { get; set; }
        public Nullable<int> EnterTimeAdjust { get; set; }
        public Nullable<int> NotePreShow { get; set; }
        public Nullable<int> LevelTime { get; set; }
        public Nullable<int> BarAmount { get; set; }
        public Nullable<int> BeginBarLen { get; set; }
        public Nullable<bool> IsFourTrack { get; set; }
        public Nullable<int> TrackCount { get; set; }
        public Nullable<int> LevelPreTime { get; set; }
        public Nullable<int> Star { get; set; }
        public string SongName { get; set; }
        public string Artist { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<BubbleNoteInfo> BubbleNoteInfo { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<IdolNoteInfo> IdolNoteInfo { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PinBallNoteInfo> PinBallNoteInfo { get; set; }

          }
}
