#!/usr/bin/env python3
"""
云算力部署脚本

用于在租用的GPU服务器上快速部署AI音游写谱助手
"""

import os
import subprocess
import sys
from pathlib import Path

def setup_environment():
    """设置云端环境"""
    print("🌩️ 云算力环境设置")
    print("=" * 30)
    
    # 更新系统包
    print("📦 更新系统包...")
    try:
        subprocess.run(["apt", "update", "-y"], check=True)
        subprocess.run(["apt", "install", "-y", "git", "wget", "curl"], check=True)
    except subprocess.CalledProcessError:
        print("⚠️ 系统包更新失败，可能需要sudo权限")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}")
    
    if python_version < (3, 8):
        print("❌ Python版本过低，建议升级到3.8+")
        return False
    
    return True

def install_cuda_pytorch():
    """安装CUDA版本的PyTorch"""
    print("\n🔥 安装GPU版本PyTorch...")
    
    # 检测CUDA版本
    try:
        result = subprocess.run(["nvidia-smi"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ NVIDIA GPU检测成功")
            print("CUDA信息:")
            print(result.stdout.split('\n')[2])  # 显示CUDA版本行
        else:
            print("⚠️ 未检测到NVIDIA GPU，将安装CPU版本")
            return install_cpu_pytorch()
    except FileNotFoundError:
        print("⚠️ nvidia-smi未找到，将安装CPU版本")
        return install_cpu_pytorch()
    
    # 安装CUDA版本PyTorch
    cuda_commands = [
        # CUDA 11.8 (最常见)
        [sys.executable, "-m", "pip", "install", "torch", "torchvision", "torchaudio", 
         "--index-url", "https://download.pytorch.org/whl/cu118"],
        # CUDA 12.1
        [sys.executable, "-m", "pip", "install", "torch", "torchvision", "torchaudio", 
         "--index-url", "https://download.pytorch.org/whl/cu121"],
    ]
    
    for cmd in cuda_commands:
        try:
            print(f"尝试安装: {' '.join(cmd[-4:])}")
            subprocess.run(cmd, check=True)
            
            # 验证安装
            import torch
            if torch.cuda.is_available():
                print(f"✅ GPU PyTorch安装成功!")
                print(f"   PyTorch版本: {torch.__version__}")
                print(f"   CUDA版本: {torch.version.cuda}")
                print(f"   GPU数量: {torch.cuda.device_count()}")
                return True
        except (subprocess.CalledProcessError, ImportError):
            continue
    
    print("❌ GPU版本安装失败，尝试CPU版本")
    return install_cpu_pytorch()

def install_cpu_pytorch():
    """安装CPU版本的PyTorch"""
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "torch", "torchvision", "torchaudio", "--index-url", 
            "https://download.pytorch.org/whl/cpu"
        ], check=True)
        print("✅ CPU版本PyTorch安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyTorch安装失败")
        return False

def install_project_deps():
    """安装项目依赖"""
    print("\n📦 安装项目依赖...")
    
    # 基础依赖
    basic_deps = [
        "numpy>=1.21.0",
        "pandas>=1.3.0",
        "scipy>=1.7.0",
        "matplotlib>=3.5.0",
        "streamlit>=1.28.0",
        "plotly>=5.0.0",
        "pretty_midi>=0.2.9",
        "mido>=1.2.10",
        "flask>=2.2.0",
        "pyyaml>=6.0",
        "tqdm>=4.64.0"
    ]
    
    success_count = 0
    for dep in basic_deps:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                         check=True, capture_output=True)
            print(f"✅ {dep.split('>=')[0]}")
            success_count += 1
        except subprocess.CalledProcessError:
            print(f"❌ {dep.split('>=')[0]} 安装失败")
    
    print(f"依赖安装完成: {success_count}/{len(basic_deps)}")
    return success_count >= len(basic_deps) * 0.8  # 80%成功率即可

def setup_jupyter():
    """设置Jupyter环境"""
    print("\n📓 设置Jupyter环境...")
    
    try:
        # 安装Jupyter
        subprocess.run([sys.executable, "-m", "pip", "install", 
                       "jupyter", "jupyterlab"], check=True)
        
        # 生成配置文件
        subprocess.run(["jupyter", "notebook", "--generate-config"], check=True)
        
        print("✅ Jupyter安装成功")
        print("启动命令: jupyter lab --ip=0.0.0.0 --port=8888 --allow-root")
        return True
    except subprocess.CalledProcessError:
        print("⚠️ Jupyter安装失败")
        return False

def create_startup_script():
    """创建启动脚本"""
    print("\n📝 创建启动脚本...")
    
    startup_script = """#!/bin/bash
# AI音游写谱助手 - 云端启动脚本

echo "🎵 AI音游写谱助手 - 云端版本"
echo "================================"

# 设置环境变量
export STREAMLIT_SERVER_HEADLESS=true
export STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

# 检查GPU
if command -v nvidia-smi &> /dev/null; then
    echo "🔥 GPU信息:"
    nvidia-smi --query-gpu=name,memory.total,memory.used --format=csv,noheader,nounits
    echo ""
fi

# 启动选项
echo "选择启动方式:"
echo "1. Streamlit UI界面 (端口8501)"
echo "2. Jupyter Lab (端口8888)"
echo "3. 命令行模式"
echo ""

read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo "🚀 启动Streamlit UI..."
        python run_ui.py --host 0.0.0.0 --port 8501
        ;;
    2)
        echo "📓 启动Jupyter Lab..."
        jupyter lab --ip=0.0.0.0 --port=8888 --allow-root --no-browser
        ;;
    3)
        echo "💻 进入命令行模式"
        python main.py --help
        ;;
    *)
        echo "❌ 无效选择"
        ;;
esac
"""
    
    with open("start_cloud.sh", "w") as f:
        f.write(startup_script)
    
    os.chmod("start_cloud.sh", 0o755)
    print("✅ 启动脚本创建完成: start_cloud.sh")

def main():
    """主部署流程"""
    print("🌩️ AI音游写谱助手 - 云算力部署")
    print("=" * 40)
    
    # 环境设置
    if not setup_environment():
        print("❌ 环境设置失败")
        return
    
    # 安装PyTorch
    if not install_cuda_pytorch():
        print("❌ PyTorch安装失败")
        return
    
    # 安装项目依赖
    if not install_project_deps():
        print("⚠️ 部分依赖安装失败，但可以继续")
    
    # 设置Jupyter（可选）
    setup_jupyter()
    
    # 创建启动脚本
    create_startup_script()
    
    print("\n🎉 云端部署完成!")
    print("\n📋 使用说明:")
    print("1. 启动服务: ./start_cloud.sh")
    print("2. 访问UI: http://服务器IP:8501")
    print("3. 访问Jupyter: http://服务器IP:8888")
    print("\n💡 提示:")
    print("- 确保云服务器防火墙开放相应端口")
    print("- 建议使用tmux或screen保持会话")
    print("- 定期保存重要数据和模型")

if __name__ == "__main__":
    main()
