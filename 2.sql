create table Midi(MidiID INT PRIMARY KEY IDENTITY(1,1) NOT NULL,
<PERSON><PERSON><PERSON> nvarchar(100),<PERSON>er nvarchar(10),TrackCount int, KeySignature nvarchar(10),QuarternotetTime int);

create table MidiTrack(MidiT<PERSON><PERSON> INT PRIMARY KEY IDENTITY(1,1)NOT NULL,
MidiID INT foreign key references Midi(MidiID),Number int,Instrument nvarchar(100)
)

create table MidiTrackList(MidiTrackListID INT PRIMARY KEY IDENTITY(1,1)NOT NULL,
MidiTrackID INT foreign key references MidiTrack(MidiTrackID),
Channel int,Duration int,SyllabelNumber int,StartTime int,EndTime int,MidiID INT);

 create table Score(ScoreID INT PRIMARY KEY IDENTITY(1,1) NOT NULL,
SongName nvarchar(100),Artist nvarchar(100),MODE INT,ShowTimeStartBar int);

create table ScoreList(ScoreListID INT PRIMARY KEY IDENTITY(1,1)NOT NULL,
ScoreID INT foreign key references Score(ScoreID),
Bar int,Pos int,CurrentCombo int,ScoreNum int,CurrentCount int,NoteType int,SonId int);

alter table ScoreList add XMLNoteId int;
