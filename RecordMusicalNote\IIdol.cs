﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote
{
    public interface IIdol
    {
        int IdolNoteInfoID { get; }

        int LevelInfoId { get; set; }
        int Bar { get; set; }
        double Pos { get; set; }
        string FromTrack { get; set; }
        string TargetTrack { get; set; }
        string EndTrack { get; set; }
        int EndBar { get; set; }
        double EndPos { get; set; }
        string NoteType { get; set; }
        int CombineNoteNum { get; set; }

       
    }
}
