﻿using CommonModel;
using Helper;
using System;
using System.Collections.Generic;
using System.Transactions;

namespace MidiSheetMusic.Helper
{
    public   class Common
    {
        public static bool CheckHasExist(string songName)
        {
            SqlHelper helper = new SqlHelper();
            string sql = @"SELECT  count(0) from Midi where SongName='{0}'";
            sql = string.Format(sql, songName);
            return Convert.ToInt16(helper.ExecuteScalar(sql)) > 0;
        }

        public static bool SaveMidiInfo(MidiSheetMusicMidi midi, IList<CommonModel.MidiSheetMusicMidiTrack> tracks)
        {

            using (TransactionScope ts = new TransactionScope())
            {

                int midiKey = 0;
                int tracksKey = 0;
                SqlHelper helper = new SqlHelper();
                string sql = @"insert into midi(SongName,Meter,TrackCount,KeySignature,QuarternotetTime)values('{0}',
                   '{1}',{2},'{3}',{4}) select @@IDENTITY";
                sql = string.Format(sql, midi.SongName, midi.Meter, midi.TrackCount, midi.KeySignature,midi.QuarternotetTime);
                midiKey = Convert.ToInt32(helper.ExecuteScalar(sql));
                foreach (CommonModel.MidiSheetMusicMidiTrack track in tracks)
                {
                    sql = "insert into MidiTrack(MidiID,Number,Instrument)values({0},{1},'{2}')  select @@IDENTITY";
                    sql = string.Format(sql, midiKey, track.Number, track.Instrument);
                    tracksKey = Convert.ToInt32(helper.ExecuteScalar(sql));
                    foreach (MidiSheetMusicMidiTrackList MidiTrackList in track.MidiTrackList)
                    {
                        sql = @"insert into MidiTrackList(MidiTrackID, Channel,Duration,SyllabelNumber,StartTime,EndTime,MidiID)
                     values({0},{1},{2},{3},{4},{5},{6})";
                        sql = string.Format(sql, tracksKey, MidiTrackList.Channel, MidiTrackList.Duration, MidiTrackList.SyllabelNumber,
                             MidiTrackList.StartTime, MidiTrackList.EndTime, midiKey);
                        helper.ExecuteNonQuery(sql);
                    }
                }

                ts.Complete();
            }
            return true;
        }
    }
}
