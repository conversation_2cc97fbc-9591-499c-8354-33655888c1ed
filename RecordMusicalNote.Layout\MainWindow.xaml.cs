﻿using Microsoft.Win32;
using MyWPF.Layout.BackGround;
using RecordMusicalNote;
using RecordMusicalNote.Library;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;

namespace MyWPF.Layout
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            this.KeyDown += MainWindow_KeyDown;
            this.KeyUp += MainWindow_KeyUp;
            btnSave.Click += BtnSave_Click;
            btnContinue.Click += BtnClear_Click;
            // CeateXMLFile();
            _shortinfo = new IdolShortNoteInfoModel();
            _slipInfoModel = new IdolSlipInfoModel();
            _longInfoModel = new IdolLongInfoModel();
            btnContinue.IsEnabled = false;
            this.Closing += MainWindow_Closing;
            _HasError = false;
            this.WindowStartupLocation = WindowStartupLocation.CenterScreen;
            btnSave.IsEnabled = false;
            btnStartMusic.IsEnabled = false;

            // 在所有控件初始化完成后设置默认选中状态
            InitializeDefaultSettings();
        }

        /// <summary>
        /// 初始化默认设置，确保所有控件都已正确初始化
        /// </summary>
        private void InitializeDefaultSettings()
        {
            try
            {
                // 设置默认键位模式为4K
                if (rb4K != null)
                {
                    rb4K.IsChecked = true;
                }

                // 确保键位控件状态正确
                if (txtJ != null)
                {
                    txtJ.IsEnabled = false;
                }

                // 设置默认轨道数
                if (txtChannelCount != null)
                {
                    txtChannelCount.Text = "4";
                }

                _keyCount = 4;
            }
            catch (Exception ex)
            {
                // 记录错误但不影响程序启动
                System.Diagnostics.Debug.WriteLine($"初始化默认设置时出错: {ex.Message}");
            }
        }

        [DllImport("kernel32.dll")]
        public static extern IntPtr _lopen(string lpPathName, int iReadWrite);

        [DllImport("kernel32.dll")]
        public static extern bool CloseHandle(IntPtr hObject);
        public const int OF_READWRITE = 2;
        public const int OF_SHARE_DENY_NONE = 0x40;
        public readonly IntPtr HFILE_ERROR = new IntPtr(-1);

        private void MidiInfo_Click(object sender, RoutedEventArgs e)
        {
            if (MessageBox.Show("进入MidiInfo吗?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.Yes)
            {
                // this.Hide();
                ReadMidiInfo readMidiInfo = new ReadMidiInfo();
                readMidiInfo.Show();

            }
        }

        private void classicalMenu_Click(object sender, RoutedEventArgs e)
        {
            ClassicalTrans ct = new ClassicalTrans();
            ct.ShowDialog();
            bool _isDouble = ct.IsDouble;
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            string filePath = dlg.FileName;
            MainWindowHelper.TranslateClassical(_isDouble, filePath);
            MessageBox.Show("转换成功");
        }

        private void menuPinBallToBubble_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            string filePath = dlg.FileName;
            if (MessageBox.Show("是否转换成泡泡模式?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.No) return;
            MainWindowHelper MainWindowHelper = new MainWindowHelper();
            if (MessageBox.Show("是否全屏随机生成?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.No)
            {
                MainWindowHelper.PinballToBubble(filePath, false);
            }
            else
            {
                MainWindowHelper.PinballToBubble(filePath, true);
            }

            MessageBox.Show("转换成功");
        }

        private void menuTranslateBPM_Click(object sender, RoutedEventArgs e)
        {
            SetBPMMultiple BPMMultipleWin = new SetBPMMultiple();
            BPMMultipleWin.ShowDialog();
            if (BPMMultipleWin.BPMMultiple > 0)
            {
                OpenFileDialog dlg = new OpenFileDialog();
                dlg.Filter = "XML格式(*.XML)|*.XML";
                dlg.Multiselect = true;
                if (dlg.ShowDialog() != true) return; //不为确定时返回
                string filePath = dlg.FileName;
                MainWindowHelper.TranslateBPMIdolOrPinball(filePath, BPMMultipleWin.BPMMultiple);
                MessageBox.Show("转换BPM成功");
            }
        }

        private void menuCorrection_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            string filePath = dlg.FileName;
            MainWindowHelper.Correction(filePath);
            MessageBox.Show("修正成功!");
        }

        private void menuDeleteRepeatItemenuClear_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            string filePath = dlg.FileName;
            MainWindowHelper.DeleteRepeatNote(filePath);
        }

        private void menuClear_Click(object sender, RoutedEventArgs e)
        {

            // _sr = File.CreateText(FILE_NAME);//删除内容
            if (_sr != null)
            {
                _sr.Close();
                btnSave.IsEnabled = false;
                btnEnd.IsEnabled = false;
                btnContinue.IsEnabled = true;
                mediaelement.Pause();
                DeleteFile(FILE_NAME);
            }
            else
            {
                DeleteFile(FILE_NAME);
            }
        }
        bool isOccupy = false;
        public void DeleteFile(string path)
        {
            if (!File.Exists(path))
            {
                MessageBox.Show("文件都不存在");
                return;
            }

            IntPtr vHandle = _lopen(path, OF_READWRITE | OF_SHARE_DENY_NONE);
            if (vHandle == HFILE_ERROR)
            {
                isOccupy = true;
            }
            CloseHandle(vHandle);
            if (!isOccupy)
            {
                FileAttributes attr = File.GetAttributes(path);
                if (attr == FileAttributes.Directory)
                {
                    Directory.Delete(path, true);
                }
                else
                {
                    File.Delete(path);
                }
            }
            else
            {
                MessageBox.Show("文件被占用");
            }
        }
        private void menuIdolToBubble_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            string filePath = dlg.FileName;
            if (MessageBox.Show("是否转换成泡泡模式?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.No) return;
            MainWindowHelper.IdolToBubble(filePath);
            MessageBox.Show("转换成功");
        }

        private void menuIdolToPinball_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            string filePath = dlg.FileName;
            if (MessageBox.Show("是否转换成弹珠模式?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.No) return;
            MainWindowHelper.IdolToPinball(filePath);
            MessageBox.Show("转换成功");
        }

        private void menuItemmenuBubble_Click(object sender, RoutedEventArgs e)
        {

        }

        private void menuItemPinball_Click(object sender, RoutedEventArgs e)
        {

        }

        private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            if (_sr != null)
                _sr.Close();
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            btnSave.IsEnabled = true;
            btnContinue.IsEnabled = false;
            btnEnd.IsEnabled = true;

        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            _sr.Close();
            btnSave.IsEnabled = false;
            btnEnd.IsEnabled = false;
            btnContinue.IsEnabled = true;
            mediaelement.Pause();
        }

        private const string FILE_NAME = @"E:\\IdolXML.xml";
        StreamWriter _sr;
        IdolShortNoteInfoModel _shortinfo;
        IdolSlipInfoModel _slipInfoModel;
        IdolLongInfoModel _longInfoModel;
        bool _HasError;
        private void CeateXMLFile()
        {

            if (File.Exists(FILE_NAME)) //如果文件存在,则创建File.AppendText对象
            {
                _sr = File.AppendText(FILE_NAME);
            }
            else   //如果文件不存在,则创建File.CreateText对象
            {
                _sr = File.CreateText(FILE_NAME);
                _sr.WriteLine("<Normal>");
            }

        }
        private void btnEnd_Click(object sender, RoutedEventArgs e)
        {
            if (!_sr.BaseStream.CanWrite) return;
            _sr.WriteLine("</Normal>");
            _sr.Close();
            this.Close();
        }
        private void WriteShortNote(IdolShortNoteInfoModel sni)
        {
            if (!btnSave.IsEnabled) { MessageBox.Show("请先播放音乐"); return; }
            _sr.Write("<Note ");
            _sr.Write("Bar=" + '"' + sni.Bar + '"' + " ");
            _sr.Write("Pos=" + '"' + sni.Pos + '"' + " ");
            _sr.Write("from_track=" + '"' + sni.From_Track + '"' + " ");
            _sr.Write("target_track=" + '"' + sni.Target_Track + '"' + " ");
            _sr.Write("note_type=" + '"' + sni.Note_Type + '"' + " ");
            _sr.Write("/>");
            _sr.Write("\r\n");


        }
        private void WriteLongNote(IdolLongInfoModel lim)
        {
            if (!btnSave.IsEnabled) { MessageBox.Show("请先播放音乐"); return; }
            if (lim.EndPos < 0) lim.EndPos = 0;
            _sr.Write("<Note ");
            _sr.Write("Bar=" + '"' + lim.Bar + '"' + " ");
            _sr.Write("Pos=" + '"' + lim.Pos + '"' + " ");
            _sr.Write("from_track=" + '"' + lim.From_Track + '"' + " ");
            _sr.Write("target_track=" + '"' + lim.Target_Track + '"' + " ");
            _sr.Write("note_type=" + '"' + lim.Note_Type + '"' + " ");
            _sr.Write("EndBar=" + '"' + lim.EndBar + '"' + " ");
            _sr.Write("EndPos=" + '"' + lim.EndPos + '"' + " ");
            _sr.Write("/>");
            _sr.Write("\r\n");
        }
        private void WriteSlipNote(IdolSlipInfoModel sni)
        {
            if (!btnSave.IsEnabled) { MessageBox.Show("请先播放音乐"); return; }
            _sr.Write("<Note ");
            _sr.Write("Bar=" + '"' + sni.Bar + '"' + " ");
            _sr.Write("Pos=" + '"' + sni.Pos + '"' + " ");
            _sr.Write("target_track=" + '"' + sni.Target_track + '"' + " ");
            _sr.Write("end_track=" + '"' + sni.End_track + '"' + " ");
            _sr.Write("note_type=" + '"' + sni.Note_Type + '"' + " ");
            _sr.Write("/>");
            _sr.Write("\r\n");
        }
        private DateTime _keyDownTime;
        private DateTime _keyUpTime;
        private bool _isSuppressKey;
        private void MainWindow_KeyUp(object sender, KeyEventArgs e)
        {
            if (_sr == null) return;
            if (_HasError) return;
            if (!btnSave.IsEnabled) return;
            _keyUpTime = DateTime.Now;
            _isReleaseKey = true;
            if ((_keyUpTime - _keyDownTime).TotalMilliseconds > 200)
            {
                _isSuppressKey = true;
                //  _longInfoModel.EndBar = int.Parse(Math.Floor(CurrentMusicMillSecond / BarTime).ToString());
                float TimePerGZ = MusicTime / AllBarCount / 32;
                int currentGZ = int.Parse(Math.Floor((CurrentMusicMillSecond - ET) / TimePerGZ).ToString());
                _longInfoModel.EndPos = (currentGZ % 32 - 1) * 2;
                _longInfoModel.EndBar = int.Parse((currentGZ / 32).ToString()) + 1;
            }
            else
                _isSuppressKey = false;
            if (e.Key == Key.D)
            {
                txtD.Background = new SolidColorBrush(Colors.White);
                if (!_isSuppressKey)
                {
                    _shortinfo.From_Track = "Left2";
                    _shortinfo.Target_Track = "Left2";
                    WriteShortNote(_shortinfo);
                }
                else
                {
                    _longInfoModel.From_Track = "Left2";
                    _longInfoModel.Target_Track = "Left2";
                    WriteLongNote(_longInfoModel);
                }
            }
            if (e.Key == Key.F)
            {
                txtF.Background = new SolidColorBrush(Colors.White);
                if (!_isSuppressKey)
                {
                    _shortinfo.From_Track = "Left1";
                    _shortinfo.Target_Track = "Left1";
                    WriteShortNote(_shortinfo);
                }
                else
                {
                    _longInfoModel.From_Track = "Left1";
                    _longInfoModel.Target_Track = "Left1";
                    WriteLongNote(_longInfoModel);
                }
            }
            if (txtJ.IsEnabled && e.Key == Key.J)
            {

                txtJ.Background = new SolidColorBrush(Colors.White);
                if (!_isSuppressKey)
                {
                    _shortinfo.From_Track = "Middle";
                    _shortinfo.Target_Track = "Middle";
                    WriteShortNote(_shortinfo);
                }
                else
                {
                    _longInfoModel.From_Track = "Middle";
                    _longInfoModel.Target_Track = "Middle";
                    WriteLongNote(_longInfoModel);
                }
            }
            if (e.Key == Key.K)
            {

                txtK.Background = new SolidColorBrush(Colors.White);
                if (!_isSuppressKey)
                {
                    _shortinfo.From_Track = "Right1";
                    _shortinfo.Target_Track = "Right1";
                    WriteShortNote(_shortinfo);
                }
                else
                {
                    _longInfoModel.From_Track = "Right1";
                    _longInfoModel.Target_Track = "Right1";
                    WriteLongNote(_longInfoModel);
                }
            }
            if (e.Key == Key.L)
            {
                txtL.Background = new SolidColorBrush(Colors.White);
                if (!_isSuppressKey)
                {
                    _shortinfo.From_Track = "Right2";
                    _shortinfo.Target_Track = "Right2";
                    WriteShortNote(_shortinfo);
                }
                else
                {
                    _longInfoModel.From_Track = "Right2";
                    _longInfoModel.Target_Track = "Right2";
                    WriteLongNote(_longInfoModel);
                }
            }
            if (_isReleaseControlKey && e.Key == Key.Space)
            {
                txtLeft.Background = new SolidColorBrush(Colors.White);
                if (!_isSuppressKey)
                {
                    _slipInfoModel.Target_track = "Right2";
                    _slipInfoModel.End_track = "Right1";
                    WriteSlipNote(_slipInfoModel);
                }
            }
            //if (_isReleaseControlKey && e.Key == Key.B)
            //{
            //    txtRight.Background = new SolidColorBrush(Colors.White);
            //    if (!_isSuppressKey)
            //    {
            //        _slipInfoModel.Target_track = "Left2";
            //        _slipInfoModel.End_track = "Left1";
            //        WriteSlipNote(_slipInfoModel);
            //    }
            //}
            if (e.Key == Key.LeftCtrl)
            {
                _isReleaseControlKey = true;
                if (_enterControlKeyCounter > 2)
                {
                    _sr.Write("EndBar=" + '"' + _longInfoModel.EndBar + '"' + " ");
                    _sr.Write("EndPos=" + '"' + _longInfoModel.EndPos + '"' + " ");
                    _sr.Write("/>" + "\r\n");
                }
                _enterControlKeyCounter = 0;
                _lastEndBar = 0;
                _lastEndPos = 0;
                _sr.Write("</CombineNote>" + "\r\n");
            }
            if (!_isReleaseControlKey && e.Key == Key.Left)
            {
                txtLeft.Background = new SolidColorBrush(Colors.White);
                if (!btnSave.IsEnabled) { MessageBox.Show("请先播放音乐"); return; }
                //_longInfoModel.EndBar = int.Parse(Math.Floor(CurrentMusicMillSecond / BarTime).ToString());
                //_longInfoModel.EndPos = int.Parse(Math.Floor((CurrentMusicMillSecond % BarTime) * 32 / BarTime).ToString()) * 2;
                float TimePerGZ = MusicTime / AllBarCount / 32;
                int currentGZ;
                if (_enterControlKeyCounter == 0)
                {
                    _sr.Write("<CombineNote>" + "\r\n");

                }
                currentGZ = int.Parse(Math.Floor((CurrentMusicMillSecond - ET) / TimePerGZ).ToString());
                _longInfoModel.EndPos = (currentGZ % 32 - 1) * 2;
                _longInfoModel.EndBar = int.Parse((currentGZ / 32).ToString()) + 1;

                if (_enterControlKeyCounter == 0) _sr.Write("<CombineNote>" + "\r\n");

                _longInfoModel.From_Track = "Right2";
                _longInfoModel.Target_Track = "Right2";
                if (_enterControlKeyCounter > 1)
                {
                    _sr.Write("EndBar=" + '"' + _longInfoModel.EndBar + '"' + " ");
                    _sr.Write("EndPos=" + '"' + _longInfoModel.EndPos + '"' + " ");
                    _sr.Write("/>" + "\r\n");
                    //画箭头
                    _sr.Write("<Note ");
                    _sr.Write("Bar=" + '"' + _longInfoModel.EndBar + '"' + " ");
                    _sr.Write("Pos=" + '"' + _longInfoModel.EndPos + '"' + " ");
                    _sr.Write("target_track=" + '"' + _currentArrowDirection + '"' + " ");
                    _currentArrowDirection = GetArrowDirection(_currentArrowDirection, 0, _keyCount);
                    _sr.Write("end_track=" + '"' + _currentArrowDirection + '"' + " ");
                    _sr.Write("note_type=" + '"' + "slip" + '"' + " ");
                    _sr.Write("/>");
                    _sr.Write("\r\n");
                    _lastEndBar = _longInfoModel.EndBar;
                    _lastEndPos = _longInfoModel.EndPos;
                }
                if (_enterControlKeyCounter >= 1)
                {
                    _sr.Write("<Note ");
                    _sr.Write("Bar=" + '"' + _lastEndBar + '"' + " ");
                    _sr.Write("Pos=" + '"' + _lastEndPos + '"' + " ");
                    _sr.Write("from_track=" + '"' + _currentArrowDirection + '"' + " ");
                    _sr.Write("target_track=" + '"' + _currentArrowDirection + '"' + " ");
                    _sr.Write("note_type=" + '"' + "long" + '"' + " ");

                }

                else// (_enterControlKeyCounter <1)
                {
                    _currentArrowDirection = GetArrowDirection(_longInfoModel.Target_Track, 0, _keyCount);
                    //画长条
                    _sr.Write("<Note ");
                    _sr.Write("Bar=" + '"' + _longInfoModel.Bar + '"' + " ");
                    _sr.Write("Pos=" + '"' + _longInfoModel.Pos + '"' + " ");
                    _sr.Write("from_track=" + '"' + _longInfoModel.From_Track + '"' + " ");
                    _sr.Write("target_track=" + '"' + _longInfoModel.Target_Track + '"' + " ");
                    _sr.Write("note_type=" + '"' + "long" + '"' + " ");
                    _sr.Write("EndBar=" + '"' + _longInfoModel.EndBar + '"' + " ");
                    _sr.Write("EndPos=" + '"' + _longInfoModel.EndPos + '"' + " ");
                    _sr.Write("/>" + "\r\n");
                    //画箭头
                    _sr.Write("<Note ");
                    _sr.Write("Bar=" + '"' + _longInfoModel.EndBar + '"' + " ");
                    _sr.Write("Pos=" + '"' + _longInfoModel.EndPos + '"' + " ");
                    _sr.Write("target_track=" + '"' + _longInfoModel.Target_Track + '"' + " ");
                    _sr.Write("end_track=" + '"' + _currentArrowDirection + '"' + " ");
                    _sr.Write("note_type=" + '"' + "slip" + '"' + " ");
                    _sr.Write("/>");
                    _sr.Write("\r\n");
                    _lastEndBar = _longInfoModel.EndBar;
                    _lastEndPos = _longInfoModel.EndPos;

                }


                _enterControlKeyCounter += 1;
            }


            if (!_isReleaseControlKey && e.Key == Key.Right)
            {
                txtRight.Background = new SolidColorBrush(Colors.White);
                if (!btnSave.IsEnabled) { MessageBox.Show("请先播放音乐"); return; }
                //  _longInfoModel.EndBar = int.Parse(Math.Floor(CurrentMusicMillSecond / BarTime).ToString());
                // _longInfoModel.EndPos = int.Parse(Math.Floor((CurrentMusicMillSecond % BarTime) * 32 / BarTime).ToString()) * 2;
                float TimePerGZ = MusicTime / AllBarCount / 32;
                int currentGZ;
                if (_enterControlKeyCounter == 0)
                {
                    _sr.Write("<CombineNote>" + "\r\n");

                }
                currentGZ = int.Parse(Math.Floor((CurrentMusicMillSecond - ET) / TimePerGZ).ToString());
                _longInfoModel.EndPos = (currentGZ % 32 - 1) * 2;
                _longInfoModel.EndBar = int.Parse((currentGZ / 32).ToString()) + 1;
                //画长条
                _longInfoModel.From_Track = "Left2";
                _longInfoModel.Target_Track = "Left2";

                if (_enterControlKeyCounter > 1)
                {
                    _sr.Write("EndBar=" + '"' + _longInfoModel.EndBar + '"' + " ");
                    _sr.Write("EndPos=" + '"' + _longInfoModel.EndPos + '"' + " ");
                    _sr.Write("/>" + "\r\n");
                    //画箭头
                    _sr.Write("<Note ");
                    _sr.Write("Bar=" + '"' + _longInfoModel.EndBar + '"' + " ");
                    _sr.Write("Pos=" + '"' + _longInfoModel.EndPos + '"' + " ");
                    _sr.Write("target_track=" + '"' + _currentArrowDirection + '"' + " ");
                    _currentArrowDirection = GetArrowDirection(_currentArrowDirection, 1, _keyCount);
                    _sr.Write("end_track=" + '"' + _currentArrowDirection + '"' + " ");
                    _sr.Write("note_type=" + '"' + "slip" + '"' + " ");
                    _sr.Write("/>");
                    _sr.Write("\r\n");
                    _lastEndBar = _longInfoModel.EndBar;
                    _lastEndPos = _longInfoModel.EndPos;
                }
                if (_enterControlKeyCounter >= 1)
                {
                    _sr.Write("<Note ");
                    _sr.Write("Bar=" + '"' + _lastEndBar + '"' + " ");
                    _sr.Write("Pos=" + '"' + _lastEndPos + '"' + " ");
                    _sr.Write("from_track=" + '"' + _currentArrowDirection + '"' + " ");
                    _sr.Write("target_track=" + '"' + _currentArrowDirection + '"' + " ");
                    _sr.Write("note_type=" + '"' + "long" + '"' + " ");

                }
                else
                {
                    _currentArrowDirection = GetArrowDirection(_longInfoModel.Target_Track, 1, _keyCount);
                    _sr.Write("<Note ");
                    _sr.Write("Bar=" + '"' + _longInfoModel.Bar + '"' + " ");
                    _sr.Write("Pos=" + '"' + _longInfoModel.Pos + '"' + " ");
                    _sr.Write("from_track=" + '"' + _longInfoModel.From_Track + '"' + " ");
                    _sr.Write("target_track=" + '"' + _longInfoModel.Target_Track + '"' + " ");
                    _sr.Write("note_type=" + '"' + "long" + '"' + " ");

                    _sr.Write("EndBar=" + '"' + _longInfoModel.EndBar + '"' + " ");
                    _sr.Write("EndPos=" + '"' + _longInfoModel.EndPos + '"' + " ");
                    _sr.Write("/>" + "\r\n");
                    //画箭头
                    _sr.Write("<Note ");
                    _sr.Write("Bar=" + '"' + _longInfoModel.EndBar + '"' + " ");
                    _sr.Write("Pos=" + '"' + _longInfoModel.EndPos + '"' + " ");
                    _sr.Write("target_track=" + '"' + _longInfoModel.Target_Track + '"' + " ");
                    _sr.Write("end_track=" + '"' + _currentArrowDirection + '"' + " ");
                    _sr.Write("note_type=" + '"' + "slip" + '"' + " ");
                    _sr.Write("/>");
                    _sr.Write("\r\n");
                    _lastEndBar = _longInfoModel.EndBar;
                    _lastEndPos = _longInfoModel.EndPos;
                }
                _enterControlKeyCounter += 1;

            }



        }

        private float BarTime { get { return 60 / (BPM / 4) * 1000; } }
        bool _isReleaseKey = true;
        bool _isReleaseControlKey = true;
        int _enterControlKeyCounter = 0;
        int _lastEndBar = 0;
        int _lastEndPos = 0;
        string _currentArrowDirection;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="currentArrowDirection"></param>
        /// <param name="direction">0-left 1--right</param>
        /// <param name="keyCount">4k 或是5k</param>
        /// <returns></returns>
        private string GetArrowDirection(string currentArrowDirection, int direction, int keyCount)
        {
            if (keyCount == 4)
            {
                if (direction == 0)
                {
                    if (currentArrowDirection == "Left2")
                        return "Left1";

                    else if (currentArrowDirection == "Left1")
                        return "Left2";

                    else if (currentArrowDirection == "Right1")
                        return "Left1";

                    else if (currentArrowDirection == "Right2")
                        return "Right1";
                    else return "";
                }
                else
                {
                    if (currentArrowDirection == "Left2")
                        return "Left1";
                    else if (currentArrowDirection == "Left1")
                        return "Right1";
                    else if (currentArrowDirection == "Right1")
                        return "Right2";
                    else if (currentArrowDirection == "Right2")
                        return "Right1";
                    else return "";
                }
            }
            else//5K
            {
                if (direction == 0)
                {
                    if (currentArrowDirection == "Left2")
                        return "Left1";

                    else if (currentArrowDirection == "Left1")
                        return "Left2";
                    else if (currentArrowDirection == "Middle")
                        return "Left1";

                    else if (currentArrowDirection == "Right1")
                        return "Middle";

                    else if (currentArrowDirection == "Right2")
                        return "Right1";
                    else return "";
                }
                else
                {
                    if (currentArrowDirection == "Left2")
                        return "Left1";
                    else if (currentArrowDirection == "Left1")
                        return "Middle";
                    else if (currentArrowDirection == "Middle")
                        return "Right1";
                    else if (currentArrowDirection == "Right1")
                        return "Right2";
                    else if (currentArrowDirection == "Right2")
                        return "Right1";
                    else return "";
                }

            }
        }
        private void MainWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (CurrentMusicMillSecond <= 0) return;
            //if (btnSave.IsEnabled)
            {
                if (_isReleaseKey)
                {
                    _keyDownTime = DateTime.Now;
                    // _shortinfo.Bar = int.Parse(Math.Floor(CurrentMusicMillSecond / BarTime).ToString());
                    float TimePerGZ = MusicTime / AllBarCount / 32;
                    int currentGZ = int.Parse(Math.Ceiling((CurrentMusicMillSecond - ET) / TimePerGZ).ToString());
                    _shortinfo.Pos = currentGZ % 32 * 2;
                    _shortinfo.Bar = int.Parse((currentGZ / 32).ToString()) + 1;
                }
                _isReleaseKey = false;
                _slipInfoModel.Bar = _shortinfo.Bar;
                _slipInfoModel.Pos = _shortinfo.Pos;
                _longInfoModel.Bar = _shortinfo.Bar;
                _longInfoModel.Pos = _shortinfo.Pos;
                if (e.Key == Key.D)
                {
                    txtD.Content = "D";
                    txtD.Background = new SolidColorBrush(Colors.Yellow);
                }
                if (e.Key == Key.F)
                {
                    txtF.Content = "F";
                    txtF.Background = new SolidColorBrush(Colors.Yellow);

                }
                if (txtJ.IsEnabled && e.Key == Key.J)
                {
                    txtJ.Content = "J";
                    txtJ.Background = new SolidColorBrush(Colors.Yellow);

                }
                if (e.Key == Key.K)
                {
                    txtK.Content = "K";
                    txtK.Background = new SolidColorBrush(Colors.Yellow);

                }
                if (e.Key == Key.L)
                {
                    txtL.Content = "L";
                    txtL.Background = new SolidColorBrush(Colors.Yellow);

                }
                if (e.Key == Key.A)
                {
                    txtLeft.Background = new SolidColorBrush(Colors.Yellow);

                }
                if (e.Key == Key.B)
                {
                    txtRight.Background = new SolidColorBrush(Colors.Yellow);

                }
                if (e.KeyboardDevice.Modifiers == ModifierKeys.Control)
                {
                    _isReleaseControlKey = false;
                }
                if (e.KeyboardDevice.Modifiers == ModifierKeys.Control && e.Key == Key.Left)
                {

                    txtLeft.Background = new SolidColorBrush(Colors.Yellow);

                }

                if (e.KeyboardDevice.Modifiers == ModifierKeys.Control && e.Key == Key.Right)
                {

                    _longInfoModel.From_Track = "Right1";
                    _longInfoModel.Target_Track = "Right1";
                    txtRight.Background = new SolidColorBrush(Colors.Yellow);
                }
            }
        }


        public float CurrentMusicMillSecond
        {
            get
            {
                // 直接从媒体元素获取当前位置的毫秒数
                return (float)mediaelement.Position.TotalMilliseconds;
            }
        }
        public float BPM
        {
            get
            {
                float bpm = 0;
                float.TryParse(txtBPM.Text, out bpm);
                return bpm;
            }

            set
            {
                txtBPM.Text = value.ToString(); ;
            }
        }
        public float ET
        {
            get
            {
                float et = 0;
                float.TryParse(txtET.Text, out et);
                return et;
            }

            set
            {
                txtET.Text = value.ToString(); ;
            }
        }
        /// <summary>
        /// 音乐总时间
        /// </summary>
        public float MusicTime
        {
            get
            {
                float mt = 0;
                float.TryParse(txtAllTime.Text, out mt);
                return mt;
            }

            set
            {
                txtAllTime.Text = value.ToString(); ;
            }
        }
        /// <summary>
        /// 总小节数
        /// </summary>
        public int AllBarCount
        {
            get
            {
                int abc = 0;
                int.TryParse(txtAllBarCount.Text, out abc);
                return abc;
            }

            set
            {
                txtAllBarCount.Text = value.ToString(); ;
            }
        }
        /// <summary>
        /// 开场小节数
        /// </summary>
        public int StartBarCount
        {
            get
            {
                int sbc = 0;
                int.TryParse(txtStartBarCount.Text, out sbc);
                return sbc;
            }

            set
            {
                txtStartBarCount.Text = value.ToString(); ;
            }
        }

        private void btnSelectMusic_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "音频格式(*.mp3,*.wav)|*.mp3;*.wav";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            mediaelement.Source = new Uri(dlg.FileName);
            btnStartMusic.IsEnabled = true;
        }

        private void btnStartMusic_Click(object sender, RoutedEventArgs e)
        {
            if (BPM <= 0)
            {
                MessageBox.Show("请输入BPM");
                return;
            }

            CeateXMLFile();
            mediaelement.Play();
            btnSave.IsEnabled = true;
            btnStartMusic.IsEnabled = false;

        }

        private void btnPause_Click(object sender, RoutedEventArgs e)
        {
            mediaelement.Pause();
        }

        private void sliderPosition_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            mediaelement.Position = TimeSpan.FromSeconds(sliderPosition.Value);
        }
        DispatcherTimer timer = null;
        private void mediaelement_MediaOpened(object sender, RoutedEventArgs e)
        {
            if (BPM <= 0)
            {
                MessageBox.Show("请输入BPM");
                return;
            }
            sliderPosition.Maximum = mediaelement.NaturalDuration.TimeSpan.TotalSeconds;
            // txtAllTime.Text = mediaelement.NaturalDuration.TimeSpan.TotalMilliseconds.ToString();
            //媒体文件打开成功
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(0.1);
            timer.Tick += new EventHandler(timer_tick);
            timer.Start();
            //double barCount = BPM / 4;
            // double AllBC =Math.Ceiling ( mediaelement.NaturalDuration.TimeSpan.TotalSeconds/60* barCount);
            // AllBarCount = int.Parse(AllBC.ToString());


        }
        private void timer_tick(object sender, EventArgs e)
        {
            sliderPosition.Value = mediaelement.Position.TotalSeconds;
            lblCurrentMusic.Text = TimeSpan.FromMilliseconds(mediaelement.Position.TotalMilliseconds).ToString(@"hh\:mm\:ss");
        }
        private int _keyCount;
        private void rb4K_Checked(object sender, RoutedEventArgs e)
        {
            try
            {
                if (txtJ != null)
                {
                    txtJ.IsEnabled = false;
                }
                _keyCount = 4;
                if (txtChannelCount != null)
                {
                    txtChannelCount.Text = "4";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"rb4K_Checked 错误: {ex.Message}");
            }
        }

        private void rb5K_Checked(object sender, RoutedEventArgs e)
        {
            try
            {
                if (txtJ != null)
                {
                    txtJ.IsEnabled = true;
                }
                _keyCount = 5;
                if (txtChannelCount != null)
                {
                    txtChannelCount.Text = "5";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"rb5K_Checked 错误: {ex.Message}");
            }
        }

        private void GetMidiInfo_Click(object sender, RoutedEventArgs e)
        {
            MidiSheetMusic.RecordMidiInfo getMidInfoWin = new MidiSheetMusic.RecordMidiInfo();
            getMidInfoWin.Show();
        }

        private void menuIdolToPinballNoDeltedRepeat_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            string filePath = dlg.FileName;
            bool isChangeSlipNote = true;
            if (MessageBox.Show("是否转换成弹珠模式?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.No) return;
            if (MessageBox.Show("是否将滑条箭头转为滑动弹珠?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.No)
            {
                isChangeSlipNote = false;
            }

            MainWindowHelper.IdolToPinballNoDeleteRepeat(filePath, isChangeSlipNote);
            MessageBox.Show("转换成功");
        }

        private void menuIdolToPinballNew_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            string filePath = dlg.FileName;

            if (MessageBox.Show("是否转换成弹珠模式(New)?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.No) return;

            MainWindowHelper.IdolToPinballNew(filePath);
            MessageBox.Show("转换成功");
        }

        private void menuNoteEditor_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var noteEditor = new NoteEditor();
                noteEditor.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开音符编辑器失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void menuIdolToXY_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            string filePath = dlg.FileName;
            if (MessageBox.Show("是否转换成弦月模式?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.No) return;
            MainWindowHelper.IdolToXY(filePath);
            MessageBox.Show("转换成功");
        }

        private void menuCalculateBD_Click(object sender, RoutedEventArgs e)
        {
            CalculateBD bd = new CalculateBD();
            bd.Show();
        }

        private void menuChartGenerator_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var chartGenerator = new ChartGenerator.ChartGeneratorWindow();
                chartGenerator.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开谱面生成器失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


        private void FireAnalyse_Click(object sender, RoutedEventArgs e)
        {
            ScoreInfoAnalyse analyse = new ScoreInfoAnalyse();
            analyse.Show();
        }

        private void FileReName_Click(object sender, RoutedEventArgs e)
        {
            System.Windows.Forms.FolderBrowserDialog dialog = new System.Windows.Forms.FolderBrowserDialog();
            dialog.Description = "请选择XML所在文件夹";
            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                if (string.IsNullOrEmpty(dialog.SelectedPath))
                {
                    System.Windows.MessageBox.Show(this, "文件夹路径不能为空", "提示");
                    return;
                }

            }
            else
                return;
            DirectoryInfo theFolder = new DirectoryInfo(dialog.SelectedPath);
            if (!Directory.Exists(dialog.SelectedPath + @"\" + @"reName"))
            {
                Directory.CreateDirectory(dialog.SelectedPath + @"\" + @"reName");
            }
            foreach (FileInfo NextFile in theFolder.GetFiles())//遍历文件
            {
                if (NextFile.Name.ToLower().EndsWith("xml"))
                {
                    string fileName = NextFile.Name;
                    string songName = MainWindowHelper.GetXmlSongName(dialog.SelectedPath + @"\" + fileName);
                    string author = MainWindowHelper.GetXmlAuthorName(dialog.SelectedPath + @"\" + fileName);
                    string invalid = new string(Path.GetInvalidFileNameChars()) + new string(Path.GetInvalidPathChars());
                    foreach (char c in invalid)
                    {
                        songName = songName.Replace(c.ToString(), "");
                        author = author.Replace(c.ToString(), "");
                    }
                    string filePath = dialog.SelectedPath + @"\" + @"reName\" + songName + "_" + author+"_"+fileName;
                    if (!File.Exists(filePath))
                        NextFile.CopyTo(filePath);
                }

            }
            MessageBox.Show("重命名完成");
        }

        private void CreateIdolShortModel_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            string filePath = dlg.FileName;
            MainWindowHelper.CreateIdolShortModel(filePath);
            MessageBox.Show("生成成功!");

        }

        private void MusicXMLInfo_Click(object sender, RoutedEventArgs e)
        {

            if (MessageBox.Show("进入MusicInfo吗?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.Yes)
            {
                // this.Hide();
                ReadMusicXMLInfo readMidiInfo = new ReadMusicXMLInfo();
                readMidiInfo.Show();

            }
        }

        private void ImdTxtToIdol_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.Txt)|*.txt";
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            IMDSettings iMDSettings = new IMDSettings();
            iMDSettings.ShowDialog();
            string filePath = dlg.FileName;
            string str1 = File.ReadAllText(filePath);
            string[] dataInfo=str1.Replace("\r\n", "@").Split('@');
            List<IMDTxtDataModel> iMDTxtDataModels = new List<IMDTxtDataModel>();
            foreach (var item in dataInfo)
            {
               string []strItem= item.Split('\t');
                IMDTxtDataModel iMDTxtDataModel = new IMDTxtDataModel();
                iMDTxtDataModel.折线 = strItem[0];
                iMDTxtDataModel.类型 = strItem[1];
                iMDTxtDataModel.时间点 = int.Parse(strItem[2]);
                iMDTxtDataModel.键位 = int.Parse(strItem[3]);
                iMDTxtDataModel.参数 = int.Parse(strItem[4]);
                iMDTxtDataModels.Add(iMDTxtDataModel);
            }
            IMDHelper.ToIdol(iMDSettings.BPM,iMDSettings.Key, iMDTxtDataModels);
        }
    }
}
