﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MyWPF.Layout
{
    public class IdolCellInfo
    {
        public float CurrentTime { get; set; }
        public int CurrentCount { get; set; }

        public string Tip { get; set; }

        public string TargetTrackName { get; set; }

        public string EndTrackName { get; set; }

        public int CurrentBar
        {

            get
            {
                return CurrentCount / 32 + 1;
            }
        }
        public int CurrentPos
        {
            get
            {
                return (CurrentCount % 32) * 2;
            }
        }
        public string NotyTypeStr
        {
            get
            {
                if (NoteType == 1)
                    return "short";
                else if (NoteType == 2)
                    return "slip";
                else
                    return "long";
            }
        }
        /// <summary>
        /// 0代表空白 1 short 2 long 3 slip
        /// </summary>
        /// 
        private int _noteType;
        public int NoteType
        {
            get { return _noteType; }
            set
            {
                _noteType = value;

            }
        }
        /// <summary>
        ///  长条持续了的长度
        /// </summary>
        /// 
        private int _lastedLength;


        public int LastedLength
        {
            get { return _lastedLength; }
            set
            {
                _lastedLength = value;

            }
        }

        public int LongNoteTypEndCount
        {
            get
            {
                if (this.NoteType == 3)
                    return CurrentCount + (LastedLength / 2 - 1);
                return -1;
            }
        }
        /// <summary>
        /// 是否第一次访问改对象
        /// </summary>
        /// 
        private bool _isFirstAccess = true;
        public bool IsFirstAccess
        {
            get
            {
                return _isFirstAccess;
            }
            set
            {
                _isFirstAccess = value;
            }
        }
    }

    public class PinballCellInfo
    {
        public float CurrentTime { get; set; }
        public int CurrentCount { get; set; }

        public string Tip { get; set; }

        public string TargetTrackName { get; set; }

        public string EndTrackName { get; set; }

        public int SonId { get; set; }

        public int CurrentBar
        {

            get
            {
                return CurrentCount / 32 + 1;
            }
        }
        public int CurrentPos
        {
            get
            {
                return (CurrentCount % 32) * 2;
            }
        }
        public string NotyTypeStr
        {
            get
            {
                if (NoteType == 1)
                    return "PinballSingle";
                else if (NoteType == 2)
                    return "PinballSlip";
                else if (NoteType == 3)
                    return "PinballSeries";
                else return "PinballLong";
            }
        }
        /// <summary>
        /// 0代表空白 1 short 2 long 3 slip
        /// </summary>
        /// 
        private int _noteType;
        public int NoteType
        {
            get { return _noteType; }
            set
            {
                _noteType = value;

            }
        }
        /// <summary>
        ///  长条持续了的长度
        /// </summary>
        /// 
        private int _lastedLength;


        public int LastedLength
        {
            get { return _lastedLength; }
            set
            {
                _lastedLength = value;

            }
        }

        public int LongNoteTypEndCount
        {
            get
            {
                if (this.NoteType == 4)
                    return CurrentCount + (LastedLength / 2 - 1);
                return -1;
            }
        }
        /// <summary>
        /// 是否第一次访问改对象
        /// </summary>
        /// 
        private bool _isFirstAccess = true;
        public bool IsFirstAccess
        {
            get
            {
                return _isFirstAccess;
            }
            set
            {
                _isFirstAccess = value;
            }
        }


        public int XMLNoteId
        {
            get;
            set;
          
        }
    }
}
