using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;
using Microsoft.Win32;
using CommonModel;
using IOPath = System.IO.Path;

namespace MyWPF.Layout.Audio
{
    /// <summary>
    /// 音频分析面板
    /// </summary>
    public partial class AudioAnalysisPanel : UserControl
    {
        private Mp3Analyzer _analyzer;
        private Mp3Analyzer.WaveformData _waveformData;
        private Mp3Analyzer.BeatDetectionResult _beatResult;
        private Mp3Analyzer.EnergyAnalysisResult _energyResult;
        private string _currentAudioFile;
        private double _zoomLevel = 1.0;
        private bool _isAnalyzing = false;
        private double? _originalBPM; // 保存原始分析的BPM值

        // 鼠标交互
        private bool _isDragging = false;
        private Point _lastMousePosition;

        public AudioAnalysisPanel()
        {
            InitializeComponent();
            _analyzer = new Mp3Analyzer();
            UpdateUI();
        }

        #region 事件处理

        private async void BtnLoadAudio_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择音频文件",
                Filter = "音频文件|*.mp3;*.wav;*.m4a|MP3文件|*.mp3|WAV文件|*.wav|所有文件|*.*",
                RestoreDirectory = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                await LoadAudioFile(openFileDialog.FileName);
            }
        }

        private async void BtnAnalyze_Click(object sender, RoutedEventArgs e)
        {
            if (_waveformData == null) return;

            await AnalyzeAudio();
        }

        private void ChkDisplay_Changed(object sender, RoutedEventArgs e)
        {
            DrawWaveform();
        }

        private void SliderZoom_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (txtZoomValue == null) return; // 控件未初始化时跳过
            
            _zoomLevel = e.NewValue;
            txtZoomValue.Text = $"{_zoomLevel:F1}x";
            DrawWaveform();
        }

        private void WaveformCanvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            _isDragging = true;
            _lastMousePosition = e.GetPosition(waveformCanvas);
            waveformCanvas.CaptureMouse();
        }

        private void WaveformCanvas_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isDragging)
            {
                var currentPosition = e.GetPosition(waveformCanvas);
                var deltaX = currentPosition.X - _lastMousePosition.X;
                
                // 实现拖拽滚动
                var scrollViewer = waveformScrollViewer;
                scrollViewer.ScrollToHorizontalOffset(scrollViewer.HorizontalOffset - deltaX);
                
                _lastMousePosition = currentPosition;
            }
            else if (_waveformData != null)
            {
                // 显示时间位置
                var position = e.GetPosition(waveformCanvas);
                var timePosition = (position.X / waveformCanvas.ActualWidth) * _waveformData.Duration.TotalSeconds;
                txtStatus.Text = $"时间位置: {TimeSpan.FromSeconds(timePosition):mm\\:ss\\.ff}";
            }
        }

        private void WaveformCanvas_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            _isDragging = false;
            waveformCanvas.ReleaseMouseCapture();
        }

        private async void BtnGenerateNotes_Click(object sender, RoutedEventArgs e)
        {
            if (_beatResult == null || _energyResult == null) return;

            await GenerateSuggestedNotes();
        }

        private void BtnApplyBPM_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (double.TryParse(txtBPMInput.Text, out double newBPM))
                {
                    if (newBPM >= 60 && newBPM <= 200)
                    {
                        ApplyManualBPM(newBPM);
                        txtBPMStatus.Text = $"已应用手动BPM: {newBPM}";
                        txtBPMStatus.Foreground = new SolidColorBrush(Colors.Green);
                    }
                    else
                    {
                        txtBPMStatus.Text = "BPM值应在60-200之间";
                        txtBPMStatus.Foreground = new SolidColorBrush(Colors.Red);
                    }
                }
                else
                {
                    txtBPMStatus.Text = "请输入有效的数字";
                    txtBPMStatus.Foreground = new SolidColorBrush(Colors.Red);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用BPM失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnResetBPM_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_beatResult != null && _originalBPM.HasValue)
                {
                    // 恢复到原始分析结果
                    _beatResult.EstimatedBPM = _originalBPM.Value;
                    txtBPMInput.Text = _originalBPM.Value.ToString("F1");
                    txtEstimatedBPM.Text = $"估算BPM: {_originalBPM.Value:F1} (置信度: {_beatResult.Confidence:P1})";

                    // 重新进行原始的节拍检测
                    ResetToOriginalBeats();

                    txtBPMStatus.Text = "已重置为原始分析结果";
                    txtBPMStatus.Foreground = new SolidColorBrush(Colors.Blue);

                    // 清除原始BPM保存
                    _originalBPM = null;

                    // 重新绘制波形
                    DrawWaveform();
                }
                else if (_beatResult != null)
                {
                    txtBPMInput.Text = _beatResult.EstimatedBPM.ToString("F1");
                    txtBPMStatus.Text = "当前就是原始分析结果";
                    txtBPMStatus.Foreground = new SolidColorBrush(Colors.Gray);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置BPM失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 重置到原始节拍检测结果
        /// </summary>
        private async void ResetToOriginalBeats()
        {
            try
            {
                if (_waveformData != null)
                {
                    // 重新进行节拍检测
                    _beatResult = await _analyzer.DetectBeatsAsync(_waveformData);

                    // 更新节拍点列表显示
                    var beatItems = _beatResult.BeatTimes.Select((time, index) => new
                    {
                        Index = index + 1,
                        Time = time,
                        TimeDisplay = $"节拍 {index + 1}: {TimeSpan.FromSeconds(time):mm\\:ss\\.ff}"
                    }).ToList();
                    lstBeats.ItemsSource = beatItems;
                }
            }
            catch (Exception ex)
            {
                txtBPMStatus.Text = $"重置节拍失败: {ex.Message}";
                txtBPMStatus.Foreground = new SolidColorBrush(Colors.Red);
            }
        }

        #endregion

        #region 核心功能

        /// <summary>
        /// 加载音频文件
        /// </summary>
        private async Task LoadAudioFile(string filePath)
        {
            try
            {
                SetStatus("正在加载音频文件...", true);
                
                _currentAudioFile = filePath;
                _waveformData = await _analyzer.AnalyzeWaveformAsync(filePath);
                
                // 更新UI信息
                txtFileName.Text = $"文件: {IOPath.GetFileName(filePath)}";
                txtDuration.Text = $"时长: {_waveformData.Duration:mm\\:ss}";
                txtSampleRate.Text = $"采样率: {_waveformData.SampleRate} Hz";
                txtChannels.Text = $"声道: {_waveformData.Channels}";
                
                // 绘制波形
                DrawWaveform();
                
                btnAnalyze.IsEnabled = true;
                SetStatus("音频文件加载完成", false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载音频文件失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                SetStatus("加载失败", false);
            }
        }

        /// <summary>
        /// 分析音频
        /// </summary>
        private async Task AnalyzeAudio()
        {
            if (_isAnalyzing) return;

            try
            {
                _isAnalyzing = true;
                SetStatus("正在分析音频...", true);

                // 并行执行节拍检测和能量分析
                var beatTask = _analyzer.DetectBeatsAsync(_waveformData);
                var energyTask = _analyzer.AnalyzeEnergyAsync(_waveformData);

                await Task.WhenAll(beatTask, energyTask);

                _beatResult = await beatTask;
                _energyResult = await energyTask;

                // 更新UI
                UpdateAnalysisResults();
                DrawWaveform(); // 重新绘制以显示分析结果
                
                btnGenerateNotes.IsEnabled = true;
                SetStatus("音频分析完成", false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"音频分析失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                SetStatus("分析失败", false);
            }
            finally
            {
                _isAnalyzing = false;
            }
        }

        /// <summary>
        /// 生成建议音符
        /// </summary>
        private async Task GenerateSuggestedNotes()
        {
            try
            {
                SetStatus("正在生成建议音符...", true);

                var suggestedNotes = new List<SuggestedNote>();

                // 基于节拍点生成音符
                foreach (var beatTime in _beatResult.BeatTimes)
                {
                    var bar = (int)(beatTime / 4) + 1; // 假设4/4拍
                    var pos = (beatTime % 4) * 4; // 转换为16分音符位置

                    // 根据能量选择轨道
                    var energyIndex = (int)(beatTime / 0.1); // 100ms窗口
                    var track = SelectTrackByEnergy(energyIndex);

                    suggestedNotes.Add(new SuggestedNote
                    {
                        Time = beatTime,
                        Bar = bar,
                        Pos = pos,
                        Track = track,
                        NoteType = "short",
                        Confidence = 0.8f
                    });
                }

                // 基于能量峰值生成额外音符
                foreach (var peak in _energyResult.Peaks.Where(p => p.Confidence > 0.6))
                {
                    var bar = (int)(peak.Time / 4) + 1;
                    var pos = (peak.Time % 4) * 4;
                    var track = SelectTrackByEnergy((int)(peak.Time / 0.1));

                    // 避免重复
                    if (!suggestedNotes.Any(n => Math.Abs(n.Time - peak.Time) < 0.2))
                    {
                        suggestedNotes.Add(new SuggestedNote
                        {
                            Time = peak.Time,
                            Bar = bar,
                            Pos = pos,
                            Track = track,
                            NoteType = "short",
                            Confidence = peak.Confidence
                        });
                    }
                }

                // 排序并更新列表
                suggestedNotes = suggestedNotes.OrderBy(n => n.Time).ToList();
                lstSuggestedNotes.ItemsSource = suggestedNotes;

                SetStatus($"生成了 {suggestedNotes.Count} 个建议音符", false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成建议音符失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                SetStatus("生成失败", false);
            }
        }

        #endregion

        #region UI更新

        /// <summary>
        /// 绘制波形
        /// </summary>
        private void DrawWaveform()
        {
            if (_waveformData == null || waveformCanvas == null) return;

            waveformCanvas.Children.Clear();
            
            var canvasWidth = Math.Max(800, _waveformData.Duration.TotalSeconds * 50 * _zoomLevel);
            var canvasHeight = waveformCanvas.Height;
            waveformCanvas.Width = canvasWidth;

            // 绘制波形
            if (chkShowWaveform.IsChecked == true)
            {
                DrawWaveformData(canvasWidth, canvasHeight);
            }

            // 绘制能量
            if (chkShowEnergy.IsChecked == true && _energyResult != null)
            {
                DrawEnergyData(canvasWidth, canvasHeight);
            }

            // 绘制节拍点
            if (chkShowBeats.IsChecked == true && _beatResult != null)
            {
                DrawBeatMarkers(canvasWidth, canvasHeight);
            }

            // 绘制时间刻度
            DrawTimeScale(canvasWidth, canvasHeight);
        }

        /// <summary>
        /// 绘制波形数据
        /// </summary>
        private void DrawWaveformData(double canvasWidth, double canvasHeight)
        {
            if (_waveformData.PeakData == null) return;

            var path = new System.Windows.Shapes.Path
            {
                Stroke = Brushes.Blue,
                StrokeThickness = 1
            };

            var geometry = new PathGeometry();
            var figure = new PathFigure();
            
            var centerY = canvasHeight / 2;
            var scaleY = centerY * 0.8;

            for (int i = 0; i < _waveformData.PeakData.Length; i++)
            {
                var x = (double)i / _waveformData.PeakData.Length * canvasWidth;
                var y = centerY - _waveformData.PeakData[i] * scaleY;

                if (i == 0)
                {
                    figure.StartPoint = new Point(x, y);
                }
                else
                {
                    figure.Segments.Add(new LineSegment(new Point(x, y), true));
                }
            }

            geometry.Figures.Add(figure);
            path.Data = geometry;
            waveformCanvas.Children.Add(path);
        }

        /// <summary>
        /// 绘制能量数据
        /// </summary>
        private void DrawEnergyData(double canvasWidth, double canvasHeight)
        {
            var path = new System.Windows.Shapes.Path
            {
                Stroke = Brushes.Red,
                StrokeThickness = 2,
                Opacity = 0.7
            };

            var geometry = new PathGeometry();
            var figure = new PathFigure();
            
            var maxEnergy = _energyResult.EnergyLevels.Max();
            var scaleY = canvasHeight * 0.3;

            for (int i = 0; i < _energyResult.EnergyLevels.Length; i++)
            {
                var x = (i * 0.1) / _waveformData.Duration.TotalSeconds * canvasWidth;
                var y = canvasHeight - (_energyResult.EnergyLevels[i] / maxEnergy) * scaleY;

                if (i == 0)
                {
                    figure.StartPoint = new Point(x, y);
                }
                else
                {
                    figure.Segments.Add(new LineSegment(new Point(x, y), true));
                }
            }

            geometry.Figures.Add(figure);
            path.Data = geometry;
            waveformCanvas.Children.Add(path);
        }

        /// <summary>
        /// 绘制节拍标记
        /// </summary>
        private void DrawBeatMarkers(double canvasWidth, double canvasHeight)
        {
            foreach (var beatTime in _beatResult.BeatTimes)
            {
                var x = (beatTime / _waveformData.Duration.TotalSeconds) * canvasWidth;
                
                var line = new Line
                {
                    X1 = x,
                    Y1 = 0,
                    X2 = x,
                    Y2 = canvasHeight,
                    Stroke = Brushes.Green,
                    StrokeThickness = 2,
                    Opacity = 0.6
                };

                waveformCanvas.Children.Add(line);
            }
        }

        /// <summary>
        /// 绘制时间刻度
        /// </summary>
        private void DrawTimeScale(double canvasWidth, double canvasHeight)
        {
            var duration = _waveformData.Duration.TotalSeconds;
            var interval = Math.Max(1, duration / 20); // 大约20个刻度

            for (double time = 0; time <= duration; time += interval)
            {
                var x = (time / duration) * canvasWidth;
                
                var line = new Line
                {
                    X1 = x,
                    Y1 = canvasHeight - 20,
                    X2 = x,
                    Y2 = canvasHeight,
                    Stroke = Brushes.Gray,
                    StrokeThickness = 1
                };

                var text = new TextBlock
                {
                    Text = TimeSpan.FromSeconds(time).ToString(@"mm\:ss"),
                    FontSize = 10,
                    Foreground = Brushes.Gray
                };

                Canvas.SetLeft(text, x - 15);
                Canvas.SetTop(text, canvasHeight - 15);

                waveformCanvas.Children.Add(line);
                waveformCanvas.Children.Add(text);
            }
        }

        /// <summary>
        /// 更新分析结果显示
        /// </summary>
        private void UpdateAnalysisResults()
        {
            // 更新BPM信息
            txtEstimatedBPM.Text = $"估算BPM: {_beatResult.EstimatedBPM:F1} (置信度: {_beatResult.Confidence:P1})";

            // 更新BPM输入框
            txtBPMInput.Text = _beatResult.EstimatedBPM.ToString("F1");
            txtBPMInput.IsEnabled = true;
            btnApplyBPM.IsEnabled = true;
            btnResetBPM.IsEnabled = true;
            txtBPMStatus.Text = "可以手动修正BPM值";
            txtBPMStatus.Foreground = new SolidColorBrush(Colors.Gray);

            // 更新节拍点列表
            var beatItems = _beatResult.BeatTimes.Select((time, index) => new
            {
                Index = index + 1,
                Time = time,
                TimeDisplay = $"节拍 {index + 1}: {TimeSpan.FromSeconds(time):mm\\:ss\\.ff}"
            }).ToList();
            lstBeats.ItemsSource = beatItems;

            // 更新能量峰值列表
            var peakItems = _energyResult.Peaks.Select((peak, index) => new
            {
                Index = index + 1,
                Time = peak.Time,
                Energy = peak.Energy,
                Confidence = peak.Confidence,
                PeakDisplay = $"峰值 {index + 1}: {TimeSpan.FromSeconds(peak.Time):mm\\:ss\\.ff} (能量: {peak.Energy:F3}, 置信度: {peak.Confidence:P1})"
            }).ToList();
            lstEnergyPeaks.ItemsSource = peakItems;
        }

        /// <summary>
        /// 应用手动设置的BPM
        /// </summary>
        private void ApplyManualBPM(double newBPM)
        {
            if (_beatResult == null) return;

            // 保存原始BPM（如果还没保存过）
            if (!_originalBPM.HasValue)
            {
                _originalBPM = _beatResult.EstimatedBPM;
            }

            // 更新BPM值
            _beatResult.EstimatedBPM = newBPM;

            // 根据新BPM重新计算节拍点
            RecalculateBeatsFromBPM(newBPM);

            // 更新显示
            txtEstimatedBPM.Text = $"手动BPM: {newBPM:F1} (原分析值: {_originalBPM:F1})";

            // 重新生成建议音符
            if (btnGenerateNotes.IsEnabled)
            {
                GenerateSuggestedNotes();
            }

            // 重新绘制波形
            DrawWaveform();
        }

        /// <summary>
        /// 根据BPM重新计算节拍点
        /// </summary>
        private void RecalculateBeatsFromBPM(double bpm)
        {
            if (_waveformData == null) return;

            var beatInterval = 60.0 / bpm; // 每拍的秒数
            var totalDuration = _waveformData.Duration.TotalSeconds;
            var newBeatTimes = new List<double>();

            // 从第一拍开始，按固定间隔生成节拍点
            for (double time = 0; time < totalDuration; time += beatInterval)
            {
                newBeatTimes.Add(time);
            }

            // 更新节拍结果
            _beatResult.BeatTimes = newBeatTimes;
            _beatResult.Confidence = 1.0; // 手动设置的置信度为100%

            // 更新节拍点列表显示
            var beatItems = _beatResult.BeatTimes.Select((time, index) => new
            {
                Index = index + 1,
                Time = time,
                TimeDisplay = $"节拍 {index + 1}: {TimeSpan.FromSeconds(time):mm\\:ss\\.ff} [手动]"
            }).ToList();
            lstBeats.ItemsSource = beatItems;
        }

        /// <summary>
        /// 设置状态
        /// </summary>
        private void SetStatus(string message, bool showProgress)
        {
            txtStatus.Text = message;
            progressBar.Visibility = showProgress ? Visibility.Visible : Visibility.Collapsed;
        }

        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUI()
        {
            btnAnalyze.IsEnabled = _waveformData != null;
            btnGenerateNotes.IsEnabled = _beatResult != null && _energyResult != null;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 根据能量选择轨道
        /// </summary>
        private string SelectTrackByEnergy(int energyIndex)
        {
            if (_energyResult?.EnergyLevels == null || energyIndex >= _energyResult.EnergyLevels.Length)
                return "Left1";

            var energy = _energyResult.EnergyLevels[energyIndex];
            var maxEnergy = _energyResult.EnergyLevels.Max();
            var normalizedEnergy = energy / maxEnergy;

            // 根据能量级别分配轨道
            if (normalizedEnergy > 0.8) return "Right2";
            if (normalizedEnergy > 0.6) return "Right1";
            if (normalizedEnergy > 0.4) return "Left1";
            return "Left2";
        }

        #endregion

        #region 数据模型

        /// <summary>
        /// 建议音符
        /// </summary>
        public class SuggestedNote
        {
            public double Time { get; set; }
            public int Bar { get; set; }
            public double Pos { get; set; }
            public string Track { get; set; }
            public string NoteType { get; set; }
            public float Confidence { get; set; }

            public string NoteDisplay => 
                $"小节{Bar} 位置{Pos:F1} {Track} ({NoteType}) - 置信度: {Confidence:P1}";
        }

        #endregion

        /// <summary>
        /// 获取建议音符列表（供外部调用）
        /// </summary>
        public List<SuggestedNote> GetSuggestedNotes()
        {
            return lstSuggestedNotes.ItemsSource as List<SuggestedNote> ?? new List<SuggestedNote>();
        }

        /// <summary>
        /// 获取当前音频文件路径（供外部调用）
        /// </summary>
        public string GetCurrentAudioFile()
        {
            return _currentAudioFile;
        }

        /// <summary>
        /// 获取音频分析结果（供AI制谱使用）
        /// </summary>
        public MyWPF.Layout.AI.AudioAnalysisData GetAudioAnalysisData()
        {
            if (_waveformData == null || _beatResult == null || _energyResult == null)
                return null;

            var audioData = new MyWPF.Layout.AI.AudioAnalysisData
            {
                BPM = _beatResult.EstimatedBPM,
                BPMConfidence = _beatResult.Confidence,
                Duration = _waveformData.Duration,
                SampleRate = _waveformData.SampleRate,
                Channels = _waveformData.Channels,
                BeatTimes = new List<double>(_beatResult.BeatTimes),
                EnergyPeaks = _energyResult.Peaks.Select(p => new MyWPF.Layout.AI.EnergyPeakData
                {
                    Time = p.Time,
                    Energy = p.Energy,
                    Confidence = p.Confidence
                }).ToList(),
                EnergyLevels = _energyResult.EnergyLevels,
                SourceFile = _currentAudioFile,
                AnalysisTime = DateTime.Now,
                DetailedAnalysis = GenerateDetailedAudioAnalysis()
            };

            return audioData;
        }

        /// <summary>
        /// 生成详细音频分析
        /// </summary>
        private MyWPF.Layout.AI.DetailedAudioAnalysis GenerateDetailedAudioAnalysis()
        {
            var detailedAnalysis = new MyWPF.Layout.AI.DetailedAudioAnalysis();

            if (_beatResult?.BeatTimes != null && _energyResult?.EnergyLevels != null)
            {
                // 生成节拍段落
                detailedAnalysis.BeatSegments = GenerateBeatSegments();

                // 生成能量段落
                detailedAnalysis.EnergySegments = GenerateEnergySegments();

                // 分析音频结构
                detailedAnalysis.StructureAnalysis = AnalyzeAudioStructure();

                // 生成频率分析（简化版）
                detailedAnalysis.FrequencyAnalysis = GenerateFrequencyAnalysis();
            }

            return detailedAnalysis;
        }

        /// <summary>
        /// 生成节拍段落
        /// </summary>
        private List<MyWPF.Layout.AI.BeatSegment> GenerateBeatSegments()
        {
            var segments = new List<MyWPF.Layout.AI.BeatSegment>();
            var beatTimes = _beatResult.BeatTimes;

            if (beatTimes.Count < 4) return segments;

            // 每30秒为一个段落
            var segmentDuration = 30.0;
            var totalDuration = _waveformData.Duration.TotalSeconds;

            for (double startTime = 0; startTime < totalDuration; startTime += segmentDuration)
            {
                var endTime = Math.Min(startTime + segmentDuration, totalDuration);
                var beatsInSegment = beatTimes.Where(b => b >= startTime && b < endTime).ToList();

                if (beatsInSegment.Count >= 2)
                {
                    var intervals = new List<double>();
                    for (int i = 1; i < beatsInSegment.Count; i++)
                    {
                        intervals.Add(beatsInSegment[i] - beatsInSegment[i - 1]);
                    }

                    var avgInterval = intervals.Average();
                    var avgBPM = 60.0 / avgInterval;
                    var variance = intervals.Select(x => Math.Pow(x - avgInterval, 2)).Average();
                    var stability = Math.Max(0, 1.0 - Math.Sqrt(variance) / avgInterval);

                    segments.Add(new MyWPF.Layout.AI.BeatSegment
                    {
                        StartTime = startTime,
                        EndTime = endTime,
                        BeatsInSegment = beatsInSegment,
                        AverageBPM = avgBPM,
                        Confidence = stability,
                        Characteristics = GetBeatCharacteristics(stability, avgBPM)
                    });
                }
            }

            return segments;
        }

        /// <summary>
        /// 获取节拍特征描述
        /// </summary>
        private string GetBeatCharacteristics(double stability, double bpm)
        {
            var characteristics = new List<string>();

            if (stability > 0.8) characteristics.Add("稳定");
            else if (stability > 0.6) characteristics.Add("较稳定");
            else characteristics.Add("变化");

            if (bpm < 80) characteristics.Add("慢速");
            else if (bpm < 120) characteristics.Add("中速");
            else if (bpm < 160) characteristics.Add("快速");
            else characteristics.Add("极快");

            return string.Join(", ", characteristics);
        }

        /// <summary>
        /// 生成能量段落
        /// </summary>
        private List<MyWPF.Layout.AI.EnergySegment> GenerateEnergySegments()
        {
            var segments = new List<MyWPF.Layout.AI.EnergySegment>();
            var energyLevels = _energyResult.EnergyLevels;
            var energyPeaks = _energyResult.Peaks;

            if (energyLevels == null || energyLevels.Length == 0) return segments;

            // 每20秒为一个能量段落
            var segmentDuration = 20.0;
            var totalDuration = _waveformData.Duration.TotalSeconds;
            var windowSize = 0.1; // 100ms窗口

            for (double startTime = 0; startTime < totalDuration; startTime += segmentDuration)
            {
                var endTime = Math.Min(startTime + segmentDuration, totalDuration);
                var startIndex = (int)(startTime / windowSize);
                var endIndex = Math.Min((int)(endTime / windowSize), energyLevels.Length);

                if (endIndex > startIndex)
                {
                    var segmentEnergy = energyLevels.Skip(startIndex).Take(endIndex - startIndex).ToArray();
                    var avgEnergy = segmentEnergy.Average();
                    var peakEnergy = segmentEnergy.Max();
                    var peaksInSegment = energyPeaks.Where(p => p.Time >= startTime && p.Time < endTime).ToList();

                    segments.Add(new MyWPF.Layout.AI.EnergySegment
                    {
                        StartTime = startTime,
                        EndTime = endTime,
                        AverageEnergy = avgEnergy,
                        PeakEnergy = peakEnergy,
                        PeaksInSegment = peaksInSegment.Select(p => new MyWPF.Layout.AI.EnergyPeakData
                        {
                            Time = p.Time,
                            Energy = p.Energy,
                            Confidence = p.Confidence
                        }).ToList(),
                        EnergyLevel = GetEnergyLevel(avgEnergy)
                    });
                }
            }

            return segments;
        }

        /// <summary>
        /// 获取能量级别描述
        /// </summary>
        private string GetEnergyLevel(float avgEnergy)
        {
            if (avgEnergy < 0.1f) return "低";
            if (avgEnergy < 0.3f) return "中";
            if (avgEnergy < 0.6f) return "高";
            return "极高";
        }

        /// <summary>
        /// 分析音频结构
        /// </summary>
        private MyWPF.Layout.AI.AudioStructureAnalysis AnalyzeAudioStructure()
        {
            var structure = new MyWPF.Layout.AI.AudioStructureAnalysis();
            var energyLevels = _energyResult.EnergyLevels;

            if (energyLevels == null || energyLevels.Length == 0) return structure;

            var avgEnergy = energyLevels.Average();
            var sections = new List<MyWPF.Layout.AI.AudioSection>();

            // 简化的段落识别：基于能量变化
            var sectionDuration = 60.0; // 60秒段落
            var totalDuration = _waveformData.Duration.TotalSeconds;
            var sectionNames = new[] { "前奏", "主歌A", "副歌", "主歌B", "桥段", "尾奏" };

            for (int i = 0; i * sectionDuration < totalDuration; i++)
            {
                var startTime = i * sectionDuration;
                var endTime = Math.Min((i + 1) * sectionDuration, totalDuration);
                var sectionName = i < sectionNames.Length ? sectionNames[i] : $"段落{i + 1}";

                // 计算该段落的平均能量
                var startIndex = (int)(startTime / 0.1);
                var endIndex = Math.Min((int)(endTime / 0.1), energyLevels.Length);
                var sectionEnergy = energyLevels.Skip(startIndex).Take(endIndex - startIndex).Average();

                sections.Add(new MyWPF.Layout.AI.AudioSection
                {
                    Name = sectionName,
                    StartTime = startTime,
                    EndTime = endTime,
                    AverageEnergy = sectionEnergy,
                    AverageBPM = _beatResult.EstimatedBPM,
                    Characteristics = GetSectionCharacteristics(sectionEnergy, avgEnergy)
                });
            }

            structure.Sections = sections;
            return structure;
        }

        /// <summary>
        /// 获取段落特征
        /// </summary>
        private string GetSectionCharacteristics(float sectionEnergy, float avgEnergy)
        {
            if (sectionEnergy > avgEnergy * 1.5f) return "高能量，适合密集音符";
            if (sectionEnergy > avgEnergy * 1.2f) return "中高能量，适合正常密度";
            if (sectionEnergy > avgEnergy * 0.8f) return "中等能量，平衡布局";
            return "低能量，适合稀疏音符";
        }

        /// <summary>
        /// 生成频率分析（简化版）
        /// </summary>
        private List<MyWPF.Layout.AI.FrequencyBand> GenerateFrequencyAnalysis()
        {
            var frequencyAnalysis = new List<MyWPF.Layout.AI.FrequencyBand>();
            var samples = _waveformData.Samples;
            var sampleRate = _waveformData.SampleRate;

            // 每5秒分析一次频率特征
            var analysisInterval = 5.0;
            var samplesPerAnalysis = (int)(sampleRate * analysisInterval);

            for (int i = 0; i < samples.Length; i += samplesPerAnalysis)
            {
                var endIndex = Math.Min(i + samplesPerAnalysis, samples.Length);
                var segment = samples.Skip(i).Take(endIndex - i).ToArray();

                if (segment.Length > 0)
                {
                    var time = i / (double)sampleRate;

                    // 简化的频率分析
                    var lowFreq = CalculateFrequencyBandEnergy(segment, 0, 0.2f);
                    var midFreq = CalculateFrequencyBandEnergy(segment, 0.2f, 0.6f);
                    var highFreq = CalculateFrequencyBandEnergy(segment, 0.6f, 1.0f);
                    var spectralCentroid = CalculateSpectralCentroid(segment);

                    frequencyAnalysis.Add(new MyWPF.Layout.AI.FrequencyBand
                    {
                        Time = time,
                        LowFrequency = lowFreq,
                        MidFrequency = midFreq,
                        HighFrequency = highFreq,
                        SpectralCentroid = spectralCentroid
                    });
                }
            }

            return frequencyAnalysis;
        }

        /// <summary>
        /// 计算频率带能量（简化版）
        /// </summary>
        private float CalculateFrequencyBandEnergy(float[] segment, float startRatio, float endRatio)
        {
            // 简化实现：基于时域信号的统计特征
            var startIndex = (int)(segment.Length * startRatio);
            var endIndex = (int)(segment.Length * endRatio);

            float energy = 0;
            for (int i = startIndex; i < endIndex && i < segment.Length; i++)
            {
                energy += segment[i] * segment[i];
            }

            return energy / (endIndex - startIndex);
        }

        /// <summary>
        /// 计算频谱质心（简化版）
        /// </summary>
        private float CalculateSpectralCentroid(float[] segment)
        {
            // 简化实现：使用过零率作为频谱质心的近似
            int zeroCrossings = 0;
            for (int i = 1; i < segment.Length; i++)
            {
                if ((segment[i] >= 0) != (segment[i - 1] >= 0))
                {
                    zeroCrossings++;
                }
            }

            return zeroCrossings / (float)segment.Length;
        }

        /// <summary>
        /// 检查是否有可用的分析结果
        /// </summary>
        public bool HasAnalysisResults()
        {
            return _waveformData != null && _beatResult != null && _energyResult != null;
        }
    }
}