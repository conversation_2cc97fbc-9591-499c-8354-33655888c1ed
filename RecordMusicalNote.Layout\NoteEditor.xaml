<Window x:Class="MyWPF.Layout.NoteEditor"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:audio="clr-namespace:MyWPF.Layout.Audio"
        Title="音符编辑器" Height="800" Width="1400" WindowStartupLocation="CenterScreen">
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 工具栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
            <Button Name="btnLoad" Content="加载文件" Width="100" Height="35" Margin="5" Click="BtnLoad_Click"/>
            <Button Name="btnSave" Content="保存文件" Width="100" Height="35" Margin="5" Click="BtnSave_Click"/>
            <Separator Width="10"/>
            <!--<Button Name="btnAdd" Content="添加音符" Width="100" Height="35" Margin="5" Click="BtnAdd_Click"/>
            <Button Name="btnEdit" Content="编辑音符" Width="100" Height="35" Margin="5" Click="BtnEdit_Click"/>-->
            <Button Name="btnDelete" Content="删除音符" Width="100" Height="35" Margin="5" Click="BtnDelete_Click"/>
            <Button Name="btnCopy" Content="复制音符" Width="100" Height="35" Margin="5" Click="BtnCopy_Click"/>
            <Separator Width="10"/>
            <TextBox Name="txtSearch" Width="150" Height="25" Margin="5" ToolTip="搜索音符..."/>
            <Button Name="btnSearch" Content="搜索" Width="80" Height="35" Margin="5" Click="BtnSearch_Click"/>
            <Button Name="btnClearSearch" Content="清除" Width="80" Height="35" Margin="5" Click="BtnClearSearch_Click"/>
            <Separator Width="10"/>
            <CheckBox Name="chkAutoRefresh" Content="自动刷新" VerticalAlignment="Center" Margin="5" Checked="ChkAutoRefresh_Changed" Unchecked="ChkAutoRefresh_Changed"/>
            <Button Name="btnManualRefresh" Content="手动刷新" Width="100" Height="35" Margin="5" Click="BtnManualRefresh_Click"/>
            <Separator Width="10"/>
            <Button Name="btnAIAssist" Content="🤖 AI辅助" Width="100" Height="35" Margin="5" Click="BtnAIAssist_Click"
                    ToolTip="使用AI辅助生成音符"/>
            <Button Name="btnAIChat" Content="💬 AI对话" Width="100" Height="35" Margin="5" Click="BtnAIChat_Click"
                    ToolTip="与AI助手对话制谱"/>
            <Button Name="btnAIConfig" Content="⚙️ AI配置" Width="100" Height="35" Margin="5" Click="BtnAIConfig_Click"
                    ToolTip="配置AI服务设置"/>
        </StackPanel>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="3*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧：音符编辑区 -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- 音频分析工具栏 -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,5" Background="#F0F0F0">
                    <Button Name="btnAudioAnalysis" Content="🎵 音频分析" Width="120" Height="30" Margin="5" 
                            Click="BtnAudioAnalysis_Click"/>
                    <Button Name="btnImportSuggested" Content="📥 导入建议" Width="120" Height="30" Margin="5" 
                            Click="BtnImportSuggested_Click" IsEnabled="False"/>
                    <TextBlock Text="音符编辑" VerticalAlignment="Center" Margin="10,0" FontWeight="Bold"/>
                </StackPanel>
                
                <!-- 音符列表 -->
                <DataGrid Grid.Row="1" Name="dgNotes" AutoGenerateColumns="False" CanUserAddRows="False" 
                          SelectionMode="Single" GridLinesVisibility="All" HeadersVisibility="All"
                          AlternatingRowBackground="LightGray" RowBackground="White">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="ID" Binding="{Binding ID}" Width="40" IsReadOnly="True"/>
                        <DataGridTextColumn Header="小节" Binding="{Binding Bar}" Width="50"/>
                        <DataGridTextColumn Header="位置" Binding="{Binding Pos}" Width="50"/>
                        <DataGridTextColumn Header="起始轨道" Binding="{Binding FromTrack}" Width="70"/>
                        <DataGridTextColumn Header="目标轨道" Binding="{Binding TargetTrack}" Width="70"/>
                        <DataGridTextColumn Header="类型" Binding="{Binding NoteType}" Width="60"/>
                        <DataGridTextColumn Header="结束小节" Binding="{Binding EndBar}" Width="60"/>
                        <DataGridTextColumn Header="结束位置" Binding="{Binding EndPos}" Width="60"/>
                        <DataGridTextColumn Header="时间" Binding="{Binding TimeDisplay}" Width="80" IsReadOnly="True"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
            
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="LightGray"/>
            
            <!-- 右侧：音频分析面板 -->
            <TabControl Grid.Column="2" Margin="5,0,0,0">
                <TabItem Header="🎵 音频分析">
                    <audio:AudioAnalysisPanel x:Name="audioAnalysisPanel"/>
                </TabItem>
                <TabItem Header="📊 分析结果">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                            <Button Name="btnSyncWithAudio" Content="🔄 同步音频" Width="120" Height="30" Margin="5" 
                                    Click="BtnSyncWithAudio_Click" IsEnabled="False"/>
                            <Button Name="btnExportAnalysis" Content="📤 导出分析" Width="120" Height="30" Margin="5" 
                                    Click="BtnExportAnalysis_Click" IsEnabled="False"/>
                        </StackPanel>
                        
                        <ScrollViewer Grid.Row="1" Margin="5">
                            <StackPanel Name="analysisResultsPanel">
                                <TextBlock Text="音频分析结果将显示在这里..." 
                                          Margin="10" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </ScrollViewer>
                    </Grid>
                </TabItem>
            </TabControl>
        </Grid>
        
        <!-- 音符详情编辑区 -->
        <GroupBox Grid.Row="2" Header="音符详情" Margin="0,10,0,10" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 第一行 -->
                <Label Grid.Row="0" Grid.Column="0" Content="小节:" VerticalAlignment="Center"/>
                <TextBox Grid.Row="0" Grid.Column="1" Name="txtBar" Height="25" Margin="5"/>
                
                <Label Grid.Row="0" Grid.Column="2" Content="位置:" VerticalAlignment="Center"/>
                <TextBox Grid.Row="0" Grid.Column="3" Name="txtPos" Height="25" Margin="5"/>
                
                <Label Grid.Row="0" Grid.Column="4" Content="起始轨道:" VerticalAlignment="Center"/>
                <ComboBox Grid.Row="0" Grid.Column="5" Name="cmbFromTrack" Height="25" Margin="5">
                    <ComboBoxItem Content="Left1"/>
                    <ComboBoxItem Content="Left2"/>
                    <ComboBoxItem Content="Right1"/>
                    <ComboBoxItem Content="Right2"/>
                </ComboBox>
                
                <Label Grid.Row="0" Grid.Column="6" Content="目标轨道:" VerticalAlignment="Center"/>
                <ComboBox Grid.Row="0" Grid.Column="7" Name="cmbTargetTrack" Height="25" Margin="5">
                    <ComboBoxItem Content="Left1"/>
                    <ComboBoxItem Content="Left2"/>
                    <ComboBoxItem Content="Right1"/>
                    <ComboBoxItem Content="Right2"/>
                </ComboBox>
                
                <!-- 第二行 -->
                <Label Grid.Row="1" Grid.Column="0" Content="音符类型:" VerticalAlignment="Center"/>
                <ComboBox Grid.Row="1" Grid.Column="1" Name="cmbNoteType" Height="25" Margin="5">
                    <ComboBoxItem Content="short"/>
                    <ComboBoxItem Content="long"/>
                    <ComboBoxItem Content="slip"/>
                </ComboBox>
                
                <Label Grid.Row="1" Grid.Column="2" Content="结束小节:" VerticalAlignment="Center"/>
                <TextBox Grid.Row="1" Grid.Column="3" Name="txtEndBar" Height="25" Margin="5"/>
                
                <Label Grid.Row="1" Grid.Column="4" Content="结束位置:" VerticalAlignment="Center"/>
                <TextBox Grid.Row="1" Grid.Column="5" Name="txtEndPos" Height="25" Margin="5"/>
                
                <StackPanel Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="3" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                    <Button Name="btnSaveNote" Content="保存音符" Width="100" Height="30" Margin="5" Click="BtnSaveNote_Click"/>
                    <Button Name="btnCancelEdit" Content="取消" Width="80" Height="30" Margin="5" Click="BtnCancelEdit_Click"/>
                </StackPanel>
            </Grid>
        </GroupBox>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="3">
            <StatusBarItem>
                <TextBlock Name="txtStatus" Text="就绪"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Name="txtNoteCount" Text="音符数量: 0"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
