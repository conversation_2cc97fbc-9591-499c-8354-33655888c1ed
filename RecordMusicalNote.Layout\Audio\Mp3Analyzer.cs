using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using NAudio.Wave;
using System.Threading.Tasks;

namespace MyWPF.Layout.Audio
{
    /// <summary>
    /// MP3音频分析器
    /// </summary>
    public class Mp3Analyzer
    {
        /// <summary>
        /// 音频波形数据
        /// </summary>
        public class WaveformData
        {
            public float[] Samples { get; set; }
            public int SampleRate { get; set; }
            public int Channels { get; set; }
            public TimeSpan Duration { get; set; }
            public float[] PeakData { get; set; }
            public float[] RmsData { get; set; }
        }

        /// <summary>
        /// 节拍检测结果
        /// </summary>
        public class BeatDetectionResult
        {
            public List<double> BeatTimes { get; set; } = new List<double>();
            public double EstimatedBPM { get; set; }
            public double Confidence { get; set; }
        }

        /// <summary>
        /// 能量分析结果
        /// </summary>
        public class EnergyAnalysisResult
        {
            public float[] EnergyLevels { get; set; }
            public float[] SpectralCentroid { get; set; }
            public float[] ZeroCrossingRate { get; set; }
            public List<EnergyPeak> Peaks { get; set; } = new List<EnergyPeak>();
        }

        /// <summary>
        /// 能量峰值
        /// </summary>
        public class EnergyPeak
        {
            public double Time { get; set; }
            public float Energy { get; set; }
            public float Confidence { get; set; }
        }

        /// <summary>
        /// 分析音频文件（支持WAV、MP3等格式）
        /// </summary>
        /// <param name="filePath">音频文件路径</param>
        /// <returns>波形数据</returns>
        public async Task<WaveformData> AnalyzeWaveformAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    AudioFileReader reader = null;
                    
                    try
                    {
                        // 尝试使用AudioFileReader，它支持多种格式
                        reader = new AudioFileReader(filePath);
                    }
                    catch
                    {
                        // 如果失败，尝试WAV格式
                        if (Path.GetExtension(filePath).ToLower() == ".wav")
                        {
                            using (var wavReader = new WaveFileReader(filePath))
                            {
                                return AnalyzeWaveFile(wavReader);
                            }
                        }
                        else
                        {
                            throw new Exception("不支持的音频格式。请使用WAV文件，或将MP3转换为WAV格式。");
                        }
                    }

                    using (reader)
                    {
                        var waveData = new WaveformData
                        {
                            SampleRate = reader.WaveFormat.SampleRate,
                            Channels = reader.WaveFormat.Channels,
                            Duration = reader.TotalTime
                        };

                        // 读取所有样本数据
                        var samples = new List<float>();
                        var buffer = new float[reader.WaveFormat.SampleRate * reader.WaveFormat.Channels];
                        int samplesRead;

                        while ((samplesRead = reader.Read(buffer, 0, buffer.Length)) > 0)
                        {
                            for (int i = 0; i < samplesRead; i++)
                            {
                                samples.Add(buffer[i]);
                            }
                        }

                        waveData.Samples = samples.ToArray();

                        // 生成峰值数据（用于波形显示）
                        waveData.PeakData = GeneratePeakData(waveData.Samples, 1000);

                        // 计算RMS数据（用于能量分析）
                        waveData.RmsData = CalculateRMS(waveData.Samples, waveData.SampleRate);

                        return waveData;
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"分析音频文件失败: {ex.Message}", ex);
                }
            });
        }

        /// <summary>
        /// 分析WAV文件
        /// </summary>
        private WaveformData AnalyzeWaveFile(WaveFileReader reader)
        {
            var waveData = new WaveformData
            {
                SampleRate = reader.WaveFormat.SampleRate,
                Channels = reader.WaveFormat.Channels,
                Duration = reader.TotalTime
            };

            // 读取所有样本数据
            var samples = new List<float>();
            var buffer = new byte[reader.WaveFormat.AverageBytesPerSecond];
            int bytesRead;

            while ((bytesRead = reader.Read(buffer, 0, buffer.Length)) > 0)
            {
                // 转换字节数据为浮点数样本
                for (int i = 0; i < bytesRead; i += reader.WaveFormat.BlockAlign)
                {
                    if (reader.WaveFormat.BitsPerSample == 16)
                    {
                        if (i + 1 < bytesRead)
                        {
                            short sample = (short)(buffer[i] | (buffer[i + 1] << 8));
                            samples.Add(sample / 32768.0f);
                        }
                    }
                    else if (reader.WaveFormat.BitsPerSample == 8)
                    {
                        samples.Add((buffer[i] - 128) / 128.0f);
                    }
                }
            }

            waveData.Samples = samples.ToArray();
            waveData.PeakData = GeneratePeakData(waveData.Samples, 1000);
            waveData.RmsData = CalculateRMS(waveData.Samples, waveData.SampleRate);

            return waveData;
        }

        /// <summary>
        /// 检测节拍
        /// </summary>
        /// <param name="waveData">波形数据</param>
        /// <returns>节拍检测结果</returns>
        public async Task<BeatDetectionResult> DetectBeatsAsync(WaveformData waveData)
        {
            return await Task.Run(() =>
            {
                var result = new BeatDetectionResult();

                try
                {
                    // 简单的节拍检测算法
                    var energyData = CalculateEnergyWindows(waveData.Samples, waveData.SampleRate, 0.1); // 100ms窗口
                    var peaks = FindEnergyPeaks(energyData, 0.1); // 100ms间隔

                    // 转换为时间
                    result.BeatTimes = peaks.Select(p => p * 0.1).ToList();

                    // 估算BPM
                    if (result.BeatTimes.Count > 1)
                    {
                        var intervals = new List<double>();
                        for (int i = 1; i < result.BeatTimes.Count; i++)
                        {
                            intervals.Add(result.BeatTimes[i] - result.BeatTimes[i - 1]);
                        }

                        var avgInterval = intervals.Average();
                        result.EstimatedBPM = 60.0 / avgInterval;
                        result.Confidence = CalculateBPMConfidence(intervals);
                    }

                    return result;
                }
                catch (Exception ex)
                {
                    throw new Exception($"节拍检测失败: {ex.Message}", ex);
                }
            });
        }

        /// <summary>
        /// 分析音频能量
        /// </summary>
        /// <param name="waveData">波形数据</param>
        /// <returns>能量分析结果</returns>
        public async Task<EnergyAnalysisResult> AnalyzeEnergyAsync(WaveformData waveData)
        {
            return await Task.Run(() =>
            {
                var result = new EnergyAnalysisResult();

                try
                {
                    // 计算能量级别（每100ms一个窗口）
                    result.EnergyLevels = CalculateEnergyWindows(waveData.Samples, waveData.SampleRate, 0.1);

                    // 计算频谱质心（音色特征）
                    result.SpectralCentroid = CalculateSpectralCentroid(waveData.Samples, waveData.SampleRate);

                    // 计算过零率（音调特征）
                    result.ZeroCrossingRate = CalculateZeroCrossingRate(waveData.Samples, waveData.SampleRate);

                    // 找到能量峰值
                    var peakIndices = FindEnergyPeaks(result.EnergyLevels, 0.1);
                    foreach (var peakIndex in peakIndices)
                    {
                        result.Peaks.Add(new EnergyPeak
                        {
                            Time = peakIndex * 0.1,
                            Energy = result.EnergyLevels[peakIndex],
                            Confidence = CalculatePeakConfidence(result.EnergyLevels, peakIndex)
                        });
                    }

                    return result;
                }
                catch (Exception ex)
                {
                    throw new Exception($"能量分析失败: {ex.Message}", ex);
                }
            });
        }

        #region 私有辅助方法

        /// <summary>
        /// 生成峰值数据用于波形显示
        /// </summary>
        private float[] GeneratePeakData(float[] samples, int targetPoints)
        {
            if (samples.Length <= targetPoints)
                return samples;

            var peaks = new float[targetPoints];
            var samplesPerPoint = samples.Length / targetPoints;

            for (int i = 0; i < targetPoints; i++)
            {
                var start = i * samplesPerPoint;
                var end = Math.Min(start + samplesPerPoint, samples.Length);
                
                float max = 0;
                for (int j = start; j < end; j++)
                {
                    max = Math.Max(max, Math.Abs(samples[j]));
                }
                peaks[i] = max;
            }

            return peaks;
        }

        /// <summary>
        /// 计算RMS值
        /// </summary>
        private float[] CalculateRMS(float[] samples, int sampleRate, double windowSize = 0.1)
        {
            var windowSamples = (int)(sampleRate * windowSize);
            var rmsData = new List<float>();

            for (int i = 0; i < samples.Length; i += windowSamples)
            {
                var end = Math.Min(i + windowSamples, samples.Length);
                var sum = 0.0;
                var count = end - i;

                for (int j = i; j < end; j++)
                {
                    sum += samples[j] * samples[j];
                }

                rmsData.Add((float)Math.Sqrt(sum / count));
            }

            return rmsData.ToArray();
        }

        /// <summary>
        /// 计算能量窗口
        /// </summary>
        private float[] CalculateEnergyWindows(float[] samples, int sampleRate, double windowSize)
        {
            var windowSamples = (int)(sampleRate * windowSize);
            var energyData = new List<float>();

            for (int i = 0; i < samples.Length; i += windowSamples)
            {
                var end = Math.Min(i + windowSamples, samples.Length);
                var energy = 0.0f;

                for (int j = i; j < end; j++)
                {
                    energy += samples[j] * samples[j];
                }

                energyData.Add(energy / (end - i));
            }

            return energyData.ToArray();
        }

        /// <summary>
        /// 找到能量峰值
        /// </summary>
        private List<int> FindEnergyPeaks(float[] energyData, double minInterval)
        {
            var peaks = new List<int>();
            var minSamples = (int)(minInterval / 0.1); // 假设每个能量窗口是100ms

            for (int i = 1; i < energyData.Length - 1; i++)
            {
                if (energyData[i] > energyData[i - 1] && energyData[i] > energyData[i + 1])
                {
                    // 检查是否满足最小间隔
                    if (peaks.Count == 0 || i - peaks.Last() >= minSamples)
                    {
                        peaks.Add(i);
                    }
                }
            }

            return peaks;
        }

        /// <summary>
        /// 计算频谱质心
        /// </summary>
        private float[] CalculateSpectralCentroid(float[] samples, int sampleRate)
        {
            // 简化实现，实际应该使用FFT
            var windowSize = sampleRate / 10; // 100ms窗口
            var centroids = new List<float>();

            for (int i = 0; i < samples.Length; i += windowSize)
            {
                var end = Math.Min(i + windowSize, samples.Length);
                var centroid = 0.0f;
                var magnitude = 0.0f;

                for (int j = i; j < end; j++)
                {
                    var freq = (float)(j - i) / windowSize * sampleRate / 2;
                    var mag = Math.Abs(samples[j]);
                    centroid += freq * mag;
                    magnitude += mag;
                }

                centroids.Add(magnitude > 0 ? centroid / magnitude : 0);
            }

            return centroids.ToArray();
        }

        /// <summary>
        /// 计算过零率
        /// </summary>
        private float[] CalculateZeroCrossingRate(float[] samples, int sampleRate)
        {
            var windowSize = sampleRate / 10; // 100ms窗口
            var zcrData = new List<float>();

            for (int i = 0; i < samples.Length; i += windowSize)
            {
                var end = Math.Min(i + windowSize, samples.Length);
                var crossings = 0;

                for (int j = i + 1; j < end; j++)
                {
                    if ((samples[j] >= 0) != (samples[j - 1] >= 0))
                    {
                        crossings++;
                    }
                }

                zcrData.Add((float)crossings / (end - i));
            }

            return zcrData.ToArray();
        }

        /// <summary>
        /// 计算BPM置信度
        /// </summary>
        private double CalculateBPMConfidence(List<double> intervals)
        {
            if (intervals.Count < 2) return 0.0;

            var mean = intervals.Average();
            var variance = intervals.Select(x => Math.Pow(x - mean, 2)).Average();
            var stdDev = Math.Sqrt(variance);

            // 标准差越小，置信度越高
            return Math.Max(0, 1.0 - (stdDev / mean));
        }

        /// <summary>
        /// 计算峰值置信度
        /// </summary>
        private float CalculatePeakConfidence(float[] energyData, int peakIndex)
        {
            var windowSize = 5;
            var start = Math.Max(0, peakIndex - windowSize);
            var end = Math.Min(energyData.Length, peakIndex + windowSize + 1);

            var peakValue = energyData[peakIndex];
            var avgAround = 0.0f;
            var count = 0;

            for (int i = start; i < end; i++)
            {
                if (i != peakIndex)
                {
                    avgAround += energyData[i];
                    count++;
                }
            }

            if (count > 0)
            {
                avgAround /= count;
                return Math.Min(1.0f, (peakValue - avgAround) / peakValue);
            }

            return 0.0f;
        }

        #endregion
    }
}