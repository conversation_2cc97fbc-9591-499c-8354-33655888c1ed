﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace RecordMusicalNote
{
    /// <summary>
    /// ClassicalTrans.xaml 的交互逻辑
    /// </summary>
    public partial class ClassicalTrans : Window
    {
        public ClassicalTrans()
        {
            InitializeComponent();
            _rdDouble.Checked += _rdDouble_Checked;
            _rdSingle.Checked += _rdSingle_Checked;
            this.WindowStartupLocation = WindowStartupLocation.CenterScreen;
            _rdDouble.IsChecked = true;
        }

        private void _rdSingle_Checked(object sender, RoutedEventArgs e)
        {
            IsDouble = false;
        }

        private void _rdDouble_Checked(object sender, RoutedEventArgs e)
        {
            IsDouble = true;
        }

        public bool IsDouble { get; set; }

        private void btnConfirm_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
