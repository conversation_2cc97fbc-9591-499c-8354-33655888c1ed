"""
谱面数据结构

定义谱面的数据结构和基本操作
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Any
import json
import xml.etree.ElementTree as ET
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


@dataclass
class NoteInfo:
    """音符信息"""
    time: float  # 时间位置（秒）
    track: int   # 轨道编号
    note_type: int  # 音符类型：0=无音符, 1=短音符, 2=长音符
    duration: float = 0.0  # 音符持续时间（长音符用）
    velocity: int = 127    # 力度
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'time': self.time,
            'track': self.track,
            'note_type': self.note_type,
            'duration': self.duration,
            'velocity': self.velocity
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'NoteInfo':
        """从字典创建"""
        return cls(
            time=data['time'],
            track=data['track'],
            note_type=data['note_type'],
            duration=data.get('duration', 0.0),
            velocity=data.get('velocity', 127)
        )


@dataclass
class ChartMetadata:
    """谱面元数据"""
    title: str = "Untitled"
    artist: str = "Unknown"
    creator: str = "AI Assistant"
    difficulty: int = 5
    bpm: float = 120.0
    duration: float = 0.0
    track_count: int = 4
    total_notes: int = 0
    created_time: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'title': self.title,
            'artist': self.artist,
            'creator': self.creator,
            'difficulty': self.difficulty,
            'bpm': self.bpm,
            'duration': self.duration,
            'track_count': self.track_count,
            'total_notes': self.total_notes,
            'created_time': self.created_time
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ChartMetadata':
        """从字典创建"""
        return cls(**data)


class ChartData:
    """谱面数据类"""
    
    def __init__(self, metadata: Optional[ChartMetadata] = None):
        """
        初始化谱面数据
        
        Args:
            metadata: 谱面元数据
        """
        self.metadata = metadata or ChartMetadata()
        self.notes: List[NoteInfo] = []
        self._track_cache: Dict[int, List[NoteInfo]] = {}
        self._time_sorted = True
    
    def add_note(self, note: NoteInfo):
        """
        添加音符
        
        Args:
            note: 音符信息
        """
        self.notes.append(note)
        self._track_cache.clear()  # 清空缓存
        self._time_sorted = False
        self.metadata.total_notes = len([n for n in self.notes if n.note_type > 0])
    
    def add_notes(self, notes: List[NoteInfo]):
        """
        批量添加音符
        
        Args:
            notes: 音符列表
        """
        self.notes.extend(notes)
        self._track_cache.clear()
        self._time_sorted = False
        self.metadata.total_notes = len([n for n in self.notes if n.note_type > 0])
    
    def get_notes_by_track(self, track: int) -> List[NoteInfo]:
        """
        获取指定轨道的音符
        
        Args:
            track: 轨道编号
            
        Returns:
            List[NoteInfo]: 该轨道的音符列表
        """
        if track not in self._track_cache:
            self._track_cache[track] = [note for note in self.notes if note.track == track]
            # 按时间排序
            self._track_cache[track].sort(key=lambda x: x.time)
        
        return self._track_cache[track]
    
    def get_notes_in_time_range(self, start_time: float, end_time: float) -> List[NoteInfo]:
        """
        获取指定时间范围内的音符
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            List[NoteInfo]: 时间范围内的音符
        """
        self._ensure_time_sorted()
        return [note for note in self.notes 
                if start_time <= note.time <= end_time and note.note_type > 0]
    
    def get_all_notes(self, include_empty: bool = False) -> List[NoteInfo]:
        """
        获取所有音符
        
        Args:
            include_empty: 是否包含空音符（note_type=0）
            
        Returns:
            List[NoteInfo]: 音符列表
        """
        self._ensure_time_sorted()
        if include_empty:
            return self.notes.copy()
        else:
            return [note for note in self.notes if note.note_type > 0]
    
    def _ensure_time_sorted(self):
        """确保音符按时间排序"""
        if not self._time_sorted:
            self.notes.sort(key=lambda x: x.time)
            self._time_sorted = True
    
    def remove_notes_in_range(self, start_time: float, end_time: float, track: Optional[int] = None):
        """
        删除指定时间范围内的音符
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            track: 指定轨道，None表示所有轨道
        """
        if track is None:
            self.notes = [note for note in self.notes 
                         if not (start_time <= note.time <= end_time)]
        else:
            self.notes = [note for note in self.notes 
                         if not (start_time <= note.time <= end_time and note.track == track)]
        
        self._track_cache.clear()
        self.metadata.total_notes = len([n for n in self.notes if n.note_type > 0])
    
    def get_statistics(self) -> Dict:
        """
        获取谱面统计信息
        
        Returns:
            Dict: 统计信息
        """
        active_notes = [note for note in self.notes if note.note_type > 0]
        
        if not active_notes:
            return {
                'total_notes': 0,
                'track_distribution': {},
                'note_type_distribution': {},
                'time_range': (0, 0),
                'average_density': 0
            }
        
        # 轨道分布
        track_distribution = {}
        for note in active_notes:
            track_distribution[note.track] = track_distribution.get(note.track, 0) + 1
        
        # 音符类型分布
        note_type_distribution = {}
        for note in active_notes:
            note_type_distribution[note.note_type] = note_type_distribution.get(note.note_type, 0) + 1
        
        # 时间范围
        times = [note.time for note in active_notes]
        time_range = (min(times), max(times))
        
        # 平均密度（每秒音符数）
        duration = time_range[1] - time_range[0] if time_range[1] > time_range[0] else 1
        average_density = len(active_notes) / duration
        
        return {
            'total_notes': len(active_notes),
            'track_distribution': track_distribution,
            'note_type_distribution': note_type_distribution,
            'time_range': time_range,
            'average_density': average_density
        }
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'metadata': self.metadata.to_dict(),
            'notes': [note.to_dict() for note in self.notes]
        }
    
    def to_json(self, indent: int = 2) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), indent=indent, ensure_ascii=False)
    
    def save_json(self, filepath: str):
        """保存为JSON文件"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(self.to_json())
        logger.info(f"谱面已保存为JSON: {filepath}")
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ChartData':
        """从字典创建谱面数据"""
        metadata = ChartMetadata.from_dict(data.get('metadata', {}))
        chart = cls(metadata)
        
        notes_data = data.get('notes', [])
        notes = [NoteInfo.from_dict(note_data) for note_data in notes_data]
        chart.add_notes(notes)
        
        return chart
    
    @classmethod
    def from_json(cls, json_str: str) -> 'ChartData':
        """从JSON字符串创建谱面数据"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    @classmethod
    def load_json(cls, filepath: str) -> 'ChartData':
        """从JSON文件加载谱面数据"""
        with open(filepath, 'r', encoding='utf-8') as f:
            json_str = f.read()
        return cls.from_json(json_str)
    
    def to_sequence_format(self, time_resolution: float = 0.125) -> Dict[str, List[int]]:
        """
        转换为序列格式（用于AI模型）
        
        Args:
            time_resolution: 时间分辨率（秒）
            
        Returns:
            Dict: 各轨道的音符序列
        """
        if not self.notes:
            return {}
        
        # 计算序列长度
        max_time = max(note.time for note in self.notes)
        sequence_length = int(max_time / time_resolution) + 1
        
        # 初始化序列
        sequences = {}
        for track in range(self.metadata.track_count):
            sequences[f'track_{track}'] = [0] * sequence_length
        
        # 填充音符
        for note in self.notes:
            if note.note_type > 0:  # 只处理有效音符
                time_index = int(note.time / time_resolution)
                if time_index < sequence_length:
                    track_key = f'track_{note.track}'
                    if track_key in sequences:
                        sequences[track_key][time_index] = note.note_type
        
        return sequences
    
    @classmethod
    def from_sequence_format(
        cls,
        sequences: Dict[str, List[int]],
        time_resolution: float = 0.125,
        metadata: Optional[ChartMetadata] = None
    ) -> 'ChartData':
        """
        从序列格式创建谱面数据
        
        Args:
            sequences: 各轨道的音符序列
            time_resolution: 时间分辨率
            metadata: 元数据
            
        Returns:
            ChartData: 谱面数据
        """
        chart = cls(metadata)
        
        for track_name, sequence in sequences.items():
            if track_name.startswith('track_'):
                track_id = int(track_name.split('_')[1])
                
                for time_index, note_type in enumerate(sequence):
                    if note_type > 0:
                        time = time_index * time_resolution
                        note = NoteInfo(
                            time=time,
                            track=track_id,
                            note_type=note_type
                        )
                        chart.add_note(note)
        
        return chart
    
    def validate(self) -> List[str]:
        """
        验证谱面数据的有效性
        
        Returns:
            List[str]: 错误信息列表，空列表表示无错误
        """
        errors = []
        
        # 检查基本信息
        if self.metadata.track_count <= 0:
            errors.append("轨道数量必须大于0")
        
        if self.metadata.bpm <= 0:
            errors.append("BPM必须大于0")
        
        # 检查音符
        for i, note in enumerate(self.notes):
            if note.time < 0:
                errors.append(f"音符{i}: 时间不能为负数")
            
            if note.track < 0 or note.track >= self.metadata.track_count:
                errors.append(f"音符{i}: 轨道编号超出范围")
            
            if note.note_type < 0 or note.note_type > 2:
                errors.append(f"音符{i}: 音符类型无效")
            
            if note.note_type == 2 and note.duration <= 0:
                errors.append(f"音符{i}: 长音符持续时间必须大于0")
        
        return errors
