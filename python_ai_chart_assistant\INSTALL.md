# 🚀 安装指南

## 快速安装

### 方式1: 智能安装脚本（推荐）
```bash
# 自动检测Python版本并安装合适的依赖
python install_deps.py

# 包含开发工具
python install_deps.py --dev
```

### 方式2: 最小依赖安装
```bash
# 适用于Python 3.13或遇到兼容性问题时
pip install -r requirements-minimal.txt
```

### 方式3: 完整依赖安装
```bash
# 标准安装（可能在Python 3.13上遇到TensorFlow问题）
pip install -r requirements.txt
```

## 🐛 常见问题解决

### Python 3.13 TensorFlow兼容性问题

**问题**: `ERROR: No matching distribution found for tensorflow>=2.9.0`

**解决方案**:
1. 使用智能安装脚本：
   ```bash
   python install_deps.py
   ```

2. 或手动安装预发布版本：
   ```bash
   pip install tensorflow>=2.20.0rc0
   ```

3. 或跳过TensorFlow（项目仍可运行）：
   ```bash
   pip install -r requirements-minimal.txt
   ```

### 音频处理依赖问题

**问题**: `librosa` 或 `soundfile` 安装失败

**解决方案**:
1. Windows用户：
   ```bash
   # 安装Microsoft C++ Build Tools
   # 然后重新安装
   pip install librosa soundfile
   ```

2. 或使用conda：
   ```bash
   conda install -c conda-forge librosa soundfile
   ```

3. 跳过音频处理（仅使用MIDI功能）：
   ```bash
   # 项目核心功能不依赖音频处理
   pip install numpy pandas torch streamlit
   ```

### Streamlit相关问题

**问题**: Streamlit启动失败

**解决方案**:
1. 检查端口占用：
   ```bash
   python run_ui.py --port 8502
   ```

2. 重新安装Streamlit：
   ```bash
   pip uninstall streamlit
   pip install streamlit>=1.28.0
   ```

3. 清除缓存：
   ```bash
   streamlit cache clear
   ```

## 🔧 环境配置

### Python版本兼容性
- ✅ **Python 3.8-3.12**: 完全支持
- ⚠️ **Python 3.13**: 部分支持（TensorFlow可能需要预发布版本）
- ❌ **Python < 3.8**: 不支持

### 推荐环境
```bash
# 创建虚拟环境
python -m venv ai_chart_env

# 激活环境
# Windows:
ai_chart_env\Scripts\activate
# Linux/Mac:
source ai_chart_env/bin/activate

# 安装依赖
python install_deps.py
```

## 📦 依赖说明

### 核心依赖（必需）
- `numpy`, `pandas`, `scipy` - 数据处理
- `torch` - 深度学习框架
- `streamlit` - Web界面
- `pretty_midi`, `mido` - MIDI处理

### 可选依赖
- `tensorflow` - 额外的深度学习功能
- `librosa`, `soundfile` - 音频处理
- `music21` - 高级音乐分析
- `streamlit-option-menu` - UI增强

### 开发依赖
- `pytest` - 测试框架
- `black` - 代码格式化
- `flake8` - 代码检查

## 🚀 验证安装

### 1. 检查核心功能
```python
# 测试MIDI处理
import pretty_midi
import numpy as np
print("✅ 核心依赖正常")
```

### 2. 检查UI界面
```bash
# 启动UI测试
python run_ui.py
# 访问 http://localhost:8501
```

### 3. 检查AI功能
```python
# 测试PyTorch
import torch
print(f"PyTorch版本: {torch.__version__}")

# 测试TensorFlow（可选）
try:
    import tensorflow as tf
    print(f"TensorFlow版本: {tf.__version__}")
except ImportError:
    print("TensorFlow未安装（可选）")
```

## 💡 性能优化

### GPU支持
```bash
# NVIDIA GPU用户
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 验证GPU可用性
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"
```

### 内存优化
- 大文件处理时调整批处理大小
- 在系统设置中限制最大内存使用
- 使用SSD存储临时文件

## 🆘 获取帮助

如果遇到安装问题：

1. **查看详细错误信息**
2. **检查Python版本兼容性**
3. **尝试智能安装脚本**: `python install_deps.py`
4. **使用最小依赖**: `pip install -r requirements-minimal.txt`
5. **查看项目文档**: `ui/README.md`

## 📋 安装检查清单

- [ ] Python 3.8+ 已安装
- [ ] 虚拟环境已创建并激活
- [ ] 核心依赖安装成功
- [ ] Streamlit可以正常启动
- [ ] UI界面可以访问
- [ ] MIDI文件可以上传和处理
- [ ] 谱面生成功能正常

完成以上检查后，你就可以开始使用AI音游写谱助手了！
