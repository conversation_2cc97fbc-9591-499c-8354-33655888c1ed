"""
Malody格式转换器

支持Malody音游的.mc格式导入导出
"""

import json
import math
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

from .base_converter import BaseConverter, register_converter, ConversionError
from ..chart_generation.chart_data import ChartData, ChartMetadata, NoteInfo

logger = logging.getLogger(__name__)


@register_converter
class MalodyConverter(BaseConverter):
    """Malody格式转换器"""
    
    def __init__(self):
        super().__init__("malody")
        self.supported_extensions = ['.mc', '.json']
    
    def export(self, chart_data: ChartData, output_path: str, **kwargs) -> bool:
        """
        导出为Malody格式
        
        Args:
            chart_data: 谱面数据
            output_path: 输出文件路径
            **kwargs: 额外参数
                - key_count: 键位数量 (4, 5, 6, 7)
                - level: 难度等级
                
        Returns:
            bool: 是否成功
        """
        try:
            # 验证数据
            errors = self._validate_chart_data(chart_data)
            if errors:
                raise ConversionError(f"谱面数据验证失败: {'; '.join(errors)}", self.format_name)
            
            # 获取参数
            key_count = kwargs.get('key_count', chart_data.metadata.track_count)
            level = kwargs.get('level', chart_data.metadata.difficulty)
            
            # 构建Malody格式数据
            malody_data = self._build_malody_data(chart_data, key_count, level)
            
            # 保存文件
            output_path = Path(output_path)
            if output_path.suffix.lower() not in ['.mc', '.json']:
                output_path = output_path.with_suffix('.mc')
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(malody_data, f, indent=2, ensure_ascii=False)
            
            self._log_export_info(chart_data, str(output_path))
            return True
            
        except Exception as e:
            logger.error(f"导出Malody格式失败: {e}")
            return False
    
    def import_chart(self, file_path: str, **kwargs) -> Optional[ChartData]:
        """
        从Malody格式文件导入谱面
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
            
        Returns:
            Optional[ChartData]: 导入的谱面数据
        """
        try:
            if not self.validate_file_extension(file_path):
                raise ConversionError(f"不支持的文件扩展名", self.format_name, file_path)
            
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                malody_data = json.load(f)
            
            # 解析数据
            chart_data = self._parse_malody_data(malody_data)
            
            self._log_import_info(chart_data, file_path)
            return chart_data
            
        except Exception as e:
            logger.error(f"导入Malody格式失败: {e}")
            return None
    
    def _build_malody_data(self, chart_data: ChartData, key_count: int, level: int) -> Dict:
        """
        构建Malody格式数据
        
        Args:
            chart_data: 谱面数据
            key_count: 键位数量
            level: 难度等级
            
        Returns:
            Dict: Malody格式数据
        """
        # 基础元数据
        meta = {
            "creator": chart_data.metadata.creator,
            "background": "",
            "version": "1.0",
            "id": 0,
            "mode": 0,  # 0 = Key mode
            "time": int(chart_data.metadata.created_time.replace('-', '').replace(':', '').replace('T', '')[:14]),
            "song": {
                "title": chart_data.metadata.title,
                "artist": chart_data.metadata.artist,
                "id": 0
            },
            "mode_ext": {
                "column": key_count
            }
        }
        
        # 时间信息
        time_data = [
            {
                "beat": [0, 0, 1],
                "bpm": chart_data.metadata.bpm
            }
        ]
        
        # 转换音符
        note_data = self._convert_notes_to_malody(chart_data, key_count)
        
        # 额外信息
        extra = {
            "test": {
                "divide": 4,
                "speed": 100,
                "save": 0,
                "lock": 0,
                "edit_mode": 0
            }
        }
        
        return {
            "meta": meta,
            "time": time_data,
            "note": note_data,
            "extra": extra
        }
    
    def _convert_notes_to_malody(self, chart_data: ChartData, key_count: int) -> List[Dict]:
        """
        将音符转换为Malody格式
        
        Args:
            chart_data: 谱面数据
            key_count: 键位数量
            
        Returns:
            List[Dict]: Malody格式音符列表
        """
        malody_notes = []
        bpm = chart_data.metadata.bpm
        
        # 计算每拍的时间（秒）
        beat_duration = 60.0 / bpm
        
        for note in chart_data.get_all_notes():
            # 计算节拍位置
            beat_time = note.time / beat_duration
            beat_bar = int(beat_time // 4)  # 每4拍一小节
            beat_in_bar = beat_time % 4
            
            # 转换为Malody的beat格式 [bar, beat, subdivision]
            # 这里简化处理，使用16分音符精度
            subdivision = 16
            beat_position = [
                beat_bar,
                int(beat_in_bar * subdivision),
                subdivision
            ]
            
            # 映射轨道到键位
            column = min(note.track, key_count - 1)
            
            if note.note_type == 1:
                # 短音符
                malody_note = {
                    "beat": beat_position,
                    "column": column
                }
            elif note.note_type == 2:
                # 长音符
                end_beat_time = (note.time + note.duration) / beat_duration
                end_beat_bar = int(end_beat_time // 4)
                end_beat_in_bar = end_beat_time % 4
                
                end_beat_position = [
                    end_beat_bar,
                    int(end_beat_in_bar * subdivision),
                    subdivision
                ]
                
                malody_note = {
                    "beat": beat_position,
                    "endbeat": end_beat_position,
                    "column": column
                }
            else:
                continue  # 跳过无效音符
            
            malody_notes.append(malody_note)
        
        # 按时间排序
        malody_notes.sort(key=lambda x: (x["beat"][0], x["beat"][1]))
        
        return malody_notes
    
    def _parse_malody_data(self, malody_data: Dict) -> ChartData:
        """
        解析Malody格式数据
        
        Args:
            malody_data: Malody格式数据
            
        Returns:
            ChartData: 解析后的谱面数据
        """
        # 解析元数据
        meta = malody_data.get("meta", {})
        song = meta.get("song", {})
        mode_ext = meta.get("mode_ext", {})
        
        metadata = ChartMetadata(
            title=song.get("title", "Unknown"),
            artist=song.get("artist", "Unknown"),
            creator=meta.get("creator", "Unknown"),
            difficulty=5,  # Malody没有直接的难度字段，使用默认值
            track_count=mode_ext.get("column", 4)
        )
        
        # 解析BPM
        time_data = malody_data.get("time", [])
        if time_data:
            metadata.bpm = time_data[0].get("bpm", 120.0)
        
        chart_data = ChartData(metadata)
        
        # 解析音符
        notes_data = malody_data.get("note", [])
        notes = self._parse_malody_notes(notes_data, metadata.bpm)
        
        chart_data.add_notes(notes)
        
        # 更新时长
        if notes:
            max_time = max(note.time + note.duration for note in notes)
            chart_data.metadata.duration = max_time
        
        return chart_data
    
    def _parse_malody_notes(self, notes_data: List[Dict], bpm: float) -> List[NoteInfo]:
        """
        解析Malody格式音符
        
        Args:
            notes_data: Malody音符数据
            bpm: BPM值
            
        Returns:
            List[NoteInfo]: 解析后的音符列表
        """
        notes = []
        beat_duration = 60.0 / bpm
        
        for note_data in notes_data:
            try:
                # 解析开始时间
                beat = note_data.get("beat", [0, 0, 1])
                start_time = self._beat_to_time(beat, beat_duration)
                
                column = note_data.get("column", 0)
                
                # 判断音符类型
                if "endbeat" in note_data:
                    # 长音符
                    end_beat = note_data["endbeat"]
                    end_time = self._beat_to_time(end_beat, beat_duration)
                    duration = max(0.1, end_time - start_time)  # 最小持续时间0.1秒
                    
                    note = NoteInfo(
                        time=start_time,
                        track=column,
                        note_type=2,
                        duration=duration
                    )
                else:
                    # 短音符
                    note = NoteInfo(
                        time=start_time,
                        track=column,
                        note_type=1
                    )
                
                notes.append(note)
                
            except Exception as e:
                logger.warning(f"解析音符失败: {e}, 数据: {note_data}")
                continue
        
        return notes
    
    def _beat_to_time(self, beat: List[int], beat_duration: float) -> float:
        """
        将Malody的beat格式转换为时间（秒）
        
        Args:
            beat: [bar, beat, subdivision] 格式
            beat_duration: 每拍时长（秒）
            
        Returns:
            float: 时间（秒）
        """
        if len(beat) < 3:
            return 0.0
        
        bar, beat_in_bar, subdivision = beat
        
        # 计算总拍数
        total_beats = bar * 4 + (beat_in_bar / subdivision) * 4
        
        # 转换为时间
        return total_beats * beat_duration
    
    def get_format_info(self) -> Dict[str, Any]:
        """获取Malody格式信息"""
        info = super().get_format_info()
        info.update({
            'description': 'Malody音游格式 (.mc)',
            'features': [
                '支持4K/5K/6K/7K键位',
                '支持短音符和长音符',
                '支持BPM变化',
                '支持多难度'
            ],
            'limitations': [
                '不支持滑动音符',
                '不支持复杂的时间签名变化'
            ]
        })
        return info
