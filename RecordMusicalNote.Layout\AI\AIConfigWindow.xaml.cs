using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Newtonsoft.Json;
using System.Net.Http;

namespace MyWPF.Layout.AI
{
    /// <summary>
    /// AI配置窗口
    /// </summary>
    public partial class AIConfigWindow : Window
    {
        private AIConfig _config;
        private bool _isLoading = false;

        public AIConfigWindow()
        {
            InitializeComponent();
            InitializeUI();
            LoadConfiguration();
        }

        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            // 绑定滑块值变化事件
            sliderTemperature.ValueChanged += (s, e) => txtTemperatureValue.Text = e.NewValue.ToString("F1");
            sliderMaxTokens.ValueChanged += (s, e) => txtMaxTokensValue.Text = e.NewValue.ToString("F0");
            sliderTimeout.ValueChanged += (s, e) => txtTimeoutValue.Text = e.NewValue.ToString("F0");
            sliderDefaultDifficulty.ValueChanged += (s, e) => txtDifficultyValue.Text = e.NewValue.ToString("F0");
            sliderNoteDensity.ValueChanged += (s, e) => txtNoteDensityValue.Text = e.NewValue.ToString("F1");
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            _isLoading = true;
            
            try
            {
                _config = AIConfigManager.LoadConfig();
                
                // 设置AI服务商
                foreach (ComboBoxItem item in cmbAIProvider.Items)
                {
                    if (item.Tag.ToString() == _config.Provider)
                    {
                        cmbAIProvider.SelectedItem = item;
                        break;
                    }
                }
                
                // 设置API配置
                txtApiKey.Password = _config.ApiKey ?? "";
                txtBaseUrl.Text = _config.BaseUrl ?? "";
                txtModel.Text = _config.Model ?? "";
                sliderTemperature.Value = _config.Temperature;
                sliderMaxTokens.Value = _config.MaxTokens;
                sliderTimeout.Value = _config.TimeoutSeconds;
                
                // 设置默认参数
                sliderDefaultDifficulty.Value = _config.DefaultDifficulty;
                sliderNoteDensity.Value = _config.DefaultNoteDensity;
                
                // 设置默认风格
                foreach (ComboBoxItem item in cmbDefaultStyle.Items)
                {
                    if (item.Tag.ToString() == _config.DefaultStyle)
                    {
                        cmbDefaultStyle.SelectedItem = item;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// AI服务商选择变化
        /// </summary>
        private void CmbAIProvider_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isLoading || cmbAIProvider.SelectedItem == null) return;
            
            var selectedProvider = ((ComboBoxItem)cmbAIProvider.SelectedItem).Tag.ToString();
            var preset = AIConfigPresets.GetPreset(selectedProvider);
            
            if (preset != null)
            {
                txtBaseUrl.Text = preset.BaseUrl;
                txtModel.Text = preset.Model;
                txtProviderDescription.Text = preset.Description;
            }
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        private async void BtnTestConnection_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(txtApiKey.Password))
            {
                txtTestResult.Text = "❌ 请先输入API密钥";
                return;
            }

            btnTestConnection.IsEnabled = false;
            txtTestResult.Text = "🔄 正在测试连接...";

            try
            {
                var testConfig = CreateConfigFromUI();
                var success = await TestAIConnection(testConfig);
                
                if (success)
                {
                    txtTestResult.Text = "✅ 连接测试成功！AI服务可正常使用";
                }
                else
                {
                    txtTestResult.Text = "❌ 连接测试失败，请检查配置信息";
                }
            }
            catch (Exception ex)
            {
                txtTestResult.Text = $"❌ 测试失败: {ex.Message}";
            }
            finally
            {
                btnTestConnection.IsEnabled = true;
            }
        }

        /// <summary>
        /// 测试AI连接
        /// </summary>
        private async Task<bool> TestAIConnection(AIConfig config)
        {
            try
            {
                using (var httpClient = new HttpClient())
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(config.TimeoutSeconds);
                    httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {config.ApiKey}");

                    var testRequest = new
                    {
                        model = config.Model,
                        messages = new[]
                        {
                            new { role = "user", content = "Hello" }
                        },
                        max_tokens = 10
                    };

                    var json = JsonConvert.SerializeObject(testRequest);
                    var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                    var response = await httpClient.PostAsync(config.BaseUrl, content);
                    return response.IsSuccessStatusCode;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从UI创建配置对象
        /// </summary>
        private AIConfig CreateConfigFromUI()
        {
            return new AIConfig
            {
                Provider = ((ComboBoxItem)cmbAIProvider.SelectedItem)?.Tag.ToString() ?? "deepseek",
                ApiKey = txtApiKey.Password,
                BaseUrl = txtBaseUrl.Text,
                Model = txtModel.Text,
                Temperature = sliderTemperature.Value,
                MaxTokens = (int)sliderMaxTokens.Value,
                TimeoutSeconds = (int)sliderTimeout.Value,
                DefaultDifficulty = (int)sliderDefaultDifficulty.Value,
                DefaultNoteDensity = sliderNoteDensity.Value,
                DefaultStyle = ((ComboBoxItem)cmbDefaultStyle.SelectedItem)?.Tag.ToString() ?? "mixed"
            };
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证必填项
                if (string.IsNullOrEmpty(txtApiKey.Password))
                {
                    MessageBox.Show("请输入API密钥", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrEmpty(txtBaseUrl.Text))
                {
                    MessageBox.Show("请输入API基础URL", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrEmpty(txtModel.Text))
                {
                    MessageBox.Show("请输入模型名称", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 创建配置对象
                var config = CreateConfigFromUI();
                
                // 保存配置
                AIConfigManager.SaveConfig(config);
                
                MessageBox.Show("配置保存成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        private void BtnReset_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要重置为默认配置吗？这将清除所有当前设置。", 
                                       "确认重置", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _config = AIConfigManager.GetDefaultConfig();
                LoadConfiguration();
                txtTestResult.Text = "配置已重置为默认值";
            }
        }

        /// <summary>
        /// 取消
        /// </summary>
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
