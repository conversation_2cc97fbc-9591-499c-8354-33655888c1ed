using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using MidiSheetMusic;
using CommonModel;
using RecordMusicalNote.DataModel;

namespace MyWPF.Layout.ChartGenerator
{
    /// <summary>
    /// 音乐游戏谱面生成器
    /// 支持从MIDI文件自动生成游戏谱面
    /// </summary>
    public class MusicChartGenerator
    {
        #region 私有字段
        private MidiFile _midiFile;
        private RecordMusicalNote.DataModel.LevelInfo _levelInfo;
        private List<IdolNoteInfo> _idolNotes;
        private List<PinBallNoteInfo> _pinballNotes;
        private List<BubbleNoteInfo> _bubbleNotes;
        private bool _enableCurveNotes = true;
        #endregion

        #region 构造函数
        public MusicChartGenerator()
        {
            _idolNotes = new List<IdolNoteInfo>();
            _pinballNotes = new List<PinBallNoteInfo>();
            _bubbleNotes = new List<BubbleNoteInfo>();
        }
        #endregion

        #region 公共方法

        /// <summary>
        /// 从MIDI文件生成谱面
        /// </summary>
        /// <param name="midiFilePath">MIDI文件路径</param>
        /// <param name="gameMode">游戏模式 1=星动 2=弹珠 3=泡泡</param>
        /// <param name="difficulty">难度等级 1-10</param>
        /// <param name="trackCount">轨道数量</param>
        /// <param name="enableCurveNotes">是否启用拐弯音符</param>
        /// <returns>生成的关卡信息</returns>
        public RecordMusicalNote.DataModel.LevelInfo GenerateChart(string midiFilePath, int gameMode, int difficulty, int trackCount = 4, bool enableCurveNotes = true)
        {
            try
            {
                // 1. 加载MIDI文件
                _midiFile = new MidiFile(midiFilePath);

                // 2. 设置拐弯音符开关
                _enableCurveNotes = enableCurveNotes;

                // 3. 创建基础关卡信息
                _levelInfo = CreateBaseLevelInfo(midiFilePath, trackCount);
                
                // 3. 根据游戏模式生成对应的音符
                switch (gameMode)
                {
                    case 1: // 星动模式
                        GenerateIdolNotes(difficulty, trackCount);
                        break;
                    case 2: // 弹珠模式
                        GeneratePinballNotes(difficulty, trackCount);
                        break;
                    case 3: // 泡泡模式
                        GenerateBubbleNotes(difficulty, trackCount);
                        break;
                    default:
                        throw new ArgumentException("不支持的游戏模式");
                }

                return _levelInfo;
            }
            catch (Exception ex)
            {
                throw new Exception($"生成谱面失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取生成的星动模式音符
        /// </summary>
        public List<IdolNoteInfo> GetIdolNotes() => _idolNotes;

        /// <summary>
        /// 获取生成的弹珠模式音符
        /// </summary>
        public List<PinBallNoteInfo> GetPinballNotes() => _pinballNotes;

        /// <summary>
        /// 获取生成的泡泡模式音符
        /// </summary>
        public List<BubbleNoteInfo> GetBubbleNotes() => _bubbleNotes;

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建基础关卡信息
        /// </summary>
        private RecordMusicalNote.DataModel.LevelInfo CreateBaseLevelInfo(string midiFilePath, int trackCount)
        {
            var fileName = Path.GetFileNameWithoutExtension(midiFilePath);
            var timeSignature = _midiFile.Time;
            
            // 计算BPM (每分钟节拍数)
            double bpm = CalculateBPM();
            
            // 计算总时长和小节数
            int totalTime = CalculateTotalTime();
            int barAmount = CalculateBarAmount();

            return new RecordMusicalNote.DataModel.LevelInfo
            {
                SongName = fileName,
                Artist = "Unknown",
                BPM = bpm,
                BeatPerBar = timeSignature.Numerator,
                BeatLen = 64, // 64分音符精度，与Pos范围(0-63)对应
                TrackCount = trackCount,
                IsFourTrack = trackCount == 4,
                LevelTime = totalTime,
                BarAmount = barAmount,
                BeginBarLen = 4,
                EnterTimeAdjust = 0,
                NotePreShow = 1,
                LevelPreTime = 2000, // 2秒预备时间
                Star = 1 // 默认1星难度
            };
        }

        /// <summary>
        /// 计算BPM（从MIDI文件中获取真实BPM）
        /// </summary>
        private double CalculateBPM()
        {
            // 直接从MIDI文件获取BPM
            double bpm = _midiFile.BPM;

            // 限制BPM在合理范围内（60-300）
            if (bpm < 60) bpm = 120; // 如果BPM过低，使用默认值120
            if (bpm > 300) bpm = 300; // 如果BPM过高，限制为300

            return Math.Round(bpm, 1);
        }

        /// <summary>
        /// 计算总时长(毫秒)（基于真实BPM）
        /// </summary>
        private int CalculateTotalTime()
        {
            int maxEndTime = 0;
            for (int i = 1; i <= _midiFile.TotalTracks; i++)
            {
                var track = _midiFile.GetTrack(i);
                foreach (var note in track.Notes)
                {
                    if (note.EndTime > maxEndTime)
                        maxEndTime = note.EndTime;
                }
            }

            // 基于真实BPM计算时长
            double bpm = CalculateBPM();
            double beatsPerSecond = bpm / 60.0;
            double totalBeats = ConvertTicksToBeats(maxEndTime);
            double totalSeconds = totalBeats / beatsPerSecond;

            return (int)(totalSeconds * 1000); // 转换为毫秒
        }

        /// <summary>
        /// 计算小节数量（基于真实BPM）
        /// </summary>
        private int CalculateBarAmount()
        {
            int maxEndTime = 0;
            for (int i = 1; i <= _midiFile.TotalTracks; i++)
            {
                var track = _midiFile.GetTrack(i);
                foreach (var note in track.Notes)
                {
                    if (note.EndTime > maxEndTime)
                        maxEndTime = note.EndTime;
                }
            }

            // 基于音乐节拍计算小节数（假设4/4拍）
            double totalBeats = ConvertTicksToBeats(maxEndTime);
            int beatsPerBar = _midiFile.Time.Numerator; // 从时间签名获取每小节拍数
            int totalBars = (int)Math.Ceiling(totalBeats / beatsPerBar);

            // 确保至少有4个小节（游戏需要）
            return Math.Max(4, totalBars);
        }

        /// <summary>
        /// 生成星动模式音符
        /// </summary>
        private void GenerateIdolNotes(int difficulty, int trackCount)
        {
            _idolNotes.Clear();

            // 选择主旋律轨道(通常是音符最多的轨道)
            var mainTrack = SelectMainTrack();
            if (mainTrack == null) return;

            // 根据难度调整音符密度
            double densityFactor = GetDensityFactor(difficulty);

            // 生成音符
            var filteredNotes = FilterNotesByDensity(mainTrack.Notes, densityFactor);

            for (int i = 0; i < filteredNotes.Count; i++)
            {
                var midiNote = filteredNotes[i];
                var noteType = DetermineNoteType(midiNote);

                // 根据难度和音符位置增加音符类型多样性
                noteType = EnhanceNoteTypeVariety(noteType, i, filteredNotes.Count, difficulty);

                string fromTrack, targetTrack, endTrack;

                // 根据音符类型设置轨道路径
                if (noteType == "slip")
                {
                    // 滑动音符必须有不同的起始和结束轨道
                    (fromTrack, targetTrack, endTrack) = GenerateSlipNotePath(midiNote, trackCount, i, filteredNotes.Count);
                }
                else if (_enableCurveNotes && noteType == "slip" && ShouldCreateCurveNote(midiNote, difficulty))
                {
                    // 生成拐弯路径（复杂滑动）
                    var nextNote = i < filteredNotes.Count - 1 ? filteredNotes[i + 1] : null;
                    (fromTrack, targetTrack, endTrack) = AnalyzeMelodyDirection(midiNote, nextNote, trackCount);
                }
                else
                {
                    // 普通音符（short/long），不拐弯
                    string trackName = AssignTrackName(midiNote, trackCount);
                    fromTrack = targetTrack = endTrack = trackName;
                }

                int noteBar = GetBarFromTime(midiNote.StartTime);
                int notePos = GetPosFromTime(midiNote.StartTime);

                // 只在第5小节及以后生成音符（前4小节是准备时间）
                if (noteBar >= 5)
                {
                    // 检查是否与现有音符重叠（同一小节、同一位置、同一轨道）
                    bool hasOverlap = _idolNotes.Any(existing =>
                        existing.Bar == noteBar &&
                        existing.Pos == notePos &&
                        existing.FromTrack == fromTrack);

                    if (!hasOverlap)
                    {
                        var idolNote = new IdolNoteInfo
                        {
                            LevelInfoId = _levelInfo.LevelInfoId,
                            Bar = noteBar,
                            Pos = (double)notePos,
                            FromTrack = fromTrack,
                            TargetTrack = targetTrack,
                            EndTrack = endTrack,
                            NoteType = noteType
                        };

                        // 设置结束位置
                        idolNote.EndBar = GetBarFromTime(midiNote.EndTime);
                        idolNote.EndPos = GetExactPosFromTime(midiNote.EndTime);

                        // 验证音符类型的合理性
                        int startBar = idolNote.Bar ?? 1;
                        double startPos = idolNote.Pos ?? 0.0;
                        int endBar = idolNote.EndBar ?? 1;
                        double endPos = idolNote.EndPos ?? 0.0;

                        double actualDuration = CalculateNoteDuration(startBar, startPos, endBar, endPos);

                        // 更宽松的音符类型验证（允许更多长音符和滑动音符）
                        if (noteType == "long" && actualDuration < 2.0)
                        {
                            noteType = "short";
                            idolNote.NoteType = noteType;
                        }
                        // 如果滑动音符的实际持续时间太短，改为长音符或短音符
                        else if (noteType == "slip" && actualDuration < 8.0)
                        {
                            noteType = actualDuration >= 2.0 ? "long" : "short";
                            idolNote.NoteType = noteType;
                        }

                        _idolNotes.Add(idolNote);
                    }
                }
            }
        }

        /// <summary>
        /// 生成弹珠模式音符
        /// </summary>
        private void GeneratePinballNotes(int difficulty, int trackCount)
        {
            _pinballNotes.Clear();

            var mainTrack = SelectMainTrack();
            if (mainTrack == null) return;

            double densityFactor = GetDensityFactor(difficulty);
            var filteredNotes = FilterNotesByDensity(mainTrack.Notes, densityFactor);

            var tempNotes = new List<PinBallNoteInfo>();
            var slipNoteIndices = new List<int>(); // 记录滑动音符的索引

            // 第一步：生成所有音符
            for (int i = 0; i < filteredNotes.Count; i++)
            {
                var midiNote = filteredNotes[i];
                string noteType = DeterminePinballNoteType(midiNote);

                var pinballNote = new PinBallNoteInfo
                {
                    LevelInfoId = _levelInfo.LevelInfoId,
                    bar = GetBarFromTime(midiNote.StartTime),
                    pos = AssignPinballPosition(midiNote),
                    NoteType = noteType,
                    EndArea = AssignPinballEndArea(midiNote),
                    MoveTime = 3 // 默认移动时间
                };

                // 记录滑动音符的位置
                if (noteType == "2") // PinballSlip
                {
                    slipNoteIndices.Add(tempNotes.Count);
                }

                tempNotes.Add(pinballNote);
            }

            // 第二步：为滑动音符设置SonId
            AssignSonIdToSlipNotes(tempNotes, slipNoteIndices);

            // 第三步：添加到最终列表
            _pinballNotes.AddRange(tempNotes);
        }

        /// <summary>
        /// 生成泡泡模式音符
        /// </summary>
        private void GenerateBubbleNotes(int difficulty, int trackCount)
        {
            _bubbleNotes.Clear();

            var mainTrack = SelectMainTrack();
            if (mainTrack == null) return;

            double densityFactor = GetDensityFactor(difficulty);
            var filteredNotes = FilterNotesByDensity(mainTrack.Notes, densityFactor);

            foreach (var midiNote in filteredNotes)
            {
                var bubbleNote = new BubbleNoteInfo
                {
                    LevelInfoId = _levelInfo.LevelInfoId,
                    Bar = GetBarFromTime(midiNote.StartTime),
                    BeatPos = (int)GetPosFromTime(midiNote.StartTime),
                    Type = DetermineBubbleNoteTypeInt(midiNote)
                };

                _bubbleNotes.Add(bubbleNote);
            }
        }

        /// <summary>
        /// 选择主旋律轨道
        /// </summary>
        private MidiSheetMusic.MidiTrack SelectMainTrack()
        {
            if (_midiFile.TotalTracks == 0)
                return null;

            // 选择音符数量最多的轨道作为主旋律
            MidiSheetMusic.MidiTrack bestTrack = null;
            int maxNotes = 0;

            for (int i = 1; i <= _midiFile.TotalTracks; i++)
            {
                var track = _midiFile.GetTrack(i);
                if (track.Notes.Count > maxNotes)
                {
                    maxNotes = track.Notes.Count;
                    bestTrack = track;
                }
            }

            return bestTrack;
        }

        /// <summary>
        /// 根据难度获取音符密度系数
        /// </summary>
        private double GetDensityFactor(int difficulty)
        {
            // 难度1-10对应密度0.1-1.0
            return Math.Min(1.0, Math.Max(0.1, difficulty * 0.1));
        }

        /// <summary>
        /// 根据密度过滤音符（改进版，避免过密和重叠）
        /// </summary>
        private List<MidiNote> FilterNotesByDensity(List<MidiNote> notes, double densityFactor)
        {
            if (notes.Count == 0) return notes;

            var filteredNotes = new List<MidiNote>();

            // 按时间排序
            var sortedNotes = notes.OrderBy(n => n.StartTime).ToList();

            // 计算最小时间间隔（基于难度）
            int minInterval = (int)(_midiFile.Time.QuarterNote / (4 * densityFactor)); // 最小间隔

            int lastTime = -minInterval; // 初始化为允许第一个音符通过

            foreach (var note in sortedNotes)
            {
                // 检查时间间隔
                if (note.StartTime - lastTime >= minInterval)
                {
                    filteredNotes.Add(note);
                    lastTime = note.StartTime;
                }
            }

            return filteredNotes;
        }

        /// <summary>
        /// 增强音符类型多样性（强制增加长音符和滑动音符比例）
        /// </summary>
        private string EnhanceNoteTypeVariety(string originalType, int noteIndex, int totalNotes, int difficulty)
        {
            var random = new Random(noteIndex); // 使用索引作为种子确保一致性

            // 更激进的音符类型分配策略
            double longNoteChance = Math.Min(0.4, difficulty * 0.04 + 0.2); // 基础20% + 难度加成，最多40%
            double slipNoteChance = Math.Min(0.3, difficulty * 0.03 + 0.15); // 基础15% + 难度加成，最多30%

            // 在谱面的不同位置使用不同策略
            double progress = (double)noteIndex / totalNotes;

            if (progress < 0.2) // 开头部分，适量长音符
            {
                longNoteChance *= 0.8;
                slipNoteChance *= 0.5;
            }
            else if (progress > 0.8) // 结尾部分，大幅增加复杂度
            {
                longNoteChance *= 1.8;
                slipNoteChance *= 2.0;
            }
            else // 中间部分，正常比例
            {
                longNoteChance *= 1.2;
                slipNoteChance *= 1.3;
            }

            double rand = random.NextDouble();

            // 强制类型转换策略
            if (originalType == "short")
            {
                // 每隔一定间隔强制生成长音符或滑动音符
                if (noteIndex % 8 == 3) // 每8个音符中第4个强制为长音符
                    return "long";
                else if (noteIndex % 12 == 7) // 每12个音符中第8个强制为滑动音符
                    return "slip";
                else if (rand < slipNoteChance)
                    return "slip";
                else if (rand < slipNoteChance + longNoteChance)
                    return "long";
                else
                    return "short";
            }
            else if (originalType == "long")
            {
                if (rand < slipNoteChance * 0.6) // 长音符有较高概率变成滑动
                    return "slip";
                else
                    return "long";
            }
            else // slip
            {
                return "slip"; // 滑动音符保持不变
            }
        }

        /// <summary>
        /// 生成滑动音符的轨道路径
        /// </summary>
        private (string fromTrack, string targetTrack, string endTrack) GenerateSlipNotePath(MidiNote note, int trackCount, int noteIndex, int totalNotes)
        {
            var random = new Random(note.StartTime + noteIndex); // 使用时间和索引作为种子

            string[] tracks;
            if (trackCount == 4)
            {
                tracks = new[] { "Left2", "Left1", "Right1", "Right2" };
            }
            else
            {
                tracks = new[] { "Left2", "Left1", "Center", "Right1", "Right2" };
            }

            // 选择起始轨道
            string startTrack = AssignTrackName(note, trackCount);
            int startIndex = Array.IndexOf(tracks, startTrack);

            // 选择结束轨道（确保与起始轨道不同）
            int endIndex;
            do {
                endIndex = random.Next(tracks.Length);
            } while (endIndex == startIndex);

            string endTrack = tracks[endIndex];

            // 对于简单滑动，target_track 就是起始轨道，end_track 是结束轨道
            return (startTrack, startTrack, endTrack);
        }

        /// <summary>
        /// 从MIDI时间获取小节号（基于真实BPM计算）
        /// </summary>
        private int GetBarFromTime(int midiTime)
        {
            // 将MIDI tick转换为音乐节拍
            double beats = ConvertTicksToBeats(midiTime);

            // 获取每小节拍数，如果为空则使用默认值4
            int beatPerBar = _levelInfo.BeatPerBar ?? 4;

            // 计算小节号（从第1小节开始）
            int bar = (int)(beats / beatPerBar) + 1;

            // 确保小节号至少为1
            return Math.Max(1, bar);
        }

        /// <summary>
        /// 从MIDI时间获取小节内位置（基于真实BPM计算，返回0-63）
        /// </summary>
        private int GetPosFromTime(int midiTime)
        {
            // 将MIDI tick转换为音乐节拍
            double beats = ConvertTicksToBeats(midiTime);

            // 获取每小节拍数，如果为空则使用默认值4
            int beatPerBar = _levelInfo.BeatPerBar ?? 4;

            // 计算在当前小节内的位置
            double beatInBar = beats % beatPerBar;

            // 转换为64分音符精度（0-63）
            double pos = (beatInBar / beatPerBar) * 64.0;

            // 使用向下取整而不是四舍五入，保持更高精度
            int result = (int)Math.Floor(pos);
            return Math.Max(0, Math.Min(63, result));
        }

        /// <summary>
        /// 获取精确的位置（保留小数，用于长音符结束位置）
        /// </summary>
        private double GetExactPosFromTime(int midiTime)
        {
            // 将MIDI tick转换为音乐节拍
            double beats = ConvertTicksToBeats(midiTime);

            // 获取每小节拍数，如果为空则使用默认值4
            int beatPerBar = _levelInfo.BeatPerBar ?? 4;

            // 计算在当前小节内的位置
            double beatInBar = beats % beatPerBar;

            // 转换为64分音符精度（0-63），保留小数
            double pos = (beatInBar / beatPerBar) * 64.0;

            // 四舍五入到2位小数，避免浮点数精度问题
            pos = Math.Round(pos, 2);

            // 确保在有效范围内
            return Math.Max(0.0, Math.Min(63.0, pos));
        }

        /// <summary>
        /// 计算音符的实际持续时间（以位置单位计算）
        /// </summary>
        private double CalculateNoteDuration(int startBar, double startPos, int endBar, double endPos)
        {
            if (endBar == startBar)
            {
                return endPos - startPos;
            }
            else
            {
                return (64 - startPos) + (endBar - startBar - 1) * 64 + endPos;
            }
        }

        /// <summary>
        /// 将MIDI tick转换为音乐节拍数
        /// </summary>
        private double ConvertTicksToBeats(int midiTicks)
        {
            // MIDI中的四分音符tick数
            int quarterNoteTicks = _midiFile.Time.QuarterNote;

            // 转换为四分音符数量（即节拍数，假设四分音符为一拍）
            return (double)midiTicks / quarterNoteTicks;
        }

        /// <summary>
        /// 获取时间转换的调试信息
        /// </summary>
        private string GetTimingDebugInfo(int midiTime)
        {
            double beats = ConvertTicksToBeats(midiTime);
            int bar = GetBarFromTime(midiTime);
            int pos = GetPosFromTime(midiTime);
            double bpm = CalculateBPM();

            return $"MIDI:{midiTime} -> Beats:{beats:F2} -> Bar:{bar}, Pos:{pos} (BPM:{bpm})";
        }

        /// <summary>
        /// 分配轨道（返回标准轨道名称）
        /// </summary>
        private string AssignTrackName(MidiNote note, int trackCount)
        {
            // 根据音高分配轨道
            int noteNumber = note.Number % 12; // 获取音名
            int trackIndex = (noteNumber % trackCount);

            // 返回标准轨道名称
            if (trackCount == 4)
            {
                switch (trackIndex)
                {
                    case 0: return "Left2";
                    case 1: return "Left1";
                    case 2: return "Right1";
                    case 3: return "Right2";
                    default: return "Left2";
                }
            }
            else if (trackCount == 5)
            {
                switch (trackIndex)
                {
                    case 0: return "Left2";
                    case 1: return "Left1";
                    case 2: return "Center";
                    case 3: return "Right1";
                    case 4: return "Right2";
                    default: return "Left2";
                }
            }

            return "Left2"; // 默认返回
        }

        /// <summary>
        /// 分配轨道（返回数字索引，兼容旧代码）
        /// </summary>
        private int AssignTrack(MidiNote note, int trackCount)
        {
            // 根据音高分配轨道
            int noteNumber = note.Number % 12; // 获取音名
            return (noteNumber % trackCount) + 1;
        }

        /// <summary>
        /// 确定星动模式音符类型（基于游戏位置差异）
        /// </summary>
        private string DetermineNoteType(MidiNote note)
        {
            // 计算MIDI时长
            int midiDuration = note.EndTime - note.StartTime;
            int quarterNote = _midiFile.Time.QuarterNote;

            // 计算游戏中的实际位置差异
            int startBar = GetBarFromTime(note.StartTime);
            int endBar = GetBarFromTime(note.EndTime);
            double startPos = GetExactPosFromTime(note.StartTime);
            double endPos = GetExactPosFromTime(note.EndTime);

            // 计算总的位置差异（考虑跨小节）
            double totalPosDiff;
            if (endBar == startBar)
            {
                totalPosDiff = endPos - startPos;
            }
            else
            {
                totalPosDiff = (64 - startPos) + (endBar - startBar - 1) * 64 + endPos;
            }

            // 调试信息
            System.Diagnostics.Debug.WriteLine($"音符分析: MIDI时长={midiDuration}, 四分音符={quarterNote}, 位置差异={totalPosDiff:F2}, StartBar={startBar}, StartPos={startPos:F2}, EndBar={endBar}, EndPos={endPos:F2}");

            // 更宽松的音符类型判断标准
            if (totalPosDiff <= 1.0)
            {
                return "short"; // 短音符：1个位置单位以下
            }
            else if (totalPosDiff >= 16.0)
            {
                return "slip"; // 滑动音符：16个位置单位以上（1/4小节以上）
            }
            else if (totalPosDiff >= 4.0)
            {
                return "long"; // 长音符：4-16个位置单位之间
            }
            else
            {
                return "short"; // 其他情况默认为短音符
            }
        }

        /// <summary>
        /// 确定弹珠模式音符类型
        /// 按照星动转弹珠的映射关系：点→1, 长条→4, 箭头→2
        /// </summary>
        private string DeterminePinballNoteType(MidiNote note)
        {
            // 先获取对应的星动音符类型
            string idolNoteType = DetermineNoteType(note);

            // 按照星动转弹珠的映射关系
            switch (idolNoteType)
            {
                case "点":
                    return "1"; // PinballSingle (单点弹珠)
                case "长条":
                    return "4"; // PinballLong (长按弹珠)
                case "箭头":
                    return "2"; // PinballSlip (滑动弹珠)
                default:
                    return "1"; // 默认单点弹珠
            }
        }

        /// <summary>
        /// 分配弹珠模式位置
        /// 弹珠模式规则：轨道0独立，轨道1,2只能选一个，轨道3,4只能选一个
        /// </summary>
        private int AssignPinballPosition(MidiNote note)
        {
            var random = new Random(note.StartTime); // 使用音符时间作为种子
            int noteClass = note.Number % 12; // 获取音名类别

            // 根据音高分配到三个区域
            int region = noteClass % 3;

            switch (region)
            {
                case 0:
                    return 0; // 轨道0
                case 1:
                    return random.Next(0, 2) == 0 ? 1 : 2; // 轨道1或2
                case 2:
                    return random.Next(0, 2) == 0 ? 3 : 4; // 轨道3或4
                default:
                    return 0;
            }
        }

        /// <summary>
        /// 确定泡泡模式音符类型
        /// </summary>
        private string DetermineBubbleNoteType(MidiNote note)
        {
            // 根据音高确定泡泡颜色/类型
            int noteNumber = note.Number % 7; // 7种基本音名
            return (noteNumber + 1).ToString();
        }

        /// <summary>
        /// 确定泡泡模式音符类型(整数版本)
        /// </summary>
        private int DetermineBubbleNoteTypeInt(MidiNote note)
        {
            // 根据音高确定泡泡颜色/类型
            int noteNumber = note.Number % 7; // 7种基本音名
            return noteNumber + 1;
        }

        /// <summary>
        /// 判断是否应该创建拐弯音符
        /// </summary>
        private bool ShouldCreateCurveNote(MidiNote note, int difficulty)
        {
            int duration = note.EndTime - note.StartTime;

            // 拐弯条件：
            // 1. 音符足够长（至少1个四分音符）
            // 2. 难度足够高（6级以上）
            // 3. 随机概率（避免所有长音符都拐弯）
            var random = new Random(note.StartTime); // 使用音符时间作为种子，确保一致性
            double curveChance = (difficulty - 5) * 0.1; // 难度6=10%, 7=20%, ..., 10=50%

            return duration >= _midiFile.Time.QuarterNote &&
                   difficulty >= 6 &&
                   random.NextDouble() < curveChance;
        }

        /// <summary>
        /// 基于音乐特征分析拐弯方向（返回标准轨道名称）
        /// </summary>
        private (string fromTrack, string targetTrack, string endTrack) AnalyzeMelodyDirection(MidiNote currentNote, MidiNote nextNote, int trackCount)
        {
            string startTrackName = AssignTrackName(currentNote, trackCount);

            if (nextNote != null)
            {
                string nextTrackName = AssignTrackName(nextNote, trackCount);
                int pitchDiff = nextNote.Number - currentNote.Number;

                // 根据音高变化决定拐弯方向
                if (Math.Abs(pitchDiff) >= 3) // 音高变化足够大才拐弯
                {
                    string middleTrackName = GetMiddleTrackName(startTrackName, nextTrackName, pitchDiff > 0, trackCount);
                    return (startTrackName, middleTrackName, nextTrackName);
                }
            }

            // 如果没有下一个音符或音高变化不大，生成随机拐弯
            return GenerateRandomCurvePath(currentNote, trackCount);
        }

        /// <summary>
        /// 获取中间轨道名称
        /// </summary>
        private string GetMiddleTrackName(string startTrack, string endTrack, bool isRising, int trackCount)
        {
            if (trackCount == 4)
            {
                var tracks = new[] { "Left2", "Left1", "Right1", "Right2" };
                int startIndex = Array.IndexOf(tracks, startTrack);
                int endIndex = Array.IndexOf(tracks, endTrack);

                if (startIndex >= 0 && endIndex >= 0)
                {
                    // 选择起始和结束轨道之间的中间轨道
                    int middleIndex = (startIndex + endIndex) / 2;
                    if (middleIndex == startIndex || middleIndex == endIndex)
                    {
                        // 如果计算的中间轨道与起始或结束轨道相同，选择相邻轨道
                        middleIndex = isRising ? Math.Min(startIndex + 1, 3) : Math.Max(startIndex - 1, 0);
                    }
                    return tracks[middleIndex];
                }
            }

            // 默认返回中间轨道
            return trackCount == 4 ? "Left1" : "Center";
        }

        /// <summary>
        /// 生成随机拐弯路径（返回标准轨道名称）
        /// </summary>
        private (string fromTrack, string targetTrack, string endTrack) GenerateRandomCurvePath(MidiNote note, int trackCount)
        {
            var random = new Random(note.StartTime); // 使用音符时间作为种子

            string[] tracks;
            if (trackCount == 4)
            {
                tracks = new[] { "Left2", "Left1", "Right1", "Right2" };
            }
            else
            {
                tracks = new[] { "Left2", "Left1", "Center", "Right1", "Right2" };
            }

            string startTrack = AssignTrackName(note, trackCount);
            string middleTrack, endTrack;

            // 生成中间轨道（确保与起始轨道不同）
            do {
                middleTrack = tracks[random.Next(tracks.Length)];
            } while (middleTrack == startTrack);

            // 生成结束轨道（确保与中间轨道不同）
            do {
                endTrack = tracks[random.Next(tracks.Length)];
            } while (endTrack == middleTrack);

            return (startTrack, middleTrack, endTrack);
        }

        /// <summary>
        /// 分配弹珠模式的EndArea
        /// </summary>
        private string AssignPinballEndArea(MidiNote note)
        {
            int position = AssignPinballPosition(note);

            // 根据位置分配EndArea
            switch (position)
            {
                case 0:
                    return ""; // 轨道0独立
                case 1:
                case 2:
                    return "1|2"; // 轨道1,2区域
                case 3:
                case 4:
                    return "3|4"; // 轨道3,4区域
                default:
                    return "";
            }
        }

        /// <summary>
        /// 为滑动音符分配SonId
        /// </summary>
        private void AssignSonIdToSlipNotes(List<PinBallNoteInfo> notes, List<int> slipNoteIndices)
        {
            foreach (int slipIndex in slipNoteIndices)
            {
                var slipNote = notes[slipIndex];
                int currentBar = slipNote.bar ?? 0;
                int targetBar = currentBar + 2; // 在2小节后寻找目标音符

                // 寻找后续的单点弹珠作为目标
                for (int i = slipIndex + 1; i < notes.Count; i++)
                {
                    var targetNote = notes[i];

                    if (targetNote.NoteType == "1" && // PinballSingle
                        (targetNote.bar ?? 0) >= targetBar)
                    {
                        // 设置SonId为目标音符的索引+1（模拟ID）
                        slipNote.son = i + 1;
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 导出星动模式谱面为XML格式
        /// </summary>
        /// <param name="filePath">输出文件路径</param>
        /// <param name="songName">歌曲名称</param>
        /// <param name="artist">艺术家</param>
        public void ExportIdolChartToXml(string filePath, string songName = null, string artist = null)
        {
            if (_levelInfo == null || _idolNotes == null || _idolNotes.Count == 0)
            {
                throw new InvalidOperationException("请先生成谱面数据");
            }

            using (var writer = new System.Xml.XmlTextWriter(filePath, System.Text.Encoding.UTF8))
            {
                writer.Formatting = System.Xml.Formatting.Indented;
                writer.Indentation = 2;

                writer.WriteStartDocument();
                writer.WriteStartElement("Level");

                // 写入关卡信息
                writer.WriteStartElement("LevelInfo");
                writer.WriteElementString("BPM", _levelInfo.BPM.ToString());
                writer.WriteElementString("BeatPerBar", _levelInfo.BeatPerBar.ToString());
                writer.WriteElementString("BeatLen", _levelInfo.BeatLen.ToString());
                writer.WriteElementString("EnterTimeAdjust", _levelInfo.EnterTimeAdjust.ToString());
                writer.WriteElementString("LevelTime", _levelInfo.LevelTime.ToString());
                writer.WriteElementString("BarAmount", (_levelInfo.BarAmount ?? 20).ToString());
                writer.WriteElementString("BeginBarLen", _levelInfo.BeginBarLen.ToString());
                writer.WriteElementString("IsFourTrack", _levelInfo.IsFourTrack.ToString());
                writer.WriteElementString("TrackCount", _levelInfo.TrackCount.ToString());
                writer.WriteElementString("Star", _levelInfo.Star.ToString());
                writer.WriteElementString("type", "Idol");
                writer.WriteEndElement(); // LevelInfo

                // 写入音乐信息
                writer.WriteStartElement("MusicInfo");
                writer.WriteElementString("Author", "MIDI Generator");
                writer.WriteElementString("Title", songName ?? _levelInfo.SongName ?? "Unknown");
                writer.WriteElementString("Artist", artist ?? _levelInfo.Artist ?? "Unknown");
                writer.WriteElementString("FilePath", $"audio/bgm/{Path.GetFileNameWithoutExtension(songName ?? "unknown")}");
                writer.WriteEndElement(); // MusicInfo

                // 写入段落序列
                writer.WriteStartElement("SectionSeq");
                writer.WriteStartElement("Section");
                writer.WriteAttributeString("type", "previous");
                writer.WriteAttributeString("endbar", "0");
                writer.WriteAttributeString("mark", "");
                writer.WriteAttributeString("param1", "");
                writer.WriteEndElement();

                writer.WriteStartElement("Section");
                writer.WriteAttributeString("type", "begin");
                writer.WriteAttributeString("startbar", "1");
                writer.WriteAttributeString("endbar", "4");
                writer.WriteAttributeString("mark", "");
                writer.WriteAttributeString("param1", "");
                writer.WriteEndElement();

                writer.WriteStartElement("Section");
                writer.WriteAttributeString("type", "note");
                writer.WriteAttributeString("startbar", "5");
                int totalBars = _levelInfo.BarAmount ?? 20; // 如果为空，使用默认值20
                writer.WriteAttributeString("endbar", (totalBars - 4).ToString());
                writer.WriteAttributeString("mark", "");
                writer.WriteAttributeString("param1", "");
                writer.WriteEndElement();

                writer.WriteStartElement("Section");
                writer.WriteAttributeString("type", "showtime");
                writer.WriteAttributeString("startbar", (totalBars - 3).ToString());
                writer.WriteAttributeString("endbar", totalBars.ToString());
                writer.WriteAttributeString("mark", "pose_default");
                writer.WriteAttributeString("param1", "");
                writer.WriteEndElement();
                writer.WriteEndElement(); // SectionSeq

                // 写入指示器重置位置
                writer.WriteStartElement("IndicatorResetPos");
                writer.WriteAttributeString("PosNum", "64");
                writer.WriteEndElement(); // IndicatorResetPos

                // 写入音符信息
                writer.WriteStartElement("NoteInfo");
                writer.WriteStartElement("Normal");

                // 按小节和位置排序音符
                var sortedNotes = _idolNotes.OrderBy(n => n.Bar).ThenBy(n => n.Pos).ToList();

                foreach (var note in sortedNotes)
                {
                    if (note.NoteType == "slip" && !string.IsNullOrEmpty(note.EndTrack) && note.TargetTrack != note.EndTrack)
                    {
                        // 滑动音符（确保起始和结束轨道不同）
                        writer.WriteStartElement("Note");
                        writer.WriteAttributeString("note_type", note.NoteType);
                        writer.WriteAttributeString("Bar", note.Bar.ToString());
                        writer.WriteAttributeString("Pos", note.Pos.ToString());
                        writer.WriteAttributeString("target_track", note.TargetTrack);
                        writer.WriteAttributeString("end_track", note.EndTrack);
                        writer.WriteEndElement();
                    }
                    else if (note.NoteType == "long")
                    {
                        // 长音符
                        writer.WriteStartElement("Note");
                        writer.WriteAttributeString("note_type", note.NoteType);
                        writer.WriteAttributeString("Bar", note.Bar.ToString());
                        writer.WriteAttributeString("Pos", note.Pos.ToString());
                        writer.WriteAttributeString("from_track", note.FromTrack);
                        writer.WriteAttributeString("target_track", note.TargetTrack);
                        writer.WriteAttributeString("EndBar", note.EndBar.ToString());
                        writer.WriteAttributeString("EndPos", note.EndPos.ToString());
                        writer.WriteEndElement();
                    }
                    else
                    {
                        // 短音符
                        writer.WriteStartElement("Note");
                        writer.WriteAttributeString("note_type", note.NoteType);
                        writer.WriteAttributeString("Bar", note.Bar.ToString());
                        writer.WriteAttributeString("Pos", note.Pos.ToString());
                        writer.WriteAttributeString("from_track", note.FromTrack);
                        writer.WriteAttributeString("target_track", note.TargetTrack);
                        writer.WriteEndElement();
                    }
                }

                writer.WriteEndElement(); // Normal
                writer.WriteEndElement(); // NoteInfo

                // 写入动作序列
                WriteActionSeq(writer);

                // 写入摄像机序列
                WriteCameraSeq(writer);

                // 写入舞者排序
                WriteDancerSort(writer);

                // 写入舞台效果序列
                WriteStageEffectSeq(writer);

                writer.WriteEndElement(); // Level
                writer.WriteEndDocument();
            }
        }

        /// <summary>
        /// 写入动作序列
        /// </summary>
        private void WriteActionSeq(System.Xml.XmlTextWriter writer)
        {
            writer.WriteStartElement("ActionSeq");
            writer.WriteAttributeString("type", "1");

            // 从第5小节开始，每4小节生成一个动作
            int startBar = 5;
            int totalBars = _levelInfo.BarAmount ?? 20; // 如果为空，使用默认值20
            int endBar = totalBars - 4; // 排除最后的showtime段落

            for (int bar = startBar; bar <= endBar; bar += 4)
            {
                writer.WriteStartElement("ActionList");
                writer.WriteAttributeString("start_bar", bar.ToString());

                // 根据位置调整舞蹈长度
                if (bar == startBar)
                {
                    writer.WriteAttributeString("dance_len", "1");
                    writer.WriteAttributeString("seq_len", "2");
                }
                else if (bar >= endBar - 4)
                {
                    writer.WriteAttributeString("dance_len", "4");
                    writer.WriteAttributeString("seq_len", "4");
                }
                else
                {
                    writer.WriteAttributeString("dance_len", "2");
                    writer.WriteAttributeString("seq_len", "4");
                }

                writer.WriteAttributeString("level", "2");
                writer.WriteAttributeString("type", "");
                writer.WriteEndElement(); // ActionList
            }

            writer.WriteEndElement(); // ActionSeq
        }

        /// <summary>
        /// 写入摄像机序列
        /// </summary>
        private void WriteCameraSeq(System.Xml.XmlTextWriter writer)
        {
            writer.WriteStartElement("CameraSeq");

            int totalBars = _levelInfo.BarAmount ?? 20; // 如果为空，使用默认值20

            writer.WriteStartElement("Camera");
            writer.WriteAttributeString("name", "SC");
            writer.WriteAttributeString("bar", "5");
            writer.WriteAttributeString("pos", "0");
            writer.WriteAttributeString("end_bar", (totalBars + 1).ToString());
            writer.WriteAttributeString("end_pos", "0");
            writer.WriteEndElement(); // Camera

            writer.WriteEndElement(); // CameraSeq
        }

        /// <summary>
        /// 写入舞者排序
        /// </summary>
        private void WriteDancerSort(System.Xml.XmlTextWriter writer)
        {
            writer.WriteStartElement("DancerSort");

            // 从第10小节开始，每2小节一个排序点，直到音符结束前
            int startBar = 10;
            int totalBars = _levelInfo.BarAmount ?? 20; // 如果为空，使用默认值20
            int endBar = totalBars - 4; // 排除showtime段落

            for (int bar = startBar; bar <= endBar; bar += 2)
            {
                writer.WriteStartElement("Bar");
                writer.WriteString(bar.ToString());
                writer.WriteEndElement(); // Bar
            }

            writer.WriteEndElement(); // DancerSort
        }

        /// <summary>
        /// 写入舞台效果序列
        /// </summary>
        private void WriteStageEffectSeq(System.Xml.XmlTextWriter writer)
        {
            writer.WriteStartElement("StageEffectSeq");

            int totalBars = _levelInfo.BarAmount ?? 20; // 如果为空，使用默认值20

            // 全局舞台脚本
            writer.WriteStartElement("effect");
            writer.WriteAttributeString("name", "wutai_scene_global_script");
            writer.WriteAttributeString("bar", "-1");
            writer.WriteAttributeString("length", "-1");
            writer.WriteEndElement(); // effect

            // 音符段落效果
            writer.WriteStartElement("effect");
            writer.WriteAttributeString("name", "wutai_scene_note_biaozhun_01");
            writer.WriteAttributeString("bar", "5");
            writer.WriteAttributeString("length", (totalBars - 9).ToString()); // 从第5小节到showtime前
            writer.WriteEndElement(); // effect

            // showtime段落效果
            writer.WriteStartElement("effect");
            writer.WriteAttributeString("name", "wutai_scene_note_biaozhun_01");
            writer.WriteAttributeString("bar", (totalBars - 3).ToString());
            writer.WriteAttributeString("length", "-1");
            writer.WriteEndElement(); // effect

            writer.WriteEndElement(); // StageEffectSeq
        }

        #endregion
    }
}
