<Window x:Class="MyWPF.Layout.AI.AIConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="AI配置设置" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <TextBlock Text="⚙️ AI服务配置"
                       FontSize="20"
                       FontWeight="Bold"
                       VerticalAlignment="Center"/>
        </StackPanel>

        <!-- 配置内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="10">
                
                <!-- AI服务商选择 -->
                <GroupBox Header="AI服务商" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <Label Content="选择AI服务商:" Margin="0,0,0,5"/>
                        <ComboBox Name="cmbAIProvider"
                                  SelectionChanged="CmbAIProvider_SelectionChanged"
                                  Margin="0,0,0,10">
                            <ComboBoxItem Content="DeepSeek" Tag="deepseek"/>
                            <ComboBoxItem Content="OpenAI" Tag="openai"/>
                            <ComboBoxItem Content="Claude (Anthropic)" Tag="claude"/>
                            <ComboBoxItem Content="通义千问 (Qwen)" Tag="qwen"/>
                            <ComboBoxItem Content="文心一言 (ERNIE)" Tag="ernie"/>
                            <ComboBoxItem Content="自定义" Tag="custom"/>
                        </ComboBox>

                        <TextBlock Name="txtProviderDescription"
                                   Text="请选择AI服务商"
                                   FontSize="12"
                                   Margin="0,5,0,0"
                                   Foreground="Gray"/>
                    </StackPanel>
                </GroupBox>

                <!-- API配置 -->
                <GroupBox Header="API配置" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <Label Content="API密钥:" Margin="0,0,0,5"/>
                        <PasswordBox Name="txtApiKey"
                                     Margin="0,0,0,15"/>

                        <Label Content="API基础URL:" Margin="0,0,0,5"/>
                        <TextBox Name="txtBaseUrl"
                                 Margin="0,0,0,15"/>

                        <Label Content="模型名称:" Margin="0,0,0,5"/>
                        <TextBox Name="txtModel"
                                 Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <Label Content="温度参数" Margin="0,0,0,5"/>
                                <Slider Name="sliderTemperature"
                                        Minimum="0" Maximum="2" Value="0.7"
                                        TickFrequency="0.1" IsSnapToTickEnabled="True"/>
                                <TextBlock Name="txtTemperatureValue" Text="0.7"
                                           HorizontalAlignment="Center"
                                           FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <Label Content="最大Token数" Margin="0,0,0,5"/>
                                <Slider Name="sliderMaxTokens"
                                        Minimum="1000" Maximum="8000" Value="4000"
                                        TickFrequency="500" IsSnapToTickEnabled="True"/>
                                <TextBlock Name="txtMaxTokensValue" Text="4000"
                                           HorizontalAlignment="Center"
                                           FontSize="12"/>
                            </StackPanel>
                        </Grid>
                        
                        <StackPanel Margin="0,15,0,0">
                            <Label Content="超时时间(秒)" Margin="0,0,0,5"/>
                            <Slider Name="sliderTimeout"
                                    Minimum="10" Maximum="120" Value="60"
                                    TickFrequency="10" IsSnapToTickEnabled="True"/>
                            <TextBlock Name="txtTimeoutValue" Text="60"
                                       HorizontalAlignment="Center"
                                       FontSize="12"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- 默认生成参数 -->
                <GroupBox Header="默认生成参数" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <Label Content="默认难度" Margin="0,0,0,5"/>
                                <Slider Name="sliderDefaultDifficulty"
                                        Minimum="1" Maximum="10" Value="5"
                                        TickFrequency="1" IsSnapToTickEnabled="True"/>
                                <TextBlock Name="txtDifficultyValue" Text="5"
                                           HorizontalAlignment="Center"
                                           FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <Label Content="音符密度" Margin="0,0,0,5"/>
                                <Slider Name="sliderNoteDensity"
                                        Minimum="0.1" Maximum="1.0" Value="0.7"
                                        TickFrequency="0.1" IsSnapToTickEnabled="True"/>
                                <TextBlock Name="txtNoteDensityValue" Text="0.7"
                                           HorizontalAlignment="Center"
                                           FontSize="12"/>
                            </StackPanel>
                        </Grid>

                        <Label Content="默认生成风格:" Margin="0,15,0,5"/>
                        <ComboBox Name="cmbDefaultStyle"
                                  Margin="0,0,0,0">
                            <ComboBoxItem Content="节奏型" Tag="rhythm"/>
                            <ComboBoxItem Content="旋律型" Tag="melody"/>
                            <ComboBoxItem Content="混合型" Tag="mixed"/>
                        </ComboBox>
                    </StackPanel>
                </GroupBox>

                <!-- 测试连接 -->
                <GroupBox Header="连接测试">
                    <StackPanel Margin="10">
                        <Button Name="btnTestConnection"
                                Content="测试连接"
                                Click="BtnTestConnection_Click"
                                HorizontalAlignment="Left"
                                Padding="10,5"/>

                        <TextBlock Name="txtTestResult"
                                   Text="点击'测试连接'验证配置是否正确"
                                   Margin="0,10,0,0"
                                   TextWrapping="Wrap"
                                   FontSize="12"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Name="btnReset" Content="重置默认"
                    Margin="0,0,10,0" Click="BtnReset_Click"
                    Padding="10,5"/>
            <Button Name="btnCancel" Content="取消"
                    Margin="0,0,10,0" Click="BtnCancel_Click"
                    Padding="10,5"/>
            <Button Name="btnSave" Content="保存"
                    Click="BtnSave_Click"
                    Padding="10,5"
                    Background="DodgerBlue"
                    Foreground="White"/>
        </StackPanel>
    </Grid>
</Window>
