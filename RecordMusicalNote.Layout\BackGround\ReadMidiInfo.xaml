﻿<Window x:Class="MyWPF.Layout.BackGround.ReadMidiInfo"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MyWPF.Layout.BackGround"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="MIDI信息管理" Height="700" Width="1200" MinHeight="600" MinWidth="1000"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <Style x:Key="contentCenterStyle" TargetType="{x:Type TextBlock}">
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
    </Window.Resources>

    <materialDesign:DialogHost Identifier="MidiInfoDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 顶部标题栏 -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
                <DockPanel>
                    <materialDesign:PackIcon Kind="MidiPort" Width="24" Height="24" VerticalAlignment="Center" DockPanel.Dock="Left"/>
                    <TextBlock Text="MIDI信息管理" FontSize="20" FontWeight="Medium" VerticalAlignment="Center" Margin="16,0,0,0" DockPanel.Dock="Left"/>

                    <!-- 操作按钮 -->
                    <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                        <Button Name="txtFresh" Content="刷新" Click="txtFresh_Click"
                              Style="{StaticResource MaterialDesignToolForegroundButton}" Margin="4">
                            <Button.ToolTip>
                                <ToolTip Content="刷新MIDI信息"/>
                            </Button.ToolTip>
                        </Button>

                        <Button Name="txtBroadcast" Content="播放" Click="txtBroadcast_Click"
                              Style="{StaticResource MaterialDesignToolForegroundButton}" Margin="4">
                            <Button.ToolTip>
                                <ToolTip Content="播放选中的MIDI"/>
                            </Button.ToolTip>
                        </Button>

                        <Button Name="txtStop" Content="停止" Click="txtStop_Click"
                              Style="{StaticResource MaterialDesignToolForegroundButton}" Margin="4">
                            <Button.ToolTip>
                                <ToolTip Content="停止播放"/>
                            </Button.ToolTip>
                        </Button>

                        <Button Name="txtCreateXML" Content="创建星动谱面" Click="txtCreateXML_Click"
                              Style="{StaticResource MaterialDesignRaisedLightButton}" Margin="4">
                            <Button.ToolTip>
                                <ToolTip Content="根据难度参数创建星动谱面"/>
                            </Button.ToolTip>
                        </Button>

                        <Button Name="btnDifficultySettings" Content="难度设置" Click="btnDifficultySettings_Click"
                              Style="{StaticResource MaterialDesignOutlinedButton}" Margin="4">
                            <Button.ToolTip>
                                <ToolTip Content="配置谱面难度参数"/>
                            </Button.ToolTip>
                        </Button>
                    </StackPanel>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- 主要内容区域 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 搜索和控制面板 -->
                    <materialDesign:Card Grid.Row="0" Style="{StaticResource MaterialCard}" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="搜索和控制" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="150"/>
                                </Grid.ColumnDefinitions>

                                <ComboBox Grid.Column="0" Name="_cmbSearchType"
                                        materialDesign:HintAssist.Hint="搜索类型"
                                        Style="{StaticResource MaterialDesignComboBox}"
                                        Width="120" Margin="0,0,16,0">
                                    <ComboBoxItem>歌名</ComboBoxItem>
                                    <ComboBoxItem>歌手</ComboBoxItem>
                                    <ComboBoxItem>BPM</ComboBoxItem>
                                </ComboBox>

                                <TextBox Grid.Column="1" Name="txtSearchConent"
                                       materialDesign:HintAssist.Hint="搜索内容"
                                       Style="{StaticResource MaterialTextBox}"
                                       Margin="0,0,16,0"/>

                                <TextBlock Grid.Column="2" Text="速度延迟:"
                                         VerticalAlignment="Center"
                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                         Margin="0,0,8,0"/>

                                <TextBox Grid.Column="3" Name="txtSpeed"
                                       materialDesign:HintAssist.Hint="延迟(ms)"
                                       Style="{StaticResource MaterialTextBox}"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 数据显示区域 -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="2*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 左侧面板 -->
                        <StackPanel Grid.Column="0" Margin="0,0,8,0">
                            <!-- MIDI文件列表 -->
                            <materialDesign:Card Style="{StaticResource MaterialCard}" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="MIDI文件列表" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                                    <DataGrid Name="_searchDataGrid"
                                            AutoGenerateColumns="False"
                                            IsReadOnly="True"
                                            materialDesign:DataGridAssist.CellPadding="8"
                                            materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                                            CanUserAddRows="False"
                                            CanUserDeleteRows="False"
                                            CanUserReorderColumns="False"
                                            CanUserResizeRows="False"
                                            SelectionMode="Single"
                                            GridLinesVisibility="Horizontal"
                                            HorizontalGridLinesBrush="{DynamicResource MaterialDesignDivider}"
                                            MinHeight="200">

                                        <DataGrid.Columns>
                                            <DataGridTextColumn Header="歌名" Width="*" Binding="{Binding SongName}"/>
                                            <DataGridTextColumn Header="节拍" Width="60" Binding="{Binding Meter}"/>
                                            <DataGridTextColumn Header="轨数" Width="60" Binding="{Binding TrackCount}"/>
                                            <DataGridTextColumn Header="四分音符时间" Width="100" Binding="{Binding QuarternotetTime}"/>
                                        </DataGrid.Columns>
                                    </DataGrid>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- 乐器列表 -->
                            <materialDesign:Card Style="{StaticResource MaterialCard}">
                                <StackPanel>
                                    <TextBlock Text="乐器列表" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,8"/>
                                    <TextBlock Text="提示：可修改轨道类型标注，双击选择乐器筛选"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}" Margin="0,0,0,4"/>
                                    <TextBlock Text="注意：只有标注了类型的轨道才会参与谱面生成，未标注的轨道将被跳过"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Foreground="Orange" Margin="0,0,0,8"/>

                                    <DataGrid Name="_instrumentData"
                                            AutoGenerateColumns="False"
                                            IsReadOnly="False"
                                            materialDesign:DataGridAssist.CellPadding="8"
                                            materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                                            CanUserAddRows="False"
                                            CanUserDeleteRows="False"
                                            CanUserReorderColumns="False"
                                            CanUserResizeRows="False"
                                            SelectionMode="Single"
                                            GridLinesVisibility="Horizontal"
                                            HorizontalGridLinesBrush="{DynamicResource MaterialDesignDivider}"
                                            MinHeight="200">

                                        <DataGrid.Columns>
                                            <DataGridTextColumn Header="ID" Visibility="Collapsed" Binding="{Binding MidiTrackID}"/>
                                            <DataGridTextColumn Header="乐器" Width="120" Binding="{Binding Instrument}"/>
                                            <DataGridComboBoxColumn Header="轨道类型" Width="100" x:Name="TrackTypeColumn"
                                                                    SelectedValueBinding="{Binding TrackTypeDisplay, Mode=TwoWay}">
                                                <DataGridComboBoxColumn.ElementStyle>
                                                    <Style TargetType="ComboBox">
                                                        <Setter Property="ItemsSource">
                                                            <Setter.Value>
                                                                <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                                                    <sys:String>未标注</sys:String>
                                                                    <sys:String>主旋律</sys:String>
                                                                    <sys:String>副旋律</sys:String>
                                                                    <sys:String>和声</sys:String>
                                                                    <sys:String>贝斯</sys:String>
                                                                    <sys:String>鼓</sys:String>
                                                                    <sys:String>打击乐</sys:String>
                                                                    <sys:String>和弦伴奏</sys:String>
                                                                    <sys:String>琶音</sys:String>
                                                                    <sys:String>背景音效</sys:String>
                                                                </x:Array>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </DataGridComboBoxColumn.ElementStyle>
                                                <DataGridComboBoxColumn.EditingElementStyle>
                                                    <Style TargetType="ComboBox">
                                                        <Setter Property="ItemsSource">
                                                            <Setter.Value>
                                                                <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                                                    <sys:String>未标注</sys:String>
                                                                    <sys:String>主旋律</sys:String>
                                                                    <sys:String>副旋律</sys:String>
                                                                    <sys:String>和声</sys:String>
                                                                    <sys:String>贝斯</sys:String>
                                                                    <sys:String>鼓</sys:String>
                                                                    <sys:String>打击乐</sys:String>
                                                                    <sys:String>和弦伴奏</sys:String>
                                                                    <sys:String>琶音</sys:String>
                                                                    <sys:String>背景音效</sys:String>
                                                                </x:Array>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </DataGridComboBoxColumn.EditingElementStyle>
                                            </DataGridComboBoxColumn>
                                            <DataGridTextColumn Header="优先级" Width="60" Binding="{Binding Priority, Mode=TwoWay}"/>
                                            <DataGridCheckBoxColumn Header="主旋律" Width="60" Binding="{Binding IsMainMelody, Mode=TwoWay}"/>
                                        </DataGrid.Columns>
                                    </DataGrid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>

                        <!-- 右侧面板 - MIDI轨道详细信息 -->
                        <materialDesign:Card Grid.Column="1" Style="{StaticResource MaterialCard}" Margin="8,0,0,0">
                            <StackPanel>
                                <TextBlock Text="MIDI轨道详细信息" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                                <DataGrid Name="_mainData"
                                        AutoGenerateColumns="False"
                                        IsReadOnly="True"
                                        materialDesign:DataGridAssist.CellPadding="8"
                                        materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                                        CanUserAddRows="False"
                                        CanUserDeleteRows="False"
                                        CanUserReorderColumns="False"
                                        CanUserResizeRows="False"
                                        SelectionMode="Single"
                                        GridLinesVisibility="Horizontal"
                                        HorizontalGridLinesBrush="{DynamicResource MaterialDesignDivider}"
                                        Height="730">

                                    <DataGrid.Columns>
                                        <DataGridTextColumn ElementStyle="{StaticResource contentCenterStyle}"
                                                          Header="主键" Binding="{Binding MidiTrackListID}"
                                                          Width="50" Visibility="Collapsed"/>
                                        <DataGridTextColumn ElementStyle="{StaticResource contentCenterStyle}"
                                                          Header="MidiID" Binding="{Binding MidiID}"
                                                          Width="80" Visibility="Collapsed"/>
                                        <DataGridTextColumn ElementStyle="{StaticResource contentCenterStyle}"
                                                          Header="MidiTrackID" Binding="{Binding MidiTrackID}"
                                                          Width="80" Visibility="Collapsed"/>
                                        <DataGridTextColumn ElementStyle="{StaticResource contentCenterStyle}"
                                                          Header="通道" Binding="{Binding Channel}" Width="60"/>
                                        <DataGridTextColumn ElementStyle="{StaticResource contentCenterStyle}"
                                                          Header="持续时间" Binding="{Binding Duration}" Width="80"/>
                                        <DataGridTextColumn ElementStyle="{StaticResource contentCenterStyle}"
                                                          Header="音名数值" Binding="{Binding SyllabelNumber}" Width="80"/>
                                        <DataGridTextColumn ElementStyle="{StaticResource contentCenterStyle}"
                                                          Header="音名" Binding="{Binding Syllabel}" Width="60"/>
                                        <DataGridTextColumn ElementStyle="{StaticResource contentCenterStyle}"
                                                          Header="开始时间" Binding="{Binding StartTime}" Width="80"/>
                                        <DataGridTextColumn ElementStyle="{StaticResource contentCenterStyle}"
                                                          Header="结束时间" Binding="{Binding EndTime}" Width="80"/>
                                    </DataGrid.Columns>
                                </DataGrid>

                                <!-- 分页控制 -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,16,0,0">
                                    <Button Name="btnPreviousPage" Content="上一页" Click="btnPreviousPage_Click"
                                          Style="{StaticResource MaterialDesignOutlinedButton}" Margin="4">
                                        <Button.ToolTip>
                                            <ToolTip Content="显示上一页数据"/>
                                        </Button.ToolTip>
                                    </Button>

                                    <TextBlock Name="txtPageInfo" Text="第 1/1 页" VerticalAlignment="Center"
                                             Style="{StaticResource MaterialDesignBody1TextBlock}" Margin="16,0"/>

                                    <Button Name="btnNextPage" Content="下一页" Click="btnNextPage_Click"
                                          Style="{StaticResource MaterialDesignOutlinedButton}" Margin="4">
                                        <Button.ToolTip>
                                            <ToolTip Content="显示下一页数据"/>
                                        </Button.ToolTip>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>
                </Grid>
            </ScrollViewer>
        </Grid>
    </materialDesign:DialogHost>
</Window>
