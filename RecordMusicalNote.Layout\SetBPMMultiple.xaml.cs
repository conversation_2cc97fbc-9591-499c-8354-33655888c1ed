﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace RecordMusicalNote
{
    /// <summary>
    /// SetBPMMultiple.xaml 的交互逻辑
    /// </summary>
    public partial class SetBPMMultiple : Window
    {
        public SetBPMMultiple()
        {
            InitializeComponent();
            this.WindowStartupLocation = WindowStartupLocation.CenterScreen;
        }

        private void btnClear_Click(object sender, RoutedEventArgs e)
        {
            txtBPMMultiple.Text = "";
        }
        public float BPMMultiple
        {
            get;set;
        }

        private void btnConfirm_Click(object sender, RoutedEventArgs e)
        {

            float Multiple = 0;
            if (float.TryParse(txtBPMMultiple.Text, out Multiple))
            {
                BPMMultiple= Multiple;
                this.Close();
            }
            else
            {
                MessageBox.Show("请输入正确的数字，最好是0.5的整数倍");
                BPMMultiple = Multiple;
            }
        }
    }
}
