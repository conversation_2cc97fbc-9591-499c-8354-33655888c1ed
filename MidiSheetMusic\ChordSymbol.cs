/*
 * Copyright (c) 2007-2008 <PERSON><PERSON><PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License version 2.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace MidiSheetMusic {

public enum StemDir { Up, Down };

public class NoteData {
    public WhiteNote note;
    public NoteDuration duration;
    public bool leftside;
    public Accid accid;
};

/*
 * A chord symbol represents a group of notes that are played at the same
 * time.  A chord includes the notes, the accidental symbols for each
 * note, and the stem (or stems) to use.  A single chord may have two 
 * stems if the notes have different durations (e.g. if one note is a
 * quarter note, and another is an eighth note).
 */
public class ChordSymbol : MusicSymbol {

    Clef clef;
    int starttime;  
    int endtime;
    NoteData[] notedata;
    AccidSymbol[] accidsymbols;
    int width;

    Stem stem1;
    Stem stem2;
    bool hastwostems;

    /* Create a new Chord Symbol from the given list of midi notes.
     * All the midi notes will have the same start time.  Use the
     * key signature to get the white key and accidental symbol for
     * each note.  Use the time signature to calculate the duration
     * of the notes.
     */
    public ChordSymbol(MidiNote[] midinotes, KeySignature key, 
                       TimeSignature time, Clef c) {

        int len = midinotes.Length;
        int i;

        hastwostems = false;
        clef = c;

        /* Sort the notes by their key, from bottom to top.  This is needed
         * to easily find the bottom and top notes for drawing the stems.
         */
        Array.Sort(midinotes, SortNotes);

        starttime = midinotes[0].StartTime;
        endtime = midinotes[0].EndTime;

        notedata = CreateNoteData(midinotes, key, time);
        accidsymbols = CreateAccidSymbols(notedata, clef);


        /* Find out how many stems we need (1 or 2) */
        NoteDuration dur1 = time.GetStemDuration(notedata[0].duration);
        NoteDuration dur2 = dur1;
        int change = -1;
        for (i = 0; i < notedata.Length; i++) {
            dur2 = time.GetStemDuration(notedata[i].duration);
            if (dur1 != dur2) {
                change = i;
                break;
            }
        }

        if (dur1 != dur2) {
            /* We have notes with different durations.  So we will need
             * two stems.  The first stem points down, and contains the
             * bottom note up to the note with the different duration.
             *
             * The second stem points up, and contains the note with the
             * different duration up to the top note.
             */
            hastwostems = true;
            stem1 = new Stem(notedata[0].note, 
                             notedata[change-1].note,
                             dur1, 
                             Stem.Down,
                             NotesOverlap(notedata, 0, change)
                            );

            stem2 = new Stem(notedata[change].note, 
                             notedata[notedata.Length-1].note,
                             dur2, 
                             Stem.Up,
                             NotesOverlap(notedata, change, notedata.Length)
                            );
        }
        else {
            /* All notes have the same duration, so we only need one stem. */
            int direction = StemDirection(notedata[0].note, 
                                          notedata[notedata.Length-1].note,
                                          clef);

            stem1 = new Stem(notedata[0].note,
                             notedata[notedata.Length-1].note,
                             dur1, 
                             direction,
                             NotesOverlap(notedata, 0, notedata.Length)
                            );
            stem2 = null;
        }

        /* For whole notes, no stem is drawn. */
        if (dur1 == NoteDuration.Whole)
            stem1 = null;
        if (dur2 == NoteDuration.Whole)
            stem2 = null;

        width = MinWidth;
    }

    static int SortNotes(MidiNote x, MidiNote y) {
        return x.Number - y.Number;
    }

    /* Given the raw midi notes (the note number and duration in pulses),
     * calculate the following note data:
     * - The white key
     * - The accidental (if any)
     * - The note duration (half, quarter, eighth, etc)
     * - The side it should be drawn (left or side)
     * By default, notes are drawn on the left side.  However, if two notes
     * overlap (like A and B) you cannot draw the next note directly above it.
     * Instead you must shift one of the notes to the right.
     */
 
    static NoteData[] CreateNoteData(MidiNote[] midinotes, KeySignature key,
                              TimeSignature time) {

        int len = midinotes.Length;
        NoteData[] notedata = new NoteData[len];

        for (int i = 0; i < len; i++) {
            MidiNote m = midinotes[i];
            notedata[i] = new NoteData();
            notedata[i].leftside = true;
            notedata[i].note = key.GetWhiteNote(m.Number);
            notedata[i].duration = time.GetNoteDuration(m.EndTime-m.StartTime);
            notedata[i].accid = key.GetAccidental(m.Number, 
                                                  m.StartTime / time.Measure);
            
            if (i > 0 && (notedata[i].note.Dist(notedata[i-1].note) == 1)) {
                /* This note (notedata[i]) overlaps with the previous note.
                 * Change the side of this note.
                 */

                if (notedata[i-1].leftside) {
                    notedata[i].leftside = false;
                } else {
                    notedata[i].leftside = true;
                }
            } else {
                notedata[i].leftside = true;
            }
        }
        return notedata;
    }


    /* Given the note data (the white keys and accidentals), create 
     * the Accidental Symbols and return them.
     */
    static AccidSymbol[] CreateAccidSymbols(NoteData[] notedata, Clef clef) {
        int count = 0;
        foreach (NoteData n in notedata) {
            if (n.accid != Accid.None) {
                count++;
            }
        }
        AccidSymbol[] symbols = new AccidSymbol[count];
        int i = 0;
        foreach (NoteData n in notedata) {
            if (n.accid != Accid.None) {
                symbols[i] = new AccidSymbol(n.accid, n.note, clef);
                i++;
            }
        }
        return symbols;
    }

    /* Calculate the stem direction (Up or down) based on the top and
     * bottom note in the chord.  If the average of the notes is above
     * the middle of the staff, the direction is down.  Else, the
     * direction is up.
     */
    static int StemDirection(WhiteNote bottom, WhiteNote top, Clef clef) {
        WhiteNote middle;
        if (clef == Clef.Treble)
            middle = new WhiteNote(WhiteNote.B, 5);
        else
            middle = new WhiteNote(WhiteNote.D, 3);

        int dist = middle.Dist(bottom) + middle.Dist(top);
        if (dist >= 0)
            return Stem.Up;
        else 
            return Stem.Down;
    }

    /* Return whether any of the notes in notedata (between start and
     * end indexes) overlap.
     */
    static bool NotesOverlap(NoteData[] notedata, int start, int end) {
        for (int i = start; i < end; i++) {
            if (!notedata[i].leftside) {
                return true;
            }
        }
        return false;
    }


    public override int StartTime { 
        get { return starttime; }
    }

    public int EndTime { 
        get { return endtime; }
    }

    public Clef Clef { 
        get { return clef; }
    }

    public bool HasTwoStems {
        get { return hastwostems; }
    }

    /* Return the stem will the smallest duration.  This property
     * is used when making chord pairs (chords joined by a horizontal
     * beam stem). The stem durations must match in order to make
     * a chord pair.  If a chord has two stems, we always return
     * the one with a smaller duration, because it has a better 
     * chance of making a pair.
     */
    public Stem Stem {
        get { 
            if (stem1 == null) { return stem2; }
            else if (stem2 == null) { return stem1; }
            else if (stem1.Duration < stem2.Duration) { return stem1; }
            else { return stem2; }
        }
    }

    public override int Width {
        get { return width; }
        set { width = value; }
    }

    public override int MinWidth {
        get { return GetMinWidth(); }
    }

    /* Return the minimum width needed to display this chord.
     * The width needed to draw the note circles is given by
     * NotesGetWidth().
     *
     * The accidental symbols can be drawn above one another as long
     * as they don't overlap (they must be at least 6 notes apart).
     * If two accidental symbols do overlap, the accidental symbol
     * on top must be shifted to the right.  So the width needed for
     * accidental symbols depends on whether they overlap or not.
     */
    int GetMinWidth() {
        int result = NotesGetWidth();

        AccidSymbol[] a = accidsymbols;
        if (a.Length > 0) {
            result += a[0].MinWidth;
            for (int i = 1; i < a.Length; i++) {
                if (a[i].Note.Dist(a[i-1].Note) < 6) {
                    result += a[i].MinWidth;
                }
            }
        }
        return result;
    }

    /* Return the width needed to display the note symbols
     * (excluding any accidental symbols).
     */
    int NotesGetWidth() { 
        return 2*SheetMusic.NoteHeight + SheetMusic.NoteHeight/2;
    }


    public override int AboveStaff {
        get { return GetAboveStaff(); }
    }

    int GetAboveStaff() {
        WhiteNote topnote = notedata[ notedata.Length-1 ].note;
        if (stem1 != null)
            topnote = WhiteNote.Max(topnote, stem1.End);
        if (stem2 != null)
            topnote = WhiteNote.Max(topnote, stem2.End);

        int dist = topnote.Dist(WhiteNote.Top(clef)) * SheetMusic.NoteHeight/2;
        int result = 0;
        if (dist > 0)
            result = dist;

        foreach (AccidSymbol a in accidsymbols) {
            if (a.AboveStaff > result) {
                result = a.AboveStaff;
            }
        }
        return result;
    }

    public override int BelowStaff {
        get { return GetBelowStaff(); }
    }

    int GetBelowStaff() {
        WhiteNote bottomnote = notedata[0].note;
        if (stem1 != null)
            bottomnote = WhiteNote.Min(bottomnote, stem1.End);
        if (stem2 != null)
            bottomnote = WhiteNote.Min(bottomnote, stem2.End);

        int dist = WhiteNote.Bottom(clef).Dist(bottomnote) *
                   SheetMusic.NoteHeight/2;

        int result = 0;
        if (dist > 0)
            result = dist;
 
        foreach (AccidSymbol a in accidsymbols) {
            if (a.BelowStaff > result) {
                result = a.BelowStaff;
            }
        }
        return result;
    }

    /* Draw the Chord Symbol:
     * - Draw the accidental symbols.
     * - Draw the black circle notes.
     * - Draw the stems.
     */
    public override void Draw(Graphics g, Pen pen, int ytop) {
        g.TranslateTransform(Width - MinWidth, 0);
        WhiteNote topstaff = WhiteNote.Top(clef);
        int xpos = DrawAccid(g, pen, ytop);
        g.TranslateTransform(xpos, 0);
        DrawNotes(g, pen, ytop, topstaff);
        if (stem1 != null)
            stem1.Draw(g, pen, ytop, topstaff);
        if (stem2 != null)
            stem2.Draw(g, pen, ytop, topstaff);
        g.TranslateTransform(-xpos, 0);
        g.TranslateTransform(-(Width - MinWidth), 0);
    }

    /* Draw the accidental symbols.  If two symbols overlap (if they
     * are less than 6 notes apart), we cannot draw the symbol directly
     * above the previous one.  Instead, we must shift it to the right.
     * Return the x position where we should start drawing the black
     * circle notes.
     */
    public int DrawAccid(Graphics g, Pen pen, int ytop) {
        int xpos = 0;

        AccidSymbol prev = null;
        foreach (AccidSymbol a in accidsymbols) {
            if (prev != null && a.Note.Dist(prev.Note) < 6) {
                xpos += a.Width;
            }
            g.TranslateTransform(xpos, 0);
            a.Draw(g, pen, ytop);
            g.TranslateTransform(-xpos, 0);
            prev = a;
        }
        if (prev != null) {
            xpos += prev.Width;
        }
        return xpos;
    }

    /* Draw the black circle notes. */
    public void DrawNotes(Graphics g, Pen pen, int ytop, WhiteNote topstaff) {
        foreach (NoteData n in notedata) {
            int ynote = ytop + topstaff.Dist(n.note) * 
                        SheetMusic.NoteHeight/2;
            pen.Width = SheetMusic.LineWidth;

            int xstart = SheetMusic.LineSpace/4;
            if (!n.leftside)
                xstart += SheetMusic.NoteWidth;

            /* Draw rotated ellipse.  You must first translate (0,0)
             * to the center of the ellipse.
             */
            g.TranslateTransform(xstart + SheetMusic.NoteWidth/2, 
                                 ynote - SheetMusic.LineWidth + 
                                 SheetMusic.NoteHeight/2);

            g.RotateTransform(-45);
            if (n.duration == NoteDuration.Whole || 
                n.duration == NoteDuration.Half ||
                n.duration == NoteDuration.DottedHalf) {

                g.DrawEllipse(pen, -SheetMusic.NoteWidth/2, 
                              -SheetMusic.NoteHeight/2,
                              SheetMusic.NoteWidth,
                              SheetMusic.NoteHeight);

                g.DrawEllipse(pen, -SheetMusic.NoteWidth/2, 
                              -SheetMusic.NoteHeight/2 + 1,
                              SheetMusic.NoteWidth,
                              SheetMusic.NoteHeight-2);

                g.DrawEllipse(pen, -SheetMusic.NoteWidth/2, 
                              -SheetMusic.NoteHeight/2 + 1,
                              SheetMusic.NoteWidth,
                              SheetMusic.NoteHeight-3);

            }
            else {
                g.FillEllipse(Brushes.Black, -SheetMusic.NoteWidth/2, 
                              -SheetMusic.NoteHeight/2,
                              SheetMusic.NoteWidth,
                              SheetMusic.NoteHeight);
            }
            g.RotateTransform(45);
            g.TranslateTransform( - (xstart+SheetMusic.NoteWidth/2), 
                                  - (ynote -SheetMusic.LineWidth + 
                                     SheetMusic.NoteHeight/2));

            /* Draw a dot if this is a dotted duration. */
            if (n.duration == NoteDuration.DottedHalf ||
                n.duration == NoteDuration.DottedQuarter ||
                n.duration == NoteDuration.DottedEighth) {

                g.FillEllipse(Brushes.Black, 
                              xstart + SheetMusic.NoteWidth + 
                              SheetMusic.LineSpace/3,
                              ynote + SheetMusic.LineSpace/3, 4, 4);
            }

            /* Draw horizontal lines if note is above/below the staff */
            WhiteNote top = topstaff.Add(1);
            int dist = n.note.Dist(top);
            int y = ytop - SheetMusic.LineWidth;

            if (dist >= 2) {
                for (int i = 2; i <= dist; i += 2) {
                    y -= SheetMusic.NoteHeight;
                    g.DrawLine(pen, xstart - SheetMusic.LineSpace/4, y, 
                                    xstart + SheetMusic.NoteWidth + 
                                    SheetMusic.LineSpace/4, y);
                }
            }

            WhiteNote bottom = top.Add(-8);
            y = ytop + (SheetMusic.LineSpace + SheetMusic.LineWidth) * 4;
            dist = bottom.Dist(n.note);
            if (dist >= 2) {
                for (int i = 2; i <= dist; i+= 2) {
                    y += SheetMusic.NoteHeight;
                    g.DrawLine(pen, xstart - SheetMusic.LineSpace/4, y, 
                                    xstart + SheetMusic.NoteWidth + 
                                    SheetMusic.LineSpace/4, y);
                }
            }
            /* End drawing horizontal lines */

        }
    }

    /* Return true if the chords c1 and c2 can be made into a pair,
     * where their stems are joined by a horizontal beam.
     */
    public static 
    bool CanMakePair(ChordSymbol c1, ChordSymbol c2, TimeSignature time) {
        Stem s1 = c1.Stem;
        Stem s2 = c2.Stem;

        /* Chords must be in the same measure */
        if (c1.StartTime/time.Measure != c2.StartTime/time.Measure)
            return false;

        /* There can't be a rest between the chords. */
        if (c2.StartTime - c1.EndTime >= time.QuarterNote/4)
            return false;

        /* One of the chords doesn't have a stem */
        if (s1 == null || s2 == null)
            return false;

        /* Quarter note stems cannot be joined */
        if (s1.Duration == NoteDuration.Quarter ||
            s2.Duration == NoteDuration.Quarter) {
            return false;
        }

        /* The stem durations must match */
        if (s1.Duration != s2.Duration) {
            return false;
        }

        /* Stem cannot already have a pair */
        if (s1.Receiver || s2.Receiver)
            return false;

        /* The stems must be in the same direction (both up or both down).
         * If a chord has two stems, we cannot change the stem direction.
         * If both chords have two stems, and the directions don't match,
         * we cannot make a pair.
         */
        if ((s1.Direction != s2.Direction) && 
             c1.HasTwoStems && c2.HasTwoStems) {
            return false;
        }

        return true;
    }


    /* Make a pair between chords c1 and c2.  spacing is the horizontal
     * distance (in pixels) between the right side of c1 and the right
     * side of c2.  This spacing is also equivalent to the widths of
     * the BlankSymbols between c1 and c2, plus the width of c2.
     *
     * When two chords become a pair, a horizontal beam stem is drawn
     * between them, rather than each chord having a curvy stem.
     *
     * To make two chords a pair:
     * - Change the stem directions for each chord, so they match.
     * - In c1, pass the stem location of the second chord, and
     *   the horizontal spacing to that stem.
     * - Mark c2 as a "receiver" in a pair, so that c2 will not
     *   draw a curvy stem.
     */
    public static 
    void MakePair(ChordSymbol c1, ChordSymbol c2, int spacing) {
        Stem s1 = c1.Stem;
        Stem s2 = c2.Stem;

        int newdirection;

        if (c1.HasTwoStems) {
            newdirection = s1.Direction;
        }
        else if (c2.HasTwoStems) {
            newdirection = s2.Direction;
        }
        else {
            WhiteNote w1;
            WhiteNote w2;
            w1 = (s1.Direction == Stem.Up ? s1.Top : s1.Bottom);
            w2 = (s2.Direction == Stem.Up ? s2.Top : s2.Bottom);
            newdirection = StemDirection(w1, w2, c1.Clef);
        }

        s1.Direction = newdirection;
        s2.Direction = newdirection;
        s1.SetPair(s2, spacing);
        s2.Receiver = true;

    }


    /* Return true if the chords c1, c2, c3 can be made into a triplet,
     * where their stems are joined by a horizontal beam.
     */
    public static 
    bool CanMakeTriplet(ChordSymbol c1, ChordSymbol c2, ChordSymbol c3,
                        TimeSignature time) {
        Stem s1 = c1.Stem;
        Stem s2 = c2.Stem;
        Stem s3 = c3.Stem;

        /* Chords must be in the same measure */
        if (c1.StartTime/time.Measure != c2.StartTime/time.Measure)
            return false;
        if (c2.StartTime/time.Measure != c3.StartTime/time.Measure)
            return false;

        /* There can't be a rest between the chords. */
        if (c2.StartTime - c1.EndTime >= time.QuarterNote/4)
            return false;
        if (c3.StartTime - c2.EndTime >= time.QuarterNote/4)
            return false;

        /* One of the chords doesn't have a stem */
        if (s1 == null || s2 == null || s3 == null)
            return false;

        /* Duration must be triplet */
        if (s1.Duration != NoteDuration.Triplet)
            return false;

        /* The first chord must start on a quarter note beat */
        if (c1.StartTime % time.QuarterNote > time.QuarterNote/4)
            return false;

        /* The stem durations must match */
        if (s1.Duration != s2.Duration || s2.Duration != s3.Duration) {
            return false;
        }

        /* The stem can't already have a pair */
        if (s1.Receiver || s2.Receiver || s3.Receiver)
            return false;

        /* The stems must be in the same direction (both up or both down).
         * If a chord has two stems, we cannot change the stem direction.
         * If both chords have two stems, and the directions don't match,
         * we cannot make a pair.
         */
        if ((s1.Direction != s2.Direction) && 
             c1.HasTwoStems && c2.HasTwoStems) {
            return false;
        }
        if ((s2.Direction != s3.Direction) && 
             c2.HasTwoStems && c3.HasTwoStems) {
            return false;
        }
        if ((s1.Direction != s3.Direction) && 
             c1.HasTwoStems && c3.HasTwoStems) {
            return false;
        }

        return true;
    }


    /* Make chords c1, c2, c3 into a triplet. spacing is the horizontal
     * distance (in pixels) between the right side of c1 and the right
     * side of c3.
     *
     * To make a triplet
     * - Change the stem directions for each chord, so they match.
     * - In c1, pass the stem location of the third chord, and
     *   the horizontal spacing to that stem.
     * - Mark c2, c3 as "receiver" pairs, so that c2 and c3 will not
     *   draw a curvy stem.
     */
    public static 
    void MakeTriplet(ChordSymbol c1, ChordSymbol c2, ChordSymbol c3,
                     int spacing) {
        Stem s1 = c1.Stem;
        Stem s2 = c2.Stem;
        Stem s3 = c3.Stem;

        /* Calculate the new stem direction */
        int newdirection;

        if (c1.HasTwoStems) {
            newdirection = s1.Direction;
        }
        else if (c2.HasTwoStems) {
            newdirection = s2.Direction;
        }
        else if (c3.HasTwoStems) {
            newdirection = s3.Direction;
        }
        else {
            WhiteNote w1;
            WhiteNote w2;
            w1 = (s1.Direction == Stem.Up ? s1.Top : s1.Bottom);
            w2 = (s3.Direction == Stem.Up ? s3.Top : s3.Bottom);
            newdirection = StemDirection(w1, w2, c1.Clef);
        }
        s1.Direction = newdirection;
        s2.Direction = newdirection;
        s3.Direction = newdirection;

        /* For a triplet, the stem ends must all line up horizontally */
        if (newdirection == Stem.Up) {
            WhiteNote top = WhiteNote.Max(s1.End, s2.End);
            top = WhiteNote.Max(top, s3.End);
            s1.End = top;
            s2.End = top;
            s3.End = top;
        }
        else {
            WhiteNote bottom = WhiteNote.Min(s1.End, s2.End);
            bottom = WhiteNote.Min(bottom, s3.End);
            s1.End = bottom;
            s2.End = bottom;
            s3.End = bottom;
        }

        s1.SetPair(s3, spacing);
        s2.Receiver = true;
        s3.Receiver = true;

    }

    public override string ToString() {
        return 
        string.Format("ChordSymbol start={0} end={1} width={2} noteswidth={3} ", 
                  StartTime, EndTime, Width, NotesGetWidth() );
    }

}


}


