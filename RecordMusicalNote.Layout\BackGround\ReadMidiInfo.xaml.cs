﻿using CommonModel;
using Microsoft.Win32;
using MyMidi;
using MyWPF.Layout.BackGround;
using RecordMusicalNote;
using RecordMusicalNote.IRepository;
using RecordMusicalNote.Library.Repository;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Xml;

namespace MyWPF.Layout.BackGround
{
    /// <summary>
    /// ReadMidiInfo.xaml 的交互逻辑
    /// </summary>
    public partial class ReadMidiInfo : Window
    {
        public ReadMidiInfo()
        {
            InitializeComponent();
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            Init();
        }

        private void Init()
        {
            _cmbSearchType.SelectedIndex = 0;
            txtSearchConent.KeyDown += TxtSearchConent_KeyDown;
            _searchDataGrid.MouseDoubleClick += _searchDataGrid_MouseDoubleClick;
            _instrumentData.MouseDoubleClick += _instrumentData_MouseDoubleClick;
            _instrumentData.KeyDown += _instrumentData_KeyDown;
        }

        private void _instrumentData_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Space)
            {
                var SelectedMidiTracks = _instrumentData.SelectedItems.Cast<MidiTrack>().ToList();
                if (SelectedMidiTracks != null && SelectedMidiTracks.Count > 0)
                {
                    // 处理多选轨道的筛选
                    var selectedTrackIDs = SelectedMidiTracks.Select(b => b.MidiTrackID).ToList();
                    FilterByMultipleTracks(selectedTrackIDs);
                }
            }
        }
       
        private string _selectedInstrument;
        private int _selectedMidiTrackID = -1; // 记录选中的轨道ID
        private List<int> _selectedMidiTrackIDs = new List<int>(); // 记录多选的轨道ID
        private Midi _currentMidiInfo; // 当前选中的MIDI信息

        private void _instrumentData_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            MidiTrack MidiTrack = _instrumentData.SelectedItem as MidiTrack;
            if (MidiTrack == null) return;

            _selectedMidiTrackID = MidiTrack.MidiTrackID;
            _selectedInstrument = MidiTrack.Instrument;
            _selectedMidiTrackIDs.Clear(); // 清除多选状态

            // 根据选中的轨道筛选数据并重新分页
            FilterAndLoadData();

            if (_document != null)
            {
                _document.Stop();
                _document.ChangeProgram(-1);
            }
            txtBroadcast.Content = "播放";
        }

        private void _searchDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            Midi Midi = _searchDataGrid.SelectedItem as Midi;
            if (Midi == null) return;
            _quarternotetTime = Midi.QuarternotetTime;
            IList<MidiTrack> tracks = _repository.GetMidiTrack(Midi.MidiID);
            MidiTrack emptyMT = new MidiTrack();
            emptyMT.MidiTrackID = -1;
            emptyMT.Instrument = "";
            tracks.Add(emptyMT);
            _instrumentData.ItemsSource = tracks.OrderBy(a=>a.MidiTrackID);

        }

        private void TxtSearchConent_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                Refresh();
            }
        }
        MidiRepository _repository;
        IList<MidiTrackList> _midiTrackList;
        IList<MidiTrackList> _allMidiTrackList; // 存储所有原始数据
        IList<MidiTrackList> _filteredMidiTrackList; // 存储筛选后的数据
        private int _pageSize = 50; // 每页显示500条记录
        private int _currentPage = 0; // 当前页码（从0开始）

        private void Refresh()
        {
            if (string.IsNullOrWhiteSpace(txtSearchConent.Text))
            {
                MessageBox.Show("请输入要搜索的内容");
                return;
            }

            try
            {
                System.Diagnostics.Debug.WriteLine("开始搜索...");
                _repository = new MidiRepository();

                System.Diagnostics.Debug.WriteLine($"搜索类型: {_cmbSearchType.SelectedIndex}, 搜索内容: {txtSearchConent.Text}");
                IList<Midi> results = _repository.GetMifiInfo(_cmbSearchType.SelectedIndex, txtSearchConent.Text);

                System.Diagnostics.Debug.WriteLine($"搜索结果数量: {results?.Count ?? 0}");
                _searchDataGrid.ItemsSource = results;

                if (results != null && results.Count == 1)
                {
                    Midi MidiInfo = results.FirstOrDefault();
                    _currentMidiInfo = MidiInfo; // 保存到类字段
                    _quarternotetTime = MidiInfo.QuarternotetTime;
                    if (MidiInfo != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取轨道信息，MidiID: {MidiInfo.MidiID}");
                        IList<MidiTrack> tracks = _repository.GetMidiTrack(MidiInfo.MidiID);

                        MidiTrack emptyMT = new MidiTrack();
                        emptyMT.MidiTrackID = -1;
                        emptyMT.Instrument = "";
                        tracks.Add(emptyMT);

                        // 转换为ObservableCollection以支持编辑
                        var editableTracks = new ObservableCollection<MidiTrack>(tracks.OrderBy(a => a.MidiTrackID));
                        _instrumentData.ItemsSource = editableTracks;

                        System.Diagnostics.Debug.WriteLine("获取轨道列表信息...");
                        _allMidiTrackList = _repository.MidiTrackList(MidiInfo.MidiID).OrderBy(a => a.StartTime).ToList();

                        System.Diagnostics.Debug.WriteLine($"轨道列表总数量: {_allMidiTrackList?.Count ?? 0}");

                        // 重置筛选状态，显示所有数据
                        _selectedMidiTrackID = -1;
                        _selectedMidiTrackIDs.Clear();
                        _filteredMidiTrackList = _allMidiTrackList;
                        _currentPage = 0;
                        LoadCurrentPage();
                    }
                }
                System.Diagnostics.Debug.WriteLine("搜索完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"搜索错误: {ex.Message}");
                MessageBox.Show($"搜索时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FilterByMultipleTracks(List<int> trackIDs)
        {
            _selectedMidiTrackIDs = trackIDs;
            _selectedMidiTrackID = -1; // 清除单选状态

            if (_allMidiTrackList == null || _allMidiTrackList.Count == 0)
            {
                _filteredMidiTrackList = new List<MidiTrackList>();
            }
            else
            {
                _filteredMidiTrackList = _allMidiTrackList.Where(a => trackIDs.Contains(a.MidiTrackID)).OrderBy(a => a.MidiTrackID).ToList();
                System.Diagnostics.Debug.WriteLine($"筛选多个轨道ID {string.Join(",", trackIDs)}，结果数量: {_filteredMidiTrackList.Count}");
            }

            // 重置到第一页并加载数据
            _currentPage = 0;
            LoadCurrentPage();
        }

        private void FilterAndLoadData()
        {
            if (_allMidiTrackList == null || _allMidiTrackList.Count == 0)
            {
                _filteredMidiTrackList = new List<MidiTrackList>();
            }
            else
            {
                // 根据选中的轨道ID筛选数据
                if (_selectedMidiTrackIDs.Count > 0)
                {
                    // 多选模式
                    _filteredMidiTrackList = _allMidiTrackList.Where(a => _selectedMidiTrackIDs.Contains(a.MidiTrackID)).OrderBy(a => a.MidiTrackID).ToList();
                    System.Diagnostics.Debug.WriteLine($"筛选多个轨道ID {string.Join(",", _selectedMidiTrackIDs)}，结果数量: {_filteredMidiTrackList.Count}");
                }
                else if (_selectedMidiTrackID > 0)
                {
                    // 单选模式
                    _filteredMidiTrackList = _allMidiTrackList.Where(a => a.MidiTrackID == _selectedMidiTrackID).ToList();
                    System.Diagnostics.Debug.WriteLine($"筛选轨道ID {_selectedMidiTrackID}，结果数量: {_filteredMidiTrackList.Count}");
                }
                else
                {
                    // 显示所有数据
                    _filteredMidiTrackList = _allMidiTrackList;
                    System.Diagnostics.Debug.WriteLine($"显示所有数据，数量: {_filteredMidiTrackList.Count}");
                }
            }

            // 重置到第一页并加载数据
            _currentPage = 0;
            LoadCurrentPage();
        }

        private void LoadCurrentPage()
        {
            if (_filteredMidiTrackList == null || _filteredMidiTrackList.Count == 0)
            {
                _mainData.ItemsSource = null;
                txtPageInfo.Text = "无数据";
                btnPreviousPage.IsEnabled = false;
                btnNextPage.IsEnabled = false;
                return;
            }

            int totalRecords = _filteredMidiTrackList.Count;
            int totalPages = (int)Math.Ceiling((double)totalRecords / _pageSize);

            // 确保当前页码在有效范围内
            if (_currentPage < 0) _currentPage = 0;
            if (_currentPage >= totalPages) _currentPage = totalPages - 1;

            int startIndex = _currentPage * _pageSize;
            int endIndex = Math.Min(startIndex + _pageSize, totalRecords);

            _midiTrackList = _filteredMidiTrackList.Skip(startIndex).Take(_pageSize).ToList();
            _mainData.ItemsSource = _midiTrackList;

            System.Diagnostics.Debug.WriteLine($"显示第 {_currentPage + 1}/{totalPages} 页，记录 {startIndex + 1}-{endIndex}/{totalRecords}");

            // 更新分页信息显示
            string filterInfo = "";
            if (_selectedMidiTrackIDs.Count > 0)
            {
                filterInfo = $" (筛选: {_selectedMidiTrackIDs.Count}个轨道)";
            }
            else if (_selectedMidiTrackID > 0)
            {
                filterInfo = $" (筛选: {_selectedInstrument})";
            }
            txtPageInfo.Text = $"第 {_currentPage + 1}/{totalPages} 页 (共 {totalRecords} 条){filterInfo}";

            // 更新按钮状态
            btnPreviousPage.IsEnabled = _currentPage > 0;
            btnNextPage.IsEnabled = _currentPage < totalPages - 1;
        }

        private void LoadNextPage()
        {
            if (_filteredMidiTrackList == null) return;

            int totalPages = (int)Math.Ceiling((double)_filteredMidiTrackList.Count / _pageSize);
            if (_currentPage < totalPages - 1)
            {
                _currentPage++;
                LoadCurrentPage();
            }
        }

        private void LoadPreviousPage()
        {
            if (_filteredMidiTrackList == null) return;

            if (_currentPage > 0)
            {
                _currentPage--;
                LoadCurrentPage();
            }
        }

        private void txtFresh_Click(object sender, RoutedEventArgs e)
        {
            this.Refresh();
        }
        int _quarternotetTime;
        private void txtCreateXML_Click(object sender, RoutedEventArgs e)
        {
            if (_midiTrackList == null)
            {
                MessageBox.Show("请先搜索MIDI文件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 打开难度设置窗口
            var settingsWindow = new DifficultySettingsWindow();
            settingsWindow.Owner = this;

            bool? dialogResult = settingsWindow.ShowDialog();
            System.Diagnostics.Debug.WriteLine($"=== 设置窗口结果 ===");
            System.Diagnostics.Debug.WriteLine($"DialogResult: {dialogResult}");
            System.Diagnostics.Debug.WriteLine($"IsConfirmed: {settingsWindow.IsConfirmed}");

            if (dialogResult == true && settingsWindow.IsConfirmed)
            {
                System.Diagnostics.Debug.WriteLine("使用用户设置创建谱面");
                // 用户确认了设置，使用新的设置创建谱面
                CreateAdvancedGameChart(settingsWindow.Settings);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("使用默认设置创建谱面");
                // 用户取消了设置，使用默认设置创建谱面
                CreateAdvancedGameChart(new DifficultySettings());
            }


        }

        /// <summary>
        /// 智能MIDI转游戏谱面转换器
        /// </summary>
        public class IntelligentMidiToGameConverter
        {
            private readonly int _quarternotetTime;
            private readonly IList<IdolTrack> _idolTracks;
            private readonly Dictionary<int, MidiTrack> _trackAnnotations;
            private readonly Random _random = new Random();
            private readonly IList<UsedTrack> _usedTracks = new List<UsedTrack>();

            public IntelligentMidiToGameConverter(int quarternotetTime, IList<IdolTrack> idolTracks, Dictionary<int, MidiTrack> trackAnnotations)
            {
                _quarternotetTime = quarternotetTime;
                _idolTracks = idolTracks;
                _trackAnnotations = trackAnnotations ?? new Dictionary<int, MidiTrack>();
            }

            public List<IIdol> ConvertToGameNotes(IList<MidiTrackList> midiNotes)
            {
                var gameNotes = new List<IIdol>();
                var repository = new IdolRepository();

                // 过滤掉未标注的轨道
                var filteredNotes = midiNotes.Where(note =>
                {
                    if (_trackAnnotations.ContainsKey(note.MidiTrackID))
                    {
                        var trackInfo = _trackAnnotations[note.MidiTrackID];
                        // 只处理已标注的轨道（非Unknown类型）
                        return trackInfo.TrackType != TrackType.Unknown;
                    }
                    return false; // 没有标注信息的轨道也不处理
                }).ToList();

                System.Diagnostics.Debug.WriteLine($"原始音符数量: {midiNotes.Count}, 过滤后音符数量: {filteredNotes.Count}");

                // 按时间排序并分组处理
                var sortedNotes = filteredNotes.OrderBy(n => n.StartTime).ToList();
                string lastGameTrack = "";
                int lastSyllabelNumber = 0;
                bool isFirst = true;

                foreach (var midiNote in sortedNotes)
                {
                    // 获取轨道标注信息
                    var trackInfo = _trackAnnotations.ContainsKey(midiNote.MidiTrackID)
                        ? _trackAnnotations[midiNote.MidiTrackID]
                        : null;

                    // 跳过未标注的轨道（双重检查）
                    if (trackInfo == null || trackInfo.TrackType == TrackType.Unknown)
                    {
                        System.Diagnostics.Debug.WriteLine($"跳过未标注轨道: MidiTrackID={midiNote.MidiTrackID}");
                        continue;
                    }

                    var idol = repository.CreateNewIdol();

                    // 计算位置信息
                    int oneBarTime = _quarternotetTime * 4;
                    idol.Bar = midiNote.StartTime / oneBarTime + 1;
                    idol.Pos = midiNote.StartTime / (_quarternotetTime * 4 / 64) % 64;
                    idol.EndBar = midiNote.EndTime / oneBarTime + 1;
                    idol.EndPos = midiNote.EndTime / (_quarternotetTime * 4 / 64) % 64;

                    // 智能确定音符类型
                    idol.NoteType = DetermineNoteType(midiNote, trackInfo);

                    // 智能分配轨道
                    if (isFirst)
                    {
                        idol.FromTrack = GetInitialTrack(midiNote, trackInfo);
                        idol.TargetTrack = idol.FromTrack;
                        isFirst = false;
                    }
                    else
                    {
                        idol.FromTrack = AssignTrackIntelligently(midiNote, trackInfo, lastGameTrack, lastSyllabelNumber);
                        idol.TargetTrack = idol.FromTrack;
                    }

                    // 处理滑动音符的结束轨道
                    if (idol.NoteType == "slip")
                    {
                        idol.EndTrack = GetSlipEndTrack(idol.FromTrack, midiNote, trackInfo);
                    }

                    // 记录使用的轨道
                    RecordUsedTrack(idol);

                    gameNotes.Add(idol);
                    lastGameTrack = idol.FromTrack;
                    lastSyllabelNumber = midiNote.SyllabelNumber;

                    System.Diagnostics.Debug.WriteLine($"添加音符: 轨道类型={trackInfo.TrackType}, 游戏轨道={idol.TargetTrack}, 音符类型={idol.NoteType}");
                }

                System.Diagnostics.Debug.WriteLine($"最终生成音符数量: {gameNotes.Count}");
                return gameNotes;
            }

            /// <summary>
            /// 智能确定音符类型
            /// </summary>
            private string DetermineNoteType(MidiTrackList midiNote, MidiTrack trackInfo)
            {
                // 根据轨道类型调整音符类型
                if (trackInfo != null)
                {
                    switch (trackInfo.TrackType)
                    {
                        case TrackType.Drums:
                        case TrackType.Percussion:
                            return "short"; // 鼓点通常是短音符

                        case TrackType.MainMelody:
                            // 主旋律可以有长音符和滑动
                            if (midiNote.Duration >= _quarternotetTime * 2)
                                return "long";
                            else if (_random.NextDouble() > 0.7) // 30%概率创建滑动
                                return "slip";
                            return "short";

                        case TrackType.Bass:
                            // 贝斯通常是短音符，偶尔长音符
                            return midiNote.Duration >= _quarternotetTime * 3 ? "long" : "short";

                        case TrackType.Chord:
                        case TrackType.Harmony:
                            // 和弦和和声倾向于长音符
                            return midiNote.Duration >= _quarternotetTime ? "long" : "short";

                        default:
                            break;
                    }
                }

                // 默认逻辑：根据持续时间判断
                if (midiNote.Duration >= _quarternotetTime * 2)
                    return "long";
                else if (midiNote.Duration >= _quarternotetTime && _random.NextDouble() > 0.8)
                    return "slip";
                return "short";
            }

            /// <summary>
            /// 获取初始轨道
            /// </summary>
            private string GetInitialTrack(MidiTrackList midiNote, MidiTrack trackInfo)
            {
                if (trackInfo != null)
                {
                    switch (trackInfo.TrackType)
                    {
                        case TrackType.MainMelody:
                            return "Right1"; // 主旋律放在右侧主要位置
                        case TrackType.Bass:
                            return "Left2";  // 贝斯放在左下
                        case TrackType.Drums:
                            return "Left1";  // 鼓放在左上
                        default:
                            break;
                    }
                }

                // 根据音高分配
                return AssignTrackByPitch(midiNote.SyllabelNumber);
            }

            /// <summary>
            /// 智能分配轨道
            /// </summary>
            private string AssignTrackIntelligently(MidiTrackList midiNote, MidiTrack trackInfo, string lastTrack, int lastPitch)
            {
                // 获取可用轨道
                var availableTracks = GetAvailableTracks(midiNote);

                if (availableTracks.Count == 0)
                    return lastTrack; // 没有可用轨道，使用上一个

                // 根据轨道类型优先分配
                if (trackInfo != null)
                {
                    string preferredTrack = GetPreferredTrackByType(trackInfo.TrackType, availableTracks);
                    if (!string.IsNullOrEmpty(preferredTrack))
                        return preferredTrack;
                }

                // 根据音高变化分配
                string trackByPitch = AssignTrackByMelodyDirection(midiNote.SyllabelNumber, lastPitch, lastTrack, availableTracks);
                if (availableTracks.Contains(trackByPitch))
                    return trackByPitch;

                // 随机选择可用轨道
                return availableTracks[_random.Next(availableTracks.Count)];
            }

            /// <summary>
            /// 根据轨道类型获取首选轨道
            /// </summary>
            private string GetPreferredTrackByType(TrackType trackType, List<string> availableTracks)
            {
                string[] preferences;

                switch (trackType)
                {
                    case TrackType.MainMelody:
                        preferences = new[] { "Right1", "Right2", "Left1", "Left2" };
                        break;
                    case TrackType.SubMelody:
                        preferences = new[] { "Right2", "Left1", "Right1", "Left2" };
                        break;
                    case TrackType.Bass:
                        preferences = new[] { "Left2", "Left1", "Right2", "Right1" };
                        break;
                    case TrackType.Drums:
                        preferences = new[] { "Left1", "Left2", "Right1", "Right2" };
                        break;
                    case TrackType.Harmony:
                        preferences = new[] { "Left1", "Right2", "Left2", "Right1" };
                        break;
                    case TrackType.Chord:
                        preferences = new[] { "Left2", "Right2", "Left1", "Right1" };
                        break;
                    default:
                        preferences = new[] { "Right1", "Left1", "Right2", "Left2" };
                        break;
                }

                return preferences.FirstOrDefault(p => availableTracks.Contains(p));
            }

            /// <summary>
            /// 根据音高分配轨道
            /// </summary>
            private string AssignTrackByPitch(int syllabelNumber)
            {
                // 60 = C4 为中央C
                if (syllabelNumber >= 72) // 高音区 (C5以上)
                    return "Right1";
                else if (syllabelNumber >= 60) // 中音区
                    return "Right2";
                else if (syllabelNumber >= 48) // 中低音区
                    return "Left1";
                else // 低音区
                    return "Left2";
            }

            /// <summary>
            /// 根据旋律走向分配轨道
            /// </summary>
            private string AssignTrackByMelodyDirection(int currentPitch, int lastPitch, string lastTrack, List<string> availableTracks)
            {
                int pitchDiff = currentPitch - lastPitch;

                // 音高上升，倾向于向右移动
                if (pitchDiff > 12) // 上升超过一个八度
                {
                    if (lastTrack == "Left1" && availableTracks.Contains("Right1")) return "Right1";
                    if (lastTrack == "Left2" && availableTracks.Contains("Right2")) return "Right2";
                }
                // 音高下降，倾向于向左移动
                else if (pitchDiff < -12) // 下降超过一个八度
                {
                    if (lastTrack == "Right1" && availableTracks.Contains("Left1")) return "Left1";
                    if (lastTrack == "Right2" && availableTracks.Contains("Left2")) return "Left2";
                }

                // 音高变化不大，根据音高绝对位置分配
                return AssignTrackByPitch(currentPitch);
            }

            /// <summary>
            /// 获取可用轨道
            /// </summary>
            private List<string> GetAvailableTracks(MidiTrackList midiNote)
            {
                int oneBarTime = _quarternotetTime * 4;
                int bar = midiNote.StartTime / oneBarTime + 1;
                int pos = midiNote.StartTime / (_quarternotetTime * 4 / 64) % 64;

                var usedTrackNames = GetUsedTrackNames(_usedTracks, pos, bar);
                var allTracks = new[] { "Left1", "Left2", "Right1", "Right2" };

                return allTracks.Where(t => !usedTrackNames.Contains(t)).ToList();
            }

            /// <summary>
            /// 获取已使用的轨道名称
            /// </summary>
            private IList<string> GetUsedTrackNames(IList<UsedTrack> usedTracks, double currentPos, double currentBar)
            {
                var usedTracksAtPosition = usedTracks.Where(a => a.UsedPos == currentPos && a.UsedBar == currentBar);
                if (usedTracksAtPosition == null) return new List<string>();

                IList<string> strUsedTrackNames = new List<string>();
                foreach (var usedTrack in usedTracksAtPosition)
                {
                    foreach (var trackName in usedTrack.UsedTrackName)
                    {
                        strUsedTrackNames.Add(trackName);
                    }
                }
                return strUsedTrackNames.Distinct().ToList();
            }

            /// <summary>
            /// 获取滑动音符的结束轨道
            /// </summary>
            private string GetSlipEndTrack(string fromTrack, MidiTrackList midiNote, MidiTrack trackInfo)
            {
                // 根据轨道类型和音高变化确定滑动方向
                var trackPairs = new Dictionary<string, string[]>
                {
                    { "Left1", new[] { "Left2", "Right1" } },
                    { "Left2", new[] { "Left1", "Right2" } },
                    { "Right1", new[] { "Right2", "Left1" } },
                    { "Right2", new[] { "Right1", "Left2" } }
                };

                if (trackPairs.ContainsKey(fromTrack))
                {
                    var candidates = trackPairs[fromTrack];
                    return candidates[_random.Next(candidates.Length)];
                }

                return fromTrack; // 如果找不到合适的，保持原轨道
            }

            /// <summary>
            /// 记录使用的轨道
            /// </summary>
            private void RecordUsedTrack(IIdol idol)
            {
                if (idol.NoteType == "long")
                {
                    double usedPosition = (idol.EndBar * 64 + idol.EndPos - idol.Bar * 64 + idol.Pos) / 2;
                    for (int i = 0; i < usedPosition; i++)
                    {
                        var ut = new UsedTrack
                        {
                            UsedBar = (idol.Bar * 64 + idol.Pos + i * 2) / 64,
                            UsedPos = (idol.Pos + i * 2) % 64,
                            UsedTrackName = new List<string> { idol.TargetTrack }
                        };
                        _usedTracks.Add(ut);
                    }
                }
                else if (idol.NoteType == "short")
                {
                    var ut = new UsedTrack
                    {
                        UsedBar = idol.Bar,
                        UsedPos = idol.Pos,
                        UsedTrackName = new List<string> { idol.TargetTrack }
                    };
                    _usedTracks.Add(ut);
                }
                else if (idol.NoteType == "slip")
                {
                    var ut = new UsedTrack
                    {
                        UsedBar = idol.Bar,
                        UsedPos = idol.Pos,
                        UsedTrackName = new List<string> { idol.TargetTrack, idol.EndTrack }
                    };
                    _usedTracks.Add(ut);
                }
            }
        }
        private IList<string> GetUsedTrack(IList<UsedTrack> UsedTracks, double currentPos, double currentBar)
        {
            var usedTracks = UsedTracks.Where(a => a.UsedPos == currentPos && a.UsedBar == currentBar);
            if (usedTracks == null) return new List<string>();
            IList<string> strUsedTrackNames = new List<string>();
            foreach (var usedTrack in usedTracks)
            {
                foreach (var item in usedTrack.UsedTrackName)
                {
                    strUsedTrackNames.Add(item);
                }
             
            }
            return strUsedTrackNames.Distinct().ToList();
        }
        MusicDocument _document;
        private void txtBroadcast_Click(object sender, RoutedEventArgs e)
        {
            if (_document == null)
            {
                _document = new MusicDocument();
            }
            if (_mainData.ItemsSource==null||_mainData.ItemsSource.Cast<MidiTrackList>().Count() <= 0)
                return;
            if (txtBroadcast.Content.ToString() == "播放")
            {
                int index = 0;
                if (!string.IsNullOrWhiteSpace(_selectedInstrument))
                    index = MidiTimbreMeter.instruments.ToList().IndexOf(_selectedInstrument);
                else
                    index = 0;
                _document.ChangeProgram(index);
                _document.MusicScoreInfos = GetiNote(_mainData);
                float speed = 1;
                if (!float.TryParse(txtSpeed.Text, out speed))
                    speed = 1;
                _document.Play(speed);
                txtBroadcast.Content = "暂停";
            }
            else
            {
              
                _document.Pause();
                txtBroadcast.Content = "播放";
                _document.ChangeProgram(-2);
            }
        }
        private IList<MusicScoreInfo> GetiNote(DataGrid _mainData)
        {
            IList<MusicScoreInfo> iNotes = new List<MusicScoreInfo>();
            var groupDatas = _mainData.ItemsSource.Cast<MidiTrackList>().GroupBy(a => a.StartTime).ToList();
            foreach (var groupData in groupDatas)
            {
                var maxData = groupData.Where(a => a.Duration == groupData.Max(b => b.Duration)).FirstOrDefault();
                MusicScoreInfo info = new MusicScoreInfo();
                info.Duation = maxData.Duration;
                info.Score = maxData.SyllabelNumber.ToString();
                iNotes.Add(info);
            }
            return iNotes;
        }


        private void txtStop_Click(object sender, RoutedEventArgs e)
        {
            if (_document != null)
            {
                txtBroadcast.Content = "播放";
                _document.Stop();
                _document.ChangeProgram(-2);
            }
        }

        private void btnPreviousPage_Click(object sender, RoutedEventArgs e)
        {
            LoadPreviousPage();
        }

        private void btnNextPage_Click(object sender, RoutedEventArgs e)
        {
            LoadNextPage();
        }

        private void btnDifficultySettings_Click(object sender, RoutedEventArgs e)
        {
            var settingsWindow = new DifficultySettingsWindow();
            settingsWindow.Owner = this;

            if (settingsWindow.ShowDialog() == true && settingsWindow.IsConfirmed)
            {
                // 用户确认了设置，使用新的设置创建谱面
                CreateAdvancedGameChart(settingsWindow.Settings);
            }
        }

        /// <summary>
        /// 使用高级难度设置创建谱面
        /// </summary>
        private void CreateAdvancedGameChart(DifficultySettings settings)
        {
            // 调试信息：显示接收到的设置
            System.Diagnostics.Debug.WriteLine($"=== CreateAdvancedGameChart接收到的设置 ===");
            System.Diagnostics.Debug.WriteLine($"难度等级: {settings.DifficultyLevel}");
            System.Diagnostics.Debug.WriteLine($"音符密度: {settings.NoteDensity:P0}");
            System.Diagnostics.Debug.WriteLine($"最大同时音符: {settings.MaxSimultaneousNotes}");
            System.Diagnostics.Debug.WriteLine($"启用滑动音符: {settings.EnableSlipNotes}");
            System.Diagnostics.Debug.WriteLine($"==========================================");

            if (_midiTrackList == null)
            {
                MessageBox.Show("请先搜索MIDI文件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                // 获取轨道标注信息（确保获取最新的编辑状态）
                var trackAnnotations = (_instrumentData.ItemsSource as ObservableCollection<MidiTrack>)?
                    .Where(t => t.MidiTrackID > 0)
                    .ToDictionary(t => t.MidiTrackID, t => t) ?? new Dictionary<int, MidiTrack>();

                // 获取已标注轨道的ID列表
                var annotatedTrackIds = trackAnnotations.Values
                    .Where(t => t.TrackType != TrackType.Unknown)
                    .Select(t => t.MidiTrackID)
                    .ToList();

                System.Diagnostics.Debug.WriteLine($"已标注轨道数量: {annotatedTrackIds.Count}");
                foreach (var trackId in annotatedTrackIds)
                {
                    var track = trackAnnotations[trackId];
                    System.Diagnostics.Debug.WriteLine($"轨道ID: {trackId}, 类型: {track.TrackType}, 乐器: {track.Instrument}");
                }

                // 从数据库获取完整的音符数据，但只获取已标注轨道的数据
                IList<MidiTrackList> lists;
                if (annotatedTrackIds.Count > 0)
                {
                    lists = _allMidiTrackList?.Where(note => annotatedTrackIds.Contains(note.MidiTrackID)).ToList()
                           ?? new List<MidiTrackList>();
                }
                else
                {
                    lists = new List<MidiTrackList>();
                    System.Diagnostics.Debug.WriteLine("警告: 没有已标注的轨道，将生成空谱面");
                }

                System.Diagnostics.Debug.WriteLine($"从数据库获取的音符数量: {lists.Count}");

                // 获取MIDI歌曲名称
                string songName = "Unknown";
                if (_currentMidiInfo != null && !string.IsNullOrEmpty(_currentMidiInfo.SongName))
                {
                    songName = _currentMidiInfo.SongName;
                    // 清理文件名中的非法字符
                    songName = System.IO.Path.GetInvalidFileNameChars()
                        .Aggregate(songName, (current, c) => current.Replace(c, '_'));
                }
                else if (!string.IsNullOrEmpty(_selectedInstrument))
                {
                    // 如果没有歌曲名，使用选中的乐器名作为备选
                    songName = _selectedInstrument;
                }

                // 使用高级转换器
                var converter = new AdvancedMidiToGameConverter(_quarternotetTime, settings, trackAnnotations);
                var gameNotes = converter.ConvertToGameNotes(lists);

                // 让用户选择保存位置
                var saveDialog = new Microsoft.Win32.SaveFileDialog();
                string difficultyName = GetDifficultyDisplayName(settings.DifficultyLevel);
                string trackInfo = $"{settings.TrackCount}轨{(settings.IsFourFinger ? "四指" : "")}";
                saveDialog.FileName = $"{songName}_{difficultyName}_{trackInfo}_高级谱面.xml";
                saveDialog.Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*";
                saveDialog.Title = "保存高级谱面文件";
                saveDialog.DefaultExt = "xml";

                if (saveDialog.ShowDialog() != true)
                {
                    return; // 用户取消了保存
                }

                string fileName = saveDialog.FileName;

                // 使用模板生成完整的XML文件
                GenerateXmlFromTemplate(gameNotes, fileName, songName, settings);

                // 显示转换统计信息
                ShowAdvancedConversionStats(settings, trackAnnotations, gameNotes, fileName);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建高级谱面时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 使用模板生成XML文件
        /// </summary>
        private void GenerateXmlFromTemplate(List<IIdol> gameNotes, string fileName, string songName, DifficultySettings settings)
        {
            try
            {
                // 读取模板文件
                string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Template", "a_idol.xml");
                if (!File.Exists(templatePath))
                {
                    throw new FileNotFoundException($"模板文件不存在: {templatePath}");
                }

                string templateContent = File.ReadAllText(templatePath, Encoding.UTF8);

                // 生成音符列表XML
                var noteListXml = new StringBuilder();
                foreach (var note in gameNotes)
                {
                    noteListXml.AppendLine(note.ToString());
                }

                // 替换模板中的占位符
                string finalXml = templateContent
                    .Replace("{{NoteList}}", noteListXml.ToString())
                    .Replace("{{Ttile}}", songName ?? "Unknown") // 注意模板中是Ttile不是Title
                    .Replace("{{Artist}}", _selectedInstrument ?? "Unknown");

                // 保存最终的XML文件
                File.WriteAllText(fileName, finalXml, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"生成XML文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 显示高级转换统计信息
        /// </summary>
        private void ShowAdvancedConversionStats(DifficultySettings settings, Dictionary<int, MidiTrack> trackAnnotations, List<IIdol> gameNotes, string fileName)
        {
            var annotatedTracks = trackAnnotations.Values.Where(t => t.TrackType != TrackType.Unknown).ToList();

            // 统计音符类型
            int shortNotes = gameNotes.Count(n => n.NoteType == "short");
            int longNotes = gameNotes.Count(n => n.NoteType == "long");
            int slipNotes = gameNotes.Count(n => n.NoteType == "slip");

            string statsMessage = $"高级谱面创建完成！\n\n" +
                                 $"文件保存位置: {fileName}\n\n" +
                                 $"难度设置:\n" +
                                 $"• 难度等级: {GetDifficultyDisplayName(settings.DifficultyLevel)}\n" +
                                 $"• 轨道数量: {settings.TrackCount}轨{(settings.IsFourFinger ? " (四指模式)" : "")}\n" +
                                 $"• 音符密度: {(int)(settings.NoteDensity * 100)}%\n" +
                                 $"• 最大同时音符: {settings.MaxSimultaneousNotes}个\n\n" +
                                 $"音符统计:\n" +
                                 $"• 总音符数: {gameNotes.Count}\n" +
                                 $"• 短音符: {shortNotes}\n" +
                                 $"• 长音符: {longNotes}\n" +
                                 $"• 滑动音符: {slipNotes}\n\n" +
                                 $"参与转换的轨道: {annotatedTracks.Count}个\n";

            foreach (var track in annotatedTracks)
            {
                statsMessage += $"• {track.Instrument} → {track.TrackTypeDisplay}\n";
            }

            MessageBox.Show(statsMessage, "高级谱面创建完成", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 获取难度等级显示名称
        /// </summary>
        private string GetDifficultyDisplayName(DifficultyLevel level)
        {
            switch (level)
            {
                case DifficultyLevel.Easy: return "简单";
                case DifficultyLevel.Normal: return "普通";
                case DifficultyLevel.Hard: return "困难";
                case DifficultyLevel.Expert: return "专家";
                default: return "未知";
            }
        }

    }
}
