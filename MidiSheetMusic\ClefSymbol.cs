/*
 * Copyright (c) 2007-2008 <PERSON><PERSON><PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License version 2.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace MidiSheetMusic {


public enum Clef { Treble, Bass };

/* 
 * A ClefSymbol represents either a Treble or Bass Clef image.
 * The clef can be either normal or small size.  Normal size is
 * used at the beginning of a new staff, on the left side.  The
 * small symbols are used to show clef changes within a staff.
 */

public class ClefSymbol : MusicSymbol {
    static Image treble;  /* The treble clef image */
    static Image bass;    /* The bass clef image */

    int starttime;        /* Start time of the symbol */
    bool smallsize;       /* True if this is a small clef, false otherwise */
    Clef clef;            /* The clef, Treble or Bass */
    int width;

    public ClefSymbol(Clef clef, int starttime, bool small) {
        this.clef = clef;
        this.starttime = starttime;
        smallsize = small;
        LoadImages();
        width = MinWidth;
    }

    static void LoadImages() {
        if (treble == null)
            treble = new Bitmap(typeof(ClefSymbol), "treble.bmp");

        if (bass == null)
            bass = new Bitmap(typeof(ClefSymbol), "bass.bmp");

    }

    public override int StartTime { 
        get { return starttime; }
    }

    public override int MinWidth {
        get { 
            if (smallsize)
                return SheetMusic.NoteWidth * 2;
            else
                return SheetMusic.NoteWidth * 3;
        }
    }

    public override int Width {
       get { return width; }
       set { width = value; }
    }

    public override int AboveStaff { 
        get { 
            if (clef == Clef.Treble && !smallsize)
                return SheetMusic.NoteHeight * 2;
            else
                return 0;
        }
    }
        
    public override int BelowStaff {
        get {
            if (clef == Clef.Treble && !smallsize)
                return SheetMusic.NoteHeight * 2;
            else if (clef == Clef.Treble && smallsize)
                return SheetMusic.NoteHeight;
            else
                return 0;
        }
    }

    public override 
    void Draw(Graphics g, Pen pen, int ytop) {
        g.TranslateTransform(Width - MinWidth, 0);
        int y = ytop;
        Image image;
        int height;

        /* Get the image, height, and y pixel, depending on the clef
         * and the image size.
         */
        if (clef == Clef.Treble) {
            image = treble;
            if (smallsize) {
                height = SheetMusic.StaffHeight + SheetMusic.StaffHeight/4;
            } else {
                height = 3 * SheetMusic.StaffHeight/2 + SheetMusic.NoteHeight/2;
                y = ytop - SheetMusic.NoteHeight;
            }
        }
        else {
            image = bass;
            if (smallsize) {
                height = SheetMusic.StaffHeight - 3*SheetMusic.NoteHeight/2;
            } else {
                height = SheetMusic.StaffHeight - SheetMusic.NoteHeight;
            }
        }

        int imgwidth = image.Width * height / image.Height;
        g.DrawImage(image, 0, y, imgwidth, height);
        g.TranslateTransform(-(Width - MinWidth), 0);
    }

    public override string ToString() {
        return string.Format("ClefSymbol {0} small {1} width {2}",
                             clef, smallsize, width);
    }
}


}

