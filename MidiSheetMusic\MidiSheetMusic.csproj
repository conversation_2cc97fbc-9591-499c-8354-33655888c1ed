﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="14.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{0B5CBA19-8987-4EBE-8D7F-4683FCAA5A2F}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>MidiSheetMusic</RootNamespace>
    <AssemblyName>MidiSheetMusic</AssemblyName>
    <StartupObject>MidiSheetMusic.MidiSheetMusic</StartupObject>
    <ApplicationIcon>NotePair.ico</ApplicationIcon>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>2.0</OldToolsVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AccidSymbol.cs" />
    <Compile Include="BarSymbol.cs" />
    <Compile Include="BlankSymbol.cs" />
    <Compile Include="ChordSymbol.cs" />
    <Compile Include="ClefMeasures.cs" />
    <Compile Include="ClefSymbol.cs" />
    <Compile Include="Common\Utils.cs" />
    <Compile Include="Helper\Common.cs" />
    <Compile Include="Helper\SqlHelper.cs" />
    <Compile Include="KeySignature.cs" />
    <Compile Include="MidiFile.cs" />
    <Compile Include="MidiSheetMusic.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Midi\DeviceException.cs" />
    <Compile Include="Midi\GeneralProgram.cs" />
    <Compile Include="Midi\MidiDevice.cs" />
    <Compile Include="Midi\MidiDeviceException.cs" />
    <Compile Include="Midi\MidiOutCaps.cs" />
    <Compile Include="Midi\MusicDocument.cs" />
    <Compile Include="Midi\MusicScoreInfo.cs" />
    <Compile Include="Midi\NoteTable.cs" />
    <Compile Include="Midi\OutputDeviceBase.cs" />
    <Compile Include="Model\MidiSheetMusicMidi.cs" />
    <Compile Include="Model\MidiSheetMusicMidiTrack.cs" />
    <Compile Include="Model\MidiSheetMusicMidiTrackList.cs" />
    <Compile Include="MusicSymbol.cs" />
    <Compile Include="MyAssert.cs" />
    <Compile Include="RecordMidiInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RecordMidiInfo.Designer.cs">
      <DependentUpon>RecordMidiInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="RestSymbol.cs" />
    <Compile Include="SheetMusic.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Staff.cs" />
    <Compile Include="Stem.cs" />
    <Compile Include="SymbolWidths.cs" />
    <Compile Include="TimeSignature.cs" />
    <Compile Include="WhiteNote.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="MidiSheetMusic.resx">
      <DependentUpon>MidiSheetMusic.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NotePair.ico" />
    <EmbeddedResource Include="bass.bmp" />
    <EmbeddedResource Include="RecordMidiInfo.resx">
      <DependentUpon>RecordMidiInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="treble.bmp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>