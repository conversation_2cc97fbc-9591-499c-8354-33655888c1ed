"""
命令行接口

提供命令行工具来使用AI谱面生成器
"""

import click
import logging
from pathlib import Path
from typing import Optional

from .chart_generator import AIChartGenerator
from .utils.config_manager import ConfigManager
from .utils.logger_setup import setup_logging

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='配置文件路径')
@click.option('--verbose', '-v', is_flag=True, help='详细输出')
@click.pass_context
def cli(ctx, config: Optional[str], verbose: bool):
    """AI音游写谱助手命令行工具"""
    ctx.ensure_object(dict)
    
    # 加载配置
    config_manager = ConfigManager()
    if config:
        config_manager.load_config(config)
    ctx.obj['config'] = config_manager.get_config()
    
    # 设置日志级别
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)


@cli.command()
@click.argument('midi_path', type=click.Path(exists=True))
@click.argument('output_path', type=click.Path())
@click.option('--format', '-f', 'format_name', default='malody', 
              type=click.Choice(['malody', 'rhythm_master', 'osu']),
              help='输出格式')
@click.option('--difficulty', '-d', default=5, type=click.IntRange(1, 10),
              help='难度等级 (1-10)')
@click.option('--tracks', '-t', default=4, type=click.IntRange(1, 8),
              help='轨道数量')
@click.option('--style', '-s', default='balanced',
              type=click.Choice(['balanced', 'dense', 'sparse', 'rhythmic']),
              help='生成风格')
@click.option('--title', help='歌曲标题')
@click.option('--artist', help='艺术家')
@click.option('--model', type=click.Path(exists=True), help='模型文件路径')
@click.pass_context
def generate(ctx, midi_path: str, output_path: str, format_name: str,
             difficulty: int, tracks: int, style: str,
             title: Optional[str], artist: Optional[str], model: Optional[str]):
    """从MIDI文件生成谱面"""
    
    config = ctx.obj['config']
    
    try:
        # 初始化生成器
        model_path = model or config.get('model', {}).get('generation_model_path')
        device = config.get('model', {}).get('device', 'cpu')
        
        generator = AIChartGenerator(model_path=model_path, device=device)
        
        # 生成谱面
        click.echo(f"正在生成谱面: {midi_path}")
        click.echo(f"输出格式: {format_name}")
        click.echo(f"难度等级: {difficulty}")
        click.echo(f"生成风格: {style}")
        
        success = generator.generate_from_midi(
            midi_path=midi_path,
            output_path=output_path,
            format_name=format_name,
            difficulty=difficulty,
            track_count=tracks,
            style=style,
            title=title,
            artist=artist
        )
        
        if success:
            click.echo(f"✅ 谱面生成成功: {output_path}")
        else:
            click.echo("❌ 谱面生成失败")
            ctx.exit(1)
            
    except Exception as e:
        click.echo(f"❌ 错误: {e}")
        ctx.exit(1)


@cli.command()
@click.argument('midi_path', type=click.Path(exists=True))
@click.option('--model', type=click.Path(exists=True), help='难度预测模型路径')
@click.pass_context
def predict(ctx, midi_path: str, model: Optional[str]):
    """预测MIDI文件的难度"""
    
    config = ctx.obj['config']
    
    try:
        # 初始化生成器
        generator = AIChartGenerator()
        
        # 加载难度预测器
        predictor_path = model or config.get('model', {}).get('difficulty_predictor_path')
        if predictor_path and Path(predictor_path).exists():
            generator.load_difficulty_predictor(predictor_path)
        
        # 预测难度
        click.echo(f"正在分析MIDI文件: {midi_path}")
        
        result = generator.predict_difficulty(midi_path)
        
        click.echo(f"预测难度: {result.get('difficulty', 'N/A'):.1f}")
        click.echo(f"置信度: {result.get('confidence', 0):.2f}")
        
        if 'playability' in result:
            click.echo(f"游戏性评分: {result['playability']:.2f}")
        
        if 'note_distribution' in result:
            dist = result['note_distribution']
            click.echo(f"音符分布: 空白={dist[0]:.1%}, 短音符={dist[1]:.1%}, 长音符={dist[2]:.1%}")
            
    except Exception as e:
        click.echo(f"❌ 错误: {e}")
        ctx.exit(1)


@cli.command()
@click.argument('midi_path', type=click.Path(exists=True))
@click.pass_context
def analyze(ctx, midi_path: str):
    """分析MIDI文件"""
    
    try:
        generator = AIChartGenerator()
        
        click.echo(f"正在分析MIDI文件: {midi_path}")
        
        analysis = generator.analyze_midi(midi_path)
        
        if not analysis:
            click.echo("❌ 分析失败")
            ctx.exit(1)
        
        # 显示基本信息
        basic_info = analysis.get('basic_info', {})
        click.echo("\n📊 基本信息:")
        click.echo(f"  时长: {basic_info.get('duration', 0):.1f} 秒")
        click.echo(f"  BPM: {basic_info.get('initial_bpm', 0):.1f}")
        click.echo(f"  总音符数: {basic_info.get('total_notes', 0)}")
        click.echo(f"  轨道数: {basic_info.get('total_instruments', 0)}")
        
        # 显示主旋律信息
        main_melody = analysis.get('main_melody')
        if main_melody:
            click.echo(f"\n🎵 主旋律轨道:")
            click.echo(f"  轨道名称: {main_melody.get('name', 'Unknown')}")
            click.echo(f"  音符数量: {main_melody.get('note_count', 0)}")
        
        # 显示节奏信息
        rhythm = analysis.get('rhythm_patterns', {})
        if 'note_density' in rhythm:
            click.echo(f"\n🥁 节奏信息:")
            click.echo(f"  音符密度: {rhythm['note_density']:.2f} 音符/秒")
            click.echo(f"  平均间隔: {rhythm.get('avg_interval', 0):.3f} 秒")
        
        # 显示和弦信息
        chords = analysis.get('chord_progressions', [])
        if chords:
            click.echo(f"\n🎹 和弦信息:")
            click.echo(f"  检测到 {len(chords)} 个和弦")
            
            # 显示前几个和弦
            for i, chord in enumerate(chords[:5]):
                time = chord.get('time', 0)
                chord_type = chord.get('chord_type', 'unknown')
                click.echo(f"  {time:.1f}s: {chord_type}")
            
            if len(chords) > 5:
                click.echo(f"  ... 还有 {len(chords) - 5} 个和弦")
                
    except Exception as e:
        click.echo(f"❌ 错误: {e}")
        ctx.exit(1)


@cli.command()
@click.argument('input_path', type=click.Path(exists=True))
@click.argument('output_path', type=click.Path())
@click.option('--from-format', help='源格式')
@click.option('--to-format', required=True, help='目标格式')
@click.pass_context
def convert(ctx, input_path: str, output_path: str, from_format: Optional[str], to_format: str):
    """转换谱面格式"""
    
    try:
        generator = AIChartGenerator()
        
        click.echo(f"正在转换谱面格式:")
        click.echo(f"  输入: {input_path}")
        click.echo(f"  输出: {output_path}")
        click.echo(f"  源格式: {from_format or '自动检测'}")
        click.echo(f"  目标格式: {to_format}")
        
        success = generator.convert_chart(
            input_path=input_path,
            output_path=output_path,
            target_format=to_format,
            source_format=from_format
        )
        
        if success:
            click.echo("✅ 格式转换成功")
        else:
            click.echo("❌ 格式转换失败")
            ctx.exit(1)
            
    except Exception as e:
        click.echo(f"❌ 错误: {e}")
        ctx.exit(1)


@cli.command()
@click.pass_context
def formats(ctx):
    """显示支持的格式"""
    
    try:
        generator = AIChartGenerator()
        
        supported_formats = generator.get_supported_formats()
        
        click.echo("📋 支持的格式:")
        
        for format_name in supported_formats:
            info = generator.get_format_info(format_name)
            if info:
                click.echo(f"\n  {format_name}:")
                click.echo(f"    描述: {info.get('description', 'N/A')}")
                
                extensions = info.get('supported_extensions', [])
                if extensions:
                    click.echo(f"    扩展名: {', '.join(extensions)}")
                
                features = info.get('features', [])
                if features:
                    click.echo(f"    特性:")
                    for feature in features:
                        click.echo(f"      • {feature}")
                        
    except Exception as e:
        click.echo(f"❌ 错误: {e}")
        ctx.exit(1)


@cli.command()
@click.pass_context
def styles(ctx):
    """显示可用的生成风格"""
    
    try:
        generator = AIChartGenerator()
        
        styles = generator.get_generation_styles()
        
        click.echo("🎨 可用的生成风格:")
        
        for style in styles:
            description = generator.get_style_description(style)
            click.echo(f"  {style}: {description}")
            
    except Exception as e:
        click.echo(f"❌ 错误: {e}")
        ctx.exit(1)


@cli.command()
@click.argument('midi_dir', type=click.Path(exists=True))
@click.argument('output_dir', type=click.Path())
@click.option('--format', '-f', 'format_name', default='malody',
              type=click.Choice(['malody', 'rhythm_master', 'osu']),
              help='输出格式')
@click.option('--difficulty', '-d', default=5, type=click.IntRange(1, 10),
              help='难度等级')
@click.option('--style', '-s', default='balanced',
              type=click.Choice(['balanced', 'dense', 'sparse', 'rhythmic']),
              help='生成风格')
@click.option('--parallel', '-p', is_flag=True, help='并行处理')
@click.pass_context
def batch(ctx, midi_dir: str, output_dir: str, format_name: str,
          difficulty: int, style: str, parallel: bool):
    """批量生成谱面"""
    
    try:
        midi_path = Path(midi_dir)
        output_path = Path(output_dir)
        
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 查找MIDI文件
        midi_files = list(midi_path.glob("*.mid")) + list(midi_path.glob("*.midi"))
        
        if not midi_files:
            click.echo("❌ 未找到MIDI文件")
            ctx.exit(1)
        
        click.echo(f"找到 {len(midi_files)} 个MIDI文件")
        
        generator = AIChartGenerator()
        
        success_count = 0
        
        with click.progressbar(midi_files, label='生成谱面') as files:
            for midi_file in files:
                try:
                    output_file = output_path / f"{midi_file.stem}.{format_name}"
                    
                    success = generator.generate_from_midi(
                        midi_path=str(midi_file),
                        output_path=str(output_file),
                        format_name=format_name,
                        difficulty=difficulty,
                        style=style
                    )
                    
                    if success:
                        success_count += 1
                        
                except Exception as e:
                    logger.error(f"处理文件失败 {midi_file}: {e}")
                    continue
        
        click.echo(f"✅ 成功生成 {success_count}/{len(midi_files)} 个谱面")
        
    except Exception as e:
        click.echo(f"❌ 错误: {e}")
        ctx.exit(1)


def main():
    """主函数"""
    cli()


if __name__ == '__main__':
    main()
