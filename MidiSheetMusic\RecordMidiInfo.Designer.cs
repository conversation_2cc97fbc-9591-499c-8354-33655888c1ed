﻿namespace MidiSheetMusic
{
    partial class RecordMidiInfo
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOpen = new System.Windows.Forms.Button();
            this.btnReocrd = new System.Windows.Forms.Button();
            this.txtSongName = new System.Windows.Forms.TextBox();
            this.SuspendLayout();
            // 
            // btnOpen
            // 
            this.btnOpen.Location = new System.Drawing.Point(97, 189);
            this.btnOpen.Name = "btnOpen";
            this.btnOpen.Size = new System.Drawing.Size(75, 23);
            this.btnOpen.TabIndex = 0;
            this.btnOpen.Text = "打开";
            this.btnOpen.UseVisualStyleBackColor = true;
            this.btnOpen.Click += new System.EventHandler(this.btnOpen_Click);
            // 
            // btnReocrd
            // 
            this.btnReocrd.Location = new System.Drawing.Point(338, 189);
            this.btnReocrd.Name = "btnReocrd";
            this.btnReocrd.Size = new System.Drawing.Size(75, 23);
            this.btnReocrd.TabIndex = 1;
            this.btnReocrd.Text = "记录";
            this.btnReocrd.UseVisualStyleBackColor = true;
            this.btnReocrd.Click += new System.EventHandler(this.btnReocrd_Click);
            // 
            // txtSongName
            // 
            this.txtSongName.Location = new System.Drawing.Point(175, 96);
            this.txtSongName.Name = "txtSongName";
            this.txtSongName.Size = new System.Drawing.Size(148, 21);
            this.txtSongName.TabIndex = 2;
            // 
            // RecordMidiInfo
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(484, 261);
            this.Controls.Add(this.txtSongName);
            this.Controls.Add(this.btnReocrd);
            this.Controls.Add(this.btnOpen);
            this.Name = "RecordMidiInfo";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "RecordMidiInfo";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnOpen;
        private System.Windows.Forms.Button btnReocrd;
        private System.Windows.Forms.TextBox txtSongName;
    }
}