<Window x:Class="MyWPF.Layout.AI.AIChatDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="AI制谱助手" Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBlock Text="🤖 AI制谱助手"
                           FontSize="18"
                           FontWeight="Bold"
                           VerticalAlignment="Center"/>
                <TextBlock Name="lblStatus" Text="● 已连接"
                           Foreground="Green"
                           VerticalAlignment="Center"
                           Margin="20,0,0,0"/>
            </StackPanel>

            <Button Grid.Column="1" Name="btnSettings"
                    ToolTip="设置" Click="BtnSettings_Click"
                    Padding="5">
                <TextBlock Text="⚙️"/>
            </Button>
        </Grid>

        <!-- 聊天区域 -->
        <Border Grid.Row="1"
                Background="White"
                CornerRadius="8"
                Padding="10"
                BorderBrush="LightGray"
                BorderThickness="1">
            <ScrollViewer Name="chatScrollViewer" 
                          VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled">
                <StackPanel Name="chatPanel" Margin="5">
                    <!-- 欢迎消息会在代码中动态添加 -->
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- 快捷操作区域 -->
        <Grid Grid.Row="2" Margin="0,10,0,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 快捷按钮 -->
            <WrapPanel Grid.Row="0" HorizontalAlignment="Left">
                <Button Name="btnQuickGenerate" Content="继续写谱"
                        Margin="0,0,10,0" Click="BtnQuickGenerate_Click"
                        Padding="8,4"/>
                <Button Name="btnAnalyzeChart" Content="分析当前谱面"
                        Margin="0,0,10,0" Click="BtnAnalyzeChart_Click"
                        Padding="8,4"/>
                <Button Name="btnOptimize" Content="优化难度"
                        Margin="0,0,10,0" Click="BtnOptimize_Click"
                        Padding="8,4"/>
                <Button Name="btnTemplates" Content="📝 模板"
                        Margin="0,0,10,0" Click="BtnTemplates_Click"
                        Padding="8,4"/>
            </WrapPanel>

            <!-- 模板选择区域 -->
            <Expander Name="templateExpander" Grid.Row="1" Header="常用模板" Margin="0,5,0,0" IsExpanded="False">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 分类选择 -->
                    <Label Content="选择模板分类:" Grid.Row="0" Margin="0,0,0,5"/>
                    <ComboBox Name="cmbTemplateCategory"
                              Grid.Row="0"
                              Margin="0,25,0,10"
                              SelectionChanged="CmbTemplateCategory_SelectionChanged"/>

                    <!-- 模板列表 -->
                    <ListBox Name="lstTemplates"
                             Grid.Row="1"
                             MaxHeight="150"
                             ScrollViewer.VerticalScrollBarVisibility="Auto"
                             SelectionChanged="LstTemplates_SelectionChanged">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Margin="5">
                                    <TextBlock Text="{Binding Title}" FontWeight="Bold"/>
                                    <TextBlock Text="{Binding Description}"
                                               Foreground="Gray"
                                               TextWrapping="Wrap"/>
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </Expander>
        </Grid>

        <!-- 输入区域 -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBox Name="txtInput"
                     Grid.Column="0"
                     Text="输入你的需求，比如：帮我写第5-10小节的铺面"
                     Foreground="Gray"
                     AcceptsReturn="True"
                     TextWrapping="Wrap"
                     MaxHeight="100"
                     VerticalScrollBarVisibility="Auto"
                     KeyDown="TxtInput_KeyDown"
                     GotFocus="TxtInput_GotFocus"
                     LostFocus="TxtInput_LostFocus"/>

            <Button Name="btnAttach"
                    Grid.Column="1"
                    ToolTip="附加文件或选择音符范围"
                    Margin="10,0,0,0"
                    Click="BtnAttach_Click"
                    Padding="5">
                <TextBlock Text="📎"/>
            </Button>

            <Button Name="btnSend"
                    Grid.Column="2"
                    ToolTip="发送 (Ctrl+Enter)"
                    Margin="5,0,0,0"
                    Click="BtnSend_Click"
                    Padding="5">
                <TextBlock Text="📤"/>
            </Button>
        </Grid>

        <!-- 加载指示器 -->
        <Grid Name="loadingOverlay"
              Grid.Row="0" Grid.RowSpan="4"
              Background="#80000000"
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Value="0"
                             IsIndeterminate="True"
                             Width="50" Height="50"/>
                <TextBlock Text="AI正在思考中..."
                           Foreground="White"
                           HorizontalAlignment="Center"
                           Margin="0,10,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
