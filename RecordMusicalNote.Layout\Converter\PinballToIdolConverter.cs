using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml;
using RecordMusicalNote.DataModel;

namespace MyWPF.Layout.Converter
{
    /// <summary>
    /// 弹珠转星动转换器
    /// 将弹珠谱转换为星动谱格式
    /// </summary>
    public class PinballToIdolConverter
    {
        #region 转换设置

        public class ConversionSettings
        {
            /// <summary>轨道映射策略</summary>
            public TrackMappingStrategy TrackStrategy { get; set; } = TrackMappingStrategy.BalancedMapping;
            
            /// <summary>长音符处理策略</summary>
            public LongNoteStrategy LongNoteHandling { get; set; } = LongNoteStrategy.PreserveDuration;
        }

        public enum TrackMappingStrategy
        {
            BalancedMapping,    // 平衡映射到4轨道
            LeftRightMapping,   // 左右区域映射
            RandomMapping       // 随机映射
        }

        public enum LongNoteStrategy
        {
            PreserveDuration,   // 保持原有时长
            ConvertToShort      // 转换为短音符
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 弹珠转星动
        /// </summary>
        /// <param name="inputFilePath">输入的弹珠XML文件路径</param>
        /// <param name="settings">转换设置</param>
        public static void ConvertPinballToIdol(string inputFilePath, ConversionSettings settings = null)
        {
            if (settings == null)
                settings = new ConversionSettings();

            try
            {
                // 1. 解析弹珠XML
                var pinballNotes = ParsePinballXml(inputFilePath);
                Console.WriteLine($"[DEBUG] 解析到 {pinballNotes.Count} 个弹珠音符");

                // 2. 转换为星动音符
                var idolNotes = ConvertNotes(pinballNotes, settings);
                Console.WriteLine($"[DEBUG] 转换为 {idolNotes.Count} 个星动音符");

                // 3. 生成输出文件名（使用idol开头）
                string fileName = System.IO.Path.GetFileNameWithoutExtension(inputFilePath);
                string directory = System.IO.Path.GetDirectoryName(inputFilePath);
                
                // 移除pinball前缀（如果存在）
                if (fileName.StartsWith("pinball_"))
                    fileName = fileName.Substring(8);
                
                string outputPath = System.IO.Path.Combine(directory, $"idol_from_pinball_{fileName}.xml");

                // 4. 保存星动XML
                SaveIdolXmlWithOriginalStructure(inputFilePath, outputPath, idolNotes);
                
                Console.WriteLine($"[DEBUG] 弹珠转星动完成，输出文件: {outputPath}");
            }
            catch (Exception ex)
            {
                throw new Exception($"弹珠转星动失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 解析弹珠XML
        /// </summary>
        private static List<PinBallNoteInfo> ParsePinballXml(string filePath)
        {
            var notes = new List<PinBallNoteInfo>();
            var doc = new XmlDocument();
            doc.Load(filePath);

            var noteNodes = doc.SelectNodes("//Note");
            foreach (XmlNode node in noteNodes)
            {
                var note = new PinBallNoteInfo
                {
                    bar = int.Parse(node.Attributes["Bar"]?.Value ?? "1"),
                    pos = int.Parse(node.Attributes["Pos"]?.Value ?? "0"),
                    NoteType = ConvertNoteTypeFromXml(node.Attributes["note_type"]?.Value ?? "PinballSingle"),
                    EndBar = ParseNullableInt(node.Attributes["EndBar"]?.Value),
                    EndPos = ParseNullableInt(node.Attributes["EndPos"]?.Value),
                    EndArea = node.Attributes["EndArea"]?.Value ?? "",
                    son = ParseNullableInt(node.Attributes["Son"]?.Value),
                    MoveTime = int.Parse(node.Attributes["MoveTime"]?.Value ?? "3")
                };
                
                Console.WriteLine($"[DEBUG] 解析弹珠音符: Bar={note.bar}, Pos={note.pos}, Type={note.NoteType}, EndArea={note.EndArea}");
                notes.Add(note);
            }

            return notes.OrderBy(n => n.bar).ThenBy(n => n.pos).ToList();
        }

        /// <summary>
        /// 转换音符
        /// </summary>
        private static List<IdolNoteInfo> ConvertNotes(List<PinBallNoteInfo> pinballNotes, ConversionSettings settings)
        {
            var idolNotes = new List<IdolNoteInfo>();
            var random = new Random(42); // 固定种子确保一致性

            foreach (var pinballNote in pinballNotes)
            {
                var idolNote = ConvertSingleNote(pinballNote, settings, random);
                if (idolNote != null)
                {
                    idolNotes.Add(idolNote);
                }
            }

            return idolNotes;
        }

        /// <summary>
        /// 转换单个音符
        /// </summary>
        private static IdolNoteInfo ConvertSingleNote(PinBallNoteInfo pinballNote, ConversionSettings settings, Random random)
        {
            // 根据EndArea确定轨道
            string trackName = MapEndAreaToTrack(pinballNote.EndArea, settings, random);
            
            var idolNote = new IdolNoteInfo
            {
                Bar = pinballNote.bar,
                Pos = pinballNote.pos,
                FromTrack = trackName,
                TargetTrack = trackName,
                EndTrack = trackName,
                NoteType = ConvertNoteTypeToIdol(pinballNote.NoteType, settings)
            };

            // 处理长音符
            if (pinballNote.NoteType == "3" && settings.LongNoteHandling == LongNoteStrategy.PreserveDuration)
            {
                idolNote.NoteType = "long";
                idolNote.EndBar = pinballNote.EndBar ?? pinballNote.bar;
                idolNote.EndPos = pinballNote.EndPos ?? pinballNote.pos;
                Console.WriteLine($"[DEBUG] 转换长音符: Bar={idolNote.Bar}, EndBar={idolNote.EndBar}, EndPos={idolNote.EndPos}");
            }

            return idolNote;
        }

        /// <summary>
        /// 将EndArea映射到星动轨道
        /// </summary>
        private static string MapEndAreaToTrack(string endArea, ConversionSettings settings, Random random)
        {
            switch (endArea)
            {
                case "":
                    // 轨道0，映射到中间轨道
                    return random.Next(0, 2) == 0 ? "Left1" : "Right1";
                    
                case "1|2":
                    // 左区域
                    return random.Next(0, 2) == 0 ? "Left1" : "Left2";
                    
                case "3|4":
                    // 右区域
                    return random.Next(0, 2) == 0 ? "Right1" : "Right2";
                    
                default:
                    return "Left1"; // 默认值
            }
        }

        /// <summary>
        /// 转换音符类型到星动格式
        /// </summary>
        private static string ConvertNoteTypeToIdol(string pinballNoteType, ConversionSettings settings)
        {
            switch (pinballNoteType)
            {
                case "1": // PinballSingle
                    return "short";
                case "2": // PinballSlip
                    return "slip";
                case "3": // PinballLong
                    return settings.LongNoteHandling == LongNoteStrategy.PreserveDuration ? "long" : "short";
                default:
                    return "short";
            }
        }

        /// <summary>
        /// 从XML转换音符类型
        /// </summary>
        private static string ConvertNoteTypeFromXml(string xmlNoteType)
        {
            switch (xmlNoteType)
            {
                case "PinballSingle":
                    return "1";
                case "PinballSlip":
                    return "2";
                case "PinballLong":
                    return "3";
                default:
                    return "1";
            }
        }

        /// <summary>
        /// 解析可空整数
        /// </summary>
        private static int? ParseNullableInt(string value)
        {
            if (string.IsNullOrEmpty(value))
                return null;
            
            if (int.TryParse(value, out int result))
                return result;
                
            return null;
        }

        /// <summary>
        /// 保存星动XML（保留原文件结构）
        /// </summary>
        private static void SaveIdolXmlWithOriginalStructure(string originalFilePath, string outputPath, List<IdolNoteInfo> notes)
        {
            var doc = new XmlDocument();
            doc.Load(originalFilePath);

            // 找到NoteInfo节点并清空
            var noteInfoNode = doc.SelectSingleNode("//NoteInfo");
            if (noteInfoNode != null)
            {
                noteInfoNode.RemoveAll();

                // 创建Normal节点
                var normalNode = doc.CreateElement("Normal");
                noteInfoNode.AppendChild(normalNode);

                // 添加转换后的星动音符
                int noteId = 1;
                foreach (var note in notes)
                {
                    var noteElement = doc.CreateElement("Note");
                    noteElement.SetAttribute("ID", noteId.ToString());
                    noteElement.SetAttribute("Bar", note.Bar.ToString());
                    noteElement.SetAttribute("Pos", note.Pos.ToString());
                    noteElement.SetAttribute("from_track", note.FromTrack);
                    noteElement.SetAttribute("target_track", note.TargetTrack);
                    noteElement.SetAttribute("note_type", note.NoteType);

                    // 处理长音符的EndBar和EndPos
                    if (note.NoteType == "long")
                    {
                        if (note.EndBar.HasValue)
                            noteElement.SetAttribute("EndBar", note.EndBar.ToString());
                        if (note.EndPos.HasValue)
                            noteElement.SetAttribute("EndPos", note.EndPos.ToString());
                    }

                    normalNode.AppendChild(noteElement);
                    noteId++;
                }
            }

            // 保存文件
            doc.Save(outputPath);
        }

        #endregion
    }
}
