/*
 * Copyright (c) 2007-2008 <PERSON><PERSON><PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License version 2.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Windows.Forms;

namespace MidiSheetMusic {

/* Accidentals */
public enum Accid {
    None, Sharp, Flat, Natural
}

/* Enumeration of the notes in a scale */
public class NoteScale {
    public const int A      = 0;
    public const int Asharp = 1;
    public const int Bflat  = 1;
    public const int B      = 2;
    public const int C      = 3;
    public const int Csharp = 4;
    public const int Dflat  = 4;
    public const int D      = 5;
    public const int Dsharp = 6;
    public const int Eflat  = 6;
    public const int E      = 7;
    public const int F      = 8;
    public const int Fsharp = 9;
    public const int Gflat  = 9;
    public const int G      = 10;
    public const int Gsharp = 11;
    public const int Aflat  = 11;

    public static int ToNumber(int notescale, int octave) {
        return 9 + notescale + octave * 12;
    }

    public static int FromNumber(int number) {
        return (number + 3) % 12;
    }
}

/*
 * The KeySignature class represents a key signature, like G Major
 * or B-flat Major.  For sheet music, we only care about the number
 * of sharps or flats in the key signature, not whether it is major
 * or minor.
 *
 * The main operations of this class are:
 * - Guessing the key signature, given the notes in a song.
 * - Generating the accidental symbols for the key signature.
 * - Determining whether a particular note requires an accidental
 *   or not.
 *
 */

public class KeySignature {
    /* The List of sharp and flat major keys, and the
     * number of sharps/flats in each key.
     */

    /* Sharp keys */
    public const int C = 0;
    public const int G = 1;
    public const int D = 2;
    public const int A = 3;
    public const int E = 4;
    public const int B = 5;

    /* Flat keys */
    public const int F = 1;
    public const int Bflat = 2;
    public const int Eflat = 3;
    public const int Aflat = 4;
    public const int Dflat = 5;
    public const int Gflat = 6;

    /* The two arrays below are key maps.  They take a major key
     * (like G major, B-flat major) and a note in the scale, and
     * return the Accidental required to display that note in the
     * given key.  In a nutshel, the map is
     *
     *   map[Key][NoteScale] -> Accidental
     */
    static Accid[][] sharpkeys;
    static Accid[][] flatkeys;

    /* Number of sharps/flats in the key, 0 thru 6.  A key signature
     * cannot have both sharps and flats, only one.
     */
    int num_flats;
    int num_sharps;

    /* The symbols to display the key signature on a staff. */
    AccidSymbol[] treble;
    AccidSymbol[] bass;

    /* The key map for this key signature:
     *   keymap[notenumber] -> Accidental
     */
    Accid[] keymap;

    /* The measure used in the previous call to GetAccidental() */
    int prevmeasure; 


    /* Create new key signature, with the given number of
     * sharps and flats.  One of the two must be 0, you can't
     * have both sharps and flats in the key signature.
     */
    public KeySignature(int num_sharps, int num_flats) {
        MyAssert.Arg(num_sharps == 0 || num_flats == 0, "");
        this.num_sharps = num_sharps;
        this.num_flats = num_flats;

        CreateAccidentalMaps();
        keymap = new Accid[129];
        ResetKeyMap();
        CreateSymbols();
    }

    /* Iniitalize the sharpkeys and flatkeys maps */
    static void CreateAccidentalMaps() {
        if (sharpkeys != null)
            return; 

        Accid[] map;
        sharpkeys = new Accid[8][];
        flatkeys = new Accid[8][];

        for (int i = 0; i < 8; i++) {
            sharpkeys[i] = new Accid[12];
            flatkeys[i] = new Accid[12];
        }

        map = sharpkeys[C];
        map[ NoteScale.A ]      = Accid.None;
        map[ NoteScale.Asharp ] = Accid.Sharp;
        map[ NoteScale.B ]      = Accid.None;
        map[ NoteScale.C ]      = Accid.None;
        map[ NoteScale.Csharp ] = Accid.Sharp;
        map[ NoteScale.D ]      = Accid.None;
        map[ NoteScale.Dsharp ] = Accid.Sharp;
        map[ NoteScale.E ]      = Accid.None;
        map[ NoteScale.F ]      = Accid.None;
        map[ NoteScale.Fsharp ] = Accid.Sharp;
        map[ NoteScale.G ]      = Accid.None;
        map[ NoteScale.Gsharp ] = Accid.Sharp;

        map = sharpkeys[G];
        map[ NoteScale.A ]      = Accid.None;
        map[ NoteScale.Asharp ] = Accid.Sharp;
        map[ NoteScale.B ]      = Accid.None;
        map[ NoteScale.C ]      = Accid.None;
        map[ NoteScale.Csharp ] = Accid.Sharp;
        map[ NoteScale.D ]      = Accid.None;
        map[ NoteScale.Dsharp ] = Accid.Sharp;
        map[ NoteScale.E ]      = Accid.None;
        map[ NoteScale.F ]      = Accid.Natural;
        map[ NoteScale.Fsharp ] = Accid.None;
        map[ NoteScale.G ]      = Accid.None;
        map[ NoteScale.Gsharp ] = Accid.Sharp;

        map = sharpkeys[D];
        map[ NoteScale.A ]      = Accid.None;
        map[ NoteScale.Asharp ] = Accid.Sharp;
        map[ NoteScale.B ]      = Accid.None;
        map[ NoteScale.C ]      = Accid.Natural;
        map[ NoteScale.Csharp ] = Accid.None;
        map[ NoteScale.D ]      = Accid.None;
        map[ NoteScale.Dsharp ] = Accid.Sharp;
        map[ NoteScale.E ]      = Accid.None;
        map[ NoteScale.F ]      = Accid.Natural;
        map[ NoteScale.Fsharp ] = Accid.None;
        map[ NoteScale.G ]      = Accid.None;
        map[ NoteScale.Gsharp ] = Accid.Sharp;

        map = sharpkeys[A];
        map[ NoteScale.A ]      = Accid.None;
        map[ NoteScale.Asharp ] = Accid.Sharp;
        map[ NoteScale.B ]      = Accid.None;
        map[ NoteScale.C ]      = Accid.Natural;
        map[ NoteScale.Csharp ] = Accid.None;
        map[ NoteScale.D ]      = Accid.None;
        map[ NoteScale.Dsharp ] = Accid.Sharp;
        map[ NoteScale.E ]      = Accid.None;
        map[ NoteScale.F ]      = Accid.Natural;
        map[ NoteScale.Fsharp ] = Accid.None;
        map[ NoteScale.G ]      = Accid.Natural;
        map[ NoteScale.Gsharp ] = Accid.None;

        map = sharpkeys[E];
        map[ NoteScale.A ]      = Accid.None;
        map[ NoteScale.Asharp ] = Accid.Sharp;
        map[ NoteScale.B ]      = Accid.None;
        map[ NoteScale.C ]      = Accid.Natural;
        map[ NoteScale.Csharp ] = Accid.None;
        map[ NoteScale.D ]      = Accid.Natural;
        map[ NoteScale.Dsharp ] = Accid.None;
        map[ NoteScale.E ]      = Accid.None;
        map[ NoteScale.F ]      = Accid.Natural;
        map[ NoteScale.Fsharp ] = Accid.None;
        map[ NoteScale.G ]      = Accid.Natural;
        map[ NoteScale.Gsharp ] = Accid.None;

        map = sharpkeys[B];
        map[ NoteScale.A ]      = Accid.Natural;
        map[ NoteScale.Asharp ] = Accid.None;
        map[ NoteScale.B ]      = Accid.None;
        map[ NoteScale.C ]      = Accid.Natural;
        map[ NoteScale.Csharp ] = Accid.None;
        map[ NoteScale.D ]      = Accid.Natural;
        map[ NoteScale.Dsharp ] = Accid.None;
        map[ NoteScale.E ]      = Accid.None;
        map[ NoteScale.F ]      = Accid.Natural;
        map[ NoteScale.Fsharp ] = Accid.None;
        map[ NoteScale.G ]      = Accid.Natural;
        map[ NoteScale.Gsharp ] = Accid.None;

        /* Flat keys */

        map = flatkeys[C];
        map[ NoteScale.A ]      = Accid.None;
        map[ NoteScale.Bflat ]  = Accid.Flat;
        map[ NoteScale.B ]      = Accid.None;
        map[ NoteScale.C ]      = Accid.None;
        map[ NoteScale.Dflat ]  = Accid.Flat;
        map[ NoteScale.D ]      = Accid.None;
        map[ NoteScale.Eflat ]  = Accid.Flat;
        map[ NoteScale.E ]      = Accid.None;
        map[ NoteScale.F ]      = Accid.None;
        map[ NoteScale.Gflat ]  = Accid.Flat;
        map[ NoteScale.G ]      = Accid.None;
        map[ NoteScale.Aflat ]  = Accid.Flat;

        map = flatkeys[F];
        map[ NoteScale.A ]      = Accid.None;
        map[ NoteScale.Bflat ]  = Accid.None;
        map[ NoteScale.B ]      = Accid.Natural;
        map[ NoteScale.C ]      = Accid.None;
        map[ NoteScale.Dflat ]  = Accid.Flat;
        map[ NoteScale.D ]      = Accid.None;
        map[ NoteScale.Eflat ]  = Accid.Flat;
        map[ NoteScale.E ]      = Accid.None;
        map[ NoteScale.F ]      = Accid.None;
        map[ NoteScale.Gflat ]  = Accid.Flat;
        map[ NoteScale.G ]      = Accid.None;
        map[ NoteScale.Aflat ]  = Accid.Flat;

        map = flatkeys[Bflat];
        map[ NoteScale.A ]      = Accid.None;
        map[ NoteScale.Bflat ]  = Accid.None;
        map[ NoteScale.B ]      = Accid.Natural;
        map[ NoteScale.C ]      = Accid.None;
        map[ NoteScale.Dflat ]  = Accid.Flat;
        map[ NoteScale.D ]      = Accid.None;
        map[ NoteScale.Eflat ]  = Accid.None;
        map[ NoteScale.E ]      = Accid.Natural;
        map[ NoteScale.F ]      = Accid.None;
        map[ NoteScale.Gflat ]  = Accid.Flat;
        map[ NoteScale.G ]      = Accid.None;
        map[ NoteScale.Aflat ]  = Accid.Flat;

        map = flatkeys[Eflat];
        map[ NoteScale.A ]      = Accid.Natural;
        map[ NoteScale.Bflat ]  = Accid.None;
        map[ NoteScale.B ]      = Accid.Natural;
        map[ NoteScale.C ]      = Accid.None;
        map[ NoteScale.Dflat ]  = Accid.Flat;
        map[ NoteScale.D ]      = Accid.None;
        map[ NoteScale.Eflat ]  = Accid.None;
        map[ NoteScale.E ]      = Accid.Natural;
        map[ NoteScale.F ]      = Accid.None;
        map[ NoteScale.Gflat ]  = Accid.Flat;
        map[ NoteScale.G ]      = Accid.None;
        map[ NoteScale.Aflat ]  = Accid.None;

        map = flatkeys[Aflat];
        map[ NoteScale.A ]      = Accid.Natural;
        map[ NoteScale.Bflat ]  = Accid.None;
        map[ NoteScale.B ]      = Accid.Natural;
        map[ NoteScale.C ]      = Accid.None;
        map[ NoteScale.Dflat ]  = Accid.None;
        map[ NoteScale.D ]      = Accid.Natural;
        map[ NoteScale.Eflat ]  = Accid.None;
        map[ NoteScale.E ]      = Accid.Natural;
        map[ NoteScale.F ]      = Accid.None;
        map[ NoteScale.Gflat ]  = Accid.Flat;
        map[ NoteScale.G ]      = Accid.None;
        map[ NoteScale.Aflat ]  = Accid.None;

        map = flatkeys[Dflat];
        map[ NoteScale.A ]      = Accid.Natural;
        map[ NoteScale.Bflat ]  = Accid.None;
        map[ NoteScale.B ]      = Accid.Natural;
        map[ NoteScale.C ]      = Accid.None;
        map[ NoteScale.Dflat ]  = Accid.None;
        map[ NoteScale.D ]      = Accid.Natural;
        map[ NoteScale.Eflat ]  = Accid.None;
        map[ NoteScale.E ]      = Accid.Natural;
        map[ NoteScale.F ]      = Accid.None;
        map[ NoteScale.Gflat ]  = Accid.None;
        map[ NoteScale.G ]      = Accid.Natural;
        map[ NoteScale.Aflat ]  = Accid.None;

        map = flatkeys[Gflat];
        map[ NoteScale.A ]      = Accid.Natural;
        map[ NoteScale.Bflat ]  = Accid.None;
        map[ NoteScale.B ]      = Accid.None;
        map[ NoteScale.C ]      = Accid.Natural;
        map[ NoteScale.Dflat ]  = Accid.None;
        map[ NoteScale.D ]      = Accid.Natural;
        map[ NoteScale.Eflat ]  = Accid.None;
        map[ NoteScale.E ]      = Accid.Natural;
        map[ NoteScale.F ]      = Accid.None;
        map[ NoteScale.Gflat ]  = Accid.None;
        map[ NoteScale.G ]      = Accid.Natural;
        map[ NoteScale.Aflat ]  = Accid.None;


    }

    void ResetKeyMap()
    {
        Accid[] key;
        if (num_flats > 0)
            key = flatkeys[num_flats];
        else
            key = sharpkeys[num_sharps];

        for (int notenumber = 0; notenumber < 128; notenumber++) {
            int notescale = (notenumber + 3) % 12;
            keymap[notenumber] = key[notescale];
        }
    }


    /* Create the Accidental symbols for this key, for
     * the treble and bass clefs.
     */
    void CreateSymbols() {
        int count = Math.Max(num_sharps, num_flats);
        treble = new AccidSymbol[count];
        bass = new AccidSymbol[count];

        if (count == 0) {
            return;
        }

        WhiteNote[] treblenotes = null;
        WhiteNote[] bassnotes = null;

        if (num_sharps > 0)  {
            treblenotes = new WhiteNote[] {
                new WhiteNote(WhiteNote.F, 5),
                new WhiteNote(WhiteNote.C, 5),
                new WhiteNote(WhiteNote.G, 5),
                new WhiteNote(WhiteNote.D, 5),
                new WhiteNote(WhiteNote.A, 6),
                new WhiteNote(WhiteNote.E, 5)
            };
            bassnotes = new WhiteNote[] {
                new WhiteNote(WhiteNote.F, 3),
                new WhiteNote(WhiteNote.C, 3),
                new WhiteNote(WhiteNote.G, 3),
                new WhiteNote(WhiteNote.D, 3),
                new WhiteNote(WhiteNote.A, 4),
                new WhiteNote(WhiteNote.E, 3)
            };
        }
        else if (num_flats > 0) {
            treblenotes = new WhiteNote[] {
                new WhiteNote(WhiteNote.B, 5),
                new WhiteNote(WhiteNote.E, 5),
                new WhiteNote(WhiteNote.A, 5),
                new WhiteNote(WhiteNote.D, 5),
                new WhiteNote(WhiteNote.G, 4),
                new WhiteNote(WhiteNote.C, 5)
            };
            bassnotes = new WhiteNote[] {
                new WhiteNote(WhiteNote.B, 3),
                new WhiteNote(WhiteNote.E, 3),
                new WhiteNote(WhiteNote.A, 3),
                new WhiteNote(WhiteNote.D, 3),
                new WhiteNote(WhiteNote.G, 2),
                new WhiteNote(WhiteNote.C, 3)
            };
        }

        Accid a = Accid.None;
        if (num_sharps > 0)
            a = Accid.Sharp;
        else
            a = Accid.Flat;

        for (int i = 0; i < count; i++) {
            treble[i] = new AccidSymbol(a, treblenotes[i], Clef.Treble);
            bass[i] = new AccidSymbol(a, bassnotes[i], Clef.Bass);
        }
    }

    /* Return the Accidental symbols for displaying this key signature
     * for the given clef.
     */
    public AccidSymbol[] GetSymbols(Clef clef) {
        if (clef == Clef.Treble)
            return treble;
        else
            return bass;
    }

    /* Given a midi note number, return the accidental (if any)
     * that should be used when displaying the note in this key 
     * signature.
     *
     * The current measure is also required.  Once we return an
     * accidental for a measure, the accidental remains for the
     * rest of the measure. So we must update the current keymap
     * with any accidentals that we return.  When we move to another
     * measure, we reset the keymap back to the key signature.
     */
    public Accid GetAccidental(int notenumber, int measure) {
        if (measure != prevmeasure) {
            ResetKeyMap();
            prevmeasure = measure;
        }

        Accid result = keymap[notenumber];
        if (result == Accid.Sharp) {
            keymap[notenumber] = Accid.None;
            keymap[notenumber-1] = Accid.Natural;
        }
        else if (result == Accid.Flat) {
            keymap[notenumber] = Accid.None;
            keymap[notenumber+1] = Accid.Natural;
        }
        else if (result == Accid.Natural && num_flats > 0) {
            keymap[notenumber] = Accid.None;
            keymap[notenumber-1] = Accid.Flat;
        }
        else if (result == Accid.Natural && num_sharps >= 0) {
            keymap[notenumber] = Accid.None;
            keymap[notenumber+1] = Accid.Sharp;
        }
        return result;
    }


    /* Given a midi note number, return the white note (the
     * non-sharp/non-flat note) that should be used when displaying
     * this note in this key signature.
     */
    public WhiteNote GetWhiteNote(int notenumber) {
        int notescale = (notenumber + 3) % 12;
        int octave = (notenumber + 3) / 12 - 1;
        int letter = 0;

        int[] whole_sharps = { 
            WhiteNote.A, WhiteNote.A, 
            WhiteNote.B, 
            WhiteNote.C, WhiteNote.C,
            WhiteNote.D, WhiteNote.D,
            WhiteNote.E,
            WhiteNote.F, WhiteNote.F,
            WhiteNote.G, WhiteNote.G
        };

        int[] whole_flats = {
            WhiteNote.A, 
            WhiteNote.B, WhiteNote.B,
            WhiteNote.C,
            WhiteNote.D, WhiteNote.D,
            WhiteNote.E, WhiteNote.E,
            WhiteNote.F,
            WhiteNote.G, WhiteNote.G,
            WhiteNote.A
        };

        if (num_flats > 0) {
            letter = whole_flats[notescale];
        } else {
            letter = whole_sharps[notescale];
        }

        if (num_flats == Gflat && notescale == NoteScale.B) {
            letter++;
        }
        else if (num_flats > 0 && notescale == NoteScale.Aflat) {
            octave++;
        }
        return new WhiteNote(letter, octave);
    }

    /* Guess the key signature, given the midi note numbers used in
     * the song.
     */
    public static KeySignature Guess(List<int> notes) {
        CreateAccidentalMaps();

        int[] notecount = new int[12];
        for (int i = 0; i < notes.Count; i++) {
            int notenumber = notes[i];
            int notescale = (notenumber + 3) % 12;
            notecount[notescale] += 1;
        }
        int bestkey = 0;
        bool is_best_sharp = true;
        int best_accid_count = notes.Count;
        int key;

        for (key = 0; key < 6; key++) {
            int accid_count = 0;
            for (int n = 0; n < 12; n++) {
                if (sharpkeys[key][n] != Accid.None) {
                    accid_count += notecount[n];
                }
            }
            if (accid_count < best_accid_count) {
                best_accid_count = accid_count;
                bestkey = key;
                is_best_sharp = true;
            }
        }

        for (key = 0; key < 7; key++) {
            int accid_count = 0;
            for (int n = 0; n < 12; n++) {
                if (flatkeys[key][n] != Accid.None) {
                    accid_count += notecount[n];
                }
            }
            if (accid_count < best_accid_count) {
                best_accid_count = accid_count;
                bestkey = key;
                is_best_sharp = false;
            }
        }
        if (is_best_sharp) {
            return new KeySignature(bestkey, 0);
        }
        else {
            return new KeySignature(0, bestkey);
        }
    }

    public bool Equals(KeySignature k) {
        if (k.num_sharps == num_sharps && k.num_flats == num_flats)
            return true;
        else
            return false;
    }

    /* Return a string representation of this key signature.
     * We only return the major key signature, not the minor one.
     */
    public override string ToString() {

        string[] flatmajor = { 
            "C major", "F major", "B-flat major", "E-flat major",
            "A-flat major", "D-flat major", "G-flat major", "C-flat major" };

        string[] sharpmajor = {
            "C major", "G major", "D major", "A major", "E major",
            "B major", "F# major", "C# major", "G# major", "D# major" };

        if (num_flats > 0)
            return flatmajor[num_flats];
        else
            return sharpmajor[num_sharps];
    } 

}

}
