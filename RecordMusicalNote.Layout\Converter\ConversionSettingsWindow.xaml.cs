using System;
using System.Text;
using System.Windows;

namespace MyWPF.Layout.Converter
{
    /// <summary>
    /// ConversionSettingsWindow.xaml 的交互逻辑
    /// </summary>
    public partial class ConversionSettingsWindow : Window
    {
        public IdolToPinballConverter.ConversionSettings Settings { get; private set; }
        public bool DialogResult { get; private set; } = false;

        public ConversionSettingsWindow()
        {
            InitializeComponent();
            InitializeEvents();
            UpdatePreview();
        }

        private void InitializeEvents()
        {
            // 绑定滑块值变化事件
            sliderDifficulty.ValueChanged += (s, e) => 
            {
                lblDifficultyValue.Text = $"{sliderDifficulty.Value:F1}x";
                UpdatePreview();
            };

            sliderTrack0Rate.ValueChanged += (s, e) => 
            {
                lblTrack0Value.Text = $"{(int)(sliderTrack0Rate.Value * 100)}%";
                UpdatePreview();
            };

            sliderLeftRightBalance.ValueChanged += (s, e) => 
            {
                lblBalanceValue.Text = $"{(int)(sliderLeftRightBalance.Value * 100)}%";
                UpdatePreview();
            };

            sliderMaxCombo.ValueChanged += (s, e) => 
            {
                lblComboValue.Text = ((int)sliderMaxCombo.Value).ToString();
                UpdatePreview();
            };

            // 绑定单选按钮事件
            rbSplitToMultiple.Checked += (s, e) => UpdatePreview();
            rbSimplifyToLine.Checked += (s, e) => UpdatePreview();
            rbConvertToCombo.Checked += (s, e) => UpdatePreview();
        }

        private void UpdatePreview()
        {
            var settings = GetCurrentSettings();
            var preview = GeneratePreviewText(settings);
            txtPreview.Text = preview;
        }

        private IdolToPinballConverter.ConversionSettings GetCurrentSettings()
        {
            var strategy = IdolToPinballConverter.CurveHandlingStrategy.SplitToMultiple;
            if (rbSimplifyToLine.IsChecked == true)
                strategy = IdolToPinballConverter.CurveHandlingStrategy.SimplifyToLine;
            else if (rbConvertToCombo.IsChecked == true)
                strategy = IdolToPinballConverter.CurveHandlingStrategy.ConvertToCombo;

            return new IdolToPinballConverter.ConversionSettings
            {
                DifficultyFactor = (float)sliderDifficulty.Value,
                Track0Rate = (float)sliderTrack0Rate.Value,
                LeftRightBalance = (float)sliderLeftRightBalance.Value,
                MaxComboLength = (int)sliderMaxCombo.Value,
                CurveStrategy = strategy,
                PreserveCurves = strategy != IdolToPinballConverter.CurveHandlingStrategy.SimplifyToLine
            };
        }

        private string GeneratePreviewText(IdolToPinballConverter.ConversionSettings settings)
        {
            var sb = new StringBuilder();
            sb.AppendLine("=== 转换设置预览 ===");
            sb.AppendLine();

            // 难度设置
            sb.AppendLine("🎯 难度调整:");
            if (settings.DifficultyFactor < 0.8f)
                sb.AppendLine("  • 降低难度 - 减少音符密度，简化操作");
            else if (settings.DifficultyFactor > 1.2f)
                sb.AppendLine("  • 提高难度 - 增加音符密度，添加额外挑战");
            else
                sb.AppendLine("  • 保持原难度 - 维持原有音符分布");
            sb.AppendLine($"  • 难度系数: {settings.DifficultyFactor:F1}x");
            sb.AppendLine();

            // 拐弯处理策略
            sb.AppendLine("🔄 拐弯音符处理:");
            switch (settings.CurveStrategy)
            {
                case IdolToPinballConverter.CurveHandlingStrategy.SplitToMultiple:
                    sb.AppendLine("  • 拆分策略 - 拐弯音符分解为多个弹珠");
                    sb.AppendLine("  • 保持复杂性，增加可玩性");
                    break;
                case IdolToPinballConverter.CurveHandlingStrategy.SimplifyToLine:
                    sb.AppendLine("  • 简化策略 - 拐弯音符转为直线滑动");
                    sb.AppendLine("  • 降低复杂度，适合休闲玩家");
                    break;
                case IdolToPinballConverter.CurveHandlingStrategy.ConvertToCombo:
                    sb.AppendLine("  • 连击策略 - 拐弯音符转为连击序列");
                    sb.AppendLine("  • 增强连击感，提升爽快度");
                    break;
            }
            sb.AppendLine();

            // 轨道分布
            sb.AppendLine("🎮 轨道分布:");
            sb.AppendLine("  • 主要使用区域1|2和3|4");
            sb.AppendLine($"  • 轨道0使用率: {(int)(settings.Track0Rate * 100)}%");
            sb.AppendLine($"  • 左右平衡: {(int)(settings.LeftRightBalance * 100)}%偏向1|2区域");
            sb.AppendLine("  • 智能冲突检测：自动避免音符重叠");
            sb.AppendLine();

            // 连击设置
            sb.AppendLine("🔗 连击设置:");
            sb.AppendLine($"  • 最大连击长度: {settings.MaxComboLength}个音符");
            sb.AppendLine("  • 滑动弹珠将通过SonId连接后续音符");
            sb.AppendLine();

            // 预期效果
            sb.AppendLine("✨ 预期效果:");
            if (settings.DifficultyFactor < 1.0f)
                sb.AppendLine("  • 适合新手玩家，操作简化");
            else if (settings.DifficultyFactor > 1.0f)
                sb.AppendLine("  • 适合高手玩家，挑战性增强");
            else
                sb.AppendLine("  • 平衡的游戏体验");

            if (settings.Track0Rate > 0.15f)
                sb.AppendLine("  • 轨道变化丰富，增加趣味性");
            else
                sb.AppendLine("  • 专注主要轨道，节奏感强");

            return sb.ToString();
        }

        private void BtnPreview_Click(object sender, RoutedEventArgs e)
        {
            UpdatePreview();
            MessageBox.Show("预览已更新！请查看右侧预览区域。", "预览", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnOK_Click(object sender, RoutedEventArgs e)
        {
            Settings = GetCurrentSettings();
            DialogResult = true;
            Close();
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
