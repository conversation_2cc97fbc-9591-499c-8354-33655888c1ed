"""
音频-谱面数据集

用于GAN训练的数据集，支持MP3音频和对应的谱面数据
"""

import torch
from torch.utils.data import Dataset
import librosa
import numpy as np
from pathlib import Path
import json
import logging
from typing import Dict, List, Tuple, Optional, Any
import random

from ..format_converters import import_chart_from_file

logger = logging.getLogger(__name__)


class AudioChartDataset(Dataset):
    """音频-谱面数据集"""
    
    def __init__(
        self,
        data_dir: str,
        max_length: int = 2000,
        sample_rate: int = 22050,
        track_count: int = 4,
        augment: bool = True
    ):
        """
        初始化数据集
        
        Args:
            data_dir: 数据目录，包含audio和charts子目录
            max_length: 最大序列长度
            sample_rate: 音频采样率
            track_count: 轨道数量
            augment: 是否进行数据增强
        """
        self.data_dir = Path(data_dir)
        self.max_length = max_length
        self.sample_rate = sample_rate
        self.track_count = track_count
        self.augment = augment
        
        # 数据目录
        self.audio_dir = self.data_dir / "audio"
        self.charts_dir = self.data_dir / "charts"
        
        # 加载数据对
        self.data_pairs = self._load_data_pairs()
        
        logger.info(f"加载了 {len(self.data_pairs)} 个音频-谱面数据对")
    
    def _load_data_pairs(self) -> List[Dict[str, Any]]:
        """加载音频-谱面数据对"""
        data_pairs = []
        
        if not self.audio_dir.exists() or not self.charts_dir.exists():
            logger.warning(f"数据目录不存在: {self.audio_dir} 或 {self.charts_dir}")
            return data_pairs
        
        # 查找音频文件
        audio_extensions = ['.mp3', '.wav', '.flac', '.m4a']
        audio_files = []
        for ext in audio_extensions:
            audio_files.extend(self.audio_dir.glob(f"*{ext}"))
        
        # 查找对应的谱面文件
        chart_extensions = ['.imd', '.xml', '.mc', '.osu', '.json']
        
        for audio_file in audio_files:
            base_name = audio_file.stem
            
            # 查找对应的谱面文件
            chart_file = None
            for ext in chart_extensions:
                potential_chart = self.charts_dir / f"{base_name}{ext}"
                if potential_chart.exists():
                    chart_file = potential_chart
                    break
            
            if chart_file:
                # 尝试加载谱面获取元数据
                try:
                    chart_data = import_chart_from_file(str(chart_file))
                    if chart_data:
                        data_pairs.append({
                            'audio_path': str(audio_file),
                            'chart_path': str(chart_file),
                            'title': chart_data.metadata.title,
                            'artist': chart_data.metadata.artist,
                            'difficulty': chart_data.metadata.difficulty,
                            'bpm': chart_data.metadata.bpm,
                            'duration': chart_data.metadata.duration
                        })
                except Exception as e:
                    logger.warning(f"加载谱面失败 {chart_file}: {e}")
            else:
                logger.warning(f"未找到对应谱面: {base_name}")
        
        return data_pairs
    
    def __len__(self) -> int:
        return len(self.data_pairs)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """获取数据项"""
        data_pair = self.data_pairs[idx]
        
        try:
            # 加载音频特征
            audio_features = self._load_audio_features(data_pair['audio_path'])
            
            # 加载谱面数据
            chart_data, quality_score = self._load_chart_data(data_pair['chart_path'])
            
            # 数据增强
            if self.augment and random.random() > 0.5:
                audio_features, chart_data = self._augment_data(audio_features, chart_data)
            
            # 确保长度一致
            min_length = min(audio_features.shape[1], chart_data.shape[0])
            target_length = min(min_length, self.max_length)
            
            audio_features = audio_features[:, :target_length]
            chart_data = chart_data[:target_length]
            
            # 填充到固定长度
            if target_length < self.max_length:
                # 音频特征填充
                pad_audio = torch.zeros(audio_features.shape[0], self.max_length - target_length)
                audio_features = torch.cat([audio_features, pad_audio], dim=1)
                
                # 谱面数据填充
                pad_chart = torch.zeros(self.max_length - target_length, self.track_count)
                chart_data = torch.cat([chart_data, pad_chart], dim=0)
            
            return {
                'audio_features': audio_features,  # [features, time]
                'chart': chart_data,  # [time, tracks]
                'quality': torch.tensor(quality_score, dtype=torch.float32),
                'difficulty': torch.tensor(data_pair['difficulty'] - 1, dtype=torch.long),  # 0-9
                'bpm': torch.tensor(data_pair['bpm'], dtype=torch.float32),
                'title': data_pair['title'],
                'artist': data_pair['artist']
            }
            
        except Exception as e:
            logger.error(f"加载数据失败 {idx}: {e}")
            # 返回空数据
            return self._get_empty_sample()
    
    def _load_audio_features(self, audio_path: str) -> torch.Tensor:
        """加载音频特征"""
        # 加载音频
        y, sr = librosa.load(audio_path, sr=self.sample_rate)
        
        # 提取多种特征
        features = []
        
        # 1. Mel频谱图 (64维)
        mel_spec = librosa.feature.melspectrogram(y=y, sr=sr, n_mels=64, hop_length=512)
        mel_spec_db = librosa.power_to_db(mel_spec, ref=np.max)
        features.append(mel_spec_db)
        
        # 2. MFCC (13维)
        mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13, hop_length=512)
        features.append(mfcc)
        
        # 3. 色度特征 (12维)
        chroma = librosa.feature.chroma(y=y, sr=sr, hop_length=512)
        features.append(chroma)
        
        # 4. 频谱质心 (1维)
        spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr, hop_length=512)
        features.append(spectral_centroids)
        
        # 5. 频谱带宽 (1维)
        spectral_bandwidth = librosa.feature.spectral_bandwidth(y=y, sr=sr, hop_length=512)
        features.append(spectral_bandwidth)
        
        # 6. 频谱滚降 (1维)
        spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr, hop_length=512)
        features.append(spectral_rolloff)
        
        # 7. 零交叉率 (1维)
        zcr = librosa.feature.zero_crossing_rate(y, hop_length=512)
        features.append(zcr)
        
        # 8. 节拍跟踪
        tempo, beats = librosa.beat.beat_track(y=y, sr=sr, hop_length=512)
        beat_features = np.zeros((1, mel_spec.shape[1]))
        beat_frames = librosa.time_to_frames(librosa.frames_to_time(beats), sr=sr, hop_length=512)
        for beat_frame in beat_frames:
            if beat_frame < beat_features.shape[1]:
                beat_features[0, beat_frame] = 1.0
        features.append(beat_features)
        
        # 合并所有特征
        combined_features = np.vstack(features)  # [features, time]
        
        return torch.FloatTensor(combined_features)
    
    def _load_chart_data(self, chart_path: str) -> Tuple[torch.Tensor, float]:
        """加载谱面数据"""
        chart_data = import_chart_from_file(chart_path)
        if not chart_data:
            raise ValueError(f"无法加载谱面: {chart_path}")
        
        # 转换为张量格式
        notes = chart_data.get_all_notes()
        
        # 计算时间范围
        if not notes:
            raise ValueError(f"谱面无音符: {chart_path}")
        
        max_time = max(note.time + note.duration for note in notes)
        time_resolution = 0.05  # 50ms分辨率
        time_steps = int(max_time / time_resolution) + 1
        
        # 创建谱面矩阵
        chart_matrix = np.zeros((time_steps, self.track_count))
        
        for note in notes:
            start_step = int(note.time / time_resolution)
            if note.note_type == 1:  # 短音符
                if start_step < time_steps and note.track < self.track_count:
                    chart_matrix[start_step, note.track] = 1
            elif note.note_type == 2:  # 长音符
                end_step = int((note.time + note.duration) / time_resolution)
                for step in range(start_step, min(end_step + 1, time_steps)):
                    if note.track < self.track_count:
                        chart_matrix[step, note.track] = 2
        
        # 计算质量分数（基于音符密度、分布等）
        quality_score = self._calculate_quality_score(chart_matrix)
        
        return torch.FloatTensor(chart_matrix), quality_score
    
    def _calculate_quality_score(self, chart_matrix: np.ndarray) -> float:
        """计算谱面质量分数"""
        # 音符密度
        note_density = np.mean(chart_matrix > 0)
        
        # 轨道分布均匀性
        track_distribution = np.sum(chart_matrix > 0, axis=0)
        track_balance = 1.0 - np.std(track_distribution) / (np.mean(track_distribution) + 1e-6)
        
        # 时间分布均匀性
        time_distribution = np.sum(chart_matrix > 0, axis=1)
        time_smoothness = 1.0 / (1.0 + np.std(np.diff(time_distribution)))
        
        # 综合质量分数 (0-10)
        quality_score = (
            note_density * 3 +
            track_balance * 3 +
            time_smoothness * 4
        )
        
        return min(10.0, max(0.0, quality_score))
    
    def _augment_data(
        self, 
        audio_features: torch.Tensor, 
        chart_data: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """数据增强"""
        # 时间拉伸（同时影响音频和谱面）
        if random.random() > 0.7:
            stretch_factor = random.uniform(0.9, 1.1)
            
            # 音频特征时间拉伸
            new_length = int(audio_features.shape[1] * stretch_factor)
            audio_features = torch.nn.functional.interpolate(
                audio_features.unsqueeze(0), 
                size=new_length, 
                mode='linear'
            ).squeeze(0)
            
            # 谱面时间拉伸
            chart_data = torch.nn.functional.interpolate(
                chart_data.transpose(0, 1).unsqueeze(0), 
                size=new_length, 
                mode='nearest'
            ).squeeze(0).transpose(0, 1)
        
        # 音频特征噪声
        if random.random() > 0.8:
            noise = torch.randn_like(audio_features) * 0.01
            audio_features = audio_features + noise
        
        return audio_features, chart_data
    
    def _get_empty_sample(self) -> Dict[str, torch.Tensor]:
        """获取空样本"""
        return {
            'audio_features': torch.zeros(95, self.max_length),  # 95个特征维度
            'chart': torch.zeros(self.max_length, self.track_count),
            'quality': torch.tensor(0.0, dtype=torch.float32),
            'difficulty': torch.tensor(0, dtype=torch.long),
            'bpm': torch.tensor(120.0, dtype=torch.float32),
            'title': 'Unknown',
            'artist': 'Unknown'
        }
