﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="MusicModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2008" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="BubbleNoteInfo">
          <Key>
            <PropertyRef Name="BubbleNoteInfoID" />
          </Key>
          <Property Name="BubbleNoteInfoID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="LevelInfoId" Type="int" />
          <Property Name="Bar" Type="int" />
          <Property Name="BeatPos" Type="int" />
          <Property Name="Track" Type="int" />
          <Property Name="Type" Type="int" />
          <Property Name="EndBar" Type="int" />
          <Property Name="EndPod" Type="int" />
          <Property Name="ID" Type="int" />
          <Property Name="MoveTrackNameId" Type="int" />
          <Property Name="MoveTrackDegree" Type="int" />
          <Property Name="ScreenPosX" Type="float" />
          <Property Name="ScreenPosY" Type="float" />
          <Property Name="FlyTrackNameId" Type="int" />
          <Property Name="FlyTrackDegree" Type="int" />
        </EntityType>
        <EntityType Name="IdolNoteInfo">
          <Key>
            <PropertyRef Name="IdolNoteInfoID" />
          </Key>
          <Property Name="IdolNoteInfoID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="LevelInfoId" Type="int" />
          <Property Name="Bar" Type="int" />
          <Property Name="Pos" Type="int" />
          <Property Name="FromTrack" Type="nvarchar" MaxLength="10" />
          <Property Name="TargetTrack" Type="nvarchar" MaxLength="10" />
          <Property Name="EndTrack" Type="nvarchar" MaxLength="10" />
          <Property Name="EndBar" Type="int" />
          <Property Name="EndPos" Type="int" />
          <Property Name="NoteType" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="CombineNoteNum" Type="int" />
        </EntityType>
        <EntityType Name="LevelInfo">
          <Key>
            <PropertyRef Name="LevelInfoId" />
          </Key>
          <Property Name="LevelInfoId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="BPM" Type="float" />
          <Property Name="BeatPerBar" Type="int" />
          <Property Name="BeatLen" Type="int" />
          <Property Name="EnterTimeAdjust" Type="int" />
          <Property Name="NotePreShow" Type="int" />
          <Property Name="LevelTime" Type="int" />
          <Property Name="BarAmount" Type="int" />
          <Property Name="BeginBarLen" Type="int" />
          <Property Name="IsFourTrack" Type="bit" />
          <Property Name="TrackCount" Type="int" />
          <Property Name="LevelPreTime" Type="int" />
          <Property Name="Star" Type="int" />
          <Property Name="SongName" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="Artist" Type="nvarchar" MaxLength="50" Nullable="false" />
        </EntityType>
        <EntityType Name="PinBallNoteInfo">
          <Key>
            <PropertyRef Name="PinBallNoteInfoID" />
          </Key>
          <Property Name="PinBallNoteInfoID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="LevelInfoId" Type="int" />
          <Property Name="NoteType" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="bar" Type="int" />
          <Property Name="pos" Type="int" />
          <Property Name="EndBar" Type="int" />
          <Property Name="EndPos" Type="int" />
          <Property Name="son" Type="int" />
          <Property Name="EndArea" Type="nvarchar" MaxLength="10" />
          <Property Name="MoveTime" Type="int" />
        </EntityType>
        <Association Name="foreign_BubbleLevelInfoId">
          <End Role="LevelInfo" Type="Self.LevelInfo" Multiplicity="0..1" />
          <End Role="BubbleNoteInfo" Type="Self.BubbleNoteInfo" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="LevelInfo">
              <PropertyRef Name="LevelInfoId" />
            </Principal>
            <Dependent Role="BubbleNoteInfo">
              <PropertyRef Name="LevelInfoId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="foreign_IdolLevelInfoId">
          <End Role="LevelInfo" Type="Self.LevelInfo" Multiplicity="0..1" />
          <End Role="IdolNoteInfo" Type="Self.IdolNoteInfo" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="LevelInfo">
              <PropertyRef Name="LevelInfoId" />
            </Principal>
            <Dependent Role="IdolNoteInfo">
              <PropertyRef Name="LevelInfoId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="foreign_PinballLevelInfoId">
          <End Role="LevelInfo" Type="Self.LevelInfo" Multiplicity="0..1" />
          <End Role="PinBallNoteInfo" Type="Self.PinBallNoteInfo" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="LevelInfo">
              <PropertyRef Name="LevelInfoId" />
            </Principal>
            <Dependent Role="PinBallNoteInfo">
              <PropertyRef Name="LevelInfoId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="MusicModelStoreContainer">
          <EntitySet Name="BubbleNoteInfo" EntityType="Self.BubbleNoteInfo" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="IdolNoteInfo" EntityType="Self.IdolNoteInfo" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="LevelInfo" EntityType="Self.LevelInfo" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="PinBallNoteInfo" EntityType="Self.PinBallNoteInfo" Schema="dbo" store:Type="Tables" />
          <AssociationSet Name="foreign_BubbleLevelInfoId" Association="Self.foreign_BubbleLevelInfoId">
            <End Role="LevelInfo" EntitySet="LevelInfo" />
            <End Role="BubbleNoteInfo" EntitySet="BubbleNoteInfo" />
          </AssociationSet>
          <AssociationSet Name="foreign_IdolLevelInfoId" Association="Self.foreign_IdolLevelInfoId">
            <End Role="LevelInfo" EntitySet="LevelInfo" />
            <End Role="IdolNoteInfo" EntitySet="IdolNoteInfo" />
          </AssociationSet>
          <AssociationSet Name="foreign_PinballLevelInfoId" Association="Self.foreign_PinballLevelInfoId">
            <End Role="LevelInfo" EntitySet="LevelInfo" />
            <End Role="PinBallNoteInfo" EntitySet="PinBallNoteInfo" />
          </AssociationSet>
        </EntityContainer>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="MusicModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="BubbleNoteInfo">
          <Key>
            <PropertyRef Name="BubbleNoteInfoID" />
          </Key>
          <Property Name="BubbleNoteInfoID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="LevelInfoId" Type="Int32" />
          <Property Name="Bar" Type="Int32" />
          <Property Name="BeatPos" Type="Int32" />
          <Property Name="Track" Type="Int32" />
          <Property Name="Type" Type="Int32" />
          <Property Name="EndBar" Type="Int32" />
          <Property Name="EndPod" Type="Int32" />
          <Property Name="ID" Type="Int32" />
          <Property Name="MoveTrackNameId" Type="Int32" />
          <Property Name="MoveTrackDegree" Type="Int32" />
          <Property Name="ScreenPosX" Type="Double" />
          <Property Name="ScreenPosY" Type="Double" />
          <Property Name="FlyTrackNameId" Type="Int32" />
          <Property Name="FlyTrackDegree" Type="Int32" />
          <NavigationProperty Name="LevelInfo" Relationship="Self.foreign_BubbleLevelInfoId" FromRole="BubbleNoteInfo" ToRole="LevelInfo" />
        </EntityType>
        <EntityType Name="IdolNoteInfo">
          <Key>
            <PropertyRef Name="IdolNoteInfoID" />
          </Key>
          <Property Name="IdolNoteInfoID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="LevelInfoId" Type="Int32" />
          <Property Name="Bar" Type="Int32" />
          <Property Name="Pos" Type="Int32" />
          <Property Name="FromTrack" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="TargetTrack" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="EndTrack" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="EndBar" Type="Int32" />
          <Property Name="EndPos" Type="Int32" />
          <Property Name="NoteType" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="CombineNoteNum" Type="Int32" />
          <NavigationProperty Name="LevelInfo" Relationship="Self.foreign_IdolLevelInfoId" FromRole="IdolNoteInfo" ToRole="LevelInfo" />
        </EntityType>
        <EntityType Name="LevelInfo">
          <Key>
            <PropertyRef Name="LevelInfoId" />
          </Key>
          <Property Name="LevelInfoId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="BPM" Type="Double" />
          <Property Name="BeatPerBar" Type="Int32" />
          <Property Name="BeatLen" Type="Int32" />
          <Property Name="EnterTimeAdjust" Type="Int32" />
          <Property Name="NotePreShow" Type="Int32" />
          <Property Name="LevelTime" Type="Int32" />
          <Property Name="BarAmount" Type="Int32" />
          <Property Name="BeginBarLen" Type="Int32" />
          <Property Name="IsFourTrack" Type="Boolean" />
          <Property Name="TrackCount" Type="Int32" />
          <Property Name="LevelPreTime" Type="Int32" />
          <Property Name="Star" Type="Int32" />
          <Property Name="SongName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Artist" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <NavigationProperty Name="BubbleNoteInfo" Relationship="Self.foreign_BubbleLevelInfoId" FromRole="LevelInfo" ToRole="BubbleNoteInfo" />
          <NavigationProperty Name="IdolNoteInfo" Relationship="Self.foreign_IdolLevelInfoId" FromRole="LevelInfo" ToRole="IdolNoteInfo" />
          <NavigationProperty Name="PinBallNoteInfo" Relationship="Self.foreign_PinballLevelInfoId" FromRole="LevelInfo" ToRole="PinBallNoteInfo" />
        </EntityType>
        <EntityType Name="PinBallNoteInfo">
          <Key>
            <PropertyRef Name="PinBallNoteInfoID" />
          </Key>
          <Property Name="PinBallNoteInfoID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="LevelInfoId" Type="Int32" />
          <Property Name="NoteType" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="bar" Type="Int32" />
          <Property Name="pos" Type="Int32" />
          <Property Name="EndBar" Type="Int32" />
          <Property Name="EndPos" Type="Int32" />
          <Property Name="son" Type="Int32" />
          <Property Name="EndArea" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="MoveTime" Type="Int32" />
          <NavigationProperty Name="LevelInfo" Relationship="Self.foreign_PinballLevelInfoId" FromRole="PinBallNoteInfo" ToRole="LevelInfo" />
        </EntityType>
        <Association Name="foreign_BubbleLevelInfoId">
          <End Role="LevelInfo" Type="Self.LevelInfo" Multiplicity="0..1" />
          <End Role="BubbleNoteInfo" Type="Self.BubbleNoteInfo" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="LevelInfo">
              <PropertyRef Name="LevelInfoId" />
            </Principal>
            <Dependent Role="BubbleNoteInfo">
              <PropertyRef Name="LevelInfoId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="foreign_IdolLevelInfoId">
          <End Role="LevelInfo" Type="Self.LevelInfo" Multiplicity="0..1" />
          <End Role="IdolNoteInfo" Type="Self.IdolNoteInfo" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="LevelInfo">
              <PropertyRef Name="LevelInfoId" />
            </Principal>
            <Dependent Role="IdolNoteInfo">
              <PropertyRef Name="LevelInfoId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="foreign_PinballLevelInfoId">
          <End Role="LevelInfo" Type="Self.LevelInfo" Multiplicity="0..1" />
          <End Role="PinBallNoteInfo" Type="Self.PinBallNoteInfo" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="LevelInfo">
              <PropertyRef Name="LevelInfoId" />
            </Principal>
            <Dependent Role="PinBallNoteInfo">
              <PropertyRef Name="LevelInfoId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="MyMusicEntities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="BubbleNoteInfo" EntityType="Self.BubbleNoteInfo" />
          <EntitySet Name="IdolNoteInfo" EntityType="Self.IdolNoteInfo" />
          <EntitySet Name="LevelInfo" EntityType="Self.LevelInfo" />
          <EntitySet Name="PinBallNoteInfo" EntityType="Self.PinBallNoteInfo" />
          <AssociationSet Name="foreign_BubbleLevelInfoId" Association="Self.foreign_BubbleLevelInfoId">
            <End Role="LevelInfo" EntitySet="LevelInfo" />
            <End Role="BubbleNoteInfo" EntitySet="BubbleNoteInfo" />
          </AssociationSet>
          <AssociationSet Name="foreign_IdolLevelInfoId" Association="Self.foreign_IdolLevelInfoId">
            <End Role="LevelInfo" EntitySet="LevelInfo" />
            <End Role="IdolNoteInfo" EntitySet="IdolNoteInfo" />
          </AssociationSet>
          <AssociationSet Name="foreign_PinballLevelInfoId" Association="Self.foreign_PinballLevelInfoId">
            <End Role="LevelInfo" EntitySet="LevelInfo" />
            <End Role="PinBallNoteInfo" EntitySet="PinBallNoteInfo" />
          </AssociationSet>
        </EntityContainer>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="MusicModelStoreContainer" CdmEntityContainer="MyMusicEntities">
          <EntitySetMapping Name="BubbleNoteInfo">
            <EntityTypeMapping TypeName="MusicModel.BubbleNoteInfo">
              <MappingFragment StoreEntitySet="BubbleNoteInfo">
                <ScalarProperty Name="BubbleNoteInfoID" ColumnName="BubbleNoteInfoID" />
                <ScalarProperty Name="LevelInfoId" ColumnName="LevelInfoId" />
                <ScalarProperty Name="Bar" ColumnName="Bar" />
                <ScalarProperty Name="BeatPos" ColumnName="BeatPos" />
                <ScalarProperty Name="Track" ColumnName="Track" />
                <ScalarProperty Name="Type" ColumnName="Type" />
                <ScalarProperty Name="EndBar" ColumnName="EndBar" />
                <ScalarProperty Name="EndPod" ColumnName="EndPod" />
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="MoveTrackNameId" ColumnName="MoveTrackNameId" />
                <ScalarProperty Name="MoveTrackDegree" ColumnName="MoveTrackDegree" />
                <ScalarProperty Name="ScreenPosX" ColumnName="ScreenPosX" />
                <ScalarProperty Name="ScreenPosY" ColumnName="ScreenPosY" />
                <ScalarProperty Name="FlyTrackNameId" ColumnName="FlyTrackNameId" />
                <ScalarProperty Name="FlyTrackDegree" ColumnName="FlyTrackDegree" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="IdolNoteInfo">
            <EntityTypeMapping TypeName="MusicModel.IdolNoteInfo">
              <MappingFragment StoreEntitySet="IdolNoteInfo">
                <ScalarProperty Name="IdolNoteInfoID" ColumnName="IdolNoteInfoID" />
                <ScalarProperty Name="LevelInfoId" ColumnName="LevelInfoId" />
                <ScalarProperty Name="Bar" ColumnName="Bar" />
                <ScalarProperty Name="Pos" ColumnName="Pos" />
                <ScalarProperty Name="FromTrack" ColumnName="FromTrack" />
                <ScalarProperty Name="TargetTrack" ColumnName="TargetTrack" />
                <ScalarProperty Name="EndTrack" ColumnName="EndTrack" />
                <ScalarProperty Name="EndBar" ColumnName="EndBar" />
                <ScalarProperty Name="EndPos" ColumnName="EndPos" />
                <ScalarProperty Name="NoteType" ColumnName="NoteType" />
                <ScalarProperty Name="CombineNoteNum" ColumnName="CombineNoteNum" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="LevelInfo">
            <EntityTypeMapping TypeName="MusicModel.LevelInfo">
              <MappingFragment StoreEntitySet="LevelInfo">
                <ScalarProperty Name="LevelInfoId" ColumnName="LevelInfoId" />
                <ScalarProperty Name="BPM" ColumnName="BPM" />
                <ScalarProperty Name="BeatPerBar" ColumnName="BeatPerBar" />
                <ScalarProperty Name="BeatLen" ColumnName="BeatLen" />
                <ScalarProperty Name="EnterTimeAdjust" ColumnName="EnterTimeAdjust" />
                <ScalarProperty Name="NotePreShow" ColumnName="NotePreShow" />
                <ScalarProperty Name="LevelTime" ColumnName="LevelTime" />
                <ScalarProperty Name="BarAmount" ColumnName="BarAmount" />
                <ScalarProperty Name="BeginBarLen" ColumnName="BeginBarLen" />
                <ScalarProperty Name="IsFourTrack" ColumnName="IsFourTrack" />
                <ScalarProperty Name="TrackCount" ColumnName="TrackCount" />
                <ScalarProperty Name="LevelPreTime" ColumnName="LevelPreTime" />
                <ScalarProperty Name="Star" ColumnName="Star" />
                <ScalarProperty Name="SongName" ColumnName="SongName" />
                <ScalarProperty Name="Artist" ColumnName="Artist" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PinBallNoteInfo">
            <EntityTypeMapping TypeName="MusicModel.PinBallNoteInfo">
              <MappingFragment StoreEntitySet="PinBallNoteInfo">
                <ScalarProperty Name="PinBallNoteInfoID" ColumnName="PinBallNoteInfoID" />
                <ScalarProperty Name="LevelInfoId" ColumnName="LevelInfoId" />
                <ScalarProperty Name="NoteType" ColumnName="NoteType" />
                <ScalarProperty Name="bar" ColumnName="bar" />
                <ScalarProperty Name="pos" ColumnName="pos" />
                <ScalarProperty Name="EndBar" ColumnName="EndBar" />
                <ScalarProperty Name="EndPos" ColumnName="EndPos" />
                <ScalarProperty Name="son" ColumnName="son" />
                <ScalarProperty Name="EndArea" ColumnName="EndArea" />
                <ScalarProperty Name="MoveTime" ColumnName="MoveTime" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="无" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>