"""
节拍检测器

从MIDI数据中检测节拍点和强弱拍模式
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from .midi_analyzer import MidiAnalyzer

logger = logging.getLogger(__name__)


class BeatDetector:
    """节拍检测器"""
    
    def __init__(self):
        self.analyzer = MidiAnalyzer()
    
    def detect_beats(self, midi_path: str) -> Dict:
        """
        检测MIDI文件中的节拍点
        
        Args:
            midi_path: MIDI文件路径
            
        Returns:
            Dict: 节拍检测结果
        """
        if not self.analyzer.load_midi(midi_path):
            raise ValueError(f"无法加载MIDI文件: {midi_path}")
        
        logger.info(f"开始检测节拍: {midi_path}")
        
        basic_info = self.analyzer.get_basic_info()
        main_melody = self.analyzer.get_main_melody_track()
        
        if not main_melody:
            raise ValueError("没有找到主旋律轨道")
        
        # 获取BPM和时长
        bpm = basic_info['initial_bpm']
        duration = basic_info['duration']
        
        # 计算理论节拍点
        beat_interval = 60.0 / bpm  # 每拍的时间间隔（秒）
        theoretical_beats = self._generate_theoretical_beats(duration, beat_interval)
        
        # 基于音符密度检测实际节拍点
        actual_beats = self._detect_actual_beats(main_melody, beat_interval)
        
        # 分析强弱拍模式
        beat_strength = self._analyze_beat_strength(main_melody, theoretical_beats)
        
        # 检测节拍变化
        tempo_changes = self._detect_tempo_changes(main_melody)
        
        result = {
            'bpm': bpm,
            'beat_interval': beat_interval,
            'theoretical_beats': theoretical_beats,
            'actual_beats': actual_beats,
            'beat_strength': beat_strength,
            'tempo_changes': tempo_changes,
            'total_beats': len(theoretical_beats),
            'beat_accuracy': self._calculate_beat_accuracy(theoretical_beats, actual_beats)
        }
        
        logger.info("节拍检测完成")
        return result
    
    def _generate_theoretical_beats(self, duration: float, beat_interval: float) -> List[float]:
        """
        生成理论节拍点
        
        Args:
            duration: 音乐时长
            beat_interval: 节拍间隔
            
        Returns:
            List[float]: 理论节拍点时间列表
        """
        beats = []
        current_time = 0.0
        
        while current_time <= duration:
            beats.append(current_time)
            current_time += beat_interval
        
        return beats
    
    def _detect_actual_beats(self, main_melody: Dict, beat_interval: float) -> List[Dict]:
        """
        基于音符密度检测实际节拍点
        
        Args:
            main_melody: 主旋律轨道数据
            beat_interval: 理论节拍间隔
            
        Returns:
            List[Dict]: 实际节拍点信息
        """
        notes = main_melody['notes']
        if not notes:
            return []
        
        # 创建时间窗口来统计音符密度
        window_size = beat_interval / 4  # 使用1/4拍作为窗口大小
        duration = self.analyzer.get_basic_info()['duration']
        
        # 计算每个时间窗口的音符密度
        time_windows = []
        current_time = 0.0
        
        while current_time <= duration:
            # 统计当前窗口内的音符数量
            note_count = 0
            total_velocity = 0
            
            for note in notes:
                if current_time <= note['start'] < current_time + window_size:
                    note_count += 1
                    total_velocity += note['velocity']
            
            avg_velocity = total_velocity / note_count if note_count > 0 else 0
            
            time_windows.append({
                'time': current_time,
                'note_count': note_count,
                'avg_velocity': avg_velocity,
                'density_score': note_count * (avg_velocity / 127.0)  # 归一化的密度分数
            })
            
            current_time += window_size
        
        # 找出密度峰值作为节拍点
        actual_beats = []
        threshold = np.mean([w['density_score'] for w in time_windows]) * 1.2  # 阈值设为平均值的1.2倍
        
        for i, window in enumerate(time_windows):
            if window['density_score'] > threshold:
                # 检查是否是局部最大值
                is_peak = True
                for j in range(max(0, i-2), min(len(time_windows), i+3)):
                    if j != i and time_windows[j]['density_score'] > window['density_score']:
                        is_peak = False
                        break
                
                if is_peak:
                    actual_beats.append({
                        'time': window['time'],
                        'strength': window['density_score'],
                        'note_count': window['note_count'],
                        'avg_velocity': window['avg_velocity']
                    })
        
        return actual_beats
    
    def _analyze_beat_strength(self, main_melody: Dict, theoretical_beats: List[float]) -> List[Dict]:
        """
        分析每个节拍点的强度
        
        Args:
            main_melody: 主旋律轨道数据
            theoretical_beats: 理论节拍点
            
        Returns:
            List[Dict]: 节拍强度信息
        """
        notes = main_melody['notes']
        beat_strength = []
        
        for i, beat_time in enumerate(theoretical_beats):
            # 在节拍点附近寻找音符
            tolerance = 0.1  # 100ms容差
            nearby_notes = []
            
            for note in notes:
                if abs(note['start'] - beat_time) <= tolerance:
                    nearby_notes.append(note)
            
            # 计算强度
            if nearby_notes:
                total_velocity = sum(note['velocity'] for note in nearby_notes)
                avg_velocity = total_velocity / len(nearby_notes)
                strength = len(nearby_notes) * (avg_velocity / 127.0)
            else:
                strength = 0.0
            
            # 判断强弱拍（简单的4/4拍模式）
            beat_position = i % 4
            if beat_position == 0:
                beat_type = "strong"  # 强拍
            elif beat_position == 2:
                beat_type = "medium"  # 次强拍
            else:
                beat_type = "weak"    # 弱拍
            
            beat_strength.append({
                'time': beat_time,
                'strength': strength,
                'note_count': len(nearby_notes),
                'beat_type': beat_type,
                'beat_position': beat_position
            })
        
        return beat_strength
    
    def _detect_tempo_changes(self, main_melody: Dict) -> List[Dict]:
        """
        检测速度变化
        
        Args:
            main_melody: 主旋律轨道数据
            
        Returns:
            List[Dict]: 速度变化信息
        """
        notes = main_melody['notes']
        if len(notes) < 10:  # 音符太少无法检测速度变化
            return []
        
        # 计算滑动窗口内的平均间隔
        window_size = 8  # 8个音符为一个窗口
        tempo_changes = []
        
        for i in range(len(notes) - window_size):
            window_notes = notes[i:i+window_size]
            
            # 计算窗口内的平均间隔
            intervals = []
            for j in range(1, len(window_notes)):
                interval = window_notes[j]['start'] - window_notes[j-1]['start']
                intervals.append(interval)
            
            avg_interval = np.mean(intervals)
            estimated_bpm = 60.0 / avg_interval if avg_interval > 0 else 0
            
            # 如果与前一个窗口的BPM差异较大，记录为速度变化
            if tempo_changes:
                prev_bpm = tempo_changes[-1]['bpm']
                if abs(estimated_bpm - prev_bpm) > 10:  # BPM变化超过10
                    tempo_changes.append({
                        'time': window_notes[0]['start'],
                        'bpm': estimated_bpm,
                        'change': estimated_bpm - prev_bpm
                    })
            else:
                tempo_changes.append({
                    'time': window_notes[0]['start'],
                    'bpm': estimated_bpm,
                    'change': 0
                })
        
        # 过滤掉变化太小的点
        significant_changes = []
        for change in tempo_changes:
            if abs(change['change']) > 5:  # 只保留变化超过5 BPM的点
                significant_changes.append(change)
        
        return significant_changes
    
    def _calculate_beat_accuracy(self, theoretical_beats: List[float], actual_beats: List[Dict]) -> float:
        """
        计算节拍检测准确度
        
        Args:
            theoretical_beats: 理论节拍点
            actual_beats: 实际检测到的节拍点
            
        Returns:
            float: 准确度（0-1）
        """
        if not actual_beats or not theoretical_beats:
            return 0.0
        
        tolerance = 0.2  # 200ms容差
        matched_beats = 0
        
        for actual_beat in actual_beats:
            actual_time = actual_beat['time']
            
            # 找最近的理论节拍点
            min_distance = float('inf')
            for theoretical_time in theoretical_beats:
                distance = abs(actual_time - theoretical_time)
                if distance < min_distance:
                    min_distance = distance
            
            # 如果在容差范围内，算作匹配
            if min_distance <= tolerance:
                matched_beats += 1
        
        accuracy = matched_beats / len(actual_beats)
        return accuracy
    
    def generate_beat_grid(self, midi_path: str, grid_resolution: float = 0.125) -> Dict:
        """
        生成节拍网格，用于谱面生成
        
        Args:
            midi_path: MIDI文件路径
            grid_resolution: 网格分辨率（秒）
            
        Returns:
            Dict: 节拍网格信息
        """
        beat_info = self.detect_beats(midi_path)
        duration = self.analyzer.get_basic_info()['duration']
        
        # 生成网格点
        grid_points = []
        current_time = 0.0
        
        while current_time <= duration:
            # 找到最近的节拍点
            nearest_beat = None
            min_distance = float('inf')
            
            for beat in beat_info['beat_strength']:
                distance = abs(current_time - beat['time'])
                if distance < min_distance:
                    min_distance = distance
                    nearest_beat = beat
            
            # 计算网格点强度
            if nearest_beat and min_distance <= 0.5:  # 500ms内
                strength = nearest_beat['strength'] * (1.0 - min_distance / 0.5)
                beat_type = nearest_beat['beat_type']
            else:
                strength = 0.1  # 最小强度
                beat_type = "off_beat"
            
            grid_points.append({
                'time': current_time,
                'strength': strength,
                'beat_type': beat_type,
                'is_beat': min_distance <= 0.1  # 100ms内算作节拍点
            })
            
            current_time += grid_resolution
        
        return {
            'grid_points': grid_points,
            'resolution': grid_resolution,
            'total_points': len(grid_points),
            'beat_info': beat_info
        }
