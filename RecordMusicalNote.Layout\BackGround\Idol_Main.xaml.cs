﻿using Microsoft.Win32;
using MyWPF.Layout.BackGround;
using RecordMusicalNote;
using RecordMusicalNote.Library.Repository;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Xml;

namespace RecordMusicalNote.BackGround
{
    /// <summary>
    /// Main.xaml 的交互逻辑
    /// </summary>
    public partial class Idol_Main : Window
    {
        public Idol_Main()
        {
            InitializeComponent();
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            Init();
        }

        private void Init()
        {
            _repository = new IdolRepository();
            txtImport.Click += TxtImport_Click;
            txtFresh.Click += TxtFresh_Click;
            _cmbSearchType.SelectedIndex = 0;
            _searchDataGrid.MouseDoubleClick += _searchDataGrid_MouseDoubleClick;
            txtSearchConent.KeyDown += TxtSearchConent_KeyDown;
        }

        private void TxtSearchConent_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                Refresh();
            }
        }

        private void _searchDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            ILevelInfo levelInfo = _searchDataGrid.SelectedItem as ILevelInfo;
            if (levelInfo != null)
            {
                txtSongName.Text = levelInfo.SongName;
                txtArtists.Text = levelInfo.Artist;
                txtBPM.Text = levelInfo.BPM.ToString();
                txtStar.Text = levelInfo.Star.ToString();
                txtTrackCount.Text = levelInfo.TrackCount.ToString();
                var idols = _repository.GetInfo(levelInfo.LevelInfoId);
                _idolMainData.ItemsSource = idols;
            }
        }
        private void Refresh()
        {
            if (string.IsNullOrWhiteSpace(txtSearchConent.Text))
            {
                MessageBox.Show("请输入要搜索的内容");
                return;
            }
            IList<ILevelInfo> results = _repository.GetInfo(_cmbSearchType.SelectedIndex, txtSearchConent.Text);
            _searchDataGrid.ItemsSource = results;
            if (results.Count == 1)
            {
                ILevelInfo levelInfo = results.FirstOrDefault();
                if (levelInfo != null)
                {
                    txtSongName.Text = levelInfo.SongName;
                    txtArtists.Text = levelInfo.Artist;
                    txtBPM.Text = levelInfo.BPM.ToString();
                    txtStar.Text = levelInfo.Star.ToString();
                    txtTrackCount.Text = levelInfo.TrackCount.ToString();
                    var idols = _repository.GetInfo(levelInfo.LevelInfoId);
                    _idolMainData.ItemsSource = idols;
                }
            }
        }
        private void TxtFresh_Click(object sender, RoutedEventArgs e)
        {
            Refresh();
        }

        IdolRepository _repository;
        private void TxtImport_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            string filePath = dlg.FileName;
            if (!filePath.ToLower().Contains("idol"))
            {
                MessageBox.Show("不是一个idol类型的txt文件,请检查格式");
                return;
            }
            XmlDocument doc = new XmlDocument();
            doc.Load(filePath);
            XmlElement root = doc.DocumentElement;

            IList<IIdol> idols = new List<IIdol>();

            ILevelInfo leveInfo = ForeCommonMethod.GetILevelInfo(filePath);
            bool isExist = Common.CheckHasExist(leveInfo.SongName, leveInfo.Star, leveInfo.BPM);
            if (isExist)
            {
                if (MessageBox.Show("已存在改歌曲数据，是否继续?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.No) return;
            }
            XmlNodeList listNodes = null;
            listNodes = root.SelectNodes("/Level/NoteInfo/Normal");
            XmlNode listNode = listNodes[0];
            foreach (XmlNode item in listNode)
            {
                if (!item.HasChildNodes)
                {
                    IIdol idol = GetIdolFromXml(item);
                    idols.Add(idol);
                }
                else
                {
                    int i = 0;
                    foreach (XmlNode combineItem in item.ChildNodes)
                    {
                        IIdol idol = GetIdolFromXml(combineItem);
                        idol.CombineNoteNum = i;
                        i += 1;
                        idols.Add(idol);
                    }
                }

            }
            string msg = "";
            _repository.SaveInfo(idols, leveInfo, out msg);
            if (string.IsNullOrWhiteSpace(msg))
            {
                MessageBox.Show("保存成功!");
            }
            else
            {
                MessageBox.Show("保存失败!" + msg);
            }
        }


        private IIdol GetIdolFromXml(XmlNode item)
        {
            IIdol idol = _repository.CreateNewIdol();
            idol.Bar = string.IsNullOrWhiteSpace(item.Attributes["Bar"].Value) ? 0 : int.Parse(item.Attributes["Bar"].Value);
            idol.Pos = string.IsNullOrWhiteSpace(item.Attributes["Pos"].Value) ? 0 : int.Parse(item.Attributes["Pos"].Value);

            idol.TargetTrack = string.IsNullOrWhiteSpace(item.Attributes["target_track"].Value) ? "" : item.Attributes["target_track"].Value;
            idol.NoteType = string.IsNullOrWhiteSpace(item.Attributes["note_type"].Value) ? "" : item.Attributes["note_type"].Value;
            if (item.Attributes["EndBar"] != null)
                idol.EndBar = string.IsNullOrWhiteSpace(item.Attributes["EndBar"].Value) ? 0 : int.Parse(item.Attributes["EndBar"].Value);
            else
                idol.EndBar = 0;
            if (item.Attributes["EndPos"] != null)
                idol.EndPos = string.IsNullOrWhiteSpace(item.Attributes["EndPos"].Value) ? 0 : int.Parse(item.Attributes["EndPos"].Value);
            else
                idol.EndPos = 0;
            if (item.Attributes["end_track"] != null)
                idol.EndTrack = string.IsNullOrWhiteSpace(item.Attributes["end_track"].Value) ? "" : item.Attributes["end_track"].Value;
            else
                idol.EndTrack = "";
            if (item.Attributes["from_track"] != null)
                idol.FromTrack = string.IsNullOrWhiteSpace(item.Attributes["from_track"].Value) ? "" : item.Attributes["from_track"].Value;
            else
                idol.FromTrack = "";
            return idol;
        }

      
    }
}
