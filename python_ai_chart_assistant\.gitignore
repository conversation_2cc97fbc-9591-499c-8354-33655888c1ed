# AI音游写谱助手 - Git忽略文件

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre类型检查器
.pyre/

# AI/ML相关文件
# 训练数据
data/midi/
data/charts/
data/processed/
data/raw/
data/temp/

# 模型文件
models/*.pth
models/*.pt
models/*.h5
models/*.pkl
models/*.joblib
models/checkpoints/
models/tensorboard_logs/

# 输出文件
output/
results/
generated/
*.mid
*.midi
*.mc
*.imd
*.osu

# 日志文件
logs/
*.log
*.log.*

# 临时文件
temp/
tmp/
.tmp/
*.tmp
*.temp

# 缓存目录
cache/
.cache/
__cache__/

# Streamlit
.streamlit/secrets.toml

# 配置文件（包含敏感信息）
config/user_config.yaml
config/production_config.yaml
config/secrets.yaml

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*~

# 操作系统文件
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# IDE文件
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# 项目特定忽略
# 用户上传的文件
uploads/
user_data/

# 测试文件
test_output/
test_data/
*.test

# 文档生成
docs/_build/
docs/build/

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 媒体文件（大文件）
*.wav
*.mp3
*.flac
*.ogg
*.m4a

# 其他
.DS_Store?
.Spotlight-V100
.Trashes
Icon?
ehthumbs.db
Thumbs.db
