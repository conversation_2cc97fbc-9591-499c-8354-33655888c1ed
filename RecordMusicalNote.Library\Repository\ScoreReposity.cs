﻿using Helper;
using RecordMusicalNote.IRepository;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote.Library.Repository
{
    public class ScoreReposity : IScoreReposity
    {
        public bool CheckIsExist(string songName, string artist,int mode)
        {
            SqlHelper helper = new SqlHelper();
            string sql = "select count(0)  from Score where SongName='{0}' and Artist='{1}' and mode={2}";
            sql = string.Format(sql, songName, artist,mode);
            return (int)helper.ExecuteScalar(sql)>0;
        }

        public IList<IScore> GetScoreBySearch(int index, string searchContent)
        {
            IList<IScore> scores = new List<IScore>();
            string sql = "";
            if (index == 0)
            {
                sql = "select * from score where songName like '%{0}%'";
                sql = string.Format(sql, searchContent);
            }
            else if (index == 1)
            {
                sql = "select * from score where artist like '%{0}%'";
                sql = string.Format(sql, searchContent);
            }
            else
            {
                sql = "select * from score where Mode='{0}'";
                sql = string.Format(sql, searchContent);
            }
            SqlHelper helper = new SqlHelper();
            DataSet ds = helper.ExecuteDataSet(sql);
            if (ds.Tables.Count > 0)
            {
            
                DataTable dt = ds.Tables[0];
                foreach (DataRow dr in dt.Rows)
                {
                    IScore score = new Score();
                    score.Id = (int)dr[0];
                    score.SongName = dr[1].ToString();
                    score.Artist = dr[2].ToString();
                    score.Mode = (int)dr[3];
                    score.ShowTimeStartBar = (int)dr[4];
                    scores.Add(score);
                }
            }
            return scores;
        }

        public IList<IIdolScoreDetailInfo> GetIdolDetailInfoByScoreId(int scoreId)
        {
            string sql = "select  * from ScoreList where scoreId="+scoreId;
            IList<IIdolScoreDetailInfo> scoreDetails = new List<IIdolScoreDetailInfo>();
            SqlHelper helper = new SqlHelper();
            DataSet ds = helper.ExecuteDataSet(sql);
            if (ds.Tables.Count > 0)
            {

                DataTable dt = ds.Tables[0];
                foreach (DataRow dr in dt.Rows)
                {
                    IIdolScoreDetailInfo scoreDetail = new IdolScoreDetailInfo();
                    scoreDetail.Id = (int)dr[0];
                    scoreDetail.Bar = (int)dr[2];
                    scoreDetail.Pos = (int)dr[3];
                    scoreDetail.CurrentCombo = (int)dr[4];
                    scoreDetail.ShowScore = (int)dr[5];
                    scoreDetail.CurrentCount= (int)dr[6];
                    scoreDetail.NoteType = (int)dr[7];
                    scoreDetails.Add(scoreDetail);
                }
            }
            return scoreDetails;
        }

        public bool SaveScoreInfo(string songName, string artist, int showTimeStartBar,IList<IScoreDetailInfo> detailInfo,int mode)
        {
            SqlHelper helper = new SqlHelper();
            try
            {
                string sql = @"insert into Score(SongName,Artist,mode,showTimeStartBar)values('{0}','{1}',{2},{3}) select @@IDENTITY";
                sql = string.Format(sql, songName, artist,mode, showTimeStartBar);
                int primaryKey = Convert.ToInt32(helper.ExecuteScalar(sql));
                foreach (IScoreDetailInfo item in detailInfo)
                {
                    sql = @"insert into ScoreList(ScoreID,Bar,Pos,CurrentCombo,ScoreNum,CurrentCount,NoteType,SonId,XMLNoteId)
                        values({0},{1},{2},{3},{4},{5},{6},{7},{8})";
                    sql = string.Format(sql, primaryKey, item.Bar, item.Pos, item.CurrentCombo, 
                        item.ScoreNumInXXAndLab,item.CurrentCount,item.NoteType,item.SonId,item.XMLNoteId);
                    helper.ExecuteNonQuery(sql);
                }
                return true;
            }
            catch(Exception ex)
            {
                return false;
            }

        }

        public IList<IPinballScoreDetailInfo> GetPinballDetailInfoByScoreId(int scoreId)
        {
            string sql = "select  * from ScoreList where scoreId=" + scoreId;
            IList<IPinballScoreDetailInfo> scoreDetails = new List<IPinballScoreDetailInfo>();
            SqlHelper helper = new SqlHelper();
            DataSet ds = helper.ExecuteDataSet(sql);
            if (ds.Tables.Count > 0)
            {

                DataTable dt = ds.Tables[0];
                foreach (DataRow dr in dt.Rows)
                {
                    IPinballScoreDetailInfo scoreDetail = new PinballScoreDetailInfo();
                    scoreDetail.Id = (int)dr[0];
                    scoreDetail.Bar = (int)dr[2];
                    scoreDetail.Pos = (int)dr[3];
                    scoreDetail.CurrentCombo = (int)dr[4];
                    scoreDetail.ShowScore = (int)dr[5];
                    scoreDetail.CurrentCount = (int)dr[6];
                    scoreDetail.NoteType= (int)dr[7];
                    scoreDetail.SonId = (int)dr[8];
                    scoreDetail.XMLNoteId = (int)dr[9];
                    scoreDetails.Add(scoreDetail);
                }
            }
            return scoreDetails;
        }
    }
}
