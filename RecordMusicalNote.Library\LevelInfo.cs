﻿using RecordMusicalNote.DataModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote.Library
{
    public class LevelInfo : ILevelInfo
    {
        public LevelInfo()
        {
            _levelInfo.LevelInfoId = -1;
        }
        public LevelInfo(DataModel.LevelInfo levelInfo)
        {
            _levelInfo = levelInfo;
        }
        DataModel.LevelInfo _levelInfo = new DataModel.LevelInfo();
        public int LevelInfoId
        {
            get
            {
                return _levelInfo.LevelInfoId;
            }
        }
        public string Artist
        {
            get
            {
                return _levelInfo.Artist;
            }

            set
            {
                _levelInfo.Artist = value;
            }
        }

        public int BarAmount
        {
            get
            {
                return _levelInfo.BarAmount.Value;
            }

            set
            {
                _levelInfo.BarAmount = value;
            }
        }

        public int BeatLen
        {
            get
            {
                return _levelInfo.BeatLen.Value;
            }

            set
            {
                _levelInfo.BeatLen = value;
            }
        }

        public int BeatPerBar
        {
            get
            {
                return _levelInfo.BeatPerBar.Value;
            }

            set
            {
                _levelInfo.BeatPerBar = value;
            }
        }

        public int BeginBarLen
        {
            get
            {
                return _levelInfo.BeginBarLen.Value;
            }

            set
            {
                _levelInfo.BeginBarLen = value;
            }
        }

        public float BPM
        {
            get
            {
                if (_levelInfo.BPM == null) return 0;
                else
                return  float.Parse(_levelInfo.BPM.Value.ToString());
            }

            set
            {
                _levelInfo.BPM = value;
            }
        }

        public int EnterTimeAdjust
        {
            get
            {
                return _levelInfo.EnterTimeAdjust.Value;
            }

            set
            {
                _levelInfo.EnterTimeAdjust = value;
            }
        }

        public bool IsFourTrack
        {
            get
            {
                return _levelInfo.IsFourTrack.Value;
            }

            set
            {
                _levelInfo.IsFourTrack = value;
            }
        }

    

        public int LevelPreTime
        {
            get
            {
                return _levelInfo.LevelPreTime.Value;
            }

            set
            {
                _levelInfo.LevelPreTime = value;
            }
        }

        public int LevelTime
        {
            get
            {
                if (_levelInfo.LevelTime == null) return -1;
                return _levelInfo.LevelTime.Value;
            }

            set
            {
                _levelInfo.LevelTime = value;
            }
        }

        public int NotePreShow
        {
            get
            {
                return _levelInfo.NotePreShow.Value;
            }

            set
            {
                _levelInfo.NotePreShow = value;
            }
        }

        public string SongName
        {
            get
            {
                return _levelInfo.SongName;
            }

            set
            {
                _levelInfo.SongName = value;
            }
        }

        public int Star
        {
            get
            {
                return _levelInfo.Star.Value;
            }

            set
            {
                _levelInfo.Star = value;
            }
        }

        public int TrackCount
        {
            get
            {
                return _levelInfo.TrackCount.Value;
            }

            set
            {
                _levelInfo.TrackCount = value;
            }
        }
    }
}
