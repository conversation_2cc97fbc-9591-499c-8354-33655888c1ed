/*
 * Copyright (c) 2007-2008 <PERSON><PERSON><PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License version 2.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Text;


namespace MidiSheetMusic {


/* Midi File classes.
 *
 * The Midi File format is described below.  The description uses
 * the following abbreviations.
 *
 * u1     - One byte
 * u2     - Two bytes (big endian)
 * u4     - Four bytes (big endian)
 * varlen - A variable length integer, that can be 1 to 4 bytes. The 
 *          integer ends when you encounter a byte that doesn't have 
 *          the 8th bit set (a byte less than 0x80).
 * len?   - The length of the data depends on some code
 *          
 *
 * The Midi files begins with the main Midi header
 * u4 = The four ascii characters 'MThd'
 * u4 = The length of the MThd header = 6 bytes
 * u2 = 0 if the file contains a single track
 *      1 if the file contains one or more simultaneous tracks
 *      2 if the file contains one or more independent tracks
 * u2 = number of tracks
 * u2 = if >  0, the number of pulses per quarter note
 *      if <= 0, then ???
 *
 * Next come the individual Midi tracks.  The total number of Midi
 * tracks was given above, in the MThd header.  Each track starts
 * with a header:
 *
 * u4 = The four ascii characters 'MTrk'
 * u4 = Amount of track data, in bytes.
 * 
 * The track data consists of a series of Midi events.  Each Midi event
 * has the following format:
 *
 * varlen  - The time between the previous event and this event, measured
 *           in "pulses".  The number of pulses per quarter note is given
 *           in the MThd header.
 * u1      - The Event code, always betwee 0x80 and 0xFF
 * len?    - The event data.  The length of this data is determined by the
 *           event code.  The first byte of the event data is always < 0x80.
 *
 * The event code is optional.  If the event code is missing, then it
 * defaults to the previous event code.  For example:
 *
 *   varlen, eventcode1, eventdata,
 *   varlen, eventcode2, eventdata,
 *   varlen, eventdata,  // eventcode is eventcode2
 *   varlen, eventdata,  // eventcode is eventcode2
 *   varlen, eventcode3, eventdata,
 *   ....
 *
 *   How do you know if the eventcode is there or missing? Well:
 *   - All event codes are between 0x80 and 0xFF
 *   - The first byte of eventdata is always less than 0x80.
 *   So, after the varlen delta time, if the next byte is between 0x80
 *   and 0xFF, its an event code.  Otherwise, its event data.
 *
 * The Event codes and event data for each event code are shown below.
 *
 * Code:  u1 - 0x80 thru 0x8F - Note Off event.
 *             0x80 is for channel 1, 0x8F is for channel 16.
 * Data:  u1 - The note number, 0-127.  Middle C is 60 (0x3C)
 *        u1 - The note velocity.  This should be 0
 * 
 * Code:  u1 - 0x90 thru 0x9F - Note On event.
 *             0x90 is for channel 1, 0x9F is for channel 16.
 * Data:  u1 - The note number, 0-127.  Middle C is 60 (0x3C)
 *        u1 - The note velocity, from 0 (no sound) to 127 (loud).
 *             A value of 0 is equivalent to a Note Off.
 *
 * Code:  u1 - 0xA0 thru 0xAF - Key Pressure
 * Data:  u1 - The note number, 0-127.
 *        u1 - The pressure.
 *
 * Code:  u1 - 0xB0 thru 0xBF - Control Change
 * Data:  u1 - The controller number
 *        u1 - The value
 *
 * Code:  u1 - 0xC0 thru 0xCF - Program Change
 * Data:  u1 - The program number.
 *
 * Code:  u1 - 0xD0 thru 0xDF - Channel Pressure
 *        u1 - The pressure.
 *
 * Code:  u1 - 0xE0 thru 0xEF - Pitch Bend
 * Data:  u2 - Some data
 *
 * Code:  u1     - 0xFF - Meta Event
 * Data:  u1     - Metacode
 *        varlen - Length of meta event
 *        u1[varlen] - Meta event data.
 *
 *
 * The Meta Event codes are listed below:
 *
 * Metacode: u1         - 0x0  Sequence Number
 *           varlen     - 0 or 2
 *           u1[varlen] - Sequence number
 *
 * Metacode: u1         - 0x1  Text
 *           varlen     - Length of text
 *           u1[varlen] - Text
 *
 * Metacode: u1         - 0x2  Copyright
 *           varlen     - Length of text
 *           u1[varlen] - Text
 *
 * Metacode: u1         - 0x3  Track Name
 *           varlen     - Length of name
 *           u1[varlen] - Track Name
 *
 * Metacode: u1         - 0x58  Time Signature
 *           varlen     - 4 
 *           u1         - numerator
 *           u1         - log2(denominator)
 *           u1         - clocks in metronome click
 *           u1         - 32nd notes in quarter note (usually 8)
 *
 * Metacode: u1         - 0x59  Key Signature
 *           varlen     - 2
 *           u1         - if >= 0, then number of sharps
 *                        if < 0, then number of flats * -1
 *           u1         - 0 if major key
 *                        1 if minor key
 *
 */



/*
 * A MidiNote contains
 *
 * starttime - The time (measured in pulses) when the note is pressed.
 * channel   - The channel the note is from.  This is used when matching
 *             NoteOff events with the corresponding NoteOn event.
 *             The channels for the NoteOn and NoteOff events must be
 *             the same.
 * notenumber - The note number, from 0 to 127.  Middle C is 60.
 * duration  - The time duration (measured in pulses) after which the 
 *             note is released.
 *
 * A MidiNote is created when we encounter a NoteOff event.  The duration
 * is initially unknown (set to 0).  When the corresponding NoteOff event
 * is found, the duration is set by the method NoteOff().
 */

public class MidiNote : IComparer<MidiNote> {
    int starttime;   /* The start time, in pulses */
    int channel;     /* The channel */
    int notenumber;  /* The note, from 0 to 127. Middle C is 60 */
    int duration;    /* The duration, in pulses */


    /* Create a new MidiNote.  This is called when a NoteOn event is
     * encountered in the MidiFile.
     */
    public MidiNote(int starttime, int channel, int notenumber, int duration) {
        this.starttime = starttime;
        this.channel = channel;
        this.notenumber = notenumber;
        this.duration = duration;
    }


    public int StartTime {
        get { return starttime; }
    }

    public int EndTime {
        get { return starttime + duration; }
    }

    public int Channel {
        get { return channel; }
    }

    public int Number {
        get { return notenumber; }
    }

    public int Duration {
        get { return duration; }
    }

    /* A NoteOff event occurs for this note at the given time.
     * Calculate the note duration based on the noteoff event.
     */
    public void NoteOff(int endtime) {
        duration = endtime - starttime;
    }

    /* Compare two MidiNotes based on their start times */
    public int Compare(MidiNote x, MidiNote y) {
        return x.StartTime - y.StartTime;
    }

    public override 
    string ToString() {
        return string.Format("MidiNote channel {0} number {1} " +
                             "start {2} duration {3} ",
                             channel, notenumber, starttime, duration);

    }

}

/*
 * The MidiTrack class simply contains the list of MidiNotes for 
 * that track.  An instance is created when a new midi track is read.
 *
 * For each NoteOn event in the midi file, a new MidiNote is created
 * and added to the track, using the AddNote() method.
 * 
 * The NoteOff() method is called when a NoteOff event is encountered,
 * in order to update the duration of the MidiNote.
 */ 
public class MidiTrack {
    int tracknum;            /* The track number */
    List<MidiNote> notes;    /* List of Midi notes */
    string instrument;       /* Instrument for this track */

    public MidiTrack(int tracknum) {
        this.tracknum = tracknum;
        notes = new List<MidiNote>(100);
        instrument = "";
    }

    public int Number {
        get { return tracknum; }
    }

    public List<MidiNote> Notes {
        get { return notes; }
    }

    public string Instrument {
        get { if (notes[0].Channel == 10)
                  return "Percussion";
              else
                  return instrument; 
            }
        set { instrument = value; }
    }

    public override 
    string ToString() {
        string result = "Track " + tracknum + " " + instrument + "\n";

        foreach (MidiNote n in notes) {
           result = result + n + "\n";
        }

        result += "End Track " + tracknum + "\n";
        return result;
    }

    /* Add a MidiNote to this track.  This is called for each NoteOn event */
    public void AddNote(MidiNote m) {
        notes.Add(m);
    }

    /* A NoteOff event occured.  Find the MidiNote of the corresponding
     * NoteOn event, and update the duration of the MidiNote.
     */
    public void NoteOff(int channel, int notenumber, int endtime) {
        for (int i = notes.Count-1; i >= 0; i--) {
            MidiNote m = notes[i];
            if (m.Channel == channel && m.Number == notenumber &&
                m.Duration == 0) {
                m.NoteOff(endtime);
                return;
            }
        }
    }

    /* Return true if this track contains multiple channels */
    public bool HasMultipleChannels() {
        int channel = notes[0].Channel;
        foreach (MidiNote m in notes) {
            if (m.Channel != channel) {
                return true;
            }
        }
        return false;
    }
}

/*
 * A MidiFileException is thrown when an error occurs
 * while parsing the Midi File.  The constructore takes
 * the file offset (in bytes) where the error occurred,
 * and a string describing the error.
 */
public class MidiFileException : System.Exception {
    public MidiFileException (string s, int offset) :
        base(s + " at offset " + offset) {
    }
}


/*
 * The MidiFileReader is used to read low-level binary data from a file.
 * This class can do the following:
 *
 * - Peek at the next byte in the file.
 * - Read a byte
 * - Read a 16-bit big endian short
 * - Read a 32-bit big endian int
 * - Read a fixed length ascii string (not null terminated)
 * - Read a "variable length" integer.  The format of the variable length
 *   int is described at the top of this file.
 * - Skip ahead a given number of bytes
 * - Return the current file offset.
 */

public class MidiFileReader {
    bool did_peek;
    byte[] buf;
    FileStream file;
    int file_offset;

    public MidiFileReader(string filename) {
        file = File.Open(filename, FileMode.Open, 
                         FileAccess.Read, FileShare.Read);
        buf = new byte[8];
        file_offset = 0;
        did_peek = false;
    }

    public byte Peek() {
        if (did_peek == true) {
            throw new System.InvalidOperationException("Cant call Peek() twice");
        }
        did_peek = true;
        file.Read(buf, 0, 1);
        return buf[0];
    }

    private void DoRead(int len) {
        int off = 0;

        file_offset += len;

        if (did_peek) {
            did_peek = false;
            off = 1;
            len--;
        }
        if (len <= 0) {
            return;
        }

        if (len > buf.Length) {
            byte[] buf2 = new byte[len+1];
            buf2[0] = buf[0];
            buf = buf2;
        } 
        file.Read(buf, off, len);
    }

    public byte ReadByte() { 
        DoRead(1);
        return buf[0];
    }

    public ushort ReadShort() {
        DoRead(2);
        return (ushort) ( (buf[0] << 8) | buf[1] );
    }

    public int ReadInt() {
        DoRead(4);
        return (int)( (buf[0] << 24) | (buf[1] << 16) | (buf[2] << 8) | buf[3] );
    }

    public string ReadAscii(int len) {
        DoRead(len);
        return ASCIIEncoding.ASCII.GetString(buf, 0, len);
    }

    public int ReadVarlen() {
        uint result = 0;
        byte b;

        b = ReadByte();
        result = (uint)(b & 0x7f);

        for (int i = 0; i < 3; i++) {
            if ((b & 0x80) != 0) {
                b = ReadByte();
                result = (uint)( (result << 7) + (b & 0x7f) );
            }
            else {
                break;
            }
        }
        return (int)result;
    }

    public void Skip(int amount) {
        if (amount == 0) {
            return;
        }
        while (amount > 0) {
            int n = Math.Min(buf.Length, amount);
            DoRead(n);
            amount = amount - n;
        }
    }

    public int GetOffset() {
        return file_offset;
    }

    public void Close() {
        file.Close();
    }
}


/*
 * The MidiFile class contains the parsed data from the Midi File.
 * It contains:
 * - All the tracks in the midi file, including all MidiNotes per track.
 * - The time signature (e.g. 4/4, 3/4, 6/8)
 * - The number of pulses per quarter note.
 *
 * The constructor takes a filename as input, and upon returning,
 * contains the parsed data from the midi file.
 *
 * The methods ReadTrack() and ReadMetaEvent() are helper functions called
 * by the constructor during the parsing.
 *
 * After the MidiFile is parsed and created, the user can retrieve the 
 * tracks and notes by calling GetTrack(). 
 *
 * The CombineToTwoTracks() method is specifically for piano music. Many
 * midi piano songs have 1, 2, 3, or more tracks.  But a piano usually
 * has just two tracks/staffs: the left hand and right hand.  This method
 * combines all tracks into just 2 tracks.  The lowest notes go in the
 * bottom (left-hand) track, and the topmost notes go in the top (right-hand)
 * track.
 *
 * The methods RoundStartTimes() and RoundDurations() are used to make
 * the sheet music look better.  The RoundStartTimes() groups notes that are
 * close together (time-wise) by assigning them the same start time.
 * RoundDurations() extends the duration of a note to the next note.  Longer
 * notes and fewer rests looks better than 16th/32nd notes with lots of rests
 * in between them.
 */

public class MidiFile {
    string filename;
    List<MidiTrack> tracks;
    TimeSignature timesig;
    int quarternote;
    int tempo; // 微秒每四分音符，默认500000（120 BPM）

    public int TotalTracks {
        get { return tracks.Count; }
    }

    public TimeSignature Time {
        get { return timesig; }
    }

    public string FileName {
        get { return filename; }
    }

    public int Tempo {
        get { return tempo; }
    }

    public double BPM {
        get { return 60000000.0 / tempo; }
    }

    /* The list of Midi Events */
    const int EventNoteOff         = 0x80;
    const int EventNoteOn          = 0x90;
    const int EventKeyPressure     = 0xA0;
    const int EventControlChange   = 0xB0;
    const int EventProgramChange   = 0xC0;
    const int EventChannelPressure = 0xD0;
    const int EventPitchBend       = 0xE0;
    const int SysexEvent1          = 0xF0;
    const int SysexEvent2          = 0xF7;
    const int MetaEvent            = 0xFF;

    /* The list of Meta Events */
    const int MetaEventSequence      = 0x0;
    const int MetaEventText          = 0x1;
    const int MetaEventCopyright     = 0x2;
    const int MetaEventSequenceName  = 0x3;
    const int MetaEventInstrument    = 0x4;
    const int MetaEventLyric         = 0x5;
    const int MetaEventMarker        = 0x6;
    const int MetaEventEndOfTrack    = 0x2F;
    const int MetaEventTempo         = 0x51;
    const int MetaEventSMPTEOffset   = 0x54;
    const int MetaEventTimeSignature = 0x58;
    const int MetaEventKeySignature  = 0x59;

    /* The Program Change event gives the instrument that should
     * be used for a particular channel.  The following table
     * maps each instrument number (0 thru 128) to an instrument
     * name.
     */
    static string[] instruments = { 
        "Acoustic Grand Piano",
        "Bright Acoustic Piano",
        "Electric Grand Piano",
        "Honky-tonk Piano",
        "Electric Piano 1",
        "Electric Piano 2",
        "Harpsichord",
        "Clavi",
        "Celesta",
        "Glockenspiel",
        "Music Box",
        "Vibraphone",
        "Marimba",
        "Xylophone",
        "Tubular Bells",
        "Dulcimer",
        "Drawbar Organ",
        "Percussive Organ",
        "Rock Organ",
        "Church Organ",
        "Reed Organ",
        "Accordion",
        "Harmonica",
        "Tango Accordion",
        "Acoustic Guitar (nylon)",
        "Acoustic Guitar (steel)",
        "Electric Guitar (jazz)",
        "Electric Guitar (clean)",
        "Electric Guitar (muted)",
        "Overdriven Guitar",
        "Distortion Guitar",
        "Guitar harmonics",
        "Acoustic Bass",
        "Electric Bass (finger)",
        "Electric Bass (pick)",
        "Fretless Bass",
        "Slap Bass 1",
        "Slap Bass 2",
        "Synth Bass 1",
        "Synth Bass 2",
        "Violin",
        "Viola",
        "Cello",
        "Contrabass",
        "Tremolo Strings",
        "Pizzicato Strings",
        "Orchestral Harp",
        "Timpani",
        "String Ensemble 1",
        "String Ensemble 2",
        "SynthStrings 1",
        "SynthStrings 2",
        "Choir Aahs",
        "Voice Oohs",
        "Synth Voice",
        "Orchestra Hit",
        "Trumpet",
        "Trombone",
        "Tuba",
        "Muted Trumpet",
        "French Horn",
        "Brass Section",
        "SynthBrass 1",
        "SynthBrass 2",
        "Soprano Sax",
        "Alto Sax",
        "Tenor Sax",
        "Baritone Sax",
        "Oboe",
        "English Horn",
        "Bassoon",
        "Clarinet",
        "Piccolo",
        "Flute",
        "Recorder",
        "Pan Flute",
        "Blown Bottle",
        "Shakuhachi",
        "Whistle",
        "Ocarina",
        "Lead 1 (square)",
        "Lead 2 (sawtooth)",
        "Lead 3 (calliope)",
        "Lead 4 (chiff)",
        "Lead 5 (charang)",
        "Lead 6 (voice)",
        "Lead 7 (fifths)",
        "Lead 8 (bass + lead)",
        "Pad 1 (new age)",
        "Pad 2 (warm)",
        "Pad 3 (polysynth)",
        "Pad 4 (choir)",
        "Pad 5 (bowed)",
        "Pad 6 (metallic)",
        "Pad 7 (halo)",
        "Pad 8 (sweep)",
        "FX 1 (rain)",
        "FX 2 (soundtrack)",
        "FX 3 (crystal)",
        "FX 4 (atmosphere)",
        "FX 5 (brightness)",
        "FX 6 (goblins)",
        "FX 7 (echoes)",
        "FX 8 (sci-fi)",
        "Sitar",
        "Banjo",
        "Shamisen",
        "Koto",
        "Kalimba",
        "Bag pipe",
        "Fiddle",
        "Shanai",
        "Tinkle Bell",
        "Agogo",
        "Steel Drums",
        "Woodblock",
        "Taiko Drum",
        "Melodic Tom",
        "Synth Drum",
        "Reverse Cymbal",
        "Guitar Fret Noise",
        "Breath Noise",
        "Seashore",
        "Bird Tweet",
        "Telephone Ring",
        "Helicopter",
        "Applause",
        "Gunshot"
    };
    /* End instruments */


    /* Return a string representation of a Midi event */
    string EventName(int ev) {
        if (ev >= EventNoteOff && ev < EventNoteOff + 16)
            return "NoteOff";
        else if (ev >= EventNoteOn && ev < EventNoteOn + 16) 
            return "NoteOn";
        else if (ev >= EventKeyPressure && ev < EventKeyPressure + 16) 
            return "KeyPressure";
        else if (ev >= EventControlChange && ev < EventControlChange + 16) 
            return "ControlChange";
        else if (ev >= EventProgramChange && ev < EventProgramChange + 16) 
            return "ProgramChange";
        else if (ev >= EventChannelPressure && ev < EventChannelPressure + 16)
            return "ChannelPressure";
        else if (ev >= EventPitchBend && ev < EventPitchBend + 16)
            return "PitchBend";
        else if (ev == MetaEvent)
            return "MetaEvent";
        else if (ev == SysexEvent1 || ev == SysexEvent2)
            return "SysexEvent";
        else
            return "Unknown";
    }


    /* Read the given Midi file, and return an instance of this MidiFile
     * class.  After reading the midi file, this object will contain:
     * - The Time Signature of the song
     * - All the tracks in the song which contain notes.  (Tracks that
     *   don't contain notes are ignored).
     * - The number, starttime, and duration of each note.
     */
    public MidiFile(string filename) {
        string id;
        int len;

        this.filename = filename;
        tracks = new List<MidiTrack>();
        tempo = 500000; // 默认tempo：500000微秒每四分音符（120 BPM）
        MidiFileReader file = new MidiFileReader(filename);
        id = file.ReadAscii(4);
        if (id != "MThd") {
            throw new MidiFileException("Doesn't start with MThd", 0);
        }
        len = file.ReadInt(); 
        if (len !=  6) {
            throw new MidiFileException("Bad MThd header", 4);
        }
        file.Skip(2);
        int num_tracks = file.ReadShort();
        quarternote = file.ReadShort(); 

        for (int tracknum = 0; tracknum < num_tracks; tracknum++) {
            MidiTrack t = ReadTrack(file, tracknum);
            if (t.Notes.Count > 0) {
                tracks.Add(t);
            }
        }

        /* If we only have one track with multiple channels, then treat
         * each channel is a separate track.
         */
        if (tracks.Count == 1 && tracks[0].HasMultipleChannels()) {
            tracks = SplitChannels(tracks[0]);
        }

        CheckStartTimes(tracks);
        if (timesig == null) {
            timesig = new TimeSignature(4, 4, quarternote);
        }
        else {
            timesig = new TimeSignature(timesig.Numerator, 
                                        timesig.Denominator, 
                                        quarternote);
        }
        file.Close();
    }


    /* Read a single track from the Midi file.  Return the parsed MidiTrack.
     * Entering this function, the file offset should be at the start of
     * the MTrk header.  Upon exiting, the file offset should be at the
     * start of the next MTrk header.
     */
    MidiTrack ReadTrack(MidiFileReader file, int tracknum) {
        MidiTrack track = new MidiTrack(tracknum);
        int starttime = 0;
        string id = file.ReadAscii(4);

        if (id != "MTrk") {
            throw new MidiFileException("Bad MTrk header", file.GetOffset() - 4);
        }
        int tracklen = file.ReadInt();
        int trackend = tracklen + file.GetOffset();

        int eventflag = 0;

        while (file.GetOffset() < trackend) {
            int startoffset = file.GetOffset();
            int deltatime = file.ReadVarlen();
            starttime += deltatime;

            byte peekevent = file.Peek();
            if (peekevent >= EventNoteOff) {
                eventflag = file.ReadByte();
                // Console.WriteLine("Read new event {0} {1}", 
                //                  eventflag, EventName(eventflag));
            }

            // Console.WriteLine("offset {0}: event {1} {2} delta {3}", 
            //                 startoffset, eventflag, EventName(eventflag), 
            //                 deltatime);

            if (eventflag >= EventNoteOn && eventflag < EventNoteOn + 16) {
                int channel = eventflag - EventNoteOn;
                int notenumber = file.ReadByte();
                int velocity = file.ReadByte();

                if (velocity > 0) {
                    MidiNote m = new MidiNote(starttime, channel, notenumber, 0);
                    track.AddNote(m);
                }
                else {
                    track.NoteOff(channel, notenumber, starttime);
                }
            }
            else if (eventflag >= EventNoteOff && eventflag < EventNoteOff + 16) {
                int channel = eventflag - EventNoteOff;
                int note = file.ReadByte();
                file.Skip(1);
                track.NoteOff(channel, note, starttime);
            }
            else if (eventflag >= EventKeyPressure && 
                     eventflag < EventKeyPressure + 16) {
                file.Skip(2);
            }
            else if (eventflag >= EventControlChange && 
                     eventflag < EventControlChange + 16) {
                file.Skip(2);
            }
            else if (eventflag >= EventProgramChange && 
                     eventflag < EventProgramChange + 16) {
                int patchnum = file.ReadByte();
                if (patchnum >= 0 && patchnum < instruments.Length) {
                    track.Instrument = instruments[patchnum];
                }
                else {
                    // Console.WriteLine("unknown patchnum " + patchnum);
                }
                
            }
            else if (eventflag >= EventChannelPressure && 
                     eventflag < EventChannelPressure + 16) {
                file.Skip(1);
            }
            else if (eventflag >= EventPitchBend && 
                     eventflag < EventPitchBend + 16) {
                file.Skip(2);
            }
            else if (eventflag == SysexEvent1 ||
                     eventflag == SysexEvent2) {
                int syslen = file.ReadVarlen();
                file.Skip(syslen);
            }
            else if (eventflag == MetaEvent) {
                int meta = ReadMetaEvent(file, track, starttime);
                if (meta == MetaEventEndOfTrack) {
                    break; 
                }
            }
            else {
                throw new MidiFileException("Unknown event " + eventflag,
                                             file.GetOffset()-1); 
            }
        }

        return track;
    }


    /* Read a meta event from the MidiFile.  The file offset should be just
     * after the MetaEvent (0xFF) byte.  When the function exits, the file
     * offset should be at the start of the next delta time.
     */

    int ReadMetaEvent(MidiFileReader file, MidiTrack track, int starttime) {
        int metaevent = file.ReadByte();
        int len = file.ReadVarlen();

        // Console.WriteLine("    Metaevent {0} len {1}",  metaevent, len);
        switch (metaevent) {

            case MetaEventTempo:
                if (len != 3) {
                    throw new MidiFileException(
                      "Meta Event Tempo len == " + len +
                      " != 3", file.GetOffset());
                }
                // 读取3字节的tempo值（微秒每四分音符）
                int b1 = file.ReadByte();
                int b2 = file.ReadByte();
                int b3 = file.ReadByte();
                tempo = (b1 << 16) | (b2 << 8) | b3;
                break;

            case MetaEventTimeSignature:
                if (len != 4) {
                    throw new MidiFileException(
                      "Meta Event Time Signature len == " + len +
                      " != 4", file.GetOffset());
                }
                int numerator= file.ReadByte();
                byte log2 = file.ReadByte();
                int denominator = (int) System.Math.Pow(2, log2);
                file.Skip(2);
                timesig = new TimeSignature(numerator, denominator, quarternote);
                break;

            /***
                We're going to ignore Key Signature events for now.
                They're often missing or even incorrect.  Instead, we'll
                use the KeySignature.Guess() method to guess the correct
                key signature based on the notes in the song.

            case MetaEventKeySignature:
                if (len != 2) {
                    throw new MidiFileException(
                       "Meta Event Key Signature len != 2", file.GetOffset());
                }
                sbyte sharpflat = (sbyte)file.ReadByte();
                bool ismajor = (file.ReadByte() == 0);
                int numsharps = 0;
                int numflats = 0;
                if (sharpflat < 0) 
                    numflats = -sharpflat;
                else
                    numsharps = sharpflat;
            ***/

            default:
                file.Skip((int)len);
                break;
        }
        return metaevent;
    }


    /* Find the highest note between starttime and endtime.  If there are no
     * notes in that interval, return the closest note to that interval.
     */
    static int 
    FindHighestNote(List<MidiNote> notes, int starttime, int endtime)
    {
        if (notes.Count == 0)
            return 76; /* E5, top of treble staff */

        int i = 0;
        while (i < notes.Count && notes[i].EndTime <= starttime)
            i++;
        if (i == notes.Count)
            return notes[i-1].Number;

        int high = notes[i].Number;
        while (i < notes.Count && notes[i].StartTime < endtime) {
            if (high < notes[i].Number) {
                high = notes[i].Number;
            }
            i++;
        }
        return high;
    }
 
    /* Find the lowest note between starttime and endtime.  If there are no
     * notes in that interval, return the closest note to that interval.
     */
    static int 
    FindLowestNote(List<MidiNote> notes, int starttime, int endtime)
    {
        if (notes.Count == 0)
            return 45;  /* A3, bottom of bass staff */

        int i = 0;
        while (i < notes.Count && notes[i].EndTime <= starttime)
            i++;
        if (i == notes.Count)
            return notes[i-1].Number;

        int low = notes[i].Number;
        while (i < notes.Count && notes[i].StartTime < endtime) {
            if (low > notes[i].Number) {
                low = notes[i].Number;
            }
            i++;
        }
        return low;
    }
 

    /* Split the given MidiTrack into two tracks, top and bottom.
     * The highest notes will go into top, the lowest into bottom.
     * This function is used to split piano songs into left-hand (bottom)
     * and right-hand (top) tracks.
     */
    static List<MidiTrack> SplitTrack(MidiTrack track) {
        List<MidiNote> notes = track.Notes;
        int count = notes.Count;

        MidiTrack top = new MidiTrack(1);
        MidiTrack bottom = new MidiTrack(2);
        List<MidiNote> unknown = new List<MidiNote>();


        foreach (MidiNote m in notes) {
            /* Get the highest and lowest notes that occur in the same
             * interval as this note.
             */
            int high = FindHighestNote(notes, m.StartTime, m.EndTime);
            int low = FindLowestNote(notes, m.StartTime, m.EndTime);

            /* If this note is more than an octave from the high/low
             * note, add it to the bottom/top staff.
             */
            if (high - m.Number > 12 && m.Number - low > 12) {
                if (high - m.Number <= m.Number - low)
                    top.AddNote(m);
                else
                    bottom.AddNote(m);
            }
            else if (high - m.Number > 12) {
                bottom.AddNote(m);
            }
            else if (m.Number - low > 12) {
                top.AddNote(m);
            }
            /* Otherwise, add this note to the unknown list */
            else {
                unknown.Add(m);
            }
        } 

        /* For each remaining note, find out whether it's closest to
         * the top or bottom track, and add it to the proper track.
         */ 
        foreach (MidiNote m in unknown) {
            int high = FindHighestNote(top.Notes, m.StartTime, m.EndTime);
            int low = FindLowestNote(bottom.Notes, m.StartTime, m.EndTime);

            if (high - m.Number <= m.Number - low)
                top.AddNote(m);
            else
                bottom.AddNote(m);
        }

        top.Notes.Sort(notes[0]);
        bottom.Notes.Sort(notes[0]);

        List<MidiTrack> result = new List<MidiTrack>(2);
        result.Add(top); result.Add(bottom);
        return result;
    }


    /* Return the given midi track.  Track numbers start at 1,
     * but the array starts with index 0.
     */
    public MidiTrack GetTrack(int tracknum)
    {
        return tracks[tracknum-1];
    }


    /* Combine the notes in all the tracks listed in tracknums into 
     * a single MidiTrack, and return it.  Track numbers go from
     * 1 to the number of tracks.
     */
    static MidiTrack CombineToSingleTrack(List<MidiTrack> thetracks)
    {
        /* Add all notes into one track */
        MidiTrack result = new MidiTrack(1);
        foreach (MidiTrack t in thetracks) {
            foreach (MidiNote m in t.Notes) {
                result.AddNote(m);
            }
        }
        result.Notes.Sort( result.Notes[0] );

        /* Remove duplicate notes, notes with the same starttime
         * and note number.
         */
        for (int i = 0; i < result.Notes.Count; i++) {
            MidiNote m1 = result.Notes[i];
            for (int j = i+1; j < result.Notes.Count; j++) {
                MidiNote m2 = result.Notes[j];
                if (m1.StartTime != m2.StartTime) {
                    break;
                }
                if (m1.StartTime == m2.StartTime &&
                    m1.Number == m2.Number) {

                    if (m1.Duration > m2.Duration) {
                        result.Notes.Remove(m2);
                    }
                    else {
                        result.Notes.Remove(m2);
                    }
                    j--;
                }
            }
        }
        return result;
    }


    /* Combine the notes in all the tracks given into two MidiTracks,
     * and return them.  Track numbers go from 1 to the number of tracks.
     * This function is intended for piano songs, when we want to display
     * a left-hand track and a right-hand track.  The lower notes go into 
     * the left-hand track, and the higher notes go into the right hand 
     * track.
     */
    public List<MidiTrack> CombineToTwoTracks(List<MidiTrack> tracks)
    {
        MidiTrack single = CombineToSingleTrack(tracks);
        List<MidiTrack> result = SplitTrack(single);
        return result;
    }


    /* Guess the correct value of the quarter note (the number of pulses
     * per quarter note).  The pulses per quarter note appears at the
     * very beginning of the MidiFile.  However, I've found that this value
     * is occasionally wrong, and it screws up the sheet music displayed.
     *
     * To make sure the value is correct, do the following:
     * - Calculate all the durations (in pulses) between notes.  Store the
     *   frequency for each duration.
     *
     * - Find the duration that occurs the most.
     *   then ???
     */
    int GuessQuarterNote(int quarternote, List<MidiTrack> thetracks) {
        Dictionary<int,int> durations = new Dictionary<int,int>();
        int minduration = timesig.QuarterNote/8;

        foreach (MidiTrack t in thetracks) {
            List<MidiNote> notes = t.Notes;

            for (int i = 0; i < notes.Count; i++) {
                MidiNote m = notes[i];
                int duration = 0;
                for (int j = i; j < notes.Count; j++) {
                    duration = notes[j].StartTime - notes[i].StartTime;

                    /* Round duration to multiple of 4 */
                    duration = (duration + 2) / 4 * 4;

                    if (duration > minduration) {
                        if (durations.ContainsKey(duration)) {
                            durations[duration] += 1;
                        }
                        else {
                            durations[duration] = 1;
                        }
                        break;
                    }
                }
            }
        }

        int bestfrequency = 0;
        int frequentduration = 0;
        foreach (KeyValuePair<int,int> p in durations) {
            if (p.Value > bestfrequency) {
                bestfrequency = p.Value;
                frequentduration = p.Key;
            }
        }


        int closestduration = frequentduration;

        int[] multiples = new int[] { 1, 2, 3, 4, 6, 8 };
        foreach (int i in multiples) {
            if (Math.Abs(quarternote - frequentduration*i) <
                Math.Abs(quarternote - closestduration) ) {

                closestduration = frequentduration*i;
            }
        }

        foreach (int i in multiples) {
            if (Math.Abs(quarternote - frequentduration/i) <
                Math.Abs(quarternote - closestduration) ) {

                closestduration = frequentduration/i;
            }
        }

        // Console.WriteLine("quarter={0} closest={1} frequent={2}", 
        //                  quarternote, closestduration, frequentduration);
        if (Math.Abs(quarternote - closestduration) >= 10) {
            return closestduration;
        }
        else {
            return quarternote;
        }
    }

    /* Check that the MidiNote start times are in increasing order */
    static void CheckStartTimes(List<MidiTrack> tracks) {
        foreach (MidiTrack t in tracks) {
            int prevtime = -1;
            foreach (MidiNote m in t.Notes) {
                MyAssert.Arg(m.StartTime >= prevtime,  
                           "start times not in increasing order");
                prevtime = m.StartTime;
            }
        }
    }


    /* In Midi Files, time is measured in pulses.  Notes that have
     * pulse times that are close together (like within 10 pulses)
     * will sound like they're the same chord.  We want to draw
     * these notes as a single chord, it makes the sheet music much
     * easier to read.  We don't want to draw notes that are close
     * together as two separate chords.
     *
     * The SymbolSpacing class only aligns notes that have exactly the same
     * start times.  Notes with slightly different start times will
     * appear in separate vertical columns.  This isn't what we want.
     * We want to align notes with approximately the same start times.
     * So, this function is used to assign the same starttime for notes
     * that are close together (timewise).
     */
    public static 
    List<MidiTrack> RoundStartTimes(List<MidiTrack> tracks, int quarternote) {
        List<MidiTrack> result = new List<MidiTrack>( tracks.Count );

        /* Get all the starttimes in all tracks, in sorted order */
        List<int> starttimes = new List<int>();
        foreach (MidiTrack t in tracks) {
            foreach (MidiNote m in t.Notes) {
                starttimes.Add( m.StartTime );
            }
        }
        starttimes.Sort();

        /* Combine start times that are close together, within
         * 1/17 or 1/14 of the quarternote time.
         */
        int interval = 0;
        if (quarternote < 200)
            interval = quarternote/17;
        else
            interval = quarternote/14;

        for (int i = 0; i < starttimes.Count - 1; i++) {
            if (starttimes[i+1] - starttimes[i] <= interval) {
                starttimes[i+1] = starttimes[i];
            }
        }

        CheckStartTimes(tracks);
        foreach (MidiTrack t in tracks) {
            MidiTrack newtrack = new MidiTrack( t.Number );
            result.Add(newtrack);
            int i = 0;

            foreach (MidiNote m in t.Notes) {
                while (i < starttimes.Count &&
                       m.StartTime - interval > starttimes[i]) {
                    i++;
                }
                MidiNote resultnote = m;

                if (m.StartTime > starttimes[i] &&
                    m.StartTime - starttimes[i] <= interval) {

                    resultnote = new MidiNote(starttimes[i], m.Channel,
                                              m.Number, m.Duration);
                }
                newtrack.AddNote(resultnote);
            }
        }
        return result;
    }


    /* We want note durations to span up to the next note in general.
     * The sheet music looks nicer that way.  In contrast, sheet music
     * with lots of 16th/32nd notes separated by small rests doesn't
     * look as nice.  Having nice looking sheet music is more important
     * than faithfully representing the Midi File data.
     *
     * Therefore, this function rounds the duration of MidiNotes up to
     * the next note where possible.
     */

    public static 
    List<MidiTrack> RoundDurations(List<MidiTrack> tracks, int quarternote) {

        List<MidiTrack> result = new List<MidiTrack>( tracks.Count );
        foreach (MidiTrack t in tracks ) {
            MidiTrack newtrack = new MidiTrack(t.Number);
            result.Add(newtrack);

            for (int i = 0; i < t.Notes.Count; i++) {
                MidiNote m1 = t.Notes[i];
                MidiNote m2 = m1;
                for (int j = i+1; j < t.Notes.Count; j++) {
                    m2 = t.Notes[j];
                    if (m1.StartTime < m2.StartTime) {
                        break;
                    }
                }
                int maxduration = m2.StartTime - m1.StartTime;

                int dur = 0;
                if (quarternote <= maxduration)
                    dur = quarternote;
                else if (quarternote/2 <= maxduration)
                    dur = quarternote/2;
                else if (quarternote/3 <= maxduration)
                    dur = quarternote/3;
                else if (quarternote/4 <= maxduration)
                    dur = quarternote/4;


                if (dur < m1.Duration) {
                    dur = m1.Duration;
                }
                MidiNote resultnote = new MidiNote(m1.StartTime, m1.Channel,
                                                   m1.Number, dur);
                newtrack.AddNote(resultnote);

            }
        }
        return result;
    }

    /* Split the given track into multiple tracks, separating each
     * channel into a separate track.
     */
    static List<MidiTrack> SplitChannels(MidiTrack track) {
        List<MidiTrack> result = new List<MidiTrack>();
        foreach (MidiNote m in track.Notes) {
            bool foundchannel = false;
            foreach (MidiTrack t in result) {
                if (m.Channel == t.Notes[0].Channel) {
                    foundchannel = true;
                    t.AddNote(m); 
                }
            }
            if (!foundchannel) {
                MidiTrack t = new MidiTrack(result.Count + 1);
                t.AddNote(m);
                result.Add(t);
            }
        }
        return result;
    }


    public override string ToString() {
        string result = "Midi File\n";
        result += "Quarter: " + quarternote + "\n";
        result += "Time Signature: " + Time + "\n";
        foreach(MidiTrack t in tracks) {
            result += t;
        }
        return result;
    }

    public static void Main2(string[] arg) {
        if (arg.Length == 0) {
            Console.WriteLine("Usage: MidiFile <filename>");
            return;
        }

        MidiFile f = new MidiFile(arg[0]);
        Console.Write(f.ToString());
    }

}  /* End class MidiFile */


}  /* End namespace MidiSheetMusic */



