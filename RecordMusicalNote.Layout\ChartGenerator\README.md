# 音乐游戏谱面生成器

## 功能介绍

音乐游戏谱面生成器是一个强大的工具，可以从MIDI文件自动生成适合不同游戏模式的谱面。

### 支持的游戏模式

1. **星动模式** - 传统的音乐游戏模式，支持点击、长按、滑动音符
2. **弹珠模式** - 弹珠类型的音乐游戏，支持普通弹珠和特殊弹珠
3. **泡泡模式** - 泡泡消除类音乐游戏，支持多种颜色的泡泡

### 主要特性

- **智能音符分析** - 自动分析MIDI文件中的音符，选择最适合的主旋律轨道
- **难度调节** - 支持1-10级难度调节，自动调整音符密度
- **轨道分配** - 智能分配音符到不同轨道，确保游戏体验
- **音符类型识别** - 根据音符时长自动判断音符类型
- **BPM自动计算** - 自动计算歌曲的BPM和时间信息
- **实时预览** - 生成前可预览MIDI文件信息
- **多格式导出** - 支持XML格式导出，兼容现有系统

## 使用方法

### 1. 打开谱面生成器

在主界面菜单中选择：`转换功能` → `MIDI谱面生成器`

### 2. 选择MIDI文件

点击"浏览"按钮，选择要转换的MIDI文件（支持.mid和.midi格式）

### 3. 设置游戏模式

选择要生成的游戏模式：
- 星动模式：适合传统音游
- 弹珠模式：适合弹珠类游戏
- 泡泡模式：适合消除类游戏

### 4. 调整参数

- **难度等级**：1-10级，影响音符密度
- **轨道数量**：4/6/8轨道可选
- **音符密度**：10%-100%，控制音符数量

### 5. 高级设置（可选）

- 自动调整BPM
- 过滤短音符
- 添加长按音符
- 设置歌曲名称和艺术家

### 6. 生成和保存

1. 点击"预览谱面"查看MIDI文件信息
2. 点击"生成谱面"开始生成
3. 生成完成后点击"保存谱面"导出文件

## 技术原理

### 音符分析算法

1. **轨道选择**：选择音符数量最多的轨道作为主旋律
2. **时间计算**：将MIDI时间转换为游戏中的小节和位置
3. **音符过滤**：根据难度设置过滤音符密度
4. **轨道分配**：根据音高将音符分配到不同游戏轨道

### 音符类型判断

- **短音符**：时长 ≤ 1/4四分音符 → 点击音符
- **中等音符**：时长 ≤ 1四分音符 → 长按音符  
- **长音符**：时长 > 1四分音符 → 滑动音符

### BPM计算

```
BPM = 60,000,000 / (四分音符微秒数)
```

### 时间转换

```
小节号 = (MIDI时间 / 每小节时长) + 1
位置 = (MIDI时间 % 每小节时长) / 每小节时长 * 64
```

## 输出格式

### XML格式示例

```xml
<Level>
  <LevelInfo BPM="120" BeatPerBar="4" BeatLen="16" TrackCount="4" 
             SongName="示例歌曲" Artist="示例艺术家" BarAmount="32"/>
  <NoteInfo>
    <Normal PosNum="64">
      <Note Bar="1" Pos="0" FromTrack="1" TargetTrack="1" NoteType="点"/>
      <Note Bar="1" Pos="16" FromTrack="2" TargetTrack="2" NoteType="长条" EndBar="1" EndPos="32"/>
      <!-- 更多音符... -->
    </Normal>
  </NoteInfo>
</Level>
```

## 注意事项

1. **MIDI文件质量**：建议使用高质量的MIDI文件，音符信息越完整效果越好
2. **轨道选择**：如果MIDI文件有多个旋律轨道，系统会自动选择音符最多的轨道
3. **难度平衡**：建议先用中等难度(5级)测试，再根据实际效果调整
4. **音符密度**：过高的密度可能导致游戏难度过大，建议控制在70%以下
5. **文件大小**：复杂的MIDI文件可能生成大量音符，注意控制文件大小

## 常见问题

**Q: 生成的谱面音符太少怎么办？**
A: 尝试提高难度等级和音符密度，或者选择音符更丰富的MIDI文件。

**Q: 音符分布不均匀怎么办？**
A: 这通常是因为原MIDI文件的音符分布问题，可以尝试手动调整或选择其他MIDI文件。

**Q: 支持哪些MIDI格式？**
A: 支持标准的.mid和.midi格式文件，建议使用Type 1格式的MIDI文件。

**Q: 生成的谱面可以进一步编辑吗？**
A: 可以，生成的XML文件可以用现有的谱面编辑工具进一步调整。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持三种游戏模式
- 基础的MIDI解析和谱面生成功能
- XML格式导出
