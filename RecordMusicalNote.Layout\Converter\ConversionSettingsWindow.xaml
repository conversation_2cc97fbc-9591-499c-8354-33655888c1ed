<Window x:Class="MyWPF.Layout.Converter.ConversionSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="星动转弹珠设置 (New)" Height="600" Width="500"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="星动转弹珠转换设置" 
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- 难度调整 -->
        <materialDesign:Card Grid.Row="1" Padding="16" Margin="0,0,0,10">
            <StackPanel>
                <TextBlock Text="难度调整" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,10"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Slider x:Name="sliderDifficulty" Grid.Column="0" 
                            Minimum="0.5" Maximum="2.0" Value="1.0" 
                            TickFrequency="0.1" IsSnapToTickEnabled="True"
                            Style="{StaticResource MaterialDesignSlider}"/>
                    <TextBlock x:Name="lblDifficultyValue" Grid.Column="1" Text="1.0x" 
                               VerticalAlignment="Center" Margin="10,0,0,0" MinWidth="40"/>
                </Grid>
                <TextBlock Text="0.5x=简化难度, 1.0x=保持原样, 2.0x=增加难度" 
                           Style="{StaticResource MaterialDesignCaptionTextBlock}" 
                           Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- 拐弯处理策略 -->
        <materialDesign:Card Grid.Row="2" Padding="16" Margin="0,0,0,10">
            <StackPanel>
                <TextBlock Text="拐弯音符处理策略" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,10"/>
                <RadioButton x:Name="rbSplitToMultiple" Content="拆分为多个音符 (推荐)" IsChecked="True" Margin="0,0,0,5"/>
                <TextBlock Text="将拐弯音符拆分为起始滑动+中间过渡+结束单点" 
                           Style="{StaticResource MaterialDesignCaptionTextBlock}" 
                           Foreground="{DynamicResource MaterialDesignBodyLight}" Margin="20,0,0,10"/>
                
                <RadioButton x:Name="rbSimplifyToLine" Content="简化为直线" Margin="0,0,0,5"/>
                <TextBlock Text="忽略拐弯效果，转换为简单的滑动弹珠" 
                           Style="{StaticResource MaterialDesignCaptionTextBlock}" 
                           Foreground="{DynamicResource MaterialDesignBodyLight}" Margin="20,0,0,10"/>
                
                <RadioButton x:Name="rbConvertToCombo" Content="转换为连击序列" Margin="0,0,0,5"/>
                <TextBlock Text="将拐弯音符转换为多个连击音符" 
                           Style="{StaticResource MaterialDesignCaptionTextBlock}" 
                           Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- 轨道设置 -->
        <materialDesign:Card Grid.Row="3" Padding="16" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="轨道0使用率" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,10"/>
                    <Slider x:Name="sliderTrack0Rate" Minimum="0" Maximum="0.2" Value="0.1" 
                            TickFrequency="0.01" IsSnapToTickEnabled="True"
                            Style="{StaticResource MaterialDesignSlider}"/>
                    <TextBlock x:Name="lblTrack0Value" Text="10%" HorizontalAlignment="Center"/>
                    <TextBlock Text="轨道0用于增加变化性" 
                               Style="{StaticResource MaterialDesignCaptionTextBlock}" 
                               Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <TextBlock Text="左右平衡" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,10"/>
                    <Slider x:Name="sliderLeftRightBalance" Minimum="0.3" Maximum="0.8" Value="0.6" 
                            TickFrequency="0.05" IsSnapToTickEnabled="True"
                            Style="{StaticResource MaterialDesignSlider}"/>
                    <TextBlock x:Name="lblBalanceValue" Text="60%" HorizontalAlignment="Center"/>
                    <TextBlock Text="偏向1|2区域的概率" 
                               Style="{StaticResource MaterialDesignCaptionTextBlock}" 
                               Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- 连击设置 -->
        <materialDesign:Card Grid.Row="4" Padding="16" Margin="0,0,0,10">
            <StackPanel>
                <TextBlock Text="连击设置" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,10"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Slider x:Name="sliderMaxCombo" Grid.Column="0" 
                            Minimum="2" Maximum="8" Value="4" 
                            TickFrequency="1" IsSnapToTickEnabled="True"
                            Style="{StaticResource MaterialDesignSlider}"/>
                    <TextBlock x:Name="lblComboValue" Grid.Column="1" Text="4" 
                               VerticalAlignment="Center" Margin="10,0,0,0" MinWidth="20"/>
                </Grid>
                <TextBlock Text="最大连击长度，影响SonId连接" 
                           Style="{StaticResource MaterialDesignCaptionTextBlock}" 
                           Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- 预览信息 -->
        <materialDesign:Card Grid.Row="5" Padding="16">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <TextBlock Text="转换预览" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,10"/>
                    <TextBlock x:Name="txtPreview" TextWrapping="Wrap" 
                               Style="{StaticResource MaterialDesignBody2TextBlock}"
                               Text="选择设置后将显示转换预览信息..."/>
                </StackPanel>
            </ScrollViewer>
        </materialDesign:Card>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="6" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="btnPreview" Content="预览设置" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0" Click="BtnPreview_Click"/>
            <Button x:Name="btnOK" Content="确定转换" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Margin="0,0,10,0" Click="BtnOK_Click"/>
            <Button x:Name="btnCancel" Content="取消" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
