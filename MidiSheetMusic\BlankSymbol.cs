/*
 * Copyright (c) 2007-2008 <PERSON><PERSON><PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License version 2.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace MidiSheetMusic {


/* 
 * The Blank symbol is a music symbol that doesn't draw anything.  This
 * symbol is used for alignment purposes, to align notes in different 
 * staffs which occur at the same time.
 */
public class BlankSymbol : MusicSymbol {
    int starttime; 
    int width;

    public BlankSymbol(int starttime, int width) {
        this.starttime = starttime;
        this.width = width;
    }

    public override int StartTime { 
        get { return starttime; }
    }

    public override int MinWidth {
        get { return 0; }
    }

    public override int Width { 
        get { return width; }
        set { width = value; }
    }

    public override int AboveStaff { 
        get { return 0; }
    }

    public override int BelowStaff { 
        get { return 0; }
    }

    public override void Draw(Graphics g, Pen pen, int ytop) {}

    public override string ToString() {
        return string.Format("BlankSymbol starttime={0} width={1}", 
                             starttime, width);
    }
}


}


