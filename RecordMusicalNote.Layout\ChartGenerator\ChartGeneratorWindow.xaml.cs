using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using RecordMusicalNote.DataModel;

namespace MyWPF.Layout.ChartGenerator
{
    /// <summary>
    /// ChartGeneratorWindow.xaml 的交互逻辑
    /// </summary>
    public partial class ChartGeneratorWindow : Window
    {
        private MusicChartGenerator _generator;
        private RecordMusicalNote.DataModel.LevelInfo _generatedLevel;
        private BackgroundWorker _worker;

        public ChartGeneratorWindow()
        {
            InitializeComponent();
            _generator = new MusicChartGenerator();
            InitializeEvents();
        }

        private void InitializeEvents()
        {
            // 绑定滑块值变化事件
            sliderDifficulty.ValueChanged += (s, e) =>
            {
                lblDifficultyValue.Text = ((int)sliderDifficulty.Value).ToString();
            };

            sliderDensity.ValueChanged += (s, e) =>
            {
                lblDensityValue.Text = $"{(int)(sliderDensity.Value * 100)}%";
            };

            // 绑定游戏模式切换事件
            rbIdol.Checked += (s, e) => UpdateUIForGameMode();
            rbPinball.Checked += (s, e) => UpdateUIForGameMode();
            rbBubble.Checked += (s, e) => UpdateUIForGameMode();

            // 初始化后台工作线程
            _worker = new BackgroundWorker();
            _worker.WorkerReportsProgress = true;
            _worker.DoWork += Worker_DoWork;
            _worker.ProgressChanged += Worker_ProgressChanged;
            _worker.RunWorkerCompleted += Worker_RunWorkerCompleted;

            // 初始化界面状态
            UpdateUIForGameMode();
        }

        #region 事件处理

        /// <summary>
        /// 浏览MIDI文件
        /// </summary>
        private void BtnBrowseMidi_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择MIDI文件",
                Filter = "MIDI文件 (*.mid;*.midi)|*.mid;*.midi|所有文件 (*.*)|*.*",
                FilterIndex = 1,
                RestoreDirectory = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                txtMidiPath.Text = openFileDialog.FileName;
                
                // 自动填充歌曲名称
                if (string.IsNullOrEmpty(txtSongName.Text))
                {
                    txtSongName.Text = Path.GetFileNameWithoutExtension(openFileDialog.FileName);
                }
                
                // 启用预览按钮
                btnPreview.IsEnabled = true;
            }
        }

        /// <summary>
        /// 测试MIDI解析
        /// </summary>
        private void BtnTest_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput()) return;

            try
            {
                lblStatus.Text = "正在运行测试...";
                progressBar.IsIndeterminate = true;

                // 运行测试
                var testResult = ChartGeneratorTest.RunAllTests(txtMidiPath.Text);
                txtPreview.Text = testResult;

                lblStatus.Text = "测试完成";
                progressBar.IsIndeterminate = false;
                progressBar.Value = 100;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"测试失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                lblStatus.Text = "测试失败";
                progressBar.IsIndeterminate = false;
            }
        }

        /// <summary>
        /// 预览谱面
        /// </summary>
        private void BtnPreview_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput()) return;

            try
            {
                lblStatus.Text = "正在分析MIDI文件...";
                progressBar.IsIndeterminate = true;

                // 简单预览，不生成完整谱面
                var midiFile = new MidiSheetMusic.MidiFile(txtMidiPath.Text);
                var preview = GeneratePreviewText(midiFile);
                txtPreview.Text = preview;

                lblStatus.Text = "预览完成";
                progressBar.IsIndeterminate = false;
                progressBar.Value = 100;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"预览失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                lblStatus.Text = "预览失败";
                progressBar.IsIndeterminate = false;
            }
        }

        /// <summary>
        /// 生成谱面
        /// </summary>
        private void BtnGenerate_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput()) return;

            if (_worker.IsBusy)
            {
                MessageBox.Show("正在生成谱面，请稍候...", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 禁用按钮
            btnGenerate.IsEnabled = false;
            btnPreview.IsEnabled = false;
            
            // 开始后台生成
            _worker.RunWorkerAsync();
        }

        /// <summary>
        /// 保存谱面
        /// </summary>
        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (_generatedLevel == null)
            {
                MessageBox.Show("请先生成谱面", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // 保存到数据库
                SaveToDatabase();
                
                // 导出为XML文件
                ExportToXml();
                
                MessageBox.Show("谱面保存成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region 后台工作线程

        private void Worker_DoWork(object sender, DoWorkEventArgs e)
        {
            try
            {
                var worker = sender as BackgroundWorker;
                
                worker.ReportProgress(10, "正在加载MIDI文件...");
                
                // 获取参数
                string midiPath = "";
                int gameMode = 1;
                int difficulty = 5;
                int trackCount = 4;
                bool enableCurveNotes = true;

                Dispatcher.Invoke(() =>
                {
                    midiPath = txtMidiPath.Text;
                    gameMode = rbIdol.IsChecked == true ? 1 : (rbPinball.IsChecked == true ? 2 : 3);
                    difficulty = (int)sliderDifficulty.Value;
                    trackCount = GetTrackCount();
                    enableCurveNotes = cbEnableCurveNotes.IsChecked == true;
                });

                worker.ReportProgress(30, "正在分析音乐结构...");

                // 生成谱面
                _generatedLevel = _generator.GenerateChart(midiPath, gameMode, difficulty, trackCount, enableCurveNotes);
                
                worker.ReportProgress(70, "正在优化谱面布局...");
                
                // 设置额外信息
                Dispatcher.Invoke(() =>
                {
                    if (!string.IsNullOrEmpty(txtSongName.Text))
                        _generatedLevel.SongName = txtSongName.Text;
                    if (!string.IsNullOrEmpty(txtArtist.Text))
                        _generatedLevel.Artist = txtArtist.Text;
                });
                
                worker.ReportProgress(100, "谱面生成完成！");
                
                e.Result = "success";
            }
            catch (Exception ex)
            {
                e.Result = ex.Message;
            }
        }

        private void Worker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            progressBar.Value = e.ProgressPercentage;
            lblStatus.Text = e.UserState?.ToString() ?? "";
        }

        private void Worker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            // 恢复按钮状态
            btnGenerate.IsEnabled = true;
            btnPreview.IsEnabled = true;

            if (e.Result.ToString() == "success")
            {
                btnSave.IsEnabled = true;
                
                // 显示生成结果
                var summary = GenerateResultSummary();
                txtPreview.Text = summary;
                
                MessageBox.Show("谱面生成成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                lblStatus.Text = "生成失败";
                MessageBox.Show($"生成失败: {e.Result}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrEmpty(txtMidiPath.Text))
            {
                MessageBox.Show("请选择MIDI文件", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!File.Exists(txtMidiPath.Text))
            {
                MessageBox.Show("MIDI文件不存在", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            // 验证文件扩展名
            string extension = Path.GetExtension(txtMidiPath.Text).ToLower();
            if (extension != ".mid" && extension != ".midi")
            {
                MessageBox.Show("请选择有效的MIDI文件（.mid 或 .midi）", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            // 验证文件大小（避免过大的文件）
            var fileInfo = new FileInfo(txtMidiPath.Text);
            if (fileInfo.Length > 10 * 1024 * 1024) // 10MB
            {
                MessageBox.Show("MIDI文件过大，请选择小于10MB的文件", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 获取轨道数量
        /// </summary>
        private int GetTrackCount()
        {
            // 弹珠模式固定3轨道
            if (rbPinball.IsChecked == true)
                return 3;

            // 星动和泡泡模式可选择轨道数
            switch (cbTrackCount.SelectedIndex)
            {
                case 0: return 4;
                case 1: return 5;
                case 2: return 6;
                case 3: return 8;
                default: return 4;
            }
        }

        /// <summary>
        /// 根据游戏模式更新界面
        /// </summary>
        private void UpdateUIForGameMode()
        {
            if (rbPinball.IsChecked == true)
            {
                // 弹珠模式：禁用轨道选择和拐弯音符
                cbTrackCount.IsEnabled = false;
                cbTrackCount.SelectedIndex = 0; // 显示4轨道，但实际使用3轨道
                cbEnableCurveNotes.IsEnabled = false;
                cbEnableCurveNotes.IsChecked = false;
            }
            else
            {
                // 星动和泡泡模式：启用轨道选择
                cbTrackCount.IsEnabled = true;

                if (rbIdol.IsChecked == true)
                {
                    // 星动模式：启用拐弯音符
                    cbEnableCurveNotes.IsEnabled = true;
                }
                else
                {
                    // 泡泡模式：禁用拐弯音符
                    cbEnableCurveNotes.IsEnabled = false;
                    cbEnableCurveNotes.IsChecked = false;
                }
            }
        }

        /// <summary>
        /// 生成预览文本
        /// </summary>
        private string GeneratePreviewText(MidiSheetMusic.MidiFile midiFile)
        {
            var sb = new StringBuilder();
            sb.AppendLine("=== MIDI文件信息 ===");
            sb.AppendLine($"轨道数量: {midiFile.TotalTracks}");
            sb.AppendLine($"时间签名: {midiFile.Time.Numerator}/{midiFile.Time.Denominator}");
            sb.AppendLine($"四分音符时长: {midiFile.Time.QuarterNote}");
            sb.AppendLine();

            sb.AppendLine("=== 轨道详情 ===");
            for (int i = 1; i <= Math.Min(midiFile.TotalTracks, 5); i++)
            {
                var track = midiFile.GetTrack(i);
                sb.AppendLine($"轨道 {i}: {track.Notes.Count} 个音符, 乐器: {track.Instrument}");
            }

            if (midiFile.TotalTracks > 5)
            {
                sb.AppendLine($"... 还有 {midiFile.TotalTracks - 5} 个轨道");
            }

            return sb.ToString();
        }

        /// <summary>
        /// 生成结果摘要
        /// </summary>
        private string GenerateResultSummary()
        {
            if (_generatedLevel == null) return "";

            var sb = new StringBuilder();
            sb.AppendLine("=== 谱面生成结果 ===");
            sb.AppendLine($"歌曲名称: {_generatedLevel.SongName}");
            sb.AppendLine($"艺术家: {_generatedLevel.Artist}");
            sb.AppendLine($"BPM: {_generatedLevel.BPM:F1}");
            sb.AppendLine($"轨道数量: {_generatedLevel.TrackCount}");
            sb.AppendLine($"小节数量: {_generatedLevel.BarAmount}");
            sb.AppendLine($"总时长: {_generatedLevel.LevelTime / 1000.0:F1} 秒");
            sb.AppendLine();

            // 显示音符统计
            var gameMode = rbIdol.IsChecked == true ? "星动" : (rbPinball.IsChecked == true ? "弹珠" : "泡泡");
            sb.AppendLine($"=== {gameMode}模式音符统计 ===");

            if (rbIdol.IsChecked == true)
            {
                var idolNotes = _generator.GetIdolNotes();
                sb.AppendLine($"总音符数: {idolNotes.Count}");
                var noteTypes = idolNotes.GroupBy(n => n.NoteType).ToDictionary(g => g.Key, g => g.Count());
                foreach (var type in noteTypes)
                {
                    sb.AppendLine($"{type.Key}: {type.Value} 个");
                }
            }
            else if (rbPinball.IsChecked == true)
            {
                var pinballNotes = _generator.GetPinballNotes();
                sb.AppendLine($"总音符数: {pinballNotes.Count}");
            }
            else
            {
                var bubbleNotes = _generator.GetBubbleNotes();
                sb.AppendLine($"总音符数: {bubbleNotes.Count}");
            }

            return sb.ToString();
        }

        /// <summary>
        /// 保存到数据库
        /// </summary>
        private void SaveToDatabase()
        {
            // 这里需要实现数据库保存逻辑
            // 可以使用现有的Repository类
            lblStatus.Text = "正在保存到数据库...";
        }

        /// <summary>
        /// 导出为XML文件
        /// </summary>
        private void ExportToXml()
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "保存谱面文件",
                Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                FileName = $"{_generatedLevel.SongName}_谱面.xml"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    lblStatus.Text = "正在导出XML文件...";
                    ExportChartToXml(saveFileDialog.FileName);
                    lblStatus.Text = "XML文件导出完成";
                }
                catch (Exception ex)
                {
                    throw new Exception($"XML导出失败: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// 导出谱面到XML文件（使用新的标准格式）
        /// </summary>
        private void ExportChartToXml(string filePath)
        {
            // 使用生成器的新XML导出功能
            if (rbIdol.IsChecked == true)
            {
                _generator.ExportIdolChartToXml(filePath, _generatedLevel.SongName, _generatedLevel.Artist);
            }
            else
            {
                // 对于其他模式，暂时使用旧的导出方式
                ExportLegacyXml(filePath);
            }
        }

        /// <summary>
        /// 旧版XML导出（兼容性）
        /// </summary>
        private void ExportLegacyXml(string filePath)
        {
            var xml = new System.Xml.XmlDocument();
            var root = xml.CreateElement("Level");
            xml.AppendChild(root);

            // 添加关卡信息
            var levelInfo = xml.CreateElement("LevelInfo");
            levelInfo.SetAttribute("BPM", _generatedLevel.BPM.ToString());
            levelInfo.SetAttribute("BeatPerBar", _generatedLevel.BeatPerBar.ToString());
            levelInfo.SetAttribute("BeatLen", _generatedLevel.BeatLen.ToString());
            levelInfo.SetAttribute("TrackCount", _generatedLevel.TrackCount.ToString());
            levelInfo.SetAttribute("SongName", _generatedLevel.SongName);
            levelInfo.SetAttribute("Artist", _generatedLevel.Artist);
            levelInfo.SetAttribute("BarAmount", _generatedLevel.BarAmount.ToString());
            root.AppendChild(levelInfo);

            // 添加音符信息
            var noteInfo = xml.CreateElement("NoteInfo");
            root.AppendChild(noteInfo);

            if (rbPinball.IsChecked == true)
            {
                ExportPinballNotesToXml(xml, noteInfo);
            }
            else if (rbBubble.IsChecked == true)
            {
                ExportBubbleNotesToXml(xml, noteInfo);
            }

            xml.Save(filePath);
        }



        /// <summary>
        /// 导出弹珠模式音符到XML
        /// </summary>
        private void ExportPinballNotesToXml(System.Xml.XmlDocument xml, System.Xml.XmlElement noteInfo)
        {
            var normal = xml.CreateElement("Normal");
            normal.SetAttribute("PosNum", "64");
            noteInfo.AppendChild(normal);

            foreach (var note in _generator.GetPinballNotes())
            {
                var noteElement = xml.CreateElement("Note");
                noteElement.SetAttribute("Bar", note.bar.ToString());
                noteElement.SetAttribute("Pos", note.pos.ToString());
                noteElement.SetAttribute("NoteType", note.NoteType);

                if (note.EndBar.HasValue)
                    noteElement.SetAttribute("EndBar", note.EndBar.ToString());
                if (note.EndPos.HasValue)
                    noteElement.SetAttribute("EndPos", note.EndPos.ToString());
                if (note.MoveTime.HasValue)
                    noteElement.SetAttribute("MoveTime", note.MoveTime.ToString());

                normal.AppendChild(noteElement);
            }
        }

        /// <summary>
        /// 导出泡泡模式音符到XML
        /// </summary>
        private void ExportBubbleNotesToXml(System.Xml.XmlDocument xml, System.Xml.XmlElement noteInfo)
        {
            var normal = xml.CreateElement("Normal");
            normal.SetAttribute("PosNum", "64");
            noteInfo.AppendChild(normal);

            foreach (var note in _generator.GetBubbleNotes())
            {
                var noteElement = xml.CreateElement("Note");
                noteElement.SetAttribute("Bar", note.Bar.ToString());
                noteElement.SetAttribute("BeatPos", note.BeatPos.ToString());
                noteElement.SetAttribute("Type", note.Type.ToString());

                normal.AppendChild(noteElement);
            }
        }

        #endregion
    }
}
