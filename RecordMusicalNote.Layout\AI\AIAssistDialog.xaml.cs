using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using RecordMusicalNote;
using Newtonsoft.Json;

namespace MyWPF.Layout.AI
{
    /// <summary>
    /// AI辅助写谱对话框
    /// </summary>
    public partial class AIAssistDialog : Window
    {
        private AIChartGenerationService _aiService;
        private string _midiFilePath;
        private List<IIdol> _existingNotes;
        private AIGenerationResult _lastResult;
        private AudioAnalysisData _audioAnalysis;

        public AIAssistDialog(string midiFilePath, List<IIdol> existingNotes = null, AudioAnalysisData audioAnalysis = null)
        {
            InitializeComponent();
            _midiFilePath = midiFilePath;
            _existingNotes = existingNotes ?? new List<IIdol>();
            _audioAnalysis = audioAnalysis;

            InitializeUI();
            LoadSettings();
        }

        /// <summary>
        /// 获取生成的音符
        /// </summary>
        public List<IIdol> GeneratedNotes => _lastResult?.GeneratedNotes ?? new List<IIdol>();

        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            // 绑定滑块事件
            sliderDifficulty.ValueChanged += (s, e) => lblDifficulty.Text = ((int)e.NewValue).ToString();
            sliderDensity.ValueChanged += (s, e) => lblDensity.Text = $"{e.NewValue:P0}";
            sliderShortNotes.ValueChanged += (s, e) => lblShortNotes.Text = $"{e.NewValue:P0}";
            sliderLongNotes.ValueChanged += (s, e) => lblLongNotes.Text = $"{e.NewValue:P0}";
            sliderSlipNotes.ValueChanged += (s, e) => lblSlipNotes.Text = $"{e.NewValue:P0}";

            // 设置初始状态
            btnGenerate.IsEnabled = true;
            btnExportNotes.IsEnabled = false;
            btnCopyToClipboard.IsEnabled = false;
            progressGeneration.Visibility = Visibility.Hidden;
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            var config = AIConfigManager.LoadConfig();
            txtApiKey.Text = config.ApiKey ?? "";
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        private void SaveSettings()
        {
            var config = AIConfigManager.LoadConfig();
            config.ApiKey = txtApiKey.Text;
            AIConfigManager.SaveConfig(config);
        }

        /// <summary>
        /// 开始生成按钮点击
        /// </summary>
        private async void BtnGenerate_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtApiKey.Text))
            {
                MessageBox.Show("请输入API密钥", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // 保存设置
                SaveSettings();

                // 禁用生成按钮
                btnGenerate.IsEnabled = false;
                progressGeneration.Visibility = Visibility.Visible;
                progressGeneration.IsIndeterminate = true;
                lblGenerationStatus.Text = "正在生成中...";

                // 创建AI服务
                var config = AIConfigManager.LoadConfig();
                config.ApiKey = txtApiKey.Text; // 使用当前输入的API密钥

                _aiService = new AIChartGenerationService(config);

                // 构建请求
                var request = BuildGenerationRequest();

                // 调用AI生成
                _lastResult = await _aiService.GenerateChartAsync(request);

                // 显示结果
                DisplayResult(_lastResult);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                lblGenerationStatus.Text = $"生成失败: {ex.Message}";
            }
            finally
            {
                // 恢复UI状态
                btnGenerate.IsEnabled = true;
                progressGeneration.IsIndeterminate = false;
                progressGeneration.Visibility = Visibility.Hidden;
            }
        }

        /// <summary>
        /// 构建生成请求
        /// </summary>
        private AIGenerationRequest BuildGenerationRequest()
        {
            return new AIGenerationRequest
            {
                MidiFilePath = _midiFilePath,
                GameMode = "idol",
                TrackCount = 4,
                Difficulty = (int)sliderDifficulty.Value,
                Style = GetSelectedStyle(),
                NoteDensity = sliderDensity.Value,
                MaxNotesPerSecond = int.Parse(txtMaxNotesPerSecond.Text),
                FollowMelody = chkFollowMelody.IsChecked == true,
                TrackBalance = chkTrackBalance.IsChecked == true,
                MaxGeneratedNotes = int.Parse(txtMaxNotes.Text),
                StartTime = double.Parse(txtStartTime.Text),
                EndTime = double.Parse(txtEndTime.Text),
                ExistingNotes = _existingNotes,
                AudioAnalysis = _audioAnalysis
            };
        }

        /// <summary>
        /// 获取选中的风格
        /// </summary>
        private string GetSelectedStyle()
        {
            var selectedItem = cmbStyle.SelectedItem as ComboBoxItem;
            var content = selectedItem?.Content?.ToString() ?? "";
            
            if (content.Contains("节奏型")) return "rhythmic";
            if (content.Contains("旋律型")) return "melodic";
            if (content.Contains("混合型")) return "hybrid";
            
            return "rhythmic";
        }

        /// <summary>
        /// 获取API URL
        /// </summary>
        private string GetApiUrl()
        {
            var selectedProvider = cmbProvider.SelectedItem as ComboBoxItem;
            var provider = selectedProvider?.Content?.ToString() ?? "DeepSeek";
            
            switch (provider)
            {
                case "DeepSeek":
                    return "https://api.deepseek.com/v1/chat/completions";
                case "OpenAI":
                    return "https://api.openai.com/v1/chat/completions";
                case "Claude":
                    return "https://api.anthropic.com/v1/messages";
                default:
                    return "https://api.deepseek.com/v1/chat/completions";
            }
        }

        /// <summary>
        /// 获取模型名称
        /// </summary>
        private string GetModelName()
        {
            var selectedProvider = cmbProvider.SelectedItem as ComboBoxItem;
            var provider = selectedProvider?.Content?.ToString() ?? "DeepSeek";
            
            switch (provider)
            {
                case "DeepSeek":
                    return "deepseek-chat";
                case "OpenAI":
                    return "gpt-4";
                case "Claude":
                    return "claude-3-sonnet-20240229";
                default:
                    return "deepseek-chat";
            }
        }

        /// <summary>
        /// 显示生成结果
        /// </summary>
        private void DisplayResult(AIGenerationResult result)
        {
            if (result.Success)
            {
                lblGenerationStatus.Text = $"生成成功！共生成 {result.GeneratedNotes.Count} 个音符";
                
                // 显示音符数据
                dgGeneratedNotes.ItemsSource = result.GeneratedNotes;
                
                // 显示统计信息
                var stats = BuildStatsText(result);
                lblStats.Text = stats;
                
                // 显示原始响应
                txtRawResponse.Text = result.OriginalResponse;
                
                // 启用导出按钮
                btnExportNotes.IsEnabled = true;
                btnCopyToClipboard.IsEnabled = true;
                
                // 如果有验证错误，显示警告
                if (result.ValidationErrors.Any())
                {
                    var errorMessage = $"生成成功，但有 {result.ValidationErrors.Count} 个音符验证失败:\n" +
                                     string.Join("\n", result.ValidationErrors.Take(5));
                    MessageBox.Show(errorMessage, "验证警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            else
            {
                lblGenerationStatus.Text = $"生成失败: {result.ErrorMessage}";
                MessageBox.Show($"生成失败: {result.ErrorMessage}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                
                // 显示原始响应用于调试
                if (!string.IsNullOrEmpty(result.OriginalResponse))
                {
                    txtRawResponse.Text = result.OriginalResponse;
                }
            }
        }

        /// <summary>
        /// 构建统计信息文本
        /// </summary>
        private string BuildStatsText(AIGenerationResult result)
        {
            var stats = result.Stats;
            var metadata = result.Metadata;
            
            var text = $"生成统计信息:\n\n";
            text += $"生成时间: {stats?.GenerationTime:yyyy-MM-dd HH:mm:ss}\n";
            text += $"耗时: {stats?.Duration.TotalSeconds:F2} 秒\n";
            text += $"生成音符数: {result.GeneratedNotes.Count}\n";
            text += $"验证通过率: {stats?.SuccessRate:P0}\n\n";
            
            if (metadata != null)
            {
                text += $"AI评估信息:\n";
                text += $"预估难度: {metadata.estimatedDifficulty:F1}/10\n";
                text += $"平均音符密度: {metadata.averageNotesPerSecond:F2} 个/秒\n\n";
            }
            
            // 音符类型分布
            var noteTypeStats = result.GeneratedNotes
                .GroupBy(n => n.NoteType)
                .ToDictionary(g => g.Key, g => g.Count());
            
            text += "音符类型分布:\n";
            foreach (var kvp in noteTypeStats)
            {
                var percentage = (double)kvp.Value / result.GeneratedNotes.Count;
                text += $"  {kvp.Key}: {kvp.Value} 个 ({percentage:P0})\n";
            }
            
            // 轨道分布
            var trackStats = result.GeneratedNotes
                .GroupBy(n => n.TargetTrack)
                .ToDictionary(g => g.Key, g => g.Count());
            
            text += "\n轨道分布:\n";
            foreach (var kvp in trackStats)
            {
                var percentage = (double)kvp.Value / result.GeneratedNotes.Count;
                text += $"  {kvp.Key}: {kvp.Value} 个 ({percentage:P0})\n";
            }
            
            return text;
        }

        /// <summary>
        /// 导出音符按钮点击
        /// </summary>
        private void BtnExportNotes_Click(object sender, RoutedEventArgs e)
        {
            if (_lastResult?.GeneratedNotes == null || !_lastResult.GeneratedNotes.Any())
            {
                MessageBox.Show("没有可导出的音符", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 设置对话框结果并关闭
            DialogResult = true;
            Close();
        }

        /// <summary>
        /// 复制到剪贴板按钮点击
        /// </summary>
        private void BtnCopyToClipboard_Click(object sender, RoutedEventArgs e)
        {
            if (_lastResult?.GeneratedNotes == null || !_lastResult.GeneratedNotes.Any())
            {
                MessageBox.Show("没有可复制的音符", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                var json = JsonConvert.SerializeObject(_lastResult.GeneratedNotes, Formatting.Indented);
                Clipboard.SetText(json);
                MessageBox.Show("音符数据已复制到剪贴板", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击
        /// </summary>
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 窗口关闭时清理资源
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            _aiService?.Dispose();
            base.OnClosed(e);
        }
    }
}
