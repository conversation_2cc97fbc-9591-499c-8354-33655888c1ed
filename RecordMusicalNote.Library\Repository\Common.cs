﻿using Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote.Library.Repository
{
   public class Common
    {
        public static DataModel.LevelInfo EntityToDto(ILevelInfo levelInfo)
        {
            DataModel.LevelInfo dto = new DataModel.LevelInfo();
            if (levelInfo != null)
            {
                dto.Artist = levelInfo.Artist;
                dto.BarAmount = levelInfo.BarAmount;
                dto.BeatLen = levelInfo.BeatLen;
                dto.BeatPerBar = levelInfo.BeatPerBar;
                dto.BeginBarLen = levelInfo.BeginBarLen;
                dto.BPM = levelInfo.BPM;
                dto.EnterTimeAdjust = levelInfo.EnterTimeAdjust;
                dto.IsFourTrack = levelInfo.IsFourTrack;
                dto.LevelInfoId = levelInfo.LevelInfoId;
                dto.LevelPreTime = levelInfo.LevelPreTime;
                dto.LevelTime = levelInfo.LevelTime;
                dto.NotePreShow = levelInfo.NotePreShow;
                dto.SongName = levelInfo.SongName;
                dto.Star = levelInfo.Star;
                dto.TrackCount = levelInfo.TrackCount;
            }
            return dto;
        }
        public  static bool CheckHasExist(string songName,int star,float bpm)
        {
            SqlHelper helper = new SqlHelper();
            string sql = @"SELECT  count(0) from LevelInfo where SongName='{0}' and Star={1} and BPM={2}";
            sql = string.Format(sql, songName, star, bpm);
            return  Convert.ToInt16(helper.ExecuteScalar(sql)) > 0;
        }
        public static bool CheckHasExist(string songName)
        {
            SqlHelper helper = new SqlHelper();
            string sql = @"SELECT  count(0) from LevelInfo where SongName='{0}";
            sql = string.Format(sql, songName);
            return Convert.ToInt16(helper.ExecuteScalar(sql)) > 0;
        }
    }
}
