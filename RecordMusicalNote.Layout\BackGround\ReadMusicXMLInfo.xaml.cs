﻿using CommonModel;
using Microsoft.Win32;
using MyMidi;
using RecordMusicalNote;
using RecordMusicalNote.IRepository;
using RecordMusicalNote.Library.Repository;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Xml;

namespace MyWPF.Layout.BackGround
{
    /// <summary>
    /// ReadMidiInfo.xaml 的交互逻辑
    /// </summary>
    public partial class ReadMusicXMLInfo : Window
    {
        public ReadMusicXMLInfo()
        {
            InitializeComponent();
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            _instrumentData.MouseDoubleClick += _instrumentData_MouseDoubleClick;
            cmMode.SelectedIndex = 2;

        }
        IList<MusicXMLInfo> _selectedmusicXMLInfos;
        IList<MusicXMLInfo> _allMusicXMLInfos;
        IList<DataGrid> _dataGrids;
        private void _instrumentData_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            MidiTrack MidiTrack = _instrumentData.SelectedItem as MidiTrack;
            if (_musicXMLDoc == null || MidiTrack == null) return;
            _selectedmusicXMLInfos = GetSelectedMusicInfoFromMusicXML(MidiTrack);
            var groupByData = _selectedmusicXMLInfos.GroupBy(a => a.ClefSign);
            DataGrid _noteDetailGrid = null;
            double marigin_left = 10;
            _dataGrids = new List<DataGrid>();
            foreach (var item in groupByData)//动态创建声部表格
            {
                _noteDetailGrid = new DataGrid();
                _noteDetailGrid.AutoGenerateColumns = false;
                _noteDetailGrid.IsReadOnly = true;
                _noteDetailGrid.Margin = new Thickness(marigin_left, 0, 0, 0);
                var clefColumn = new DataGridTextColumn();
                clefColumn.Header = item.Key;
                clefColumn.Binding = new Binding("Step");
                clefColumn.Width = 100;
                _noteDetailGrid.Columns.Add(clefColumn);
                var measureColumn = new DataGridTextColumn();
                measureColumn.Header = "所在小节数";
                measureColumn.Binding = new Binding("Measure");
                measureColumn.Width = 100;
                _noteDetailGrid.Columns.Add(measureColumn);
                var positionColumn = new DataGridTextColumn();
                positionColumn.Header = "所在位置";
                positionColumn.Binding = new Binding("MusicXMLPosition");
                positionColumn.Width = 100;
                _noteDetailGrid.Columns.Add(positionColumn);
                var durationColumn = new DataGridTextColumn();
                durationColumn.Header = "时值";
                durationColumn.Binding = new Binding("Type");
                durationColumn.Width = 100;
                _noteDetailGrid.Columns.Add(durationColumn);
                _noteDetailGrid.ItemsSource = _selectedmusicXMLInfos.Where(a => a.ClefSign == item.Key);
                _grid.Children.Add(_noteDetailGrid);
                Grid.SetRow(_noteDetailGrid, 3);
                marigin_left += 300;
                _dataGrids.Add(_noteDetailGrid);
            }

        }



        private IList<MusicXMLInfo> GetSelectedMusicInfoFromMusicXML(MidiTrack selectedMidiTrack)
        {
            if (_allMusicXMLInfos == null || _allMusicXMLInfos.Count <= 0) return null;
            return _allMusicXMLInfos.Where(a => a.MusicXMLInfoID == selectedMidiTrack.MusicXMLID).ToList();
        }
        private class ClefSimpleModel
        {
            public string Number { get; set; }
            public string Sign { get; set; }
        }
        private IList<MusicXMLInfo> GetAllMusicInfoFromMusicXML(XmlDocument musicXMLDoc)
        {
            XmlNodeList list;
            IList<MusicXMLInfo> musicXMLs = new List<MusicXMLInfo>();
            list = musicXMLDoc.SelectNodes("/score-partwise/part");
            string PartName = "";
            string PartAbbreviationg = "";
            int MidiProgram = -1;
            int Measure = -1;
            int Divisions = -1;
            string lastBeam = "";
            bool isFirstMeasure;
            foreach (XmlNode partItem in list)//循环每一种乐器
            {
                IList<ClefSimpleModel> clefs = new List<ClefSimpleModel>();
                XmlNodeList measureNoteList = partItem.SelectNodes("measure");//获取所有小节
                int lastMeasureDivisions = 1;
                int beats = 0;
                int beatType = 4;
                double usedSpace = 0;
                isFirstMeasure = true;
                foreach (XmlNode measure in measureNoteList)//循环每一个小节
                {
                    if (measure.SelectNodes("attributes/clef") != null)
                    {
                        foreach (XmlNode item in measure.SelectNodes("attributes/clef"))
                        {
                            ClefSimpleModel clefSimpleModel = new ClefSimpleModel();
                            clefSimpleModel.Number = item.Attributes[0].Value;
                            clefSimpleModel.Sign = item.SelectSingleNode("sign").InnerText;
                            clefs.Add(clefSimpleModel);
                        }

                    }
                    string MusicXMLInfoID = partItem.Attributes[0].Value;
                    XmlNodeList scorePartList = musicXMLDoc.SelectNodes("/score-partwise/part-list/score-part");
                    foreach (XmlNode item in scorePartList)
                    {
                        if (partItem.Attributes[0].Value == item.Attributes[0].Value)
                        {
                            PartName = item.SelectSingleNode("part-name").InnerText;
                            PartAbbreviationg = item.SelectSingleNode("part-abbreviation").InnerText;
                            MidiProgram = item.SelectSingleNode("midi-instrument/midi-program") == null ? -1 : int.Parse(item.SelectSingleNode("midi-instrument/midi-program").InnerText);
                            break;
                        }
                    }
                    if (measure.SelectSingleNode("attributes/time") != null)
                    {
                        beats = int.Parse(measure.SelectSingleNode("attributes/time/beats").InnerText);
                        beatType = int.Parse(measure.SelectSingleNode("attributes/time/beat-type").InnerText);
                    }
                    Measure = int.Parse(measure.Attributes[0].Value);
                    if (measure.SelectSingleNode("attributes/divisions") != null)
                    {
                        int divisions = int.Parse(measure.SelectSingleNode("attributes/divisions").InnerText);
                        lastMeasureDivisions = Divisions = divisions;
                    }
                    else
                    {
                        Divisions = lastMeasureDivisions;
                    }
                    foreach (XmlNode item in measure.SelectNodes("note"))//获取每一个note的信息
                    {

                        MusicXMLInfo musicXML = new MusicXMLInfo();
                        musicXML.MusicXMLInfoID = MusicXMLInfoID;
                        musicXML.PartAbbreviationg = PartAbbreviationg;
                        musicXML.MidiProgram = MidiProgram;
                        musicXML.PartName = PartName;
                        musicXML.Measure = Measure;
                        musicXML.Divisions = Divisions;
                        musicXML.Beats = beats;
                        musicXML.BeatType = beatType;
                        if (item.SelectSingleNode("staff") != null)
                        {
                            musicXML.ClefSign = clefs.Where(a => a.Number == item.SelectSingleNode("staff").InnerText).FirstOrDefault().Sign;
                        }
                        if (item.SelectSingleNode("rest") != null)
                        {
                            musicXML.Step = "r";//休止符
                            musicXML.Octave = 0;
                            musicXML.Alter = 0;
                        }
                        else
                        {
                            if (item.SelectSingleNode("pitch/step") != null)
                            {
                                musicXML.Step = item.SelectSingleNode("pitch/step").InnerText;
                                musicXML.Octave = int.Parse(item.SelectSingleNode("pitch/octave").InnerText);
                                if (item.SelectSingleNode("/alter") != null)
                                {
                                    musicXML.Alter = int.Parse(item.SelectSingleNode("alter").InnerText);
                                }
                                else
                                {
                                    musicXML.Alter = 0;
                                }
                            }
                            else
                            {
                                musicXML.Step = item.SelectSingleNode("unpitched/display-step").InnerText;
                                musicXML.Octave = int.Parse(item.SelectSingleNode("unpitched/display-octave").InnerText);
                            }
                        }
                        musicXML.Duration = int.Parse(item.SelectSingleNode("duration").InnerText);
                        musicXML.Type = item.SelectSingleNode("type").InnerText;
                        string currentBeam = item.SelectSingleNode("beam") == null ? "" : item.SelectSingleNode("beam").InnerText;
                        if (isFirstMeasure)
                        {
                            musicXML.MusicXMLPosition = 0;
                        }
                        else
                        {
                            if (string.IsNullOrEmpty(currentBeam) || currentBeam != lastBeam)
                                usedSpace += double.Parse((musicXML.Duration / musicXML.Divisions * 16).ToString());
                            musicXML.MusicXMLPosition = usedSpace;
                        }
                        lastBeam = currentBeam;
                        isFirstMeasure = false;
                        musicXMLs.Add(musicXML);
                    }

                }
            }

            return musicXMLs;
        }
        private IList<string> GetUsedTrack(IList<UsedTrack> UsedTracks, double currentPos, double currentBar)
        {
            var usedTracks = UsedTracks.Where(a => a.UsedPos == currentPos && a.UsedBar == currentBar);
            if (usedTracks == null) return new List<string>();
            IList<string> strUsedTrackNames = new List<string>();
            foreach (var usedTrack in usedTracks)
            {
                foreach (var item in usedTrack.UsedTrackName)
                {
                    strUsedTrackNames.Add(item);
                }

            }
            return strUsedTrackNames.Distinct().ToList();
        }

        XmlDocument _musicXMLDoc;
        private void openMusicXML_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "XML格式(*.XML)|*.XML";
            dlg.Multiselect = true;
            if (dlg.ShowDialog() != true) return; //不为确定时返回
            if (_dataGrids != null)
            {
                foreach (DataGrid dataGrid in _dataGrids)
                {
                    dataGrid.ItemsSource = null;
                }
            }
            IList<MidiTrack> musicXMLInstruments;
            string filePath = dlg.FileName;
            _musicXMLDoc = new XmlDocument();
            _musicXMLDoc.Load(filePath);
            XmlElement root = null;
            root = _musicXMLDoc.DocumentElement;
            XmlNodeList listNodes = null;
            //获取乐器列表
            listNodes = root.SelectNodes("/score-partwise/part-list/score-part");
            if (listNodes == null || listNodes.Count <= 0) return;
            musicXMLInstruments = new List<MidiTrack>();
            foreach (XmlNode item in listNodes)
            {
                MidiTrack instrument = new MidiTrack();
                instrument.MusicXMLID = item.Attributes[0].Value;
                instrument.Instrument = item.FirstChild.InnerText;
                musicXMLInstruments.Add(instrument);
            }
            _instrumentData.ItemsSource = musicXMLInstruments;
            _allMusicXMLInfos = GetAllMusicInfoFromMusicXML(_musicXMLDoc);
        }
        private void BtnCreateAllNote_Click(object sender, RoutedEventArgs e)
        {

        }

        private void BtnCreateSingleChannelNote_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedmusicXMLInfos == null) return;
            IList<MusicXMLInfo> lists = _selectedmusicXMLInfos;
            IdolRepository repository = new IdolRepository();
            string instrument = "";
            IList<IdolTrack> idolTracks = CommonModel.Common.GetIdolTracks(4);
            Random rd = new Random();
            string lastGameTrack = "";
            int rdNum = rd.Next(1, 4);
            bool isFirst = true;
            FileStream fs = new FileStream("E:\\" + instrument + "MidiToXML.txt", FileMode.OpenOrCreate);
            StreamWriter sw = new StreamWriter(fs);
            sw.WriteLine("<Normal>");
            IList<UsedTrack> UsedTracks = new List<UsedTrack>();
            //原歌曲bpm 一个小节等于一个bar的长度
            foreach (MusicXMLInfo item in lists)
            {
                if (item.Step == "r") continue;
                IIdol idol = repository.CreateNewIdol();
                if (item.Duration > item.Divisions)
                    idol.NoteType = "long";
                else
                    idol.NoteType = "short";
                idol.Bar = int.Parse(Math.Floor(item.GameBar).ToString());
                idol.Pos = item.GamePos;
                idol.EndBar = int.Parse(Math.Floor(item.GameBar).ToString());
                idol.EndPos = item.GamePos;
                if (isFirst)
                {
                    idol.FromTrack = idolTracks.Where(a => a.ID == rdNum).FirstOrDefault().Name;
                    idol.TargetTrack = idol.FromTrack;
                    isFirst = false;
                }
                else
                {
                    IList<IdolTrack> usableIdolTracks = idolTracks.Where(a => !GetUsedTrack(UsedTracks, idol.Pos, idol.Bar).Contains(a.Name)).ToList();
                    if (usableIdolTracks.Count <= 0) idol.FromTrack = lastGameTrack;
                    else
                        idol.FromTrack = usableIdolTracks.OrderBy(a => Guid.NewGuid()).Take(1).FirstOrDefault().Name;

                    idol.TargetTrack = idol.FromTrack;
                }
                if (idol.NoteType == "long")
                {
                    double usedPostion = (idol.EndBar * 64 + idol.EndPos - idol.Bar * 64 + idol.Pos) / 2;
                    for (int i = 0; i < usedPostion; i++)
                    {
                        UsedTrack ut = new UsedTrack();
                        ut.UsedBar = (idol.Bar * 64 + idol.Pos + i * 2) / 64;
                        ut.UsedPos = (idol.Pos + i * 2) % 64;
                        IList<string> usedTrackNames = new List<string>();
                        usedTrackNames.Add(idol.TargetTrack);
                        ut.UsedTrackName = usedTrackNames;
                        UsedTracks.Add(ut);
                    }
                }
                else if (idol.NoteType == "short")
                {
                    UsedTrack ut = new UsedTrack();
                    ut.UsedBar = idol.Bar;
                    ut.UsedPos = idol.Pos;
                    IList<string> usedTrackNames = new List<string>();
                    usedTrackNames.Add(idol.TargetTrack);
                    ut.UsedTrackName = usedTrackNames;
                    UsedTracks.Add(ut);
                }
                else if (idol.NoteType == "slip")
                {
                    UsedTrack ut = new UsedTrack();
                    ut.UsedBar = idol.Bar;
                    ut.UsedPos = idol.Pos;
                    IList<string> usedTrackNames = new List<string>();
                    usedTrackNames.Add(idol.TargetTrack);
                    usedTrackNames.Add(idol.EndTrack);
                    ut.UsedTrackName = usedTrackNames;
                    UsedTracks.Add(ut);
                }
                sw.WriteLine(idol.ToString());
                lastGameTrack = idol.FromTrack;

            }
            sw.WriteLine("</Normal>");
            sw.Close();
            MessageBox.Show("已创建E:\\" + instrument + "MidiToXML.txt文件");
        }
    }
}
