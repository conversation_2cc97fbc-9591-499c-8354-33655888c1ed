/*
 * Copyright (c) 2007-2008 <PERSON><PERSON><PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License version 2.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;

namespace MidiSheetMusic {

/*
 * The SheetMusic Control is the main class for displaying the sheet music.
 * The SheetMusic class has the following public methods:
 *
 * SheetMusic()
 *   Create a new SheetMusic control from the given midi file.
 *   Only display the selected tracks.
 * 
 * SetZoom()
 *   Set the zoom level to display the sheet music at.
 *
 * DoPrint()
 *   Print a single page of sheet music.
 *
 * GetTotalPages()
 *   Get the total number of sheet music pages.
 *
 * These public methods are called from the MidiSheetMusic Form Window.
 *
 */
  

public class SheetMusic : Control {

    /* Constants used by the MusicSymbols when drawing. */
    public const int LineWidth = 1;
    public const int LineSpace = 7;
    public const int StaffHeight = LineSpace*4 + LineWidth*5;
    public const int NoteHeight = LineSpace + LineWidth;
    public const int NoteWidth = 3*LineSpace/2;
    public const int LeftMargin = 4;

    /* The PageWidth and PageHeight are used for printing, so they
     * should be less than 8.5 x 11 inches.
     */
    public const int PageWidth = 800;
    public const int PageHeight = 1050;

    /* The SheetMusic control consists of a list of staffs (from top to
     * bottom).  */
    List<Staff> staffs;

    /* The number of tracks */
    int numtracks;

    /* The zoom level to draw at */
    float zoom;


    /* Create a new SheetMusic control.
     * - MidiFile is the parsed midi file to display.
     * - tracksselected contains which tracks to display.
     * - When twostaffs is true, combine all tracks into two staffs.
     */
    public SheetMusic(MidiFile file, bool[] trackselected, bool twostaffs) {
        zoom = 1.0f;
        List<MidiTrack> tracks = new List<MidiTrack>();

        /* The track numbering in the Tracks menu starts at 1, not 0. */
        for (int track = 1; track <= file.TotalTracks; track++) {
            if (trackselected[track]) {
                tracks.Add(file.GetTrack(track));
            }
        }
        if (twostaffs) {
            tracks = file.CombineToTwoTracks(tracks);
        }

        TimeSignature time = file.Time; 

        /* To make the sheet music look nicer, we round the start times
         * so that notes close together appear as a single chord.  We
         * also extend the note durations, so that we have longer notes
         * and fewer rest symbols.
         */
        tracks = MidiFile.RoundStartTimes(tracks, time.QuarterNote);
        tracks = MidiFile.RoundDurations(tracks, time.QuarterNote);

        KeySignature key = GetKeySignature(tracks);
        numtracks = tracks.Count;

        /* Create all the music symbols (notes, rests, vertical bars, and
         * clef changes).  The symbols variable contains a list of music 
         * symbols for each track.  The list does not include the left-side 
         * Clef and key signature symbols.  Those can only be calculated 
         * when we create the staffs.
         */
        List<MusicSymbol>[] symbols = new List<MusicSymbol> [ numtracks ];
        for (int tracknum = 0; tracknum < numtracks; tracknum++) {
            MidiTrack t = tracks[tracknum];
            ClefMeasures clefs = new ClefMeasures(t.Notes, time.Measure);
            List<ChordSymbol> chords = CreateChords(t.Notes, key, time, clefs);
            symbols[tracknum] = CreateSymbols(chords, clefs, time);
        }

        SymbolWidths widths = new SymbolWidths(symbols);
        AlignSymbols(symbols, widths);
        MakeChordTriplets(symbols, time, widths);
        MakeChordPairs(symbols, time, widths);
        staffs = CreateStaffs(symbols, key, time.Measure);
        BackColor = Color.White;
        SetZoom(1.0f);
    }

    /* Get the best key signature given the midi notes in all the tracks. */
    KeySignature GetKeySignature(List<MidiTrack> tracks) {
        List<int> notenums = new List<int>();
        foreach (MidiTrack t in tracks) {
            foreach (MidiNote m in t.Notes) {
                notenums.Add(m.Number);
            }
        }
        return KeySignature.Guess(notenums);
    }


    /* Create the chord symbols for a single track. */
    List<ChordSymbol> CreateChords(List<MidiNote> midinotes, 
                                   KeySignature key,
                                   TimeSignature time,
                                   ClefMeasures clefs) {

        int i = 0;
        List<ChordSymbol> chords = new List<ChordSymbol>();
        int len = midinotes.Count; 

        while (i < len) {

            int starttime = midinotes[i].StartTime;
            Clef clef = clefs.GetClef(starttime);

            /* Group all the midi notes with the same start time
             * into the notes list.
             */
            List<MidiNote> notes = new List<MidiNote>();
            notes.Add(midinotes[i]);
            i++;
            while (i < len && midinotes[i].StartTime == starttime) {
                notes.Add(midinotes[i]);
                i++;
            }

            /* Create a single chord from the group of midi notes with
             * the same start time.
             */
            ChordSymbol chord = new ChordSymbol(notes.ToArray(), 
                                                key, time, clef);
            chords.Add(chord);
        }

        return chords;
    }

    /* Given the chord symbols for a track, create a new symbol list
     * that contains the chord symbols, vertical bars, rests, and
     * clef changes.
     */
    List<MusicSymbol> CreateSymbols(List<ChordSymbol> chords,
                                    ClefMeasures clefs,
                                    TimeSignature time) {

        List<MusicSymbol> symbols = new List<MusicSymbol>();
        symbols = AddBars(chords, time);
        symbols = AddRests(symbols, time);
        symbols = AddClefChanges(symbols, clefs, time);
        return symbols;
    }

    /* Add in the vertical bars delimiting measures. */
    List<MusicSymbol> AddBars(List<ChordSymbol> chords, TimeSignature time) {
        List<MusicSymbol> symbols = new List<MusicSymbol>();

        /* The starttime of the beginning of the measure */
        int measuretime = 0;

        int i = 0;
        while (i < chords.Count) {
            if (measuretime <= chords[i].StartTime) {
                symbols.Add(new BarSymbol(measuretime) );
                measuretime += time.Measure;
            }
            else {
                symbols.Add(chords[i]);
                i++;
            }
        }

        /* Add the final vertical bar to the last measure */
        symbols.Add(new BarSymbol(measuretime) );
        return symbols;
    }

    /* Add rest symbols between notes.  All times below are 
     * measured in pulses.
     */
    List<MusicSymbol> AddRests(List<MusicSymbol> symbols, TimeSignature time) {
        int prevtime = 0;

        List<MusicSymbol> result = new List<MusicSymbol>( symbols.Count );

        foreach (MusicSymbol s in symbols) {
            int starttime = s.StartTime;
            RestSymbol[] rests = GetRests(time, prevtime, starttime);
            if (rests != null) {
                foreach (RestSymbol r in rests) {
                    result.Add(r);
                }
            }

            result.Add(s);

            /* Set prevtime to the end time of the last note/symbol. */
            if (s is ChordSymbol) {
                ChordSymbol c = (ChordSymbol)s;
                prevtime = Math.Max( c.EndTime, prevtime );
            }
            else {
                prevtime = Math.Max(starttime, prevtime);
            }
        }
        return result;
    }

    /* Return the rest symbols needed to fill the time interval between
     * start and end.  If no rests are needed, return null.
     */
    RestSymbol[] GetRests(TimeSignature time, int start, int end) {
        RestSymbol[] result;
        RestSymbol r1, r2;

        if (end - start < 0)
            return null;

        NoteDuration dur = time.GetNoteDuration(end - start);
        switch (dur) {
            case NoteDuration.Whole:
            case NoteDuration.Half:
            case NoteDuration.Quarter:
            case NoteDuration.Eighth:
                r1 = new RestSymbol(start, dur);
                result = new RestSymbol[]{ r1 };
                return result;

            case NoteDuration.DottedHalf:
                r1 = new RestSymbol(start, NoteDuration.Half);
                r2 = new RestSymbol(start + time.QuarterNote*2, 
                                    NoteDuration.Quarter);
                result = new RestSymbol[]{ r1, r2 };
                return result;

            case NoteDuration.DottedQuarter:
                r1 = new RestSymbol(start, NoteDuration.Quarter);
                r2 = new RestSymbol(start + time.QuarterNote, 
                                    NoteDuration.Eighth);
                result = new RestSymbol[]{ r1, r2 };
                return result; 

            case NoteDuration.DottedEighth:
                r1 = new RestSymbol(start, NoteDuration.Eighth);
                r2 = new RestSymbol(start + time.QuarterNote/2, 
                                    NoteDuration.Sixteenth);
                result = new RestSymbol[]{ r1, r2 };
                return result;

            default:
                return null;
        }
    }

    /* The current clef is always shown at the beginning of the staff, on
     * the left side.  However, the clef can also change from measure to 
     * measure. When it does, a Clef symbol must be shown to indicate the 
     * change in clef.  This function adds these Clef change symbols.
     * This function does not add the main Clef Symbol that begins each
     * staff.  That is done in the Staff() contructor.
     */
    List<MusicSymbol> AddClefChanges(List<MusicSymbol> symbols,
                                     ClefMeasures clefs,
                                     TimeSignature time) {

        List<MusicSymbol> result = new List<MusicSymbol>( symbols.Count );
        Clef prevclef = clefs.GetClef(0);
        foreach (MusicSymbol s in symbols) {
            /* A BarSymbol indicates a new measure */
            if (s is BarSymbol) {
                Clef c = clefs.GetClef(s.StartTime);
                if (c != prevclef) {
                    result.Add(new ClefSymbol(c, s.StartTime-1, true));
                }
                prevclef = c;
            }
            result.Add(s);
        }
        return result;
    }
           

    /* Notes with the same start times in different staffs should be
     * vertically aligned.  The SymbolWidths class is used to help 
     * vertically align symbols.
     *
     * First, each track should have a symbol for every starttime that
     * appears in the Midi File.  If a track doesn't have a symbol for a
     * particular starttime, then add a "blank" symbol for that time.
     *
     * Next, make sure the symbols for each start time all have the same
     * width, across all tracks.  The SymbolWidths class stores
     * - The symbol width for each starttime, for each track
     * - The maximum symbol width for a given starttime, across all tracks.
     *
     * The method SymbolWidths.GetExtraWidth() returns the extra width
     * needed for a track to match the maximum symbol width for a given
     * starttime.
     */
    void AlignSymbols(List<MusicSymbol>[] allsymbols, SymbolWidths widths) {

        for (int track = 0; track < allsymbols.Length; track++) {
            List<MusicSymbol> symbols = allsymbols[track];
            List<MusicSymbol> result = new List<MusicSymbol>();

            int i = 0;

            /* If a track doesn't have a symbol for a starttime,
             * add a blank symbol.
             */
            foreach (int start in widths.StartTimes) {
                if (i < symbols.Count && symbols[i].StartTime == start) {

                    while (i < symbols.Count && 
                           symbols[i].StartTime == start) {

                        result.Add(symbols[i]);
                        i++;
                    }
                }
                else {
                    result.Add(new BlankSymbol(start, 0));
                }
            }

            /* For each starttime, increase the symbol width by
             * SymbolWidths.GetExtraWidth().
             */
            i = 0;
            while (i < result.Count) {
                int start = result[i].StartTime;
                int extra = widths.GetExtraWidth(track, start);
                result[i].Width += extra;

                /* Skip all remaining symbols with the same starttime. */
                while (i < result.Count && result[i].StartTime == start) {
                    i++;
                }
            } 
            allsymbols[track] = result;
        }
    }

    static bool IsChord(MusicSymbol m) {
        return m is ChordSymbol;
    }

    /* Look for chords that form a triplet.  If found, instead of having
     * each chord draw an individual curvy stem, draw a horizontal beam
     * stem connecting the three chords together.
     *
     * The TimeSignature is used to determine whether the chords are
     * in the same measure.  The SymbolWidths is used to measure the
     * horizontal distance (in pixels) between the chords.
     */
    static void MakeChordTriplets(List<MusicSymbol>[] allsymbols,
                                  TimeSignature time,
                                  SymbolWidths widths) {

        foreach (List<MusicSymbol> symbols in allsymbols) {
            List<MusicSymbol> chords;
            chords = symbols.FindAll(new Predicate<MusicSymbol>(IsChord));

            int i = 0;
            while (i < chords.Count - 2) {
                ChordSymbol c1 = (ChordSymbol) chords[i];
                ChordSymbol c2 = (ChordSymbol) chords[i+1];
                ChordSymbol c3 = (ChordSymbol) chords[i+2];

                if (ChordSymbol.CanMakeTriplet(c1, c2, c3, time)) {
                    int dist = widths.GetWidthBetween(c1.StartTime,
                                                      c3.StartTime);
                    ChordSymbol.MakeTriplet(c1, c2, c3, dist);
                    i += 3;
                }
                else {
                    i++;
                }
            }
        }
    }

    /* Look for chords that form a pair.  If found, instead of having
     * each chord draw an individual curvy stem, draw a horizontal beam
     * stem connecting the two chords together.
     *
     * The TimeSignature is used to determine whether the chords are
     * in the same measure.  The SymbolWidths is used to measure the
     * horizontal distance (in pixels) between the chords.
     */
    static void MakeChordPairs(List<MusicSymbol>[] allsymbols,
                               TimeSignature time,
                               SymbolWidths widths) {

        foreach (List<MusicSymbol> symbols in allsymbols) {
            List<MusicSymbol> chords;
            chords = symbols.FindAll(new Predicate<MusicSymbol>(IsChord));

            int i = 0;
            while (i < chords.Count - 1) {
                ChordSymbol c1 = (ChordSymbol) chords[i];
                ChordSymbol c2 = (ChordSymbol) chords[i+1];

                if (ChordSymbol.CanMakePair(c1, c2, time)) {
                    int dist = widths.GetWidthBetween(c1.StartTime,
                                                      c2.StartTime);
                    ChordSymbol.MakePair(c1, c2, dist);
                    i += 2;
                }
                else {
                    i++;
                }
            }
        }
    }


    /* Get the width (in pixels) needed to display the key signature */
    int KeySignatureWidth(KeySignature key) {
        ClefSymbol c = new ClefSymbol(Clef.Treble, 0, false);
        int result = c.MinWidth;
        AccidSymbol[] keys = key.GetSymbols(Clef.Treble);
        foreach (AccidSymbol s in keys) {
            result += s.MinWidth;
        }
        return result + SheetMusic.LeftMargin + 5;
    }

    /* Given all the MusicSymbols for every track, create the staffs
     * for the sheet music.  There are two parts to this:
     *
     * - For each track, partition the music symbols into Staffs.
     *   Each Staff has a maxmimum width of PageWidth (800 pixels).
     *   Also, measures should not span multiple Staffs.  The
     *   staffs will be stored in trackstaffs as:
     *
     *   trackstaffs[0] = { Staff0, Staff1, Staff2, ... } for track 0
     *   trackstaffs[1] = { Staff0, Staff1, Staff2, ... } for track 1
     *   trackstaffs[2] = { Staff0, Staff1, Staff2, ... } for track 2
     *
     * - Store the Staffs in the staffs list, but interleave the
     *   tracks as follows:
     *
     *   staffs = { Staff0 for track 0, Staff0 for track1, Staff0 for track2,
     *              Staff1 for track 0, Staff1 for track1, Staff1 for track2,
     *              Staff2 for track 0, Staff2 for track1, Staff2 for track2,
     *              ... } 
     */ 

    List<Staff> CreateStaffs(List<MusicSymbol>[] allsymbols,
                          KeySignature key,
                          int measurelen) {

        List<Staff>[] trackstaffs = new List<Staff>[ allsymbols.Length ];
        int xstart = KeySignatureWidth(key);
        int totaltracks = trackstaffs.Length;

        for (int track = 0; track < totaltracks; track++) {

            /* Partition the music symbols into Staffs. */
            trackstaffs[track] = new List<Staff>();
            List<MusicSymbol> symbols = allsymbols[ track ];
            int startindex = 0;

            while (startindex < symbols.Count) {
                /* startindex is the index of the first symbol in the staff.
                 * endindex is the index of the last symbol in the staff.
                 */
                int endindex = startindex;
                int width = xstart;

                while (endindex < symbols.Count &&
                       width + symbols[endindex].Width < SheetMusic.PageWidth) {

                    width += symbols[endindex].Width;
                    endindex++;
                }
                endindex--;

                /* There's 3 possibilities at this point:
                 * 1. We have all the symbols in the track.
                 *    The endindex stays the same.
                 *
                 * 2. We have symbols for less than one measure.
                 *    The endindex stays the same.
                 *
                 * 3. We have symbols for 1 or more measures.
                 *    Since measures cannot span multiple staffs, we must
                 *    make sure endindex does not occur in the middle of a
                 *    measure.  We count backwards until we come to the end
                 *    of a measure.
                 */

                if (endindex == symbols.Count - 1) {
                    /* endindex stays the same */
                }
                else if (symbols[startindex].StartTime / measurelen ==
                         symbols[endindex].StartTime / measurelen) {
                    /* endindex stays the same */
                }
                else {
                    int endmeasure = symbols[endindex+1].StartTime/measurelen;
                    while (symbols[endindex].StartTime / measurelen == 
                           endmeasure) {
                        endindex--;
                    }
                }
                int range = endindex + 1 - startindex;
                Staff s = new Staff(symbols.GetRange(startindex, range),
                                    key, track, totaltracks);
                trackstaffs[track].Add(s);
                startindex = endindex + 1;
            } 
        } /* each track */

        /* Interleave the staffs of each track */
        int maxstaffs = 0;
        for (int i = 0; i < trackstaffs.Length; i++) {
            if (maxstaffs < trackstaffs[i].Count) {
                maxstaffs = trackstaffs[i].Count;
            }
        }
        List<Staff> result = new List<Staff>(maxstaffs * trackstaffs.Length);
        for (int i = 0; i < maxstaffs; i++) {
            foreach (List<Staff> list in trackstaffs) {
                if (i < list.Count) {
                    result.Add(list[i]);
                }
            }
        }
        return result;
    }

    /* Set the zoom level to display at (between 0.8 and 1.6).
     * Recalculate the SheetMusic width and height based on the
     * zoom level.  Then redraw the SheetMusic. 
     */
    public void SetZoom(float value) {
        zoom = value;
        Width = (int)(PageWidth * zoom) + 2;
        float height = 0;
        foreach (Staff s in staffs) {
            height += (s.Height * zoom);
        }
        Height = ((int)height) + LeftMargin;
        this.Invalidate();
    }


    /* Draw the SheetMusic.
     * Get the vertical start and end points of the clip area.
     * Only draw Staffs which lie inside the clip area.
     */
    protected override void OnPaint(PaintEventArgs e) {
        Rectangle r = e.ClipRectangle;
        int ystart = (int)(r.Y / zoom);
        int yend = ystart + (int)(r.Height / zoom);

        Graphics g = e.Graphics;
        g.PageScale = zoom;
        int ypos = 0;

        g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
        foreach (Staff staff in staffs) {
            if ((ypos + staff.Height < ystart) || (ypos > yend)) {
                /* Staff is not in the clip, don't need to draw it */
            }
            else {
                g.TranslateTransform(0, ypos);
                staff.Draw(g);
                g.TranslateTransform(0, -ypos);
            }

            ypos += staff.Height;
        }
    }

    /* Print the given page of the sheet music. 
     * Page numbers start from 1.
     */
    public void DoPrint(Graphics g, int pagenumber)
    {
        int leftmargin = (int) ((g.VisibleClipBounds.Width - PageWidth)/2);
        int topmargin = (int) ((g.VisibleClipBounds.Height - PageHeight)/2);

        g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
        g.FillRectangle(Brushes.White, 0, 0, 
                        g.VisibleClipBounds.Width,
                        g.VisibleClipBounds.Height);

        /* Find the starting staff based on the currentpage */
        int ypos = 0;
        int pagenum = 1;
        int staffnum = 0;
        while (staffnum < staffs.Count && pagenum < pagenumber) {
            if (ypos + staffs[staffnum].Height > PageHeight) {
                pagenum++;
                ypos = 0;
            }
            else {
                ypos += staffs[staffnum].Height;
                staffnum++;
            }
        }

        /* Print the staffs until the height reaches PageHeight */
        ypos = 0;
        for (; staffnum < staffs.Count; staffnum++) {
            int height = ypos + staffs[staffnum].Height;

            if (height >= PageHeight)
                break;

            g.TranslateTransform(leftmargin, topmargin + ypos);
            staffs[staffnum].Draw(g);
            g.TranslateTransform(-leftmargin, -(topmargin + ypos));
            ypos += staffs[staffnum].Height;
        }
    }

    /*
     * Return the number of pages needed to print this sheet music.
     */
    public int GetTotalPages() {
        int num = 1;
        int currheight = 0;

        foreach (Staff staff in staffs) {
            if (currheight + staff.Height > PageHeight) {
                num++;
                currheight = staff.Height;
            }
            else {
                currheight += staff.Height;
            }
        }
        return num;
    }

}

}
