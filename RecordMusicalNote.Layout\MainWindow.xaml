﻿<Window x:Class="MyWPF.Layout.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MyWPF.Layout"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="音乐记录工具" Height="700" Width="1100" Icon="1.ico"
        ResizeMode="CanResize" MinHeight="600" MinWidth="800"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">
    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 顶部应用栏 -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
                <DockPanel>
                    <materialDesign:PackIcon Kind="MusicNote" Width="24" Height="24" VerticalAlignment="Center" DockPanel.Dock="Left"/>
                    <TextBlock Text="音乐记录工具" FontSize="20" FontWeight="Medium" VerticalAlignment="Center" Margin="16,0,0,0" DockPanel.Dock="Left"/>

                    <!-- 菜单按钮 -->
                    <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                        <Button Style="{StaticResource MaterialDesignToolButton}" ToolTip="绘制功能">
                            <materialDesign:PackIcon Kind="Draw"/>
                            <Button.ContextMenu>
                                <ContextMenu>
                                    <MenuItem Header="画弹珠" Click="menuItemPinball_Click">
                                        <MenuItem.Icon>
                                            <materialDesign:PackIcon Kind="Circle"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                    <MenuItem Header="画泡泡" Click="menuItemmenuBubble_Click">
                                        <MenuItem.Icon>
                                            <materialDesign:PackIcon Kind="CircleOutline"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                </ContextMenu>
                            </Button.ContextMenu>
                        </Button>

                        <!-- 转换功能下拉菜单 -->
                        <Menu>
                            <MenuItem Header="转换功能" Style="{StaticResource MaterialDesignMenuItem}">
                                <MenuItem.Icon>
                                    <materialDesign:PackIcon Kind="SwapHorizontal"/>
                                </MenuItem.Icon>
                                <MenuItem Header="星动转弹珠" Click="menuIdolToPinball_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="ArrowRight"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="星动转弹珠(不删除重复)" Click="menuIdolToPinballNoDeltedRepeat_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="ArrowRight"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="星动转弹珠(New)" Click="menuIdolToPinballNew_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="ArrowRightBold"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <Separator/>
                                <MenuItem Header="音符编辑器" Click="menuNoteEditor_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Pencil"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <Separator/>
                                <MenuItem Header="星动转弦月" Click="menuIdolToXY_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="ArrowRight"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="星动转泡泡" Click="menuIdolToBubble_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="ArrowRight"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="弹珠转泡泡" Click="menuPinBallToBubble_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="ArrowRight"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <Separator/>
                                <MenuItem Header="传统单/连按转换" Click="classicalMenu_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="SwapVertical"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="ImdTxt转星动" Click="ImdTxtToIdol_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="FileImport"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <Separator/>
                                <MenuItem Header="MIDI谱面生成器" Click="menuChartGenerator_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="MusicNoteEighth"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </MenuItem>
                        </Menu>

                        <!-- 其他功能下拉菜单 -->
                        <Menu>
                            <MenuItem Header="其他功能" Style="{StaticResource MaterialDesignMenuItem}">
                                <MenuItem.Icon>
                                    <materialDesign:PackIcon Kind="DotsVertical"/>
                                </MenuItem.Icon>
                                <MenuItem Header="获取Midi文件信息" Click="GetMidiInfo_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="FileMusic"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="删除弹珠重复Note" Click="menuDeleteRepeatItemenuClear_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="DeleteSweep"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="校正" Click="menuCorrection_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="TuneVertical"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="转换BPM" Click="menuTranslateBPM_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Speedometer"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <Separator/>
                                <MenuItem Header="保存分数信息" Click="menuCalculateBD_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Calculator"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="爆点分析" Click="FireAnalyse_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="ChartLine"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="文件重命名" Click="FileReName_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="RenameBox"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="清除Note文本" Click="menuClear_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="DeleteEmpty"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </MenuItem>
                        </Menu>

                        <!-- 后台功能下拉菜单 -->
                        <Menu>
                            <MenuItem Header="后台功能" Style="{StaticResource MaterialDesignMenuItem}">
                                <MenuItem.Icon>
                                    <materialDesign:PackIcon Kind="Database"/>
                                </MenuItem.Icon>
                                <MenuItem Header="MidiInfo" Click="MidiInfo_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="MidiPort"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="MusicXMLInfo" Click="MusicXMLInfo_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="FileMusic"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </MenuItem>
                        </Menu>

                        <!-- 模板功能下拉菜单 -->
                        <Menu>
                            <MenuItem Header="模板功能" Style="{StaticResource MaterialDesignMenuItem}">
                                <MenuItem.Icon>
                                    <materialDesign:PackIcon Kind="FileDocument"/>
                                </MenuItem.Icon>
                                <MenuItem Header="生成随机单点模板(星动)" Click="CreateIdolShortModel_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Creation"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </MenuItem>
                        </Menu>
                    </StackPanel>
                </DockPanel>
            </materialDesign:ColorZone>
            <!-- 主要内容区域 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧面板 -->
                    <StackPanel Grid.Column="0" Margin="0,0,8,0">

                        <!-- 键位设置卡片 -->
                        <materialDesign:Card Style="{StaticResource MaterialCard}">
                            <StackPanel>
                                <TextBlock Text="键位设置" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                                <!-- 键位类型选择 -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                    <RadioButton x:Name="rb4K" Content="4K" GroupName="KeyType"
                                               Checked="rb4K_Checked" Style="{StaticResource MaterialDesignRadioButton}" Margin="0,0,16,0"/>
                                    <RadioButton x:Name="rb5K" Content="5K" GroupName="KeyType"
                                               Checked="rb5K_Checked" Style="{StaticResource MaterialDesignRadioButton}"/>
                                </StackPanel>

                                <!-- 键位显示 -->
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:Chip Grid.Column="0" x:Name="txtD" Content="D" IsEnabled="False"
                                                       Background="{DynamicResource MaterialDesignChipBackground}"/>
                                    <materialDesign:Chip Grid.Column="1" x:Name="txtF" Content="F" IsEnabled="False"
                                                       Background="{DynamicResource MaterialDesignChipBackground}"/>
                                    <materialDesign:Chip Grid.Column="2" x:Name="txtJ" Content="J" IsEnabled="False"
                                                       Background="{DynamicResource MaterialDesignChipBackground}"/>
                                    <materialDesign:Chip Grid.Column="3" x:Name="txtK" Content="K" IsEnabled="False"
                                                       Background="{DynamicResource MaterialDesignChipBackground}"/>
                                    <materialDesign:Chip Grid.Column="4" x:Name="txtL" Content="L" IsEnabled="False"
                                                       Background="{DynamicResource MaterialDesignChipBackground}"/>
                                </Grid>

                                <!-- 方向键 -->
                                <Grid Margin="0,16,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:Chip Grid.Column="0" x:Name="txtLeft" Content="←(A)" IsEnabled="False"
                                                       Background="{DynamicResource MaterialDesignChipBackground}"/>
                                    <materialDesign:Chip Grid.Column="1" x:Name="txtRight" Content="→(B)" IsEnabled="False"
                                                       Background="{DynamicResource MaterialDesignChipBackground}"/>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 参数设置卡片 -->
                        <materialDesign:Card Style="{StaticResource MaterialCard}">
                            <StackPanel>
                                <TextBlock Text="参数设置" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBox Grid.Row="0" Grid.Column="0" x:Name="txtBPM"
                                           materialDesign:HintAssist.Hint="BPM" Style="{StaticResource MaterialTextBox}"/>
                                    <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtET"
                                           materialDesign:HintAssist.Hint="ET" Style="{StaticResource MaterialTextBox}"/>

                                    <TextBox Grid.Row="1" Grid.Column="0" x:Name="txtAllTime"
                                           materialDesign:HintAssist.Hint="关卡时间" Style="{StaticResource MaterialTextBox}"/>
                                    <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtAllBarCount"
                                           materialDesign:HintAssist.Hint="关卡小节" Style="{StaticResource MaterialTextBox}"/>

                                    <TextBox Grid.Row="2" Grid.Column="0" x:Name="txtStartBarCount" Text="4"
                                           materialDesign:HintAssist.Hint="开场小节" Style="{StaticResource MaterialTextBox}"/>
                                    <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtChannelCount" Text="4"
                                           materialDesign:HintAssist.Hint="轨道数" Style="{StaticResource MaterialTextBox}"/>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 控制按钮卡片 -->
                        <materialDesign:Card Style="{StaticResource MaterialCard}">
                            <StackPanel>
                                <TextBlock Text="控制操作" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                                <UniformGrid Columns="3" Rows="2">
                                    <Button x:Name="btnSave" Content="保存" Style="{StaticResource MaterialRaisedButton}">
                                        <Button.ToolTip>
                                            <ToolTip Content="保存当前记录"/>
                                        </Button.ToolTip>
                                    </Button>
                                    <Button x:Name="btnContinue" Content="继续" Style="{StaticResource MaterialRaisedButton}">
                                        <Button.ToolTip>
                                            <ToolTip Content="继续记录"/>
                                        </Button.ToolTip>
                                    </Button>
                                    <Button x:Name="btnEnd" Content="结束" Click="btnEnd_Click" Style="{StaticResource MaterialRaisedButton}">
                                        <Button.ToolTip>
                                            <ToolTip Content="结束记录"/>
                                        </Button.ToolTip>
                                    </Button>

                                    <Button x:Name="btnSelectMusic" Content="选择音乐" Click="btnSelectMusic_Click"
                                          Style="{StaticResource MaterialRaisedButton}">
                                        <Button.ToolTip>
                                            <ToolTip Content="选择音频文件"/>
                                        </Button.ToolTip>
                                    </Button>
                                    <Button x:Name="btnStartMusic" Content="播放" Click="btnStartMusic_Click"
                                          Style="{StaticResource MaterialRaisedButton}">
                                        <Button.ToolTip>
                                            <ToolTip Content="播放音乐"/>
                                        </Button.ToolTip>
                                    </Button>
                                    <Button x:Name="btnPause" Content="暂停" Click="btnPause_Click"
                                          Style="{StaticResource MaterialRaisedButton}">
                                        <Button.ToolTip>
                                            <ToolTip Content="暂停播放"/>
                                        </Button.ToolTip>
                                    </Button>
                                </UniformGrid>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>

                    <!-- 右侧面板 -->
                    <StackPanel Grid.Column="1" Margin="8,0,0,0">

                        <!-- 音乐播放器卡片 -->
                        <materialDesign:Card Style="{StaticResource MaterialCard}">
                            <StackPanel>
                                <TextBlock Text="音乐播放器" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                                <!-- 播放进度 -->
                                <StackPanel Margin="0,0,0,16">
                                    <TextBlock Text="播放进度" Style="{StaticResource MaterialDesignCaptionTextBlock}" Margin="0,0,0,8"/>
                                    <Slider x:Name="sliderPosition" ValueChanged="sliderPosition_ValueChanged"
                                          Style="{StaticResource MaterialDesignSlider}" Focusable="False"/>

                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,8,0,0">
                                        <materialDesign:PackIcon Kind="Clock" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock x:Name="lblCurrentMusic" Text="00:00:00" VerticalAlignment="Center"
                                                 Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- 隐藏的媒体元素 -->
                                <MediaElement Name="mediaelement" Visibility="Hidden" LoadedBehavior="Manual" MediaOpened="mediaelement_MediaOpened"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 实时状态卡片 -->
                        <materialDesign:Card Style="{StaticResource MaterialCard}">
                            <StackPanel>
                                <TextBlock Text="实时状态" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                                <!-- 状态指示器 -->
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <materialDesign:PackIcon Grid.Row="0" Grid.Column="0" Kind="Record" Margin="0,0,8,8"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="录制状态: 待机" VerticalAlignment="Center" Margin="0,0,0,8"/>

                                    <materialDesign:PackIcon Grid.Row="1" Grid.Column="0" Kind="Music" Margin="0,0,8,8"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="音乐状态: 未加载" VerticalAlignment="Center" Margin="0,0,0,8"/>

                                    <materialDesign:PackIcon Grid.Row="2" Grid.Column="0" Kind="Keyboard" Margin="0,0,8,8"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="键位模式: 4K" VerticalAlignment="Center" Margin="0,0,0,8"/>

                                    <materialDesign:PackIcon Grid.Row="3" Grid.Column="0" Kind="Speedometer" Margin="0,0,8,0"/>
                                    <TextBlock Grid.Row="3" Grid.Column="1" Text="BPM: 未设置" VerticalAlignment="Center"/>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 帮助信息卡片 -->
                        <materialDesign:Card Style="{StaticResource MaterialCard}">
                            <StackPanel>
                                <TextBlock Text="操作说明" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                                <StackPanel>
                                    <TextBlock Text="键位操作:" Style="{StaticResource MaterialDesignSubtitle2TextBlock}" Margin="0,0,0,8"/>
                                    <TextBlock Text="• D, F, J, K, L - 对应轨道按键" Style="{StaticResource MaterialDesignCaptionTextBlock}" Margin="0,0,0,4"/>
                                    <TextBlock Text="• A, B - 方向键操作" Style="{StaticResource MaterialDesignCaptionTextBlock}" Margin="0,0,0,4"/>
                                    <TextBlock Text="• 长按超过200ms为长条" Style="{StaticResource MaterialDesignCaptionTextBlock}" Margin="0,0,0,8"/>

                                    <TextBlock Text="快捷操作:" Style="{StaticResource MaterialDesignSubtitle2TextBlock}" Margin="0,0,0,8"/>
                                    <TextBlock Text="• Ctrl + 方向键 - 组合音符" Style="{StaticResource MaterialDesignCaptionTextBlock}" Margin="0,0,0,4"/>
                                    <TextBlock Text="• Space - 滑动音符" Style="{StaticResource MaterialDesignCaptionTextBlock}" Margin="0,0,0,4"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </Grid>
            </ScrollViewer>
        </Grid>
    </materialDesign:DialogHost>
</Window>
