﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CommonModel
{
   public  class MidiTrack
    {
        public int MidiTrackID { get; set; }
        public int MidiID { get; set; }
        public int Number { get; set; }
        public string MusicXMLID { get; set; }
        /// <summary>
        /// 乐器名称
        /// </summary>
        public string Instrument { get; set; }

        /// <summary>
        /// 轨道类型 (仅用于界面标注，不保存到数据库)
        /// </summary>
        public TrackType TrackType { get; set; } = TrackType.Unknown;

        /// <summary>
        /// 优先级 (仅用于界面标注)
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 是否为主旋律 (仅用于界面标注)
        /// </summary>
        public bool IsMainMelody { get; set; } = false;

        /// <summary>
        /// 轨道类型显示文本 (用于UI绑定)
        /// </summary>
        public string TrackTypeDisplay
        {
            get => GetTrackTypeDisplayName(TrackType);
            set => TrackType = GetTrackTypeFromDisplayName(value);
        }

        private string GetTrackTypeDisplayName(TrackType type)
        {
            switch (type)
            {
                case TrackType.MainMelody:
                    return "主旋律";
                case TrackType.SubMelody:
                    return "副旋律";
                case TrackType.Harmony:
                    return "和声";
                case TrackType.Bass:
                    return "贝斯";
                case TrackType.Drums:
                    return "鼓";
                case TrackType.Percussion:
                    return "打击乐";
                case TrackType.Chord:
                    return "和弦伴奏";
                case TrackType.Arpeggio:
                    return "琶音";
                case TrackType.Background:
                    return "背景音效";
                default:
                    return "未标注";
            }
        }

        private TrackType GetTrackTypeFromDisplayName(string displayName)
        {
            switch (displayName)
            {
                case "主旋律":
                    return TrackType.MainMelody;
                case "副旋律":
                    return TrackType.SubMelody;
                case "和声":
                    return TrackType.Harmony;
                case "贝斯":
                    return TrackType.Bass;
                case "鼓":
                    return TrackType.Drums;
                case "打击乐":
                    return TrackType.Percussion;
                case "和弦伴奏":
                    return TrackType.Chord;
                case "琶音":
                    return TrackType.Arpeggio;
                case "背景音效":
                    return TrackType.Background;
                default:
                    return TrackType.Unknown;
            }
        }
    }

    public enum TrackType
    {
        Unknown = 0,        // 未标注
        MainMelody = 1,     // 主旋律
        SubMelody = 2,      // 副旋律
        Harmony = 3,        // 和声
        Bass = 4,           // 贝斯
        Drums = 5,          // 鼓
        Percussion = 6,     // 打击乐
        Chord = 7,          // 和弦伴奏
        Arpeggio = 8,       // 琶音
        Background = 9      // 背景音效
    }
}
