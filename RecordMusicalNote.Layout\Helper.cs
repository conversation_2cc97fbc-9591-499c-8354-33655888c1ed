﻿using CommonModel;
using RecordMusicalNote.CreateNotation;
using RecordMusicalNote.IRepository;
using RecordMusicalNote.Library.Repository;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Xml;

namespace RecordMusicalNote
{
    public class MainWindowHelper
    {
        public static void IdolToPinball(string FilePath)
        {
            XmlDocument idolDoc = new XmlDocument();
            XmlDocument pinBallDoc = new XmlDocument();
            // XmlDeclaration Declaration = pinBallDoc.CreateXmlDeclaration("1.0", "utf-8", null);
            idolDoc.Load(FilePath);
            XmlElement root = null;
            root = idolDoc.DocumentElement;
            XmlNodeList listNodes = null;
            listNodes = root.SelectNodes("/Level/NoteInfo/Normal");
            XmlNode rootNode = pinBallDoc.CreateElement("Normal");
            XmlAttributeCollection collection;
            XmlNode pointNode;
            int pinBallId = 1;
            List<int> slipID = new List<int>();
            string lastEndArea1 = "";
            string lastBar1 = "";
            string lastPos1 = "";
            string lastNoteType1 = "";
            bool isSlipNote = false;
            Random rd = new Random();

            foreach (XmlNode node in listNodes[0].ChildNodes)
            {

                if (!node.HasChildNodes)
                {
                    collection = node.Attributes;
                    if (collection == null) return;
                    pointNode = pinBallDoc.CreateElement("Note");
                    //创建Note子节点
                    XmlAttribute NoteTypeAttribute = pinBallDoc.CreateAttribute("note_type");
                    XmlAttribute IDAttribute = pinBallDoc.CreateAttribute("ID");
                    IDAttribute.Value = pinBallId.ToString();
                    pointNode.Attributes.Append(IDAttribute);
                    XmlAttribute BarAttribute = pinBallDoc.CreateAttribute("Bar");
                    BarAttribute.Value = collection["Bar"].Value.ToString();
                    pointNode.Attributes.Append(BarAttribute);
                    XmlAttribute PosAttribute = pinBallDoc.CreateAttribute("Pos");
                    PosAttribute.Value = collection["Pos"].Value.ToString();
                    pointNode.Attributes.Append(PosAttribute);
                    XmlAttribute EndArea = pinBallDoc.CreateAttribute("EndArea");
                    if (collection["target_track"].Value == "Left1" || collection["target_track"].Value == "Left2")
                        EndArea.Value = "1|2";
                    else if (collection["target_track"].Value == "Middle")
                    {
                      
                        int i = rd.Next(1, 4);
                        if (i == 1)
                            EndArea.Value = "1|2";
                        else if (i == 2)
                            EndArea.Value = "";
                        else
                            EndArea.Value = "3|4";
                    }
                    else
                        EndArea.Value = "3|4";
                    if ((lastNoteType1 == "short" || lastNoteType1 == "slip" &&
                        collection["note_type"].Value == "short" ||
                        collection["note_type"].Value == "slip")
                        && EndArea.Value == lastEndArea1 &&
                        lastPos1 == PosAttribute.Value &&
                        lastBar1 == BarAttribute.Value)
                    {
                        if (EndArea.Value == "1|2")
                            EndArea.Value = "3|4";
                        else
                            EndArea.Value = "1|2";
                    }
                    lastEndArea1 = EndArea.Value;
                    lastBar1 = BarAttribute.Value;
                    lastPos1 = PosAttribute.Value;
                    lastNoteType1 = collection["note_type"].Value;
                    pointNode.Attributes.Append(EndArea);
                    XmlAttribute MoveTime = pinBallDoc.CreateAttribute("MoveTime");
                    MoveTime.Value = "3";
                    pointNode.Attributes.Append(MoveTime);

                    if (collection["note_type"].Value == "long")
                    {
                        NoteTypeAttribute.Value = "PinballLong";
                        pointNode.Attributes.Append(NoteTypeAttribute);
                        XmlAttribute EndBarAttribute = pinBallDoc.CreateAttribute("EndBar");
                        EndBarAttribute.Value = collection["EndBar"].Value.ToString();
                        pointNode.Attributes.Append(EndBarAttribute);
                        XmlAttribute EndPosAttribute = pinBallDoc.CreateAttribute("EndPos");
                        EndPosAttribute.Value = collection["EndPos"].Value.ToString();
                        pointNode.Attributes.Append(EndPosAttribute);
                        XmlAttribute SonAttribute = pinBallDoc.CreateAttribute("Son");
                        SonAttribute.Value = "";
                        pointNode.Attributes.Append(SonAttribute);
                    }
                    else if (collection["note_type"].Value == "short")
                    {
                        NoteTypeAttribute.Value = "PinballSingle";
                        pointNode.Attributes.Append(NoteTypeAttribute);
                        XmlAttribute SonPosAttribute = pinBallDoc.CreateAttribute("Son");
                        SonPosAttribute.Value = "";
                        pointNode.Attributes.Append(SonPosAttribute);
                    }
                    else if (collection["note_type"].Value == "slip")
                    {

                        NoteTypeAttribute.Value = "PinballSlip";
                        pointNode.Attributes.Append(NoteTypeAttribute);
                        XmlAttribute SonAttribute = pinBallDoc.CreateAttribute("Son");
                        SonAttribute.Value = "";
                        pointNode.Attributes.Append(SonAttribute);
                        slipID.Add(pinBallId);


                    }
                    rootNode.AppendChild(pointNode);
                    pinBallId += 1;
                }
                else
                {
                    foreach (XmlNode combineItem in node.ChildNodes)
                    {
                        pointNode = pinBallDoc.CreateElement("Note");
                        XmlAttribute IDAttribute = pinBallDoc.CreateAttribute("ID");
                        IDAttribute.Value = pinBallId.ToString();
                        pointNode.Attributes.Append(IDAttribute);
                        XmlAttribute BarAttribute = pinBallDoc.CreateAttribute("Bar");
                        BarAttribute.Value = combineItem.Attributes["Bar"].Value.ToString();
                        pointNode.Attributes.Append(BarAttribute);
                        int startTotalPos = int.Parse(combineItem.Attributes["Pos"].Value) + 64*(int.Parse(combineItem.Attributes["Bar"].Value));
                        XmlAttribute EndArea = pinBallDoc.CreateAttribute("EndArea");
                        if (combineItem.Attributes["target_track"].Value == "Left1" ||
                            combineItem.Attributes["target_track"].Value == "Left2")
                            EndArea.Value = "1|2";
                        else
                            EndArea.Value = "3|4";
                        pointNode.Attributes.Append(EndArea);
                        XmlAttribute MoveTime = pinBallDoc.CreateAttribute("MoveTime");
                        MoveTime.Value = "3";
                        pointNode.Attributes.Append(MoveTime);
                        XmlAttribute NoteTypeAttribute = pinBallDoc.CreateAttribute("note_type");
                        XmlAttributeCollection combineItemAttribute = combineItem.Attributes as XmlAttributeCollection;
                        if (combineItemAttribute == null) continue;
                        if (combineItemAttribute["note_type"].Value == "long")
                        {
                            int endTotalPos = int.Parse(combineItem.Attributes["EndPos"].Value) +64* int.Parse(combineItem.Attributes["EndBar"].Value);
                            if ((endTotalPos - startTotalPos) / 2 >= 8)
                            {
                                NoteTypeAttribute.Value = "PinballLong";
                                pointNode.Attributes.Append(NoteTypeAttribute);
                                XmlAttribute PosAttribute = pinBallDoc.CreateAttribute("Pos");
                                if ((int.Parse(combineItem.Attributes["Pos"].Value) * 1.5) >= int.Parse(combineItemAttribute["EndPos"].Value))
                                    continue;
                                PosAttribute.Value = int.Parse(combineItem.Attributes["Pos"].Value).ToString();
                                pointNode.Attributes.Append(PosAttribute);
                                XmlAttribute EndBarAttribute = pinBallDoc.CreateAttribute("EndBar");
                                EndBarAttribute.Value = combineItemAttribute["EndBar"].Value.ToString();
                                pointNode.Attributes.Append(EndBarAttribute);
                                XmlAttribute EndPosAttribute = pinBallDoc.CreateAttribute("EndPos");
                                EndPosAttribute.Value = (int.Parse(combineItemAttribute["Pos"].Value)+((int.Parse(combineItemAttribute["EndPos"].Value)-
                                    int.Parse(combineItemAttribute["Pos"].Value)) / 2)).ToString();
                                pointNode.Attributes.Append(EndPosAttribute);
                                XmlAttribute SonAttribute = pinBallDoc.CreateAttribute("Son");
                                SonAttribute.Value = "";
                                pointNode.Attributes.Append(SonAttribute);
                            }
                            else
                            {
                                NoteTypeAttribute.Value = "PinballSingle";
                                pointNode.Attributes.Append(NoteTypeAttribute);
                                XmlAttribute PosAttribute = pinBallDoc.CreateAttribute("Pos");
                                PosAttribute.Value = combineItem.Attributes["Pos"].Value.ToString();
                                pointNode.Attributes.Append(PosAttribute);
                                XmlAttribute SonPosAttribute = pinBallDoc.CreateAttribute("Son");
                                SonPosAttribute.Value = "";
                                pointNode.Attributes.Append(SonPosAttribute);
                            }
                        }
                        else if (combineItemAttribute["note_type"].Value == "slip")
                        {
                            XmlAttribute PosAttribute = pinBallDoc.CreateAttribute("Pos");
                            PosAttribute.Value = combineItem.Attributes["Pos"].Value.ToString();
                            pointNode.Attributes.Append(PosAttribute);

                            NoteTypeAttribute.Value = isSlipNote ? "PinballSlip" : "PinballSingle";
                            pointNode.Attributes.Append(NoteTypeAttribute);
                            XmlAttribute SonAttribute = pinBallDoc.CreateAttribute("Son");
                            SonAttribute.Value = "";
                            pointNode.Attributes.Append(SonAttribute);
                            if (EndArea.Value == "1|2")
                            {
                                EndArea.Value = "3|4";
                            }
                            else
                            {
                                EndArea.Value = "1|2";
                            }
                            if(isSlipNote)
                                slipID.Add(pinBallId);

                        }
                        rootNode.AppendChild(pointNode);
                        pinBallId += 1;
                    }

                }


            }

            int c = 0;
            for (int i = 0; i < slipID.Count; i += 1)
            {
                int currentBar = int.Parse(rootNode.ChildNodes[slipID[i] - 1].Attributes["Bar"].Value) + 2;
                int currentId = int.Parse(rootNode.ChildNodes[slipID[i] - 1].Attributes["ID"].Value);

                for (c = currentId; c < rootNode.ChildNodes.Count; c++)
                {
                    int targetBar = int.Parse(rootNode.ChildNodes[c].Attributes["Bar"].Value);
                    int targetPos = int.Parse(rootNode.ChildNodes[c].Attributes["Pos"].Value);
                    int targetID = int.Parse(rootNode.ChildNodes[c].Attributes["ID"].Value);
                    string endArea = rootNode.ChildNodes[c].Attributes["EndArea"].Value;
                    if (rootNode.ChildNodes[c].Attributes["note_type"].Value == "PinballSingle" && targetBar >= currentBar)
                    {
                        rootNode.ChildNodes[slipID[i] - 1].Attributes["Son"].Value = targetID.ToString();
                        XmlElement newSlipNote = pinBallDoc.CreateElement("Note");
                        newSlipNote.SetAttribute("ID", targetID.ToString());
                        newSlipNote.SetAttribute("Bar", targetBar.ToString());
                        newSlipNote.SetAttribute("Pos", targetPos.ToString());
                        newSlipNote.SetAttribute("EndArea", endArea);
                        newSlipNote.SetAttribute("MoveTime", 3.ToString());
                        newSlipNote.SetAttribute("note_type", "PinballSlip");
                        newSlipNote.SetAttribute("Son", "");
                        rootNode.InsertAfter(newSlipNote, rootNode.ChildNodes[c]);
                        rootNode.RemoveChild(rootNode.ChildNodes[c]);
                        break;
                    }
                }
            }

            pinBallDoc.AppendChild(rootNode);
            //删除重复note
            listNodes = pinBallDoc.DocumentElement.SelectNodes("/Normal/Note");
            IList<int> ids = new List<int>();
            IList<UsedTrack> usedPostions = new List<UsedTrack>();
            IList<IPinball> pinballs = new List<IPinball>();
            IPinballRepository repository = new PinballRepository();
            foreach (XmlNode node in listNodes)
            {
                IPinball pinball = repository.CreatenewPinball();
                pinball.Bar = int.Parse(node.Attributes["Bar"].Value);
                pinball.Pos = int.Parse(node.Attributes["Pos"].Value);
                pinball.EndArea = node.Attributes["EndArea"].Value;
                pinball.EndBar = node.Attributes["EndBar"] == null ? -1 : int.Parse(node.Attributes["EndBar"].Value);
                pinball.EndPos = node.Attributes["EndPos"] == null ? -1 : int.Parse(node.Attributes["EndPos"].Value);
                pinball.MoveTime = 3;
                pinball.NoteType = node.Attributes["note_type"].Value;
                pinball.PinballID = int.Parse(node.Attributes["ID"].Value);
                pinball.SonId = node.Attributes["Son"].Value;
                pinballs.Add(pinball);
                UsedTrack usedPostion = new UsedTrack();
                if (node.Attributes["note_type"].Value != "PinballSlip" && node.Attributes["note_type"].Value != "PinballSeries" &&
                    usedPostions.Any(a => a.UsedBar == int.Parse(node.Attributes["Bar"].Value) &&
                 a.UsedPos == int.Parse(node.Attributes["Pos"].Value) && a.UsedTrackName.Contains(node.Attributes["EndArea"].Value)))
                {
                    ids.Add(int.Parse(node.Attributes["ID"].Value));
                }
                if (node.Attributes["note_type"].Value == "PinballSingle")
                {
                    usedPostion.UsedBar = int.Parse(node.Attributes["Bar"].Value);
                    usedPostion.UsedPos = int.Parse(node.Attributes["Pos"].Value);
                    IList<string> usedTrackName = new List<string>();
                    usedTrackName.Add(node.Attributes["EndArea"].Value);
                    usedPostion.UsedTrackName = usedTrackName;
                    usedPostions.Add(usedPostion);
                }
                else if (node.Attributes["note_type"].Value == "PinballLong")
                {
                    int usedPostionNum = (int.Parse(node.Attributes["EndBar"].Value) * 64 + int.Parse(node.Attributes["EndPos"].Value)
                        - int.Parse(node.Attributes["Bar"].Value) * 64 + int.Parse(node.Attributes["Pos"].Value)) / 2;
                    for (int i = 0; i < usedPostionNum; i++)
                    {
                        UsedTrack ut = new UsedTrack();
                        ut.UsedBar = (int.Parse(node.Attributes["Bar"].Value) * 64 + int.Parse(node.Attributes["Pos"].Value) + i * 2) / 64;
                        ut.UsedPos = (int.Parse(node.Attributes["Pos"].Value) + i * 2) % 64;
                        IList<string> usedTrackNames = new List<string>();
                        usedTrackNames.Add(node.Attributes["EndArea"].Value);
                        ut.UsedTrackName = usedTrackNames;
                        usedPostions.Add(ut);
                    }
                }
            }
            ids = ids.Distinct().ToList();
            pinballs = pinballs.Where(a => !ids.Contains(a.PinballID)).ToList();
            FileStream fs = new FileStream(FilePath+".idolToPinball.txt", FileMode.OpenOrCreate);
            StreamWriter sw = new StreamWriter(fs);
            sw.WriteLine("<Normal>");
            foreach (IPinball pinball in pinballs)
            {
                sw.WriteLine(pinball.ToString());
            }
            sw.WriteLine("</Normal>");
            sw.Close();
            MessageBox.Show("共删除" + ids.Count + "个");
            //pinBallDoc.Save(@"E:\idolToPinball.xml");
        }

        internal static void TranslateClassical(bool _isDouble, string filePath)
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(filePath);
            XmlElement root = null;
            root = doc.DocumentElement;
            XmlNodeList listNodes = null;
            listNodes = root.GetElementsByTagName("Normal");
            for (int i = 0; i < listNodes[0].ChildNodes.Count; i++)
            {
                listNodes[0].ChildNodes[i].Attributes["length"].Value = i == 0 ? "1" : _isDouble ? "1" : "2";
                if (_isDouble)
                {
                    XmlNode nextNote = listNodes[0].ChildNodes[i + 1];
                    if (nextNote == null) break;
                    XmlNode currentNote = listNodes[0].ChildNodes[i];
                    int distance = int.Parse(nextNote.Attributes["Bar"].Value) - int.Parse(currentNote.Attributes["Bar"].Value);
                    for (int j = 1; j < distance; j++)
                    {
                        XmlNode noteElement = doc.CreateElement("Note");
                        XmlAttribute barAttribute = doc.CreateAttribute("Bar");
                        barAttribute.Value = (int.Parse(currentNote.Attributes["Bar"].Value) + j).ToString();
                        noteElement.Attributes.Append(barAttribute);
                        XmlAttribute lengthAttribute = doc.CreateAttribute("length");
                        lengthAttribute.Value = "1";
                        noteElement.Attributes.Append(lengthAttribute);
                        XmlAttribute levelAttribute = doc.CreateAttribute("level");
                        levelAttribute.Value = nextNote.Attributes["level"].Value;
                        noteElement.Attributes.Append(levelAttribute);
                        listNodes[0].InsertAfter(noteElement, currentNote);
                        currentNote = noteElement;
                        i++;
                    }

                }

            }

            listNodes = root.GetElementsByTagName("ActionSeq");
            for (int i = 0; i < listNodes[0].ChildNodes.Count; i++)
            {
                XmlNode currentNote = listNodes[0].ChildNodes[i];
                currentNote.Attributes["dance_len"].Value = _isDouble ? "1" : "2";
                currentNote.Attributes["seq_len"].Value = i == 0 ? "1" : _isDouble ? "4" : "4";
                if (_isDouble)
                {
                    XmlNode nextNote = listNodes[0].ChildNodes[i + 1];
                    if (nextNote == null)
                    {
                        currentNote.Attributes["dance_len"].Value = "4";
                        currentNote.Attributes["seq_len"].Value = "4";
                        break;
                    }

                    int currentLenth = int.Parse(currentNote.Attributes["start_bar"].Value) + int.Parse(currentNote.Attributes["seq_len"].Value);
                    if (int.Parse(nextNote.Attributes["start_bar"].Value) == currentLenth) continue;
                    int distance = (int.Parse(nextNote.Attributes["start_bar"].Value) - currentLenth) / 2;
                    for (int j = 1; j <= distance; j++)
                    {
                        XmlNode noteElement = doc.CreateElement("ActionList");
                        XmlAttribute barAttribute = doc.CreateAttribute("start_bar");
                        barAttribute.Value = (int.Parse(currentNote.Attributes["start_bar"].Value) + j * 2).ToString();
                        noteElement.Attributes.Append(barAttribute);
                        XmlAttribute lengthAttribute = doc.CreateAttribute("dance_len");
                        lengthAttribute.Value = "1";
                        noteElement.Attributes.Append(lengthAttribute);
                        XmlAttribute seqLenAttribute = doc.CreateAttribute("seq_len");
                        seqLenAttribute.Value = "4";
                        noteElement.Attributes.Append(seqLenAttribute);
                        XmlAttribute levelAttribute = doc.CreateAttribute("level");
                        levelAttribute.Value = nextNote.Attributes["level"].Value;
                        noteElement.Attributes.Append(levelAttribute);
                        XmlAttribute typeAttribute = doc.CreateAttribute("type");
                        typeAttribute.Value = "";
                        noteElement.Attributes.Append(typeAttribute);
                        listNodes[0].InsertAfter(noteElement, currentNote);
                        currentNote = noteElement;
                        i++;
                    }

                }
            }
            doc.Save(filePath + ".TranslateClassical");
        }

        internal static void TranslateBPMIdolOrPinball(string filePath, float BPMMultiple)
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(filePath);
            XmlElement root = null;
            root = doc.DocumentElement;
            XmlNodeList listNodes = null;
            var bpmNote = root.SelectNodes("/Level/LevelInfo/BPM")[0];
            bpmNote.InnerText = (float.Parse(bpmNote.InnerText) * BPMMultiple).ToString();
            var barAmountNote = root.SelectNodes("/Level/LevelInfo/BarAmount")[0];
            barAmountNote.InnerText = (float.Parse(barAmountNote.InnerText) * BPMMultiple).ToString();
            listNodes = root.GetElementsByTagName("Normal");
            foreach (XmlNode item in listNodes[0].ChildNodes)
            {
                if (!item.HasChildNodes)
                {

                    int barNum = int.Parse(item.Attributes["Bar"].Value);
                    int posNum = int.Parse(item.Attributes["Pos"].Value);
                    int totalPosNum = barNum * 32 + posNum / 2;
                    totalPosNum = int.Parse(Math.Round(totalPosNum * BPMMultiple).ToString());
                    item.Attributes["Bar"].Value = (totalPosNum / 32).ToString();
                    item.Attributes["Pos"].Value = (totalPosNum % 32 * 2).ToString();

                    if (item.Attributes["note_type"].Value == "PinballLong" || item.Attributes["note_type"].Value == "long")
                    {
                        int endBarNum = int.Parse(item.Attributes["EndBar"].Value);
                        int endPosNum = int.Parse(item.Attributes["EndPos"].Value);
                        int totalEndPosNum = endBarNum * 32 + endPosNum / 2;
                        totalPosNum = int.Parse(Math.Round(totalEndPosNum * BPMMultiple).ToString());
                        item.Attributes["EndBar"].Value = (totalPosNum / 32).ToString();
                        item.Attributes["EndPos"].Value = (totalPosNum % 32 * 2).ToString();

                    }
                }
                else
                {
                    foreach (XmlNode i in item.ChildNodes)
                    {
                        int barNum = int.Parse(i.Attributes["Bar"].Value);
                        int posNum = int.Parse(i.Attributes["Pos"].Value);
                        int totalPosNum = barNum * 32 + posNum / 2;
                        totalPosNum = int.Parse(Math.Round(totalPosNum * BPMMultiple).ToString());
                        i.Attributes["Bar"].Value = (totalPosNum / 32).ToString();
                        i.Attributes["Pos"].Value = (totalPosNum % 32 * 2).ToString();

                        if (i.Attributes["note_type"].Value == "PinballLong" || i.Attributes["note_type"].Value == "long")
                        {
                            int endBarNum = int.Parse(i.Attributes["EndBar"].Value);
                            int endPosNum = int.Parse(i.Attributes["EndPos"].Value);
                            int totalEndPosNum = endBarNum * 32 + endPosNum / 2;
                            totalPosNum = int.Parse(Math.Round(totalEndPosNum * BPMMultiple).ToString());
                            i.Attributes["EndBar"].Value = (totalPosNum / 32).ToString();
                            i.Attributes["EndPos"].Value = (totalPosNum % 32 * 2).ToString();

                        }
                    }
                }
            }
            doc.Save(filePath+ ".X" + BPMMultiple );
        }

        public static void Correction(string filePath)
        {
            XmlDocument doc = new XmlDocument();
            try
            {
                doc.Load(filePath);
                XmlElement root = null;
                root = doc.DocumentElement;
                XmlNodeList listNodes = null;
                listNodes = root.GetElementsByTagName("Normal");
                foreach (XmlNode item in listNodes[0].ChildNodes)
                {
                    if (item.Attributes["Pos"].Value == "2" || item.Attributes["Pos"].Value == "4") item.Attributes["Pos"].Value = "0";
                    else if (item.Attributes["Pos"].Value == "6" || item.Attributes["Pos"].Value == "10") item.Attributes["Pos"].Value = "8";
                    else if (item.Attributes["Pos"].Value == "12" || item.Attributes["Pos"].Value == "14" || item.Attributes["Pos"].Value == "18" || item.Attributes["Pos"].Value == "20") item.Attributes["Pos"].Value = "16";
                    else if (item.Attributes["Pos"].Value == "22" || item.Attributes["Pos"].Value == "26" || item.Attributes["Pos"].Value == "28") item.Attributes["Pos"].Value = "24";
                    else if (item.Attributes["Pos"].Value == "28" || item.Attributes["Pos"].Value == "30" || item.Attributes["Pos"].Value == "34" || item.Attributes["Pos"].Value == "36") item.Attributes["Pos"].Value = "32";
                    else if (item.Attributes["Pos"].Value == "38" || item.Attributes["Pos"].Value == "42") item.Attributes["Pos"].Value = "40";
                    else if (item.Attributes["Pos"].Value == "44" || item.Attributes["Pos"].Value == "46" || item.Attributes["Pos"].Value == "50" || item.Attributes["Pos"].Value == "52") item.Attributes["Pos"].Value = "48";
                    else if (item.Attributes["Pos"].Value == "54" || item.Attributes["Pos"].Value == "58") item.Attributes["Pos"].Value = "56";
                    else if (item.Attributes["Pos"].Value == "60" || item.Attributes["Pos"].Value == "62")
                    {
                        item.Attributes["Pos"].Value = "0";
                        int bar = int.Parse(item.Attributes["Bar"].Value) + 1;
                        item.Attributes["Bar"].Value = bar.ToString();
                    }
                }
                doc.Save(@"E:\Reserve.xml");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
        const int _bubbleSlipLength = 8;
        public static void IdolToBubble(string FilePath)
        {
            XmlDocument idolDoc = new XmlDocument();
            XmlDocument bubbleDoc = new XmlDocument();
            idolDoc.Load(FilePath);
            XmlElement root = null;
            root = idolDoc.DocumentElement;
            XmlNodeList listNodes = null;
            listNodes = root.SelectNodes("/Level/NoteInfo/Normal");
            XmlNode rootNode = bubbleDoc.CreateElement("Normal");
            XmlAttribute PosNumAttribute = bubbleDoc.CreateAttribute("PosNum");
            PosNumAttribute.Value = "64";
            rootNode.Attributes.Append(PosNumAttribute);
            XmlAttributeCollection collection;
            XmlNode pointNode;
            int bubbleId = 1;
            string track = "";
            foreach (XmlNode node in listNodes[0].ChildNodes)
            {
                if (!node.HasChildNodes)
                {
                    collection = node.Attributes as XmlAttributeCollection;
                    if (collection == null) return;
                    pointNode = bubbleDoc.CreateElement("Note");
                    XmlAttribute BarAttribute = bubbleDoc.CreateAttribute("Bar");
                    BarAttribute.Value = collection["Bar"].Value;
                    pointNode.Attributes.Append(BarAttribute);
                    XmlAttribute BeatPosAttribute = bubbleDoc.CreateAttribute("BeatPos");
                    BeatPosAttribute.Value = collection["Pos"].Value;
                    pointNode.Attributes.Append(BeatPosAttribute);
                    XmlAttribute TrackAttribute = bubbleDoc.CreateAttribute("Track");
                    if (collection["target_track"].Value == "Left1")
                    {
                        track = "0";
                    }
                    else if (collection["target_track"].Value == "Left2")
                    {
                        track = "1";
                    }
                    else if (collection["target_track"].Value == "Middle")
                    {
                        track = "2";
                    }
                    else if (collection["target_track"].Value == "Right1")
                    {
                        track = "3";
                    }
                    else if (collection["target_track"].Value == "Right2")
                    {
                        track = "4";
                    }
                    else
                    {
                        track = "0";
                    }
                    TrackAttribute.Value = track;
                    pointNode.Attributes.Append(TrackAttribute);
                    XmlAttribute TypeAttribute = bubbleDoc.CreateAttribute("Type");
                    XmlNode ScreenPosNode = bubbleDoc.CreateElement("ScreenPos"); ;
                    if (collection["note_type"].Value == "short")
                    {
                        TypeAttribute.Value = "0";
                        pointNode.Attributes.Append(TypeAttribute);
                        XmlAttribute x = bubbleDoc.CreateAttribute("x");
                        x.Value = "0";
                        ScreenPosNode.Attributes.Append(x);
                        XmlAttribute y = bubbleDoc.CreateAttribute("y");
                        y.Value = "0";
                        ScreenPosNode.Attributes.Append(y);
                        XmlNode FlyTrackNote = bubbleDoc.CreateElement("FlyTrack");
                        XmlAttribute flyTrackAttribute = bubbleDoc.CreateAttribute("name");
                        flyTrackAttribute.Value = "波浪形";
                        FlyTrackNote.Attributes.Append(flyTrackAttribute);
                        XmlAttribute degreeAttribute = bubbleDoc.CreateAttribute("degree");
                        degreeAttribute.Value = "0";
                        FlyTrackNote.Attributes.Append(degreeAttribute);
                        ScreenPosNode.AppendChild(FlyTrackNote);
                        pointNode.AppendChild(ScreenPosNode);
                    }
                    else if (collection["note_type"].Value == "slip")
                    {
                        TypeAttribute.Value = "2";
                        pointNode.Attributes.Append(TypeAttribute);
                        int currentTotalPos = int.Parse(collection["Bar"].Value) * 32 + int.Parse(collection["Pos"].Value) / 2;
                        currentTotalPos = currentTotalPos + _bubbleSlipLength;
                        XmlAttribute endBarAttribute = bubbleDoc.CreateAttribute("EndBar");
                        endBarAttribute.Value = (currentTotalPos / 32).ToString();
                        pointNode.Attributes.Append(endBarAttribute);

                        XmlAttribute endPosAttribute = bubbleDoc.CreateAttribute("EndPos");
                        endPosAttribute.Value = ((currentTotalPos % 32) * 2).ToString();
                        pointNode.Attributes.Append(endPosAttribute);

                        XmlAttribute IDAttribute = bubbleDoc.CreateAttribute("ID");
                        IDAttribute.Value = bubbleId.ToString();
                        pointNode.Attributes.Append(IDAttribute);

                        XmlNode MoveTrackNote = bubbleDoc.CreateElement("MoveTrack");

                        XmlAttribute MoveTrackNameAttribute = bubbleDoc.CreateAttribute("name");
                        MoveTrackNameAttribute.Value = "长按弧";
                        MoveTrackNote.Attributes.Append(MoveTrackNameAttribute);

                        XmlAttribute MoveTrackDegreeAttribute = bubbleDoc.CreateAttribute("degree");
                        MoveTrackDegreeAttribute.Value = "0";
                        MoveTrackNote.Attributes.Append(MoveTrackDegreeAttribute);

                        XmlNode FlyTrackNote = bubbleDoc.CreateElement("FlyTrack");
                        XmlAttribute FlyTrackNameAttribute = bubbleDoc.CreateAttribute("name");
                        FlyTrackNameAttribute.Value = "长按弧";
                        FlyTrackNote.Attributes.Append(FlyTrackNameAttribute);

                        XmlAttribute FlyTrackDegreeAttribute = bubbleDoc.CreateAttribute("degree");
                        FlyTrackDegreeAttribute.Value = "0";
                        FlyTrackNote.Attributes.Append(FlyTrackDegreeAttribute);

                        XmlNode ScreenPosNote = bubbleDoc.CreateElement("ScreenPos");
                        XmlAttribute x = bubbleDoc.CreateAttribute("x");
                        x.Value = "0";
                        ScreenPosNote.Attributes.Append(x);
                        XmlAttribute y = bubbleDoc.CreateAttribute("y");
                        y.Value = "0";
                        ScreenPosNote.Attributes.Append(y);
                        pointNode.AppendChild(MoveTrackNote);
                        pointNode.AppendChild(FlyTrackNote);
                        pointNode.AppendChild(ScreenPosNote);
                    }
                    else
                    {
                        TypeAttribute.Value = "1";
                        pointNode.Attributes.Append(TypeAttribute);
                        XmlAttribute endBarAttribute = bubbleDoc.CreateAttribute("EndBar");
                        endBarAttribute.Value = collection["EndBar"].Value;
                        pointNode.Attributes.Append(endBarAttribute);
                        XmlAttribute endPosAttribute = bubbleDoc.CreateAttribute("EndPos");
                        endPosAttribute.Value = collection["EndPos"].Value;
                        pointNode.Attributes.Append(endPosAttribute);
                        XmlAttribute IDAttribute = bubbleDoc.CreateAttribute("ID");
                        IDAttribute.Value = bubbleId.ToString();
                        pointNode.Attributes.Append(IDAttribute);
                        XmlNode MoveTrackNote = bubbleDoc.CreateElement("MoveTrack");
                        XmlAttribute MoveTrackNameAttribute = bubbleDoc.CreateAttribute("name");
                        MoveTrackNameAttribute.Value = "长按弧";
                        MoveTrackNote.Attributes.Append(MoveTrackNameAttribute);
                        XmlAttribute MoveTrackDegreeAttribute = bubbleDoc.CreateAttribute("degree");
                        MoveTrackDegreeAttribute.Value = "0";
                        MoveTrackNote.Attributes.Append(MoveTrackDegreeAttribute);
                        XmlNode FlyTrackNote = bubbleDoc.CreateElement("FlyTrack");
                        XmlAttribute FlyTrackNameAttribute = bubbleDoc.CreateAttribute("name");
                        FlyTrackNameAttribute.Value = "长按弧";
                        FlyTrackNote.Attributes.Append(FlyTrackNameAttribute);
                        XmlAttribute FlyTrackDegreeAttribute = bubbleDoc.CreateAttribute("degree");
                        FlyTrackDegreeAttribute.Value = "0";
                        FlyTrackNote.Attributes.Append(FlyTrackDegreeAttribute);
                        XmlNode ScreenPosNote = bubbleDoc.CreateElement("ScreenPos");
                        XmlAttribute x = bubbleDoc.CreateAttribute("x");
                        x.Value = "0";
                        ScreenPosNote.Attributes.Append(x);
                        XmlAttribute y = bubbleDoc.CreateAttribute("y");
                        y.Value = "0";
                        ScreenPosNote.Attributes.Append(y);
                        pointNode.AppendChild(MoveTrackNote);
                        pointNode.AppendChild(FlyTrackNote);
                        pointNode.AppendChild(ScreenPosNote);
                    }

                    bubbleId += 1;
                    rootNode.AppendChild(pointNode);
                }
                else
                {
                    foreach (XmlNode item in node.ChildNodes)
                    {
                        collection = item.Attributes as XmlAttributeCollection;
                        if (collection == null) return;
                        pointNode = bubbleDoc.CreateElement("Note");
                        XmlAttribute BarAttribute = bubbleDoc.CreateAttribute("Bar");
                        BarAttribute.Value = collection["Bar"].Value;
                        pointNode.Attributes.Append(BarAttribute);
                        XmlAttribute BeatPosAttribute = bubbleDoc.CreateAttribute("BeatPos");
                        BeatPosAttribute.Value = collection["Pos"].Value;
                        pointNode.Attributes.Append(BeatPosAttribute);
                        XmlAttribute TrackAttribute = bubbleDoc.CreateAttribute("Track");
                        if (collection["target_track"].Value == "Left1")
                        {
                            track = "0";
                        }
                        else if (collection["target_track"].Value == "Left2")
                        {
                            track = "1";
                        }
                        else if (collection["target_track"].Value == "Middle")
                        {
                            track = "2";
                        }
                        else if (collection["target_track"].Value == "Right1")
                        {
                            track = "3";
                        }
                        else if (collection["target_track"].Value == "Right2")
                        {
                            track = "4";
                        }
                        else
                        {
                            track = "0";
                        }
                        TrackAttribute.Value = track;
                        pointNode.Attributes.Append(TrackAttribute);
                        XmlAttribute TypeAttribute = bubbleDoc.CreateAttribute("Type");
                        XmlNode ScreenPosNode = bubbleDoc.CreateElement("ScreenPos"); ;
                        if (collection["note_type"].Value == "short")
                        {
                            TypeAttribute.Value = "0";
                            pointNode.Attributes.Append(TypeAttribute);
                            XmlAttribute x = bubbleDoc.CreateAttribute("x");
                            x.Value = "0";
                            ScreenPosNode.Attributes.Append(x);
                            XmlAttribute y = bubbleDoc.CreateAttribute("y");
                            y.Value = "0";
                            ScreenPosNode.Attributes.Append(y);
                            XmlNode FlyTrackNote = bubbleDoc.CreateElement("FlyTrack");
                            XmlAttribute flyTrackAttribute = bubbleDoc.CreateAttribute("name");
                            flyTrackAttribute.Value = "波浪形";
                            FlyTrackNote.Attributes.Append(flyTrackAttribute);
                            XmlAttribute degreeAttribute = bubbleDoc.CreateAttribute("degree");
                            degreeAttribute.Value = "0";
                            FlyTrackNote.Attributes.Append(degreeAttribute);
                            ScreenPosNode.AppendChild(FlyTrackNote);
                            pointNode.AppendChild(ScreenPosNode);
                        }
                        else if (collection["note_type"].Value == "slip")
                        {
                            continue;
                            TypeAttribute.Value = "2";
                            pointNode.Attributes.Append(TypeAttribute);
                            XmlAttribute endBarAttribute = bubbleDoc.CreateAttribute("EndBar");
                            endBarAttribute.Value = (int.Parse(collection["Bar"].Value) + 1).ToString();
                            pointNode.Attributes.Append(endBarAttribute);

                            XmlAttribute endPosAttribute = bubbleDoc.CreateAttribute("EndPos");
                            endPosAttribute.Value = 4.ToString();
                            pointNode.Attributes.Append(endPosAttribute);

                            XmlAttribute IDAttribute = bubbleDoc.CreateAttribute("ID");
                            IDAttribute.Value = bubbleId.ToString();
                            pointNode.Attributes.Append(IDAttribute);

                            XmlNode MoveTrackNote = bubbleDoc.CreateElement("MoveTrack");

                            XmlAttribute MoveTrackNameAttribute = bubbleDoc.CreateAttribute("name");
                            MoveTrackNameAttribute.Value = "长按弧";
                            MoveTrackNote.Attributes.Append(MoveTrackNameAttribute);

                            XmlAttribute MoveTrackDegreeAttribute = bubbleDoc.CreateAttribute("degree");
                            MoveTrackDegreeAttribute.Value = "0";
                            MoveTrackNote.Attributes.Append(MoveTrackDegreeAttribute);

                            XmlNode FlyTrackNote = bubbleDoc.CreateElement("FlyTrack");
                            XmlAttribute FlyTrackNameAttribute = bubbleDoc.CreateAttribute("name");
                            FlyTrackNameAttribute.Value = "长按弧";
                            FlyTrackNote.Attributes.Append(FlyTrackNameAttribute);

                            XmlAttribute FlyTrackDegreeAttribute = bubbleDoc.CreateAttribute("degree");
                            FlyTrackDegreeAttribute.Value = "0";
                            FlyTrackNote.Attributes.Append(FlyTrackDegreeAttribute);

                            XmlNode ScreenPosNote = bubbleDoc.CreateElement("ScreenPos");
                            XmlAttribute x = bubbleDoc.CreateAttribute("x");
                            x.Value = "0";
                            ScreenPosNote.Attributes.Append(x);
                            XmlAttribute y = bubbleDoc.CreateAttribute("y");
                            y.Value = "0";
                            ScreenPosNote.Attributes.Append(y);
                            pointNode.AppendChild(MoveTrackNote);
                            pointNode.AppendChild(FlyTrackNote);
                            pointNode.AppendChild(ScreenPosNote);
                        }
                        else
                        {
                            TypeAttribute.Value = "1";
                            pointNode.Attributes.Append(TypeAttribute);
                            XmlAttribute endBarAttribute = bubbleDoc.CreateAttribute("EndBar");
                            endBarAttribute.Value = collection["EndBar"].Value;
                            pointNode.Attributes.Append(endBarAttribute);
                            XmlAttribute endPosAttribute = bubbleDoc.CreateAttribute("EndPos");
                            endPosAttribute.Value = collection["EndPos"].Value;
                            pointNode.Attributes.Append(endPosAttribute);
                            XmlAttribute IDAttribute = bubbleDoc.CreateAttribute("ID");
                            IDAttribute.Value = bubbleId.ToString();
                            pointNode.Attributes.Append(IDAttribute);
                            XmlNode MoveTrackNote = bubbleDoc.CreateElement("MoveTrack");
                            XmlAttribute MoveTrackNameAttribute = bubbleDoc.CreateAttribute("name");
                            MoveTrackNameAttribute.Value = "长按弧";
                            MoveTrackNote.Attributes.Append(MoveTrackNameAttribute);
                            XmlAttribute MoveTrackDegreeAttribute = bubbleDoc.CreateAttribute("degree");
                            MoveTrackDegreeAttribute.Value = "0";
                            MoveTrackNote.Attributes.Append(MoveTrackDegreeAttribute);
                            XmlNode FlyTrackNote = bubbleDoc.CreateElement("FlyTrack");
                            XmlAttribute FlyTrackNameAttribute = bubbleDoc.CreateAttribute("name");
                            FlyTrackNameAttribute.Value = "长按弧";
                            FlyTrackNote.Attributes.Append(FlyTrackNameAttribute);
                            XmlAttribute FlyTrackDegreeAttribute = bubbleDoc.CreateAttribute("degree");
                            FlyTrackDegreeAttribute.Value = "0";
                            FlyTrackNote.Attributes.Append(FlyTrackDegreeAttribute);
                            XmlNode ScreenPosNote = bubbleDoc.CreateElement("ScreenPos");
                            XmlAttribute x = bubbleDoc.CreateAttribute("x");
                            x.Value = "0";
                            ScreenPosNote.Attributes.Append(x);
                            XmlAttribute y = bubbleDoc.CreateAttribute("y");
                            y.Value = "0";
                            ScreenPosNote.Attributes.Append(y);
                            pointNode.AppendChild(MoveTrackNote);
                            pointNode.AppendChild(FlyTrackNote);
                            pointNode.AppendChild(ScreenPosNote);
                        }

                        bubbleId += 1;
                        rootNode.AppendChild(pointNode);
                    }
                }
            }
            bubbleDoc.AppendChild(rootNode);
            bubbleDoc.Save(@"E:\idolToBubble.xml");
        }

        internal static string GetXmlAuthorName(string filePath)
        {
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.Load(filePath);
            XmlElement root = xmlDoc.DocumentElement;
            XmlNode node = root.SelectNodes("/Level/MusicInfo/Author")[0];
            if (node != null)
                return node.InnerXml;
            return "";
        }

        internal static void CreateIdolShortModel(string filePath)
        {
            XmlDocument idolDoc = new XmlDocument();
            idolDoc.Load(filePath);
            XmlElement root = null;
            root = idolDoc.DocumentElement;
            XmlNodeList listNodes  = root.SelectNodes("/Level/NoteInfo/Normal");
            int barCount=int.Parse(root.SelectNodes("/Level/LevelInfo/BarAmount")[0].InnerText);
            int BeginBarLen = int.Parse(root.SelectNodes("/Level/LevelInfo/BeginBarLen")[0].InnerText);
            int vaildBarLen = barCount - BeginBarLen - 4;
            int trackCount= int.Parse(root.SelectNodes("/Level/LevelInfo/TrackCount")[0].InnerText);
            bool isMultipleFinger = MessageBox.Show("是否是四指?", "提示", MessageBoxButton.YesNo) == MessageBoxResult.Yes;
            listNodes[0].InnerText = CreateIdolNotationModel.CreateRadomShortModel(trackCount, (BeginBarLen+1)*32, vaildBarLen*32, !isMultipleFinger);
            idolDoc.Save(filePath.Substring(0,filePath.LastIndexOf("\\"))+"\\model.xml");
        }

        internal static string GetXmlSongName(string filePath)
        {
            XmlDocument xmlDoc = new XmlDocument();
     
                xmlDoc.Load(filePath);
                XmlElement root = xmlDoc.DocumentElement;
                XmlNode node = root.SelectNodes("/Level/MusicInfo/Title")[0];
                if (node != null)
                    return node.InnerXml;
                return "";
         
        }

        public static void IdolToXY(string filePath)
        {
            XmlDocument IdolDoc = new XmlDocument();
            XmlDocument XYDoc = new XmlDocument();//弦乐
            IdolDoc.Load(filePath);
            XmlElement root = IdolDoc.DocumentElement;
            XmlNodeList  listNodes = root.SelectNodes("/Level/NoteInfo/Normal");
            XmlNode rootNode = XYDoc.CreateElement("Normal");
            string[] tracks = { "-50", "-40", "-30", "-20", "-10", "0", "10", "20", "30", "40", "50" };
            XmlAttributeCollection collection;
            XmlNode pointNode;
            Random rd = new Random();
            Random rd1 = new Random();
            Random rdRmove = new Random();
            int i;
            foreach (XmlNode node in listNodes[0].ChildNodes)
            {
                pointNode = XYDoc.CreateElement("Note");
                string track = "";
                collection = node.Attributes as XmlAttributeCollection;  
                if (collection == null) continue;
                if (!node.HasChildNodes)
                {
                    if (collection["target_track"].Value == "Left2" || collection["target_track"].Value == "Left1")
                    {

                        i = rd.Next(0, 5);
                        track = tracks[i];
                    }
                    else if (collection["target_track"].Value == "Right1" || collection["target_track"].Value == "Right2")
                    {
                        i = rd.Next(6, 11);
                        track = tracks[i];
                    }
                    else
                        track = tracks[5];
                    //创建Note子节点
                    XmlAttribute NoteTypeAttribute = XYDoc.CreateAttribute("note_type");//键类型
                    XmlAttribute BarAttribute = XYDoc.CreateAttribute("Bar");
                    BarAttribute.Value = collection["Bar"].Value.ToString();
                    pointNode.Attributes.Append(BarAttribute);
                    XmlAttribute PosAttribute = XYDoc.CreateAttribute("Pos");
                    PosAttribute.Value = collection["Pos"].Value.ToString();
                    pointNode.Attributes.Append(PosAttribute);

                    XmlAttribute TrackAttribute = XYDoc.CreateAttribute("track");
                    
                    TrackAttribute.Value = track;
                    pointNode.Attributes.Append(TrackAttribute);
                    XmlNode lightPointNode = XYDoc.CreateElement("Note");
                    bool isSlip = false;
                    if (collection["note_type"].Value == "long")
                    {
                        NoteTypeAttribute.Value = "long";
                        pointNode.Attributes.Append(NoteTypeAttribute);

                        XmlAttribute lengthAttribute = XYDoc.CreateAttribute("length");
                        int length = (int.Parse(collection["EndBar"].Value) * 64 + int.Parse(collection["EndPos"].Value)) -
                            (int.Parse(collection["Bar"].Value) * 64 + int.Parse(collection["Pos"].Value));
                        lengthAttribute.Value = length.ToString();
                        pointNode.Attributes.Append(lengthAttribute);
                        
                    }
                    else if (collection["note_type"].Value == "short")
                    {
                        NoteTypeAttribute.Value = "short";
                        pointNode.Attributes.Append(NoteTypeAttribute);
                    }
                 
                    else if (collection["note_type"].Value == "slip")
                    {

                        NoteTypeAttribute.Value = "light";
                        pointNode.Attributes.Append(NoteTypeAttribute);
                        int nextLightPosition = int.Parse(collection["Bar"].Value) * 64 + int.Parse(collection["Pos"].Value)+2;
                        XmlAttribute lightBarAttribute = XYDoc.CreateAttribute("Bar");
                        lightBarAttribute.Value = (nextLightPosition / 64).ToString();
                        lightPointNode.Attributes.Append(lightBarAttribute);
                        XmlAttribute lightPosAttribute = XYDoc.CreateAttribute("Pos");
                        lightPosAttribute.Value = (nextLightPosition % 64).ToString();
                        lightPointNode.Attributes.Append(lightPosAttribute);

                        XmlAttribute lightTrackAttribute = XYDoc.CreateAttribute("track");
                        if(track=="-50")
                         track = "-40";
                        else if(track=="50")
                        track = "40";
                        else
                        {
                            int i1= rd1.Next(1, 3);
                            if (i1 == 1)
                            {
                                track = (int.Parse(track) + 10).ToString();
                            }
                            else
                            {
                                track = (int.Parse(track) -10).ToString();
                            }
                        }

                        lightTrackAttribute.Value = track;
                        lightPointNode.Attributes.Append(lightTrackAttribute);

                        XmlAttribute lightNoteTypeAttribute = XYDoc.CreateAttribute("note_type");
                        lightNoteTypeAttribute.Value = "light";
                        lightPointNode.Attributes.Append(lightNoteTypeAttribute);
                        isSlip = true;
                    }

                    rootNode.AppendChild(pointNode);
                    if(isSlip)
                    {
                        rootNode.InsertAfter(lightPointNode,pointNode);
                    }
                    isSlip = false;
                }
                else
                {
                    collection = node.ChildNodes[0].Attributes as XmlAttributeCollection;
                    if (collection["target_track"].Value == "Left2" || collection["target_track"].Value == "Left1")
                    {

                        i = rd.Next(0, 5);
                        track = tracks[i];
                    }
                    else if (collection["target_track"].Value == "Right1" || collection["target_track"].Value == "Right2")
                    {
                        i = rd.Next(6, 11);
                        track = tracks[i];
                    }
                    else
                        track = tracks[5];
                    XmlAttribute NoteTypeAttribute = XYDoc.CreateAttribute("note_type");//键类型
                    NoteTypeAttribute.Value = "slip";
                    pointNode.Attributes.Append(NoteTypeAttribute);
                    XmlAttribute BarAttribute = XYDoc.CreateAttribute("Bar");
                    BarAttribute.Value = collection["Bar"].Value.ToString();
                    pointNode.Attributes.Append(BarAttribute);
                    XmlAttribute PosAttribute = XYDoc.CreateAttribute("Pos");
                    PosAttribute.Value = collection["Pos"].Value.ToString();
                    pointNode.Attributes.Append(PosAttribute);
                    XmlAttribute TrackAttribute = XYDoc.CreateAttribute("track");
                    TrackAttribute.Value = track;
                    pointNode.Attributes.Append(TrackAttribute);
                    IList<string> targetTracksLength = new List<string>();
                    IList<string> targetTracks = new List<string>();
                     i = 0;
                    foreach (XmlNode combineItem in node.ChildNodes)
                    {
                        if (combineItem.Attributes["note_type"].Value == "slip")
                            continue;
                        int length = (int.Parse(combineItem.Attributes["EndBar"].Value) * 64 + int.Parse(combineItem.Attributes["EndPos"].Value)) -
                       (int.Parse(combineItem.Attributes["Bar"].Value) * 64 + int.Parse(combineItem.Attributes["Pos"].Value));
                        targetTracksLength.Add(length.ToString());
                        if(i>0)
                            track= GetNoRepeatSlipTrack(combineItem.Attributes["target_track"].Value, targetTracks[i-1],rd);
                        else
                            track= GetNoRepeatSlipTrack(combineItem.Attributes["target_track"].Value,"",rd);
                        targetTracks.Add(track);
                        i += 1;
                    }
                    if (targetTracksLength.Count >= 5)
                    {
                        IList<int> reMoveIndexs = new List<int>();
                        IList<string> newTargetTracks = new List<string>();
                        IList<string> newTargetTracksLength = new List<string>();
                        int removeCount = targetTracksLength.Count - 4;
                        for (int j = 0; j < removeCount; j++)
                        {
                          int rdIndex= rdRmove.Next(1, targetTracksLength.Count);
                            if (reMoveIndexs.Contains(rdIndex))
                                j--;
                           reMoveIndexs.Add(rdIndex);
                           
                        }
                        for (int a = 0; a < targetTracks.Count; a++)
                        {
                                if(!reMoveIndexs.Contains(a))
                            {
                                newTargetTracks.Add(targetTracks[a]);
                                newTargetTracksLength.Add(targetTracksLength[a]);
                            }
                            
                        
                        }
                        targetTracks = newTargetTracks;
                        targetTracksLength = newTargetTracksLength;
                    }
                    XmlAttribute TargetTrackAttribute = XYDoc.CreateAttribute("target_track");
                    TargetTrackAttribute.Value = string.Join(",", targetTracks.ToList().ToArray());
                    pointNode.Attributes.Append(TargetTrackAttribute);
                    XmlAttribute LengthAttribute = XYDoc.CreateAttribute("length");
                    LengthAttribute.Value = string.Join(",", targetTracksLength.ToList().ToArray());
                    pointNode.Attributes.Append(LengthAttribute);
                }
                rootNode.AppendChild(pointNode);

            }

            XYDoc.AppendChild(rootNode);
            XYDoc.Save(@"E:\IdolToXY.xml");
        }

        private  static string GetNoRepeatSlipTrack(string idolTragetValue,string lastTrackValue, Random rd)
        {
            string[] tracks = { "-50", "-40", "-30", "-20", "-10", "0", "10", "20", "30", "40", "50" };
            string currentTraget = "";
            if (idolTragetValue == "Left2" || idolTragetValue == "Left1")
            {
             
                int i = rd.Next(0, 5);
                currentTraget= tracks[i];
                if (currentTraget == lastTrackValue)
                {
                  return  GetNoRepeatSlipTrack(idolTragetValue, lastTrackValue,rd);
                }
                else
                    return currentTraget;
            }
            else if (idolTragetValue == "Right1" || idolTragetValue == "Right2")
            {
                int i = rd.Next(6, 11);
                currentTraget = tracks[i];
                if (currentTraget == lastTrackValue)
                {
                   return  GetNoRepeatSlipTrack(idolTragetValue, lastTrackValue,rd);
                }
                else
                    return currentTraget;
            }
            else
               return  tracks[5];
        }
        internal static void IdolToPinballNoDeleteRepeat(string filePath, bool isChangeSlipNote)
        {
            XmlDocument idolDoc = new XmlDocument();
            XmlDocument pinBallDoc = new XmlDocument();
            // XmlDeclaration Declaration = pinBallDoc.CreateXmlDeclaration("1.0", "utf-8", null);
            idolDoc.Load(filePath);
            XmlElement root = null;
            root = idolDoc.DocumentElement;
            XmlNodeList listNodes = null;
            listNodes = root.SelectNodes("/Level/NoteInfo/Normal");
            XmlNode rootNode = pinBallDoc.CreateElement("Normal");
            XmlAttributeCollection collection;
            XmlNode pointNode;
            int pinBallId = 1;
            List<int> slipID = new List<int>();
            string lastEndArea1 = "";
            string lastBar1 = "";
            string lastPos1 = "";
            string lastNoteType1 = "";
            foreach (XmlNode node in listNodes[0].ChildNodes)
            {

                if (!node.HasChildNodes)
                {
                    pointNode = pinBallDoc.CreateElement("Note");
                    collection = node.Attributes as XmlAttributeCollection;
                    if (collection == null) return;
                    //创建Note子节点
                    XmlAttribute NoteTypeAttribute = pinBallDoc.CreateAttribute("note_type");
                    XmlAttribute IDAttribute = pinBallDoc.CreateAttribute("ID");
                    IDAttribute.Value = pinBallId.ToString();
                    pointNode.Attributes.Append(IDAttribute);
                    XmlAttribute BarAttribute = pinBallDoc.CreateAttribute("Bar");
                    BarAttribute.Value = collection["Bar"].Value.ToString();
                    pointNode.Attributes.Append(BarAttribute);
                    XmlAttribute PosAttribute = pinBallDoc.CreateAttribute("Pos");
                    PosAttribute.Value = collection["Pos"].Value.ToString();
                    pointNode.Attributes.Append(PosAttribute);
                    XmlAttribute EndArea = pinBallDoc.CreateAttribute("EndArea");
                    if (collection["target_track"].Value == "Left1" || collection["target_track"].Value == "Left2")
                        EndArea.Value = "1|2";
                    else if (collection["target_track"].Value == "Middle")
                    {
                        Random rd = new Random();
                        int i = rd.Next(1, 4);
                        if (i == 1)
                            EndArea.Value = "1|2";
                        else if (i == 2)
                            EndArea.Value = "";
                        else
                            EndArea.Value = "3|4";
                    }
                    else
                        EndArea.Value = "3|4";
                    if ((lastNoteType1 == "short" || lastNoteType1 == "slip" &&
                        collection["note_type"].Value == "short" ||
                        collection["note_type"].Value == "slip")
                        && EndArea.Value == lastEndArea1 &&
                        lastPos1 == PosAttribute.Value &&
                        lastBar1 == BarAttribute.Value)
                    {
                        if (EndArea.Value == "1|2")
                            EndArea.Value = "3|4";
                        else
                            EndArea.Value = "1|2";
                    }
                    lastEndArea1 = EndArea.Value;
                    lastBar1 = BarAttribute.Value;
                    lastPos1 = PosAttribute.Value;
                    lastNoteType1 = collection["note_type"].Value;
                    pointNode.Attributes.Append(EndArea);
                    XmlAttribute MoveTime = pinBallDoc.CreateAttribute("MoveTime");
                    MoveTime.Value = "3";
                    pointNode.Attributes.Append(MoveTime);

                    if (collection["note_type"].Value == "long")
                    {
                        NoteTypeAttribute.Value = "PinballLong";
                        pointNode.Attributes.Append(NoteTypeAttribute);
                        XmlAttribute EndBarAttribute = pinBallDoc.CreateAttribute("EndBar");
                        EndBarAttribute.Value = collection["EndBar"].Value.ToString();
                        pointNode.Attributes.Append(EndBarAttribute);
                        XmlAttribute EndPosAttribute = pinBallDoc.CreateAttribute("EndPos");
                        EndPosAttribute.Value = collection["EndPos"].Value.ToString();
                        pointNode.Attributes.Append(EndPosAttribute);
                        XmlAttribute SonAttribute = pinBallDoc.CreateAttribute("Son");
                        SonAttribute.Value = "";
                        pointNode.Attributes.Append(SonAttribute);
                    }
                    else if (collection["note_type"].Value == "short")
                    {
                        NoteTypeAttribute.Value = "PinballSingle";
                        pointNode.Attributes.Append(NoteTypeAttribute);
                        XmlAttribute SonPosAttribute = pinBallDoc.CreateAttribute("Son");
                        SonPosAttribute.Value = "";
                        pointNode.Attributes.Append(SonPosAttribute);
                    }
                    else if (collection["note_type"].Value == "slip")
                    {

                        NoteTypeAttribute.Value = "PinballSlip";
                        pointNode.Attributes.Append(NoteTypeAttribute);
                        XmlAttribute SonAttribute = pinBallDoc.CreateAttribute("Son");
                        SonAttribute.Value = "";
                        pointNode.Attributes.Append(SonAttribute);
                        slipID.Add(pinBallId);


                    }
                    rootNode.AppendChild(pointNode);
                    pinBallId += 1;
                }
                else
                {
                    foreach (XmlNode combineItem in node.ChildNodes)
                    {
                        pointNode = pinBallDoc.CreateElement("Note");
                        XmlAttribute IDAttribute = pinBallDoc.CreateAttribute("ID");
                        IDAttribute.Value = pinBallId.ToString();
                        pointNode.Attributes.Append(IDAttribute);
                        XmlAttribute BarAttribute = pinBallDoc.CreateAttribute("Bar");
                        BarAttribute.Value = combineItem.Attributes["Bar"].Value.ToString();
                        pointNode.Attributes.Append(BarAttribute);
                        int startTotalPos = int.Parse(combineItem.Attributes["Pos"].Value) * int.Parse(combineItem.Attributes["Bar"].Value);
                        XmlAttribute EndArea = pinBallDoc.CreateAttribute("EndArea");
                        if (combineItem.Attributes["target_track"].Value == "Left1" ||
                            combineItem.Attributes["target_track"].Value == "Left2")
                            EndArea.Value = "1|2";
                        else
                            EndArea.Value = "3|4";
                        pointNode.Attributes.Append(EndArea);
                        XmlAttribute MoveTime = pinBallDoc.CreateAttribute("MoveTime");
                        MoveTime.Value = "3";
                        pointNode.Attributes.Append(MoveTime);
                        XmlAttribute NoteTypeAttribute = pinBallDoc.CreateAttribute("note_type");
                        XmlAttributeCollection combineItemAttribute = combineItem.Attributes as XmlAttributeCollection;
                        if (combineItemAttribute == null) continue;
                        if (combineItemAttribute["note_type"].Value == "long")
                        {
                            int endTotalPos = int.Parse(combineItem.Attributes["EndPos"].Value) * int.Parse(combineItem.Attributes["EndBar"].Value);
                            if ((endTotalPos - startTotalPos) / 2 > 4)
                            {
                                NoteTypeAttribute.Value = "PinballLong";
                                pointNode.Attributes.Append(NoteTypeAttribute);
                                XmlAttribute PosAttribute = pinBallDoc.CreateAttribute("Pos");
                                if ((int.Parse(combineItem.Attributes["Pos"].Value) * 1.5) >= int.Parse(combineItemAttribute["EndPos"].Value))
                                    continue;
                                PosAttribute.Value = int.Parse(combineItem.Attributes["Pos"].Value).ToString();
                                pointNode.Attributes.Append(PosAttribute);
                                XmlAttribute EndBarAttribute = pinBallDoc.CreateAttribute("EndBar");
                                EndBarAttribute.Value = combineItemAttribute["EndBar"].Value.ToString();
                                pointNode.Attributes.Append(EndBarAttribute);
                                XmlAttribute EndPosAttribute = pinBallDoc.CreateAttribute("EndPos");
                                EndPosAttribute.Value = (int.Parse(combineItemAttribute["EndPos"].Value) / 2).ToString();
                                pointNode.Attributes.Append(EndPosAttribute);
                                XmlAttribute SonAttribute = pinBallDoc.CreateAttribute("Son");
                                SonAttribute.Value = "";
                                pointNode.Attributes.Append(SonAttribute);
                            }
                            else
                            {
                                NoteTypeAttribute.Value = "PinballSingle";
                                pointNode.Attributes.Append(NoteTypeAttribute);
                                XmlAttribute PosAttribute = pinBallDoc.CreateAttribute("Pos");
                                PosAttribute.Value = combineItem.Attributes["Pos"].Value.ToString();
                                pointNode.Attributes.Append(PosAttribute);
                                XmlAttribute SonPosAttribute = pinBallDoc.CreateAttribute("Son");
                                SonPosAttribute.Value = "";
                                pointNode.Attributes.Append(SonPosAttribute);
                            }
                        }
                        else if (combineItemAttribute["note_type"].Value == "slip")
                        {
                            XmlAttribute PosAttribute = pinBallDoc.CreateAttribute("Pos");
                            PosAttribute.Value = combineItem.Attributes["Pos"].Value.ToString();
                            pointNode.Attributes.Append(PosAttribute);
                            NoteTypeAttribute.Value = isChangeSlipNote ? "PinballSlip" : "PinballSingle";
                            pointNode.Attributes.Append(NoteTypeAttribute);
                            XmlAttribute SonAttribute = pinBallDoc.CreateAttribute("Son");
                            SonAttribute.Value = "";
                            pointNode.Attributes.Append(SonAttribute);
                            if (EndArea.Value == "1|2")
                            {
                                EndArea.Value = "3|4";
                            }
                            else
                            {
                                EndArea.Value = "1|2";
                            }
                            if (isChangeSlipNote)
                                slipID.Add(pinBallId);

                        }
                        rootNode.AppendChild(pointNode);
                        pinBallId += 1;
                    }

                }


            }

            int c = 0;
            for (int i = 0; i < slipID.Count; i += 1)
            {
                int currentBar = int.Parse(rootNode.ChildNodes[slipID[i] - 1].Attributes["Bar"].Value) + 2;
                int currentId = int.Parse(rootNode.ChildNodes[slipID[i] - 1].Attributes["ID"].Value);

                for (c = currentId; c < rootNode.ChildNodes.Count; c++)
                {
                    int targetBar = int.Parse(rootNode.ChildNodes[c].Attributes["Bar"].Value);
                    int targetPos = int.Parse(rootNode.ChildNodes[c].Attributes["Pos"].Value);
                    int targetID = int.Parse(rootNode.ChildNodes[c].Attributes["ID"].Value);
                    string endArea = rootNode.ChildNodes[c].Attributes["EndArea"].Value;
                    if (rootNode.ChildNodes[c].Attributes["note_type"].Value == "PinballSingle" && targetBar >= currentBar)
                    {
                        rootNode.ChildNodes[slipID[i] - 1].Attributes["Son"].Value = targetID.ToString();
                        XmlElement newSlipNote = pinBallDoc.CreateElement("Note");
                        newSlipNote.SetAttribute("ID", targetID.ToString());
                        newSlipNote.SetAttribute("Bar", targetBar.ToString());
                        newSlipNote.SetAttribute("Pos", targetPos.ToString());
                        newSlipNote.SetAttribute("EndArea", endArea);
                        newSlipNote.SetAttribute("MoveTime", 3.ToString());
                        newSlipNote.SetAttribute("note_type", "PinballSlip");
                        newSlipNote.SetAttribute("Son", "");
                        rootNode.InsertAfter(newSlipNote, rootNode.ChildNodes[c]);
                        rootNode.RemoveChild(rootNode.ChildNodes[c]);
                        break;
                    }
                }
            }

            pinBallDoc.AppendChild(rootNode);
           pinBallDoc.Save(filePath+".idolToPinball");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="oldXPos"></param>
        /// <param name="oldYPos"></param>
        /// <param name="directionType">泡泡生成的方向0水平样式1垂直样式2斜线样式3弯曲样式4随机样式</param>
        /// <param name="direction">0 上 1下 2左 3右 4随机</param>
        private  int[] GetXYPos(int oldXPos, int oldYPos, int directionType, int direction)
        {
            int[] result = { 0, 1 };
            if (directionType == 0)//水平样式
            {
                if (direction == 0) direction = 2;
                else if (direction == 1) direction = 3;
                else direction = 2;
                if (direction == 2)
                {
                    if (oldXPos - 180 > -650)
                        oldXPos -= 180;
                    else oldXPos = _rdXPos.Next(-650, 650);
                }
                else
                {
                    if (oldXPos + 180 <= 650)
                        oldXPos += 180;
                    else oldXPos = _rdXPos.Next(-650, 650);
                }
            }
            else if (directionType == 1)//垂直样式
            {
                if (direction == 2) direction = 0;
                else if (direction == 3) direction = 1;
                else direction = 0;
                if (direction == 0)
                {
                    if (oldYPos + 180 <= 250)
                        oldYPos += 180;
                    else oldYPos = _rdYPos.Next(-250, 250);
                }
                else
                {
                    if (oldYPos - 180 > 250)
                        oldYPos += 180;
                    else oldYPos = _rdYPos.Next(-250, 250);
                }
            }
            else if (directionType == 2)
            {

            }
            else if (directionType == 3)
            {

            }
            else if (directionType == 4)
            {

            }
            result[0] = oldXPos;
            result[1] = oldYPos;
            _currentBubbleGenerateLength += 1;
            return result;
        }
        static int _currentBubbleGenerateLength = 0;
        Random _rdXPos;
        Random _rdYPos;
        public   void PinballToBubble(string FilePath, bool isAllRandom)
        {
            XmlDocument idolDoc = new XmlDocument();
            XmlDocument bubbleDoc = new XmlDocument();
            idolDoc.Load(FilePath);
            XmlElement root = null;
            root = idolDoc.DocumentElement;
            XmlNodeList listNodes = null;
            listNodes = root.SelectNodes("/Level/NoteInfo/Normal");
            XmlNode rootNode = bubbleDoc.CreateElement("Normal");
            XmlAttribute PosNumAttribute = bubbleDoc.CreateAttribute("PosNum");
            PosNumAttribute.Value = "64";
            rootNode.Attributes.Append(PosNumAttribute);
            XmlAttributeCollection collection;
            XmlNode pointNode;
            int bubbleId = 1;
            string track = "";
            int xPos = 0;
            int yPos = 0;
            int directionStyleCode = 0;
            int directionCode = 0;
       
            bool isFirst = true;
            _currentBubbleGenerateLength = 0;
             _rdXPos = new Random();
             _rdYPos = new Random();
            Random rdDirectionStyleCode = new Random();
            Random rdDirectionCode = new Random();
            Random rdTotalBubbleGenerateLength = new Random();
            int totalBubbleGenerateLength = rdTotalBubbleGenerateLength.Next(3, 10);
            foreach (XmlNode node in listNodes[0])
            {
                collection = node.Attributes as XmlAttributeCollection;
                if (collection == null) return;
                pointNode = bubbleDoc.CreateElement("Note");
                XmlAttribute BarAttribute = bubbleDoc.CreateAttribute("Bar");
                BarAttribute.Value = collection["Bar"].Value;
                pointNode.Attributes.Append(BarAttribute);
                XmlAttribute BeatPosAttribute = bubbleDoc.CreateAttribute("BeatPos");
                BeatPosAttribute.Value = collection["Pos"].Value;
                pointNode.Attributes.Append(BeatPosAttribute);
                XmlAttribute TrackAttribute = bubbleDoc.CreateAttribute("Track");
              
                if (collection["EndArea"].Value == "1|2")
                {
                    track = "0";
                }
                else if (collection["EndArea"].Value == "3|4")
                {
                    track = "3";
                }
                else
                {
                    track = "2";
                }
                if (isAllRandom)
                {

                    xPos = _rdXPos.Next(-650, 650);
                    yPos = _rdYPos.Next(-250, 250);

                }
                else
                {
                    if (isFirst)
                    {

                        xPos = _rdXPos.Next(-650, 650);
                        yPos = _rdYPos.Next(-250, 250);
                        _currentBubbleGenerateLength = 1;
                    }
                    else
                    {
                        if (_currentBubbleGenerateLength == 0 || _currentBubbleGenerateLength > totalBubbleGenerateLength)
                        {          
                            directionStyleCode = rdDirectionStyleCode.Next(0, 2);
                            directionCode = rdDirectionCode.Next(0, 2);
                            _currentBubbleGenerateLength = 1;
                            xPos = _rdXPos.Next(-650, 650);
                            yPos = _rdYPos.Next(-250, 250);
                            totalBubbleGenerateLength = rdTotalBubbleGenerateLength.Next(3, 10);
                        }
                        else
                        {
                            int[] result = GetXYPos(xPos, yPos, directionStyleCode, directionCode);
                            xPos = result[0];
                            yPos = result[1];
                        }
                    }
                }
                TrackAttribute.Value = track;
                pointNode.Attributes.Append(TrackAttribute);
                XmlAttribute TypeAttribute = bubbleDoc.CreateAttribute("Type");
                XmlNode ScreenPosNode = bubbleDoc.CreateElement("ScreenPos");
                if (collection["note_type"].Value == "PinballSingle" || collection["note_type"].Value == "PinballSeries")
                {
                    TypeAttribute.Value = "0";
                    pointNode.Attributes.Append(TypeAttribute);
                    XmlAttribute x = bubbleDoc.CreateAttribute("x");
                    x.Value = xPos.ToString();
                    ScreenPosNode.Attributes.Append(x);
                    XmlAttribute y = bubbleDoc.CreateAttribute("y");
                    y.Value = yPos.ToString();
                    ScreenPosNode.Attributes.Append(y);
                    XmlNode FlyTrackNote = bubbleDoc.CreateElement("FlyTrack");
                    XmlAttribute flyTrackAttribute = bubbleDoc.CreateAttribute("name");
                    flyTrackAttribute.Value = "波浪形";
                    FlyTrackNote.Attributes.Append(flyTrackAttribute);
                    XmlAttribute degreeAttribute = bubbleDoc.CreateAttribute("degree");
                    degreeAttribute.Value = "0";
                    FlyTrackNote.Attributes.Append(degreeAttribute);
                    ScreenPosNode.AppendChild(FlyTrackNote);
                    pointNode.AppendChild(ScreenPosNode);
                }
                else if (collection["note_type"].Value == "PinballSlip")
                {
                    TypeAttribute.Value = "2";
                    pointNode.Attributes.Append(TypeAttribute);
                    int currentTotalPos = int.Parse(collection["Bar"].Value) * 32 + int.Parse(collection["Pos"].Value) / 2;
                    currentTotalPos = currentTotalPos + _bubbleSlipLength;
                    XmlAttribute endBarAttribute = bubbleDoc.CreateAttribute("EndBar");
                    endBarAttribute.Value = (currentTotalPos / 32).ToString();
                    pointNode.Attributes.Append(endBarAttribute);

                    XmlAttribute endPosAttribute = bubbleDoc.CreateAttribute("EndPos");
                    endPosAttribute.Value = ((currentTotalPos % 32) * 2).ToString();
                    pointNode.Attributes.Append(endPosAttribute);

                    XmlAttribute IDAttribute = bubbleDoc.CreateAttribute("ID");
                    IDAttribute.Value = bubbleId.ToString();
                    pointNode.Attributes.Append(IDAttribute);

                    XmlNode MoveTrackNote = bubbleDoc.CreateElement("MoveTrack");

                    XmlAttribute MoveTrackNameAttribute = bubbleDoc.CreateAttribute("name");
                    MoveTrackNameAttribute.Value = "长按弧";
                    MoveTrackNote.Attributes.Append(MoveTrackNameAttribute);

                    XmlAttribute MoveTrackDegreeAttribute = bubbleDoc.CreateAttribute("degree");
                    MoveTrackDegreeAttribute.Value = "0";
                    MoveTrackNote.Attributes.Append(MoveTrackDegreeAttribute);

                    XmlNode FlyTrackNote = bubbleDoc.CreateElement("FlyTrack");
                    XmlAttribute FlyTrackNameAttribute = bubbleDoc.CreateAttribute("name");
                    FlyTrackNameAttribute.Value = "长按弧";
                    FlyTrackNote.Attributes.Append(FlyTrackNameAttribute);

                    XmlAttribute FlyTrackDegreeAttribute = bubbleDoc.CreateAttribute("degree");
                    FlyTrackDegreeAttribute.Value = "0";
                    FlyTrackNote.Attributes.Append(FlyTrackDegreeAttribute);

                    XmlNode ScreenPosNote = bubbleDoc.CreateElement("ScreenPos");
                    XmlAttribute x = bubbleDoc.CreateAttribute("x");
                    x.Value = xPos.ToString();
                    ScreenPosNote.Attributes.Append(x);
                    XmlAttribute y = bubbleDoc.CreateAttribute("y");
                    y.Value = yPos.ToString();
                    ScreenPosNote.Attributes.Append(y);
                    pointNode.AppendChild(MoveTrackNote);
                    pointNode.AppendChild(FlyTrackNote);
                    pointNode.AppendChild(ScreenPosNote);
                }
                else
                {
                    TypeAttribute.Value = "1";
                    pointNode.Attributes.Append(TypeAttribute);
                    XmlAttribute endBarAttribute = bubbleDoc.CreateAttribute("EndBar");
                    endBarAttribute.Value = collection["EndBar"].Value;
                    pointNode.Attributes.Append(endBarAttribute);
                    XmlAttribute endPosAttribute = bubbleDoc.CreateAttribute("EndPos");
                    endPosAttribute.Value = collection["EndPos"].Value;
                    pointNode.Attributes.Append(endPosAttribute);
                    XmlAttribute IDAttribute = bubbleDoc.CreateAttribute("ID");
                    IDAttribute.Value = bubbleId.ToString();
                    pointNode.Attributes.Append(IDAttribute);
                    XmlNode MoveTrackNote = bubbleDoc.CreateElement("MoveTrack");
                    XmlAttribute MoveTrackNameAttribute = bubbleDoc.CreateAttribute("name");
                    MoveTrackNameAttribute.Value = "长按弧";
                    MoveTrackNote.Attributes.Append(MoveTrackNameAttribute);
                    XmlAttribute MoveTrackDegreeAttribute = bubbleDoc.CreateAttribute("degree");
                    MoveTrackDegreeAttribute.Value = "0";
                    MoveTrackNote.Attributes.Append(MoveTrackDegreeAttribute);
                    XmlNode FlyTrackNote = bubbleDoc.CreateElement("FlyTrack");
                    XmlAttribute FlyTrackNameAttribute = bubbleDoc.CreateAttribute("name");
                    FlyTrackNameAttribute.Value = "长按弧";
                    FlyTrackNote.Attributes.Append(FlyTrackNameAttribute);
                    XmlAttribute FlyTrackDegreeAttribute = bubbleDoc.CreateAttribute("degree");
                    FlyTrackDegreeAttribute.Value = "0";
                    FlyTrackNote.Attributes.Append(FlyTrackDegreeAttribute);
                    XmlNode ScreenPosNote = bubbleDoc.CreateElement("ScreenPos");
                    XmlAttribute x = bubbleDoc.CreateAttribute("x");
                    x.Value = xPos.ToString();
                    ScreenPosNote.Attributes.Append(x);
                    XmlAttribute y = bubbleDoc.CreateAttribute("y");
                    y.Value = yPos.ToString();
                    ScreenPosNote.Attributes.Append(y);
                    pointNode.AppendChild(MoveTrackNote);
                    pointNode.AppendChild(FlyTrackNote);
                    pointNode.AppendChild(ScreenPosNote);
                }
                isFirst = false;
                bubbleId += 1;
                rootNode.AppendChild(pointNode);
            }
            bubbleDoc.AppendChild(rootNode);
            bubbleDoc.Save(@"E:\pinballToBubble.xml");
        }
        public static void DeleteRepeatNote(string FilePath)
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(FilePath);
            XmlElement root = null;
            root = doc.DocumentElement;
            XmlNodeList listNodes = null;
            listNodes = root.SelectNodes("/Normal/Note");
            IList<int> ids = new List<int>();
            IList<UsedTrack> usedPostions = new List<UsedTrack>();
            IList<IPinball> pinballs = new List<IPinball>();
            IPinballRepository repository = new PinballRepository();
            foreach (XmlNode node in listNodes)
            {
                IPinball pinball= repository.CreatenewPinball();
                pinball.Bar= int.Parse(node.Attributes["Bar"].Value);
                pinball.Pos = int.Parse(node.Attributes["Pos"].Value);
                pinball.EndArea = node.Attributes["EndArea"].Value;
                pinball.EndBar= node.Attributes["EndBar"] ==null? -1: int.Parse(node.Attributes["EndBar"].Value);
                pinball.EndPos = node.Attributes["EndPos"] == null ? -1 : int.Parse(node.Attributes["EndPos"].Value);
                pinball.MoveTime = 3;
                pinball.NoteType = node.Attributes["note_type"].Value;
                pinball.PinballID = int.Parse(node.Attributes["ID"].Value);
                pinball.SonId = node.Attributes["Son"].Value;
                pinballs.Add(pinball);
                UsedTrack usedPostion = new UsedTrack();
                if (node.Attributes["note_type"].Value != "PinballSlip" && node.Attributes["note_type"].Value != "PinballSeries" &&
                    usedPostions.Any(a => a.UsedBar == int.Parse(node.Attributes["Bar"].Value) &&
                 a.UsedPos == int.Parse(node.Attributes["Pos"].Value) && a.UsedTrackName.Contains(node.Attributes["EndArea"].Value)))
                {
                    ids.Add(int.Parse(node.Attributes["ID"].Value));
                }
                if (node.Attributes["note_type"].Value == "PinballSingle")
                {
                    usedPostion.UsedBar = int.Parse(node.Attributes["Bar"].Value);
                    usedPostion.UsedPos = int.Parse(node.Attributes["Pos"].Value);
                    IList<string> usedTrackName = new List<string>();
                    usedTrackName.Add(node.Attributes["EndArea"].Value);
                    usedPostion.UsedTrackName = usedTrackName;
                    usedPostions.Add(usedPostion);
                }
                else if(node.Attributes["note_type"].Value == "PinballLong")
                {
                    int usedPostionNum = (int.Parse(node.Attributes["EndBar"].Value) * 64 + int.Parse(node.Attributes["EndPos"].Value) 
                        - int.Parse(node.Attributes["Bar"].Value) * 64 + int.Parse(node.Attributes["Pos"].Value)) / 2 ;
                    for (int i = 0; i < usedPostionNum; i++)
                    {
                        UsedTrack ut = new UsedTrack();
                        ut.UsedBar = (int.Parse(node.Attributes["Bar"].Value) * 64 + int.Parse(node.Attributes["Pos"].Value) + i * 2) / 64;
                        ut.UsedPos = (int.Parse(node.Attributes["Pos"].Value) + i * 2) % 64;
                        IList<string> usedTrackNames = new List<string>();
                        usedTrackNames.Add(node.Attributes["EndArea"].Value);
                        ut.UsedTrackName = usedTrackNames;
                        usedPostions.Add(ut);
                    }
                }
            } 
            ids = ids.Distinct().ToList();
            pinballs = pinballs.Where(a => !ids.Contains(a.PinballID)).ToList();
            FileStream fs = new FileStream("E:\\DeleteRepeatNotePinball.txt", FileMode.OpenOrCreate);
            StreamWriter sw = new StreamWriter(fs);
            sw.WriteLine("<Normal>");
            foreach (IPinball pinball in pinballs)
            {
                sw.WriteLine(pinball.ToString());
            }
            sw.WriteLine("</Normal>");

            sw.Close();
            MessageBox.Show("共删除"+ ids.Count+"个");

        }

        /// <summary>
        /// 星动转弹珠 - 新版本
        /// 支持多种转换策略和难度调整
        /// 同时生成弹珠谱和弹珠转星动谱两个文件
        /// </summary>
        /// <param name="filePath">星动XML文件路径</param>
        public static void IdolToPinballNew(string filePath)
        {
            try
            {
                // 显示设置窗口
                var settingsWindow = new MyWPF.Layout.Converter.ConversionSettingsWindow();
                settingsWindow.ShowDialog();

                if (settingsWindow.DialogResult)
                {
                    // 1. 执行星动转弹珠转换
                    MyWPF.Layout.Converter.IdolToPinballConverter.ConvertIdolToPinball(filePath, settingsWindow.Settings);

                    // 2. 生成弹珠文件路径
                    string fileName = System.IO.Path.GetFileNameWithoutExtension(filePath);
                    string directory = System.IO.Path.GetDirectoryName(filePath);
                    string pinballFilePath = System.IO.Path.Combine(directory, $"pinball_{fileName}.xml");

                    // 3. 检查弹珠文件是否生成成功
                    if (System.IO.File.Exists(pinballFilePath))
                    {
                        // 4. 执行弹珠转星动转换
                        var pinballToIdolSettings = new MyWPF.Layout.Converter.PinballToIdolConverter.ConversionSettings();
                        MyWPF.Layout.Converter.PinballToIdolConverter.ConvertPinballToIdol(pinballFilePath, pinballToIdolSettings);

                        // 5. 显示成功消息
                        string idolFromPinballPath = System.IO.Path.Combine(directory, $"idol_from_pinball_{fileName}.xml");
                        MessageBox.Show($"转换完成！\n\n生成的文件：\n1. 弹珠谱：{System.IO.Path.GetFileName(pinballFilePath)}\n2. 星动谱：{System.IO.Path.GetFileName(idolFromPinballPath)}",
                                      "转换成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("弹珠谱生成失败，无法继续生成星动谱", "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"转换失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 弹珠转星动
        /// 将弹珠谱转换为星动谱格式
        /// </summary>
        /// <param name="filePath">弹珠XML文件路径</param>
        public static void PinballToIdol(string filePath)
        {
            try
            {
                // 使用默认设置执行转换
                var settings = new MyWPF.Layout.Converter.PinballToIdolConverter.ConversionSettings();
                MyWPF.Layout.Converter.PinballToIdolConverter.ConvertPinballToIdol(filePath, settings);

                MessageBox.Show("弹珠转星动完成！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"弹珠转星动失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
