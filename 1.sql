create table LevelInfo(LevelInfoId int identity(1,1)
 primary key not null,BPM float default 0,
 BeatPerBar int default 4,Beat<PERSON>en int default 16,
 EnterTimeAdjust int default 0,NotePreShow int default 1,
 LevelTime int default 0,BarAmount int default 0,BeginBarLen int default 4,
 IsFourTrack  bit default 1,TrackCount int default 4,LevelPreTime int ,
 Star int ,SongName nvarchar(100)not null, <PERSON> nvarchar(50)not null
 );
 go
 create table PinBallNoteInfo(PinBallNoteInfoID INT PRIMARY KEY IDENTITY(1,1),
 LevelInfoId int ,NoteType nvarchar(50)not null,
  bar int, pos int,EndBar int ,EndPos int, son int ,EndArea nvarchar(10),
  MoveTime int
 )
 alter table PinBallNoteInfo add constraint  foreign_PinballLevelInfoId
 foreign key(LevelInfoId) references LevelInfo(LevelInfoId);
 go
  create table IdolNoteInfo(IdolNoteInfoID INT PRIMARY KEY IDENTITY(1,1),
 LevelInfoId int ,
  Bar int, Pos int,FromTrack  nvarchar(10) ,TargetTrack  nvarchar(10),
   EndTrack  nvarchar(10)
  , EndBar int ,EndPos int,
  NoteType nvarchar(50)not null,CombineNoteNum int 
 )
  alter table IdolNoteInfo add constraint  foreign_IdolLevelInfoId
 foreign key(LevelInfoId) references LevelInfo(LevelInfoId);
 go
   create table BubbleNoteInfo(BubbleNoteInfoID INT PRIMARY KEY IDENTITY(1,1),
 LevelInfoId int ,
  Bar int, BeatPos int,Track  int  , Type int , EndBar int, EndPod int ,ID INT,
  MoveTrackNameId int ,MoveTrackDegree int,ScreenPosX  float,ScreenPosY float,
  FlyTrackNameId int ,FlyTrackDegree int)
 
   alter table BubbleNoteInfo add constraint  foreign_BubbleLevelInfoId
 foreign key(LevelInfoId) references LevelInfo(LevelInfoId);
 go
 
