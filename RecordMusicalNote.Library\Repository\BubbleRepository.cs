﻿using RecordMusicalNote.IRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote.Library.Repository
{
    public class BubbleRepository : IBubbleRepository
    {
        public ILevelInfo CreateNewLevelInfo()
        {
            return new LevelInfo();
        }

        public IList<IBubble> GetInfo(int levelInfoId)
        {
            throw new NotImplementedException();
        }

        public IList<ILevelInfo> GetInfo(int cmbIndex, string content)
        {
            return new IdolRepository().GetInfo(cmbIndex, content);
        }

        public bool SaveInfo(IList<IBubble> modes, ILevelInfo levelInfo,out string msg)
        {
            throw new NotImplementedException();
        }
    }
}
