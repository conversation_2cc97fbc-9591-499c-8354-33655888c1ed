﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecordMusicalNote
{
   public interface IRepository<T>
    {
        bool SaveInfo(IList<T> modes, ILevelInfo levelInfo,out string msg);
        IList<ILevelInfo> GetInfo(int cmbIndex, string content);
        ILevelInfo CreateNewLevelInfo();
        IList<T> GetInfo(int levelInfoId);
    }
}
