/*
 * Copyright (c) 2007-2008 <PERSON><PERSON><PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License version 2.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;

namespace MidiSheetMusic {

/*
 * The Staff is used to draw a single Staff in the SheetMusic Control.
 * A Staff needs to draw
 * - The Clef
 * - The key signature
 * - A list of MusicSymbols
 * - The horizontal lines
 * - The left and right sides
 *
 * The height of the Staff is determined by the number of pixels each
 * MusicSymbol extends above and below the staff.
 *
 * The vertical lines (left and right sides) of the staff are joined,
 * with one exception.  The last track is not joined with the first
 * track.
 */

public class Staff {
    List<MusicSymbol> symbols;  /* The music symbols in this staff */
    int ytop;                   /* The y pixel of the top of the staff */
    Pen pen;                    /* The pen to use for drawing */
    ClefSymbol clefsym;         /* The left-side Clef symbol */
    AccidSymbol[] keys;         /* The key signature symbols */
    int height;                 /* The height of the staff in pixels */
    int tracknum;               /* The track this staff represents */
    int totaltracks;            /* The total number of tracks */

    /* Return the height of the staff */
    public int Height {
        get { return height; }
    }

    /* Create a new staff with the given list of music symbols,
     * and the given key signature.  The clef is determine by
     * the clef of the first chord symbol.
     */
    public Staff(List<MusicSymbol> symbols, KeySignature key, 
                 int tracknum, int totaltracks)  {

        this.tracknum = tracknum;
        this.totaltracks = totaltracks;
        Clef clef = FindClef(symbols);

        clefsym = new ClefSymbol(clef, 0, false);
        keys = key.GetSymbols(clef);
        this.symbols = symbols;
        pen = new Pen(Color.Black, SheetMusic.LineWidth);
        CalculateHeight();
    }

    /* Find the initial clef to use for this staff.  Use the clef of
     * the first ChordSymbol.
     */
    Clef FindClef(List<MusicSymbol> list) {
        foreach (MusicSymbol m in list) {
            if (m is ChordSymbol) {
                ChordSymbol c = (ChordSymbol) m;
                return c.Clef;
            }
        }
        return Clef.Treble;
    }

    /* Calculate the height of this staff.  Each MusicSymbol contains the
     * number of pixels it needs above and below the staff.  Get the maximum
     * values above and below the staff.
     */
    void CalculateHeight() {
        int above = 0;
        int below = 0;

        foreach (MusicSymbol s in symbols) {
            above = Math.Max(above, s.AboveStaff);
            below = Math.Max(below, s.BelowStaff);
        }
        above = Math.Max(above, clefsym.AboveStaff);
        below = Math.Max(below, clefsym.BelowStaff);
        ytop = above + SheetMusic.LeftMargin;
        height = SheetMusic.NoteHeight*4 + ytop + below;

        /* Add some extra vertical space between the last track
         * and first track.
         */
        if (tracknum == totaltracks-1)
            height += SheetMusic.LeftMargin;
    }

    /* Draw the five horizontal lines of the staff */
    void DrawHorizLines(Graphics g) {
        int line = 1;
        int y = ytop - SheetMusic.LineWidth;
        pen.Width = SheetMusic.LineWidth;
        for (line = 1; line <= 5; line++) {
            g.DrawLine(pen, SheetMusic.LeftMargin, y, 
                            SheetMusic.PageWidth-1, y);
            y += SheetMusic.LineWidth + SheetMusic.LineSpace;
        }
    }

    /* Draw the vertical lines at the far left and far right sides. */
    void DrawEndLines(Graphics g) {
        pen.Width = SheetMusic.LineWidth;

        /* Draw the vertical lines from 0 to the height of this staff,
         * including the space above and below the staff, with two exceptions:
         * - If this is the first track, don't start above the staff.
         *   Start exactly at the top of the staff (ytop - LineWidth)
         * - If this is the last track, don't end below the staff.
         *   End exactly at the bottom of the staff.
         */
        int ystart, yend;
        if (tracknum == 0)
            ystart = ytop - SheetMusic.LineWidth;
        else
            ystart = 0;

        if (tracknum == (totaltracks-1))
            yend = ytop + 4 * SheetMusic.NoteHeight;
        else
            yend = height;

        g.DrawLine(pen, SheetMusic.LeftMargin, ystart,
                        SheetMusic.LeftMargin, yend);

        g.DrawLine(pen, SheetMusic.PageWidth-1, ystart,
                        SheetMusic.PageWidth-1, yend);

    }

    /* Draw this staff */
    public void Draw(Graphics g) {
        int xpos = SheetMusic.LeftMargin + 4;

        /* Draw the left side Clef symbol */
        g.TranslateTransform(xpos, 0);
        clefsym.Draw(g, pen, ytop);
        g.TranslateTransform(-xpos, 0);
        xpos += clefsym.Width;

        /* Draw the key signature */
        foreach (AccidSymbol a in keys) {
            g.TranslateTransform(xpos, 0);
            a.Draw(g, pen, ytop);
            g.TranslateTransform(-xpos, 0);
            xpos += a.Width;
        }
       
        /* Draw the actual notes, rests, bars.  Draw the symbols one 
         * after another, using the symbol width to determine the
         * x position of the next symbol.
         */
        foreach (MusicSymbol s in symbols) {
            g.TranslateTransform(xpos, 0);
            s.Draw(g, pen, ytop);
            g.TranslateTransform(-xpos, 0);
            xpos += s.Width;
        }

        DrawHorizLines(g);
        DrawEndLines(g);
    }
}


}
