"""
基础使用示例

演示如何使用AI谱面生成器的基本功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.chart_generator import AIChartGenerator
from src.utils.logger_setup import setup_colored_logging
import logging

# 设置彩色日志
setup_colored_logging(level="INFO")
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    
    print("🎵 AI音游写谱助手 - 基础使用示例")
    print("=" * 50)
    
    # 1. 初始化生成器
    print("\n📦 初始化AI谱面生成器...")
    generator = AIChartGenerator()
    
    # 2. 显示支持的格式
    print("\n📋 支持的格式:")
    formats = generator.get_supported_formats()
    for fmt in formats:
        info = generator.get_format_info(fmt)
        if info:
            print(f"  • {fmt}: {info.get('description', 'N/A')}")
    
    # 3. 显示可用的生成风格
    print("\n🎨 可用的生成风格:")
    styles = generator.get_generation_styles()
    for style in styles:
        description = generator.get_style_description(style)
        print(f"  • {style}: {description}")
    
    # 4. 示例：分析MIDI文件（如果存在）
    sample_midi = project_root / "data" / "sample.mid"
    if sample_midi.exists():
        print(f"\n🔍 分析示例MIDI文件: {sample_midi}")
        
        analysis = generator.analyze_midi(str(sample_midi))
        if analysis:
            basic_info = analysis.get('basic_info', {})
            print(f"  时长: {basic_info.get('duration', 0):.1f} 秒")
            print(f"  BPM: {basic_info.get('initial_bpm', 0):.1f}")
            print(f"  总音符数: {basic_info.get('total_notes', 0)}")
            
            # 预测难度
            difficulty_result = generator.predict_difficulty(str(sample_midi))
            print(f"  预测难度: {difficulty_result.get('difficulty', 'N/A'):.1f}")
        else:
            print("  分析失败")
    else:
        print(f"\n⚠️  示例MIDI文件不存在: {sample_midi}")
        print("  请将MIDI文件放置在 data/sample.mid 来测试分析功能")
    
    # 5. 示例：生成谱面（如果有MIDI文件）
    if sample_midi.exists():
        print(f"\n🎼 生成示例谱面...")
        
        output_dir = project_root / "output"
        output_dir.mkdir(exist_ok=True)
        
        # 生成不同格式的谱面
        formats_to_test = ["malody"]  # 只测试已实现的格式
        
        for fmt in formats_to_test:
            output_file = output_dir / f"sample_{fmt}.{fmt}"
            
            print(f"  正在生成 {fmt} 格式...")
            
            success = generator.generate_from_midi(
                midi_path=str(sample_midi),
                output_path=str(output_file),
                format_name=fmt,
                difficulty=5,
                style="balanced",
                title="示例歌曲",
                artist="AI助手"
            )
            
            if success:
                print(f"  ✅ 成功生成: {output_file}")
            else:
                print(f"  ❌ 生成失败: {fmt}")
    
    # 6. 示例：创建和操作谱面数据
    print(f"\n📊 谱面数据操作示例...")
    
    from src.chart_generation.chart_data import ChartData, ChartMetadata, NoteInfo
    
    # 创建谱面元数据
    metadata = ChartMetadata(
        title="测试谱面",
        artist="测试艺术家",
        difficulty=6,
        bpm=128.0,
        track_count=4
    )
    
    # 创建谱面数据
    chart = ChartData(metadata)
    
    # 添加一些示例音符
    notes = [
        NoteInfo(time=0.0, track=0, note_type=1),      # 短音符
        NoteInfo(time=0.5, track=1, note_type=1),      # 短音符
        NoteInfo(time=1.0, track=2, note_type=2, duration=1.0),  # 长音符
        NoteInfo(time=2.0, track=3, note_type=1),      # 短音符
    ]
    
    chart.add_notes(notes)
    
    # 显示统计信息
    stats = chart.get_statistics()
    print(f"  总音符数: {stats['total_notes']}")
    print(f"  轨道分布: {stats['track_distribution']}")
    print(f"  音符类型分布: {stats['note_type_distribution']}")
    
    # 保存为JSON
    json_output = project_root / "output" / "test_chart.json"
    chart.save_json(str(json_output))
    print(f"  已保存JSON格式: {json_output}")
    
    # 7. 示例：格式转换（如果有输入文件）
    if json_output.exists():
        print(f"\n🔄 格式转换示例...")
        
        malody_output = project_root / "output" / "converted_chart.mc"
        
        success = generator.convert_chart(
            input_path=str(json_output),
            output_path=str(malody_output),
            target_format="malody"
        )
        
        if success:
            print(f"  ✅ 成功转换为Malody格式: {malody_output}")
        else:
            print(f"  ❌ 格式转换失败")
    
    print(f"\n🎉 示例演示完成！")
    print(f"输出文件保存在: {project_root / 'output'}")


def create_sample_data():
    """创建示例数据"""
    
    print("\n📁 创建示例数据...")
    
    # 创建必要的目录
    directories = [
        "data",
        "data/midi",
        "data/charts", 
        "output",
        "logs",
        "models"
    ]
    
    project_root = Path(__file__).parent.parent
    
    for dir_name in directories:
        dir_path = project_root / dir_name
        dir_path.mkdir(exist_ok=True)
        print(f"  创建目录: {dir_path}")
    
    # 创建示例配置文件
    config_content = """# 用户配置示例
model:
  device: "cpu"
  
chart_generation:
  default_difficulty: 6
  default_style: "balanced"
  
logging:
  level: "INFO"
  console_output: true
"""
    
    user_config_path = project_root / "config" / "user_config.yaml"
    user_config_path.parent.mkdir(exist_ok=True)
    
    with open(user_config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"  创建用户配置: {user_config_path}")
    
    print("  ✅ 示例数据创建完成")


if __name__ == "__main__":
    try:
        # 创建示例数据
        create_sample_data()
        
        # 运行主示例
        main()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断")
    except Exception as e:
        logger.error(f"示例运行失败: {e}")
        print(f"\n❌ 错误: {e}")
        sys.exit(1)
