# 音频分析功能使用说明

## 功能概述

音符编辑器现在集成了强大的音频分析功能，可以帮助你：

- 📊 **波形分析**：可视化音频波形，直观了解音乐结构
- 🎵 **节拍检测**：自动检测音频中的节拍点，估算BPM
- ⚡ **能量分析**：分析音频能量变化，识别音乐高潮部分
- 🎯 **智能建议**：基于分析结果生成音符建议，提高制谱效率

## 使用步骤

### 1. 打开音频分析面板

1. 在音符编辑器中点击 `🎵 音频分析` 按钮
2. 右侧会显示音频分析面板

### 2. 加载音频文件

1. 点击 `📁 加载音频` 按钮
2. 选择音频文件（推荐使用WAV格式，也支持MP3）
3. 系统会自动加载并显示音频信息

### 3. 进行音频分析

1. 点击 `🔍 分析` 按钮开始分析
2. 系统会并行执行：
   - 节拍检测
   - 能量分析
   - 波形处理

### 4. 查看分析结果

分析完成后，你可以：

- **波形显示**：查看音频波形图，支持缩放和拖拽
- **节拍点**：绿色竖线标记检测到的节拍位置
- **能量曲线**：红色曲线显示音频能量变化
- **分析数据**：在右侧面板查看详细的分析结果

### 5. 生成建议音符

1. 点击 `生成建议音符` 按钮
2. 系统会基于节拍点和能量峰值生成音符建议
3. 建议音符会显示在"建议音符"标签页中

### 6. 导入音符到编辑器

1. 回到音符编辑区域
2. 点击 `📥 导入建议` 按钮
3. 确认导入后，建议音符会添加到音符列表中

## 支持的音频格式

### 推荐格式
- **WAV** (*.wav) - 完全支持，推荐使用
- **AIFF** (*.aiff) - 完全支持

### 其他格式
- **MP3** (*.mp3) - 需要系统支持，可能需要额外编解码器
- **M4A** (*.m4a) - 需要系统支持

### 格式转换建议

如果遇到格式不支持的问题，建议：

1. 使用免费工具如 **Audacity** 将MP3转换为WAV
2. 使用在线转换工具
3. 使用 **FFmpeg** 命令行工具

## 分析参数说明

### 波形分析
- **采样率**：音频的采样频率，影响分析精度
- **声道数**：单声道或立体声
- **时长**：音频总时长

### 节拍检测
- **BPM估算**：基于节拍间隔计算的每分钟节拍数
- **置信度**：节拍检测的可靠性（0-100%）
- **节拍点**：检测到的节拍时间位置

### 能量分析
- **能量级别**：每100ms窗口的音频能量
- **能量峰值**：能量的局部最大值点
- **频谱质心**：音色特征指标
- **过零率**：音调特征指标

## 音符生成策略

### 基于节拍点
- 在检测到的节拍位置生成短音符
- 根据4/4拍计算小节和位置
- 置信度高的节拍点优先生成

### 基于能量峰值
- 在能量峰值位置生成额外音符
- 高能量区域倾向于生成在右侧轨道
- 低能量区域倾向于生成在左侧轨道

### 轨道分配规则
- **Right2**：能量 > 80%
- **Right1**：能量 60-80%
- **Left1**：能量 40-60%
- **Left2**：能量 < 40%

## 使用技巧

### 1. 音频预处理
- 使用高质量的音频文件（44.1kHz或更高采样率）
- 确保音频没有过度压缩或失真
- 如果可能，使用单声道文件以提高分析准确性

### 2. 分析优化
- 对于复杂音乐，可能需要手动调整建议音符
- 结合音乐理论知识来验证BPM估算
- 使用波形图来识别音乐的段落结构

### 3. 制谱工作流
1. 先进行音频分析获得基础框架
2. 导入建议音符作为起点
3. 根据音乐内容手动调整和完善
4. 使用AI辅助功能进一步优化

## 导出功能

### 分析结果导出
- 支持导出为JSON格式
- 包含完整的分析数据和建议音符
- 可用于备份或与他人分享

### 导出内容
```json
{
  "ExportTime": "2025-01-30T...",
  "AudioFile": "path/to/audio.wav",
  "SuggestedNotes": [...],
  "CurrentNotes": [...]
}
```

## 故障排除

### 常见问题

**Q: 无法加载MP3文件**
A: 尝试将MP3转换为WAV格式，或安装必要的音频编解码器

**Q: 节拍检测不准确**
A: 这是正常现象，算法基于简单的能量检测。复杂音乐可能需要手动调整

**Q: 分析速度很慢**
A: 大文件分析需要时间，建议使用较短的音频片段进行测试

**Q: 建议音符太多或太少**
A: 可以调整能量阈值，或手动筛选建议音符

### 性能优化
- 使用较短的音频文件进行初步测试
- 关闭不需要的显示选项以提高性能
- 定期清理临时数据

## 未来改进计划

- [ ] 支持更多音频格式
- [ ] 改进节拍检测算法
- [ ] 添加音调检测功能
- [ ] 支持实时音频分析
- [ ] 集成机器学习模型
- [ ] 添加音频播放功能

## 技术细节

### 算法说明
- **节拍检测**：基于能量窗口的峰值检测
- **BPM计算**：节拍间隔的统计分析
- **能量分析**：RMS能量计算
- **波形显示**：峰值数据压缩显示

### 依赖库
- **NAudio**：音频文件读取和处理
- **System.Text.Json**：数据序列化
- **WPF**：用户界面

---

*如有问题或建议，请联系开发团队*