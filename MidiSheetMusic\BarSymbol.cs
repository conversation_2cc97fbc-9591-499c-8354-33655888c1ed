/*
 * Copyright (c) 2007-2008 <PERSON><PERSON><PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License version 2.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace MidiSheetMusic {


/* 
 * The BarSymbol represents the vertical bars which delimit measures.
 * The starttime of the symbol should be the beginning of the new
 * measure.
 */
public class BarSymbol : MusicSymbol {
    int starttime;
    int width;

    public BarSymbol(int starttime) {
        this.starttime = starttime;
        width = MinWidth;
    }

    public override int StartTime {
        get { return starttime; }
    }

    public override int MinWidth { 
        get { return SheetMusic.NoteWidth; }
    }

    public override int Width {
        get { return width; }
        set { width = value; }
    }

    public override int AboveStaff { 
        get { return 0; } 
    }

    public override int BelowStaff { 
        get { return 0; }
    }

    public override 
    void Draw(Graphics g, Pen pen, int ytop) {
        int y = ytop;
        int yend = y + SheetMusic.LineSpace*4 + SheetMusic.LineWidth*4;
        pen.Width = SheetMusic.LineWidth;
        g.DrawLine(pen, SheetMusic.NoteWidth/2, y, 
                        SheetMusic.NoteWidth/2, yend);

    }

    public override string ToString() {
        return string.Format("BarSymbol starttime={0} width={1}", 
                             starttime, width);
    }
}


}

