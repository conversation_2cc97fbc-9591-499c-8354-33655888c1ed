# 音乐游戏谱面生成器使用示例

## 快速开始

### 1. 准备MIDI文件

首先，您需要准备一个MIDI文件。可以是：
- 从网上下载的MIDI文件
- 使用音乐软件制作的MIDI文件
- 从其他格式转换的MIDI文件

**推荐的MIDI文件特征：**
- 文件大小：50KB - 2MB
- 时长：2-5分钟
- 轨道数：2-8个轨道
- 包含明显的主旋律轨道

### 2. 打开谱面生成器

1. 启动音乐记录工具
2. 在顶部菜单栏点击"转换功能"
3. 选择"MIDI谱面生成器"

### 3. 基础操作流程

#### 步骤1：选择文件
```
点击"浏览" → 选择MIDI文件 → 确认
```

#### 步骤2：设置参数
```
游戏模式：星动模式（推荐新手）
难度等级：5（中等难度）
轨道数量：4轨道
音符密度：70%
```

#### 步骤3：测试和预览
```
点击"测试解析" → 查看MIDI文件分析结果
点击"预览谱面" → 查看基本信息
```

#### 步骤4：生成谱面
```
点击"生成谱面" → 等待生成完成 → 查看结果统计
```

#### 步骤5：保存文件
```
点击"保存谱面" → 选择保存位置 → 确认保存
```

## 详细参数说明

### 游戏模式选择

**星动模式**
- 适合：传统音乐游戏爱好者
- 特点：点击、长按、滑动音符
- 推荐难度：3-7级
- 推荐密度：50-80%

**弹珠模式**
- 适合：休闲游戏爱好者
- 特点：弹珠发射和碰撞
- 推荐难度：2-6级
- 推荐密度：40-70%

**泡泡模式**
- 适合：消除游戏爱好者
- 特点：颜色匹配和消除
- 推荐难度：2-5级
- 推荐密度：30-60%

### 难度等级指南

| 难度 | 描述 | 音符密度 | 适合人群 |
|------|------|----------|----------|
| 1-2  | 入门 | 10-20%   | 新手玩家 |
| 3-4  | 简单 | 30-40%   | 休闲玩家 |
| 5-6  | 中等 | 50-70%   | 一般玩家 |
| 7-8  | 困难 | 70-90%   | 高手玩家 |
| 9-10 | 专家 | 90-100%  | 专业玩家 |

### 轨道数量建议

**4轨道**
- 最常见的配置
- 适合大部分音乐类型
- 平衡性好，易于上手

**6轨道**
- 适合复杂的音乐
- 提供更多变化
- 需要更高的操作技巧

**8轨道**
- 适合非常复杂的音乐
- 专业级别的挑战
- 需要专门的练习

## 实际案例

### 案例1：流行歌曲谱面

**原始MIDI：** 某流行歌曲.mid (120 BPM, 3分钟)

**设置参数：**
```
游戏模式：星动模式
难度等级：5
轨道数量：4
音符密度：70%
```

**生成结果：**
```
总音符数：245个
音符分布：点击(180) 长条(45) 箭头(20)
平均密度：每秒1.4个音符
```

**适合人群：** 中等水平玩家

### 案例2：古典音乐谱面

**原始MIDI：** 某古典乐曲.mid (90 BPM, 4分钟)

**设置参数：**
```
游戏模式：星动模式
难度等级：7
轨道数量：6
音符密度：85%
```

**生成结果：**
```
总音符数：420个
音符分布：点击(280) 长条(100) 箭头(40)
平均密度：每秒1.8个音符
```

**适合人群：** 高水平玩家

### 案例3：电子音乐谱面

**原始MIDI：** 某电子舞曲.mid (128 BPM, 2.5分钟)

**设置参数：**
```
游戏模式：弹珠模式
难度等级：6
轨道数量：4
音符密度：75%
```

**生成结果：**
```
总音符数：310个
音符分布：普通弹珠(250) 特殊弹珠(60)
平均密度：每秒2.1个音符
```

**适合人群：** 中高水平玩家

## 常见问题解决

### 问题1：音符太少
**现象：** 生成的音符数量很少，游戏体验不佳
**解决方案：**
1. 提高难度等级到7-8
2. 增加音符密度到80-90%
3. 选择音符更丰富的MIDI文件
4. 关闭"过滤短音符"选项

### 问题2：音符太密集
**现象：** 音符过多，难度过高
**解决方案：**
1. 降低难度等级到3-4
2. 减少音符密度到40-60%
3. 开启"过滤短音符"选项
4. 选择较慢节奏的MIDI文件

### 问题3：音符分布不均
**现象：** 某些时段音符很多，某些时段很少
**解决方案：**
1. 这通常是原MIDI文件的特征
2. 可以尝试其他MIDI文件
3. 或者接受这种自然的音乐节奏变化

### 问题4：生成失败
**现象：** 程序报错，无法生成谱面
**解决方案：**
1. 检查MIDI文件是否损坏
2. 尝试其他MIDI文件
3. 确保文件路径没有中文字符
4. 重启程序再试

## 高级技巧

### 技巧1：多版本生成
为同一首歌生成不同难度的版本：
```
简单版：难度3, 密度50%
普通版：难度5, 密度70%
困难版：难度7, 密度90%
```

### 技巧2：混合模式
为不同部分使用不同游戏模式：
```
主歌部分：星动模式
副歌部分：弹珠模式
间奏部分：泡泡模式
```

### 技巧3：精细调整
生成后手动调整：
```
1. 导出XML文件
2. 用文本编辑器打开
3. 手动调整音符位置和类型
4. 重新导入测试
```

### 技巧4：批量生成
为多个MIDI文件批量生成谱面：
```
1. 准备多个MIDI文件
2. 使用相同参数设置
3. 逐个生成并保存
4. 统一管理生成的谱面
```

## 输出文件说明

### XML文件结构
```xml
<Level>
  <LevelInfo>     <!-- 关卡基本信息 -->
  <NoteInfo>      <!-- 音符信息 -->
    <Normal>      <!-- 普通音符 -->
      <Note/>     <!-- 单个音符 -->
    </Normal>
  </NoteInfo>
</Level>
```

### 文件命名规则
```
格式：[歌曲名]_[模式]_[难度]_谱面.xml
示例：
- 示例歌曲_星动_5_谱面.xml
- 测试音乐_弹珠_3_谱面.xml
- 经典乐曲_泡泡_7_谱面.xml
```

### 文件管理建议
```
项目文件夹/
├── MIDI源文件/
│   ├── 流行音乐/
│   ├── 古典音乐/
│   └── 电子音乐/
├── 生成谱面/
│   ├── 星动模式/
│   ├── 弹珠模式/
│   └── 泡泡模式/
└── 测试报告/
```

这样的组织方式便于管理和查找文件。
