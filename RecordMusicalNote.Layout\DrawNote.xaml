﻿<Window x:Class="RecordMusicalNote.DrawNote"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:RecordMusicalNote"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="制谱表格" Height="700" Width="1000" MinHeight="600" MinWidth="800"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <materialDesign:DialogHost Identifier="DrawNoteDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 顶部标题栏 -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
                <DockPanel>
                    <materialDesign:PackIcon Kind="MusicNoteEighth" Width="24" Height="24" VerticalAlignment="Center" DockPanel.Dock="Left"/>
                    <TextBlock Text="制谱表格" FontSize="20" FontWeight="Medium" VerticalAlignment="Center" Margin="16,0,0,0"/>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- 主要内容区域 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 制谱表格卡片 -->
                    <materialDesign:Card Grid.Row="0" Style="{StaticResource MaterialCard}" Margin="0,0,0,16">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- 表格标题 -->
                            <TextBlock Grid.Row="0" Text="音符编辑区域"
                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                     Margin="0,0,0,16"/>

                            <!-- 数据表格 -->
                            <DataGrid Grid.Row="1" Name="_test"
                                    AutoGenerateColumns="False"
                                    HeadersVisibility="Column"
                                    IsReadOnly="True"
                                    ScrollViewer.CanContentScroll="True"
                                    ScrollViewer.IsDeferredScrollingEnabled="True"
                                    materialDesign:DataGridAssist.CellPadding="8"
                                    materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                                    CanUserAddRows="False"
                                    CanUserDeleteRows="False"
                                    CanUserReorderColumns="False"
                                    CanUserResizeRows="False"
                                    SelectionMode="Single"
                                    GridLinesVisibility="Horizontal"
                                    HorizontalGridLinesBrush="{DynamicResource MaterialDesignDivider}"
                                    MinHeight="300">

                                <DataGrid.RowStyle>
                                    <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                                        <Style.Triggers>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                                            </Trigger>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="{DynamicResource MaterialDesignDivider}"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.RowStyle>

                                <DataGrid.Columns>
                                    <!-- 列定义将在代码中动态添加 -->
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </materialDesign:Card>

                    <!-- 控制面板 -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 参数设置卡片 -->
                        <materialDesign:Card Grid.Column="0" Style="{StaticResource MaterialCard}" Margin="0,0,8,0">
                            <StackPanel>
                                <TextBlock Text="参数设置" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBox Grid.Row="0" Grid.Column="0" Name="txtTotalTime"
                                           materialDesign:HintAssist.Hint="总时间(秒)"
                                           Style="{StaticResource MaterialTextBox}"/>

                                    <TextBox Grid.Row="0" Grid.Column="1" Name="txtBPM"
                                           materialDesign:HintAssist.Hint="BPM"
                                           Style="{StaticResource MaterialTextBox}"/>

                                    <TextBox Grid.Row="1" Grid.Column="0" Name="txtKeyCount"
                                           materialDesign:HintAssist.Hint="键位数"
                                           Style="{StaticResource MaterialTextBox}"/>
                                </Grid>

                                <!-- 音符类型选择 -->
                                <TextBlock Text="音符类型" Style="{StaticResource MaterialDesignSubtitle2TextBlock}" Margin="0,16,0,8"/>
                                <StackPanel Orientation="Horizontal">
                                    <RadioButton x:Name="_radSingle" Content="点击" GroupName="type"
                                               Style="{StaticResource MaterialDesignRadioButton}" Margin="0,0,16,0"/>
                                    <RadioButton x:Name="_radLong" Content="长条" GroupName="type"
                                               Style="{StaticResource MaterialDesignRadioButton}" Margin="0,0,16,0"/>
                                    <RadioButton x:Name="_radSlip" Content="滑动" GroupName="type"
                                               Style="{StaticResource MaterialDesignRadioButton}"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 操作控制卡片 -->
                        <materialDesign:Card Grid.Column="1" Style="{StaticResource MaterialCard}" Margin="8,0,0,0">
                            <StackPanel>
                                <TextBlock Text="操作控制" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                                <!-- 文件操作 -->
                                <TextBlock Text="文件操作" Style="{StaticResource MaterialDesignSubtitle2TextBlock}" Margin="0,0,0,8"/>
                                <UniformGrid Columns="2" Rows="2">
                                    <Button Name="btnChooseMusic" Content="选择音乐" Click="btnChooseMusic_Click"
                                          Style="{StaticResource MaterialRaisedButton}">
                                        <Button.ToolTip>
                                            <ToolTip Content="选择音频文件"/>
                                        </Button.ToolTip>
                                    </Button>

                                    <Button Name="btnGenerate" Content="生成表格" Click="btnGenerate_Click"
                                          Style="{StaticResource MaterialRaisedButton}">
                                        <Button.ToolTip>
                                            <ToolTip Content="根据参数生成制谱表格"/>
                                        </Button.ToolTip>
                                    </Button>
                                </UniformGrid>

                                <!-- 播放控制 -->
                                <TextBlock Text="播放控制" Style="{StaticResource MaterialDesignSubtitle2TextBlock}" Margin="0,16,0,8"/>
                                <UniformGrid Columns="2" Rows="1">
                                    <Button Name="btnPlay" Content="播放" Click="btnPlay_Click"
                                          Style="{StaticResource MaterialRaisedButton}">
                                        <Button.ToolTip>
                                            <ToolTip Content="播放音乐"/>
                                        </Button.ToolTip>
                                    </Button>

                                    <Button Name="btnPause" Content="暂停" Click="btnPause_Click"
                                          Style="{StaticResource MaterialRaisedButton}">
                                        <Button.ToolTip>
                                            <ToolTip Content="暂停播放"/>
                                        </Button.ToolTip>
                                    </Button>
                                </UniformGrid>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>
                </Grid>
            </ScrollViewer>

            <!-- 隐藏的媒体元素 -->
            <MediaElement Name="mediaelement" Visibility="Hidden" LoadedBehavior="Manual" MediaOpened="mediaelement_MediaOpened"/>
        </Grid>
    </materialDesign:DialogHost>
</Window>
