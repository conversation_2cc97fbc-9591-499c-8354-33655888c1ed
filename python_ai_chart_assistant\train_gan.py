#!/usr/bin/env python3
"""
GAN训练主脚本

使用对抗网络训练从MP3音频生成音游谱面的模型
"""

import argparse
import logging
import torch
from torch.utils.data import DataLoader, random_split
from pathlib import Path
import yaml

from src.models.audio_chart_gan import AudioChartGAN
from src.training.gan_trainer import GANTrainer
from src.training.audio_chart_dataset import AudioChartDataset
from src.utils.logger_setup import setup_logging


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练Audio-to-Chart GAN模型')
    
    # 数据参数
    parser.add_argument('--data-dir', type=str, required=True,
                       help='数据目录路径')
    parser.add_argument('--track-count', type=int, default=4,
                       help='轨道数量')
    parser.add_argument('--max-length', type=int, default=2000,
                       help='最大序列长度')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=100,
                       help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=16,
                       help='批次大小')
    parser.add_argument('--lr-g', type=float, default=0.0002,
                       help='生成器学习率')
    parser.add_argument('--lr-d', type=float, default=0.0002,
                       help='判别器学习率')
    parser.add_argument('--validation-split', type=float, default=0.2,
                       help='验证集比例')
    
    # 设备参数
    parser.add_argument('--device', type=str, default='auto',
                       choices=['auto', 'cpu', 'cuda'],
                       help='计算设备')
    parser.add_argument('--num-workers', type=int, default=4,
                       help='数据加载器工作线程数')
    
    # 保存参数
    parser.add_argument('--save-dir', type=str, default='models',
                       help='模型保存目录')
    parser.add_argument('--save-interval', type=int, default=10,
                       help='模型保存间隔')
    parser.add_argument('--resume', type=str, default=None,
                       help='恢复训练的模型路径')
    
    # 其他参数
    parser.add_argument('--config', type=str, default=None,
                       help='配置文件路径')
    parser.add_argument('--log-level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    return parser.parse_args()


def load_config(config_path: str) -> dict:
    """加载配置文件"""
    if not config_path or not Path(config_path).exists():
        return {}
    
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def setup_device(device_arg: str) -> str:
    """设置计算设备"""
    if device_arg == 'auto':
        if torch.cuda.is_available():
            device = 'cuda'
            print(f"✅ 使用GPU: {torch.cuda.get_device_name(0)}")
        else:
            device = 'cpu'
            print("⚠️ 使用CPU训练（建议使用GPU加速）")
    else:
        device = device_arg
        if device == 'cuda' and not torch.cuda.is_available():
            print("❌ CUDA不可用，切换到CPU")
            device = 'cpu'
    
    return device


def create_data_loaders(args, config):
    """创建数据加载器"""
    print("📊 准备数据集...")
    
    # 创建数据集
    dataset = AudioChartDataset(
        data_dir=args.data_dir,
        max_length=args.max_length,
        track_count=args.track_count,
        augment=True
    )
    
    if len(dataset) == 0:
        raise ValueError(f"数据集为空，请检查数据目录: {args.data_dir}")
    
    print(f"✅ 加载了 {len(dataset)} 个音频-谱面数据对")
    
    # 划分训练集和验证集
    val_size = int(len(dataset) * args.validation_split)
    train_size = len(dataset) - val_size
    
    train_dataset, val_dataset = random_split(
        dataset, [train_size, val_size],
        generator=torch.Generator().manual_seed(42)
    )
    
    print(f"📈 训练集: {len(train_dataset)} 样本")
    print(f"📊 验证集: {len(val_dataset)} 样本")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=args.num_workers,
        pin_memory=True if args.device == 'cuda' else False,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.num_workers,
        pin_memory=True if args.device == 'cuda' else False,
        drop_last=False
    ) if val_size > 0 else None
    
    return train_loader, val_loader


def main():
    """主函数"""
    args = parse_args()
    
    # 设置日志
    setup_logging(level=args.log_level)
    logger = logging.getLogger(__name__)
    
    print("🎵 Audio-to-Chart GAN 训练器")
    print("=" * 50)
    
    # 加载配置
    config = load_config(args.config)
    
    # 设置设备
    device = setup_device(args.device)
    
    # 创建保存目录
    save_dir = Path(args.save_dir)
    save_dir.mkdir(exist_ok=True)
    
    try:
        # 创建数据加载器
        train_loader, val_loader = create_data_loaders(args, config)
        
        # 创建模型
        print("🤖 创建GAN模型...")
        model = AudioChartGAN(
            track_count=args.track_count,
            max_length=args.max_length
        )
        
        # 创建训练器
        trainer = GANTrainer(
            model=model,
            device=device,
            lr_g=args.lr_g,
            lr_d=args.lr_d
        )
        
        # 恢复训练
        if args.resume:
            print(f"📂 恢复训练: {args.resume}")
            trainer.load_model(args.resume)
        
        # 显示模型信息
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"📊 模型参数:")
        print(f"   总参数: {total_params:,}")
        print(f"   可训练参数: {trainable_params:,}")
        print(f"   设备: {device}")
        print()
        
        # 开始训练
        print("🚀 开始训练...")
        trainer.train(
            train_dataloader=train_loader,
            val_dataloader=val_loader,
            num_epochs=args.epochs,
            save_dir=str(save_dir),
            save_interval=args.save_interval
        )
        
        # 保存最终模型
        final_model_path = save_dir / 'final_gan_model.pth'
        trainer.save_model(str(final_model_path))
        
        print(f"🎉 训练完成！模型已保存到: {final_model_path}")
        
        # 显示训练统计
        history = trainer.train_history
        if history['g_loss']:
            print(f"\n📈 训练统计:")
            print(f"   最终生成器损失: {history['g_loss'][-1]:.4f}")
            print(f"   最终判别器损失: {history['d_loss'][-1]:.4f}")
            print(f"   最终质量分数: {history['quality_score'][-1]:.2f}")
            print(f"   最终难度准确率: {history['difficulty_acc'][-1]:.3f}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
        
        # 保存中断时的模型
        interrupted_model_path = save_dir / 'interrupted_gan_model.pth'
        if 'trainer' in locals():
            trainer.save_model(str(interrupted_model_path))
            print(f"💾 已保存中断时的模型: {interrupted_model_path}")
        
    except Exception as e:
        logger.error(f"训练失败: {e}", exc_info=True)
        print(f"❌ 训练失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
