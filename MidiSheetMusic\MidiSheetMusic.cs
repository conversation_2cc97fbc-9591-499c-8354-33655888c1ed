/*
 * Copyright (c) 2007-2008 <PERSON><PERSON><PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License version 2.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

using System;
using System.IO;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;
using System.Collections.Generic;
using System.Threading;

namespace MidiSheetMusic
{


    /*
     * The MidiSheetMusic is the main window/form of the application.
     * The form supports the following menu commands
     *
     * Open 
     *     Open a midi file, read it into a MidiFile object, and create a
     *     SheetMusic child control based on the MidiFile.  The SheetMusic 
     *     control is then displayed.
     *
     * Close 
     *     Close the SheetMusic control.
     *
     * Save As Bitmaps
     *     Save the sheet music as bitmap images (one per page)
     *
     * Print Preview 
     *     Create a PrintPreview dialog and generate a preview of the SheetMusic.
     *     The SheetMusic.DoPrint() method handles the PrintPage callback function.
     *
     * Print 
     *     Create a PrintDialog to print the sheet music.  
     * 
     * Exit 
     *     Exit the application.
     *
     * Zoom
     *     Display the sheet music at the given zoom level
     *
     * Tracks
     *     Select which tracks of the Midi file to display
     *
     * Use One/Two Staffs
     *     Display the Midi tracks in one staff per track, or two staffs 
     *     for all tracks.
     *
     */

    public class MidiSheetMusic : Form
    {


        MidiFile midifile;        /* The MIDI file to display */
        SheetMusic sheetmusic;    /* The Control which displays the sheet music */
        PrintDocument printdoc;   /* The printer settings */
        int ToPage;               /* The last page number to print */
        int currentpage;          /* The current page we are printing */
        int[] zoomlevels;         /* The available zoom levels (percent) */

        MenuItem openmenu;        /* The menu items */
        MenuItem recordmenu;
        MenuItem savemenu;
        MenuItem closemenu;
        MenuItem previewmenu;
        MenuItem printmenu;
        MenuItem exitmenu;
        MenuItem trackmenu;
        MenuItem onestaffmenu;
        MenuItem twostaffmenu;
        MenuItem zoommenu;

        /* Create a new instance of this Form.  Create the menu.
         * The sheetmusic is the only child control of this form.
         * The sheetmusic is initially set to null.
         */
        public MidiSheetMusic()
        {

            Text = "Midi Sheet Music";
            Icon = new Icon(GetType(), "NotePair.ico");
            Size = new Size(SheetMusic.PageWidth / 2, SheetMusic.PageWidth / 2);
            BackColor = Color.Gray;


            CreateMenu();

            printdoc = new PrintDocument();
            printdoc.PrinterSettings.FromPage = 1;
            printdoc.PrinterSettings.ToPage = printdoc.PrinterSettings.MaximumPage;
            printdoc.PrintPage += new PrintPageEventHandler(PrintPage);

            DisableMenus();
            AutoScroll = true;
            ToPage = 0;
            currentpage = 1;
        }

        /* The Sheet Music needs to be redrawn. Either
         * - A new midifile was opened, in Open()
         * - A track number was selected/deselected, in TrackSelect()
         * - The "One/Two Staffs Per Track" menu was selected.
         */
        void RedrawSheetMusic()
        {
            if (sheetmusic != null)
            {
                /* Delete the older sheet music graphic */
                sheetmusic.Dispose();
            }

            /* Get the list of selected tracks from the Tracks menu */
            bool[] selected = new bool[midifile.TotalTracks + 1];
            for (int track = 1; track <= midifile.TotalTracks; track++)
            {
                selected[track] = trackmenu.MenuItems[track].Checked;
            }

            /* Create a new SheetMusic Control from the midifile */
            sheetmusic = new SheetMusic(midifile, selected, twostaffmenu.Checked);
            sheetmusic.SetZoom(GetZoom());
            sheetmusic.Parent = this;
            Size = new Size(sheetmusic.Width + 40, Size.Height);
            BackColor = Color.White;
        }


        /* Create the menu for this form. */
        void CreateMenu()
        {

            MainMenu menu = new MainMenu();
            MenuItem filemenu = new MenuItem("&File");

            openmenu = new MenuItem("&Open...");
            openmenu.Click += Openmenu_Click;

            recordmenu = new MenuItem("&Record...");
            recordmenu.Click += recordmenu_Click;

            closemenu = new MenuItem("&Close",
                             new EventHandler(Close),
                             Shortcut.CtrlC);

            savemenu = new MenuItem("&Save As Images...",
                                 new EventHandler(SaveImages),
                                 Shortcut.CtrlS);

            previewmenu = new MenuItem("Print Pre&view",
                                   new EventHandler(PrintPreview));

            printmenu = new MenuItem("&Print...",
                                 new EventHandler(Print),
                                 Shortcut.CtrlP);

            exitmenu = new MenuItem("E&xit", new EventHandler(Exit));


            filemenu.MenuItems.Add(openmenu);
            filemenu.MenuItems.Add(closemenu);
            filemenu.MenuItems.Add(savemenu);
            filemenu.MenuItems.Add("-");
            filemenu.MenuItems.Add(previewmenu);
            filemenu.MenuItems.Add(printmenu);
            filemenu.MenuItems.Add("-");
            filemenu.MenuItems.Add(exitmenu);

            trackmenu = new MenuItem("&Tracks");

            zoommenu = new MenuItem("&Zoom");
            zoomlevels = new int[] { 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160 };
            foreach (int level in zoomlevels)
            {
                MenuItem m = new MenuItem(level.ToString() + "%",
                                          new EventHandler(Zoom));
                m.RadioCheck = true;
                if (level == 100)
                    m.Checked = true;
                else
                    m.Checked = false;
                zoommenu.MenuItems.Add(m);
            }

            MenuItem helpmenu = new MenuItem("&Help");
            MenuItem about = new MenuItem("&About",
                                         new EventHandler(About));

            helpmenu.MenuItems.Add(about);

            menu.MenuItems.Add(filemenu);
            menu.MenuItems.Add(recordmenu);
            menu.MenuItems.Add(zoommenu);
            menu.MenuItems.Add(trackmenu);
            menu.MenuItems.Add(helpmenu);
            Menu = menu;

        }

        private void recordmenu_Click(object sender, EventArgs e)
        {
            RecordMidiInfo recordWindows = new RecordMidiInfo();
            recordWindows.Show();
        }

        private void Openmenu_Click(object sender, EventArgs e)
        {
            Open();
        }

        /* The callback function for the "Open..." menu.
         * When invoked this will
         * - Display a File dialog to select a midi filename.
         * - Read the midi file into a MidiFile instance.
         * - Create a SheetMusic control based on the MidiFile.
         * - Add the sheetmusic control to this form.
         * - Enable all the menu items.
         *
         * If any error occurs while reading the midi file,
         * display a MessageBox with the error message.
         */
        void Open()
        {
            OpenFileDialog dialog = new OpenFileDialog();
            dialog.Filter = "Midi Files (*.mid)|*.mid*|All Files (*.*)|*.*";
            dialog.FilterIndex = 1;
            dialog.RestoreDirectory = true;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    midifile = new MidiFile(dialog.FileName);
                    EnableMenus();
                    Text = Path.GetFileName(dialog.FileName) + " - Midi Sheet Music";
                    RedrawSheetMusic();
                }
                catch (MidiFileException)
                {
                    string filename = Path.GetFileName(dialog.FileName);
                    string message = "";
                    message += "MidiSheetMusic was unable to open the file "
                               + filename;
                    message += "\nIt does not appear to be a valid midi file.\n";

                    MessageBox.Show(message, "Error Opening File",
                                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                    midifile = null;
                    DisableMenus();
                }
                catch (System.IO.IOException e)
                {
                    string filename = Path.GetFileName(dialog.FileName);
                    string message = "";
                    message += "MidiSheetMusic was unable to open the file "
                               + filename;
                    message += "because:\n" + e.Message + "\n";

                    MessageBox.Show(message, "Error Opening File",
                                    MessageBoxButtons.OK, MessageBoxIcon.Error);

                    midifile = null;
                    DisableMenus();
                }
            }
        }

        /* The callback function for the "Close" menu.
         * When invoked this will 
         * - Dispose of the SheetMusic Control
         * - Disable the Close, Print Preview, and Print menus.
         */
        void Close(object obj, EventArgs args)
        {
            if (sheetmusic == null)
            {
                return;
            }
            sheetmusic.Dispose();
            sheetmusic = null;
            DisableMenus();
            BackColor = Color.Gray;
            Text = "Midi Sheet Music";
        }

        /* The callback function for the "Save As Images" menu.
         * When invoked this will save the sheet music as several
         * images, one per page.  For each page in the sheet music:
         * - Create a new bitmap, PageWidth by PageHeight
         * - Create a Graphics object for the bitmap
         * - Call the SheetMusic.DoPrint() method to draw the music
         *   onto the bitmap
         * - Save the bitmap to the file.
         */
        void SaveImages(object obj, EventArgs args)
        {
            if (sheetmusic == null)
            {
                return;
            }
            int numpages = sheetmusic.GetTotalPages();
            SaveFileDialog dialog = new SaveFileDialog();
            dialog.DefaultExt = "png";
            dialog.Filter = "PNG Image Files (*.png)|*.png";

            /* The initial filename in the dialog will be <midi filename>.png */
            string initname = midifile.FileName;
            if (initname.Substring(initname.Length - 4, 4) == ".mid")
            {
                initname = initname.Substring(0, initname.Length - 4);
            }
            initname = initname + ".png";
            dialog.FileName = initname;

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                string filename = dialog.FileName;
                if (filename.Substring(filename.Length - 4, 4) == ".png")
                {
                    filename = filename.Substring(0, filename.Length - 4);
                }
                try
                {
                    for (int page = 1; page <= numpages; page++)
                    {
                        Bitmap bitmap = new Bitmap(SheetMusic.PageWidth + 1,
                                                   SheetMusic.PageHeight + 1);
                        Graphics g = Graphics.FromImage(bitmap);
                        sheetmusic.DoPrint(g, page);
                        bitmap.Save(filename + page + ".png",
                                    System.Drawing.Imaging.ImageFormat.Png);
                        g.Dispose();
                        bitmap.Dispose();
                    }
                }
                catch (System.IO.IOException e)
                {
                    string message = "";
                    message += "MidiSheetMusic was unable to save to file " +
                                filename + ".png";
                    message += " because:\n" + e.Message + "\n";

                    MessageBox.Show(message, "Error Saving File",
                                    MessageBoxButtons.OK, MessageBoxIcon.Error);

                }
            }
        }



        /* The callback function for the "Print Preview" menu.
         * When invoked, this will spawn a PrintPreview dialog.
         * The dialog will then invoke the PrintPage() event
         * handler to actually render the pages.
         */
        void PrintPreview(object obj, EventArgs args)
        {
            currentpage = 1;
            ToPage = sheetmusic.GetTotalPages();
            PrintPreviewDialog dialog = new PrintPreviewDialog();
            dialog.Document = printdoc;
            dialog.ShowDialog();
        }

        /* The callback function for the "Print..." menu.
         * When invoked, this will spawn a Print dialog.
         * The dialog will then invoke the PrintPage() event
         * handler to actually render the pages.
         */
        void Print(object obj, EventArgs args)
        {
            PrintDialog dialog = new PrintDialog();
            dialog.Document = printdoc;
            dialog.AllowSomePages = true;
            dialog.PrinterSettings.MinimumPage = 1;
            dialog.PrinterSettings.MaximumPage = sheetmusic.GetTotalPages();
            dialog.PrinterSettings.FromPage = 1;
            dialog.PrinterSettings.ToPage = dialog.PrinterSettings.MaximumPage;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                if (dialog.PrinterSettings.PrintRange == PrintRange.AllPages)
                {
                    currentpage = 1;
                    ToPage = sheetmusic.GetTotalPages();
                }
                else
                {
                    currentpage = dialog.PrinterSettings.FromPage;
                    ToPage = dialog.PrinterSettings.ToPage;
                }
                try
                {
                    printdoc.Print();
                }
                catch (Exception) { }
            }
        }

        /* The callback function for the "Exit" menu.
         * Exit the application.
         */
        void Exit(object obj, EventArgs args)
        {
            this.Dispose();
            Application.Exit();
        }


        /* The callback function for the "Track <num>" menu items.
         * Update the checked status of the menu item.
         * Then, redraw the sheetmusic.
         */
        void TrackSelect(object obj, EventArgs args)
        {
            MenuItem m = (MenuItem)obj;
            m.Checked = !m.Checked;
            RedrawSheetMusic();
        }

        /* The callback function for the "One Staff per Track" menu.
         * Change the radio check mark on the One Staff/Two Staff menus,
         * then redraw the sheet music.
         */
        void UseOneStaff(object obj, EventArgs args)
        {
            onestaffmenu.Checked = true;
            twostaffmenu.Checked = false;
            RedrawSheetMusic();
        }


        /* The callback function for the "Combine/Split Into Two Staffs" menu.
         * Change the radio check mark on the One Staff/Two Staff menus,
         * then redraw the sheet music.
         */
        void UseTwoStaffs(object obj, EventArgs args)
        {
            twostaffmenu.Checked = true;
            onestaffmenu.Checked = false;
            RedrawSheetMusic();
        }

        /* The callback function for the "Zoom" menu.
         * Change the radio check mark for the zoom menu items.
         * Then, change the sheet music zoom level.
         */
        void Zoom(object obj, EventArgs args)
        {
            MenuItem m = (MenuItem)obj;
            if (m.Checked)
                return;

            for (int i = 0; i < zoomlevels.Length; i++)
            {
                zoommenu.MenuItems[i].Checked = false;
            }
            m.Checked = true;

            if (sheetmusic == null)
                return;
            else
                sheetmusic.SetZoom(GetZoom());

        }

        float GetZoom()
        {
            for (int i = 0; i < zoomlevels.Length; i++)
            {
                if (zoommenu.MenuItems[i].Checked)
                {
                    return zoomlevels[i] * 1.0f / 100f;
                }
            }
            return 0;
        }

        /* The callback function for the "About" menu.
         * Display the About dialog.
         */
        void About(object obj, EventArgs args)
        {
            Form aboutdialog = new Form();
            aboutdialog.Text = "About Midi Sheet Music";
            aboutdialog.FormBorderStyle = FormBorderStyle.FixedDialog;
            aboutdialog.MaximizeBox = false;
            aboutdialog.MinimizeBox = false;
            aboutdialog.ShowInTaskbar = false;

            Icon icon = new Icon(GetType(), "NotePair.ico");
            PictureBox box = new PictureBox();
            box.Image = icon.ToBitmap();
            box.Parent = aboutdialog;
            box.Location = new Point(20, 20);
            box.Width = icon.Width;
            box.Height = icon.Height;

            Label name = new Label();
            name.Text = "Midi Sheet Music";
            name.Parent = aboutdialog;
            name.Font = new Font("Arial", 16, FontStyle.Bold);
            name.Location = new Point(box.Location.X + box.Width +
                                      name.Font.Height,
                                      box.Location.Y);
            name.AutoSize = true;

            Label label = new Label();
            label.Text = "Version 1.0\nCopyright 2007-2008 Madhav Vaidyanathan\n";
            label.Font = new Font("Arial", label.Font.Size, FontStyle.Regular);
            label.Parent = aboutdialog;
            int y = Math.Max(box.Location.Y + box.Height,
                             name.Location.Y + name.Height);
            label.Location = new Point(box.Location.X, y + label.Font.Height * 2);
            label.AutoSize = true;

            aboutdialog.Width = Math.Max(name.Location.X + name.Width,
                                         label.Location.X + label.Width) +
                                label.Font.Height * 2;

            Button ok = new Button();
            ok.Text = "OK";
            ok.Parent = aboutdialog;
            ok.DialogResult = DialogResult.OK;
            ok.Width = ok.Font.Height * 3;
            int x = aboutdialog.Width / 2 - ok.Width / 2;
            ok.Location = new Point(x, label.Location.Y +
                                       label.Height + label.Font.Height);

            aboutdialog.Height = ok.Location.Y + ok.Height + 60;
            aboutdialog.ShowDialog();
        }

        /* The function which handles Print events from the Print Preview
         * and Print menus.  Determine which page we are printing,
         * and call the SheetMusic.DoPrint() method.  Check that the
         * Page settings are valid, and cancel the print job if they're
         * not.
         */
        void PrintPage(object obj, PrintPageEventArgs printevent)
        {
            if (sheetmusic == null)
            {
                printevent.Cancel = true;
                return;
            }

            Graphics g = printevent.Graphics;

            /* If the page is too small, abort the print job */
            if (g.VisibleClipBounds.Width < SheetMusic.PageWidth ||
                g.VisibleClipBounds.Height < SheetMusic.PageHeight)
            {

                printevent.Cancel = true;
                return;
            }

            sheetmusic.DoPrint(g, currentpage);
            currentpage++;
            if (currentpage > ToPage)
            {
                printevent.HasMorePages = false;
                currentpage = 1;
            }
            else
            {
                printevent.HasMorePages = true;
            }
        }

        /* Enable all menus once a MidiFile has been selected.
         * For the "Track" menu, add a menu item for each track
         * in the midi file.  Include the "sequence name" of each
         * track in the track menu.
         */
        void EnableMenus()
        {
            closemenu.Enabled = true;
            savemenu.Enabled = true;
            previewmenu.Enabled = true;
            printmenu.Enabled = true;
            trackmenu.Enabled = true;
            zoommenu.Enabled = true;

            for (int i = 0; i < zoomlevels.Length; i++)
            {
                zoommenu.MenuItems[i].Checked = false;
            }
            zoommenu.MenuItems[4].Checked = true;

            trackmenu.MenuItems.Clear();
            MenuItem m;

            m = new MenuItem("Choose Tracks to Display");
            m.Enabled = false;
            trackmenu.MenuItems.Add(m);

            for (int i = 0; i < midifile.TotalTracks; i++)
            {
                int num = i + 1;
                string name = midifile.GetTrack(num).Instrument;
                if (name != "")
                {
                    name = "   (" + name + ")";
                }
                m = new MenuItem("Track " + num + name,
                                  new EventHandler(TrackSelect));
                m.Checked = true;
                trackmenu.MenuItems.Add(m);

                /* In TrackSelect(), the MenuItem.Index tells which track 
                 * number the menu describes.
                 */
                MyAssert.Arg(m.Index == num, "Wrong menu index");
            }
            trackmenu.MenuItems.Add("-");
            onestaffmenu = new MenuItem("Show One Staff Per Track",
                                        new EventHandler(UseOneStaff));
            if (midifile.TotalTracks == 1)
            {
                twostaffmenu = new MenuItem("Split Track Into Two Staffs",
                                            new EventHandler(UseTwoStaffs));
                onestaffmenu.Checked = false;
                twostaffmenu.Checked = true;
            }
            else
            {
                twostaffmenu = new MenuItem("Combine All Tracks Into Two Staffs",
                                            new EventHandler(UseTwoStaffs));
                onestaffmenu.Checked = true;
                twostaffmenu.Checked = false;
            }
            onestaffmenu.RadioCheck = true;
            twostaffmenu.RadioCheck = true;
            trackmenu.MenuItems.Add(onestaffmenu);
            trackmenu.MenuItems.Add(twostaffmenu);
        }


        /* Disable certain menus if there is no MidiFile selected.  For
         * the Track menu, remove any sub-menus under the Track menu.
         */
        void DisableMenus()
        {
            closemenu.Enabled = false;
            savemenu.Enabled = false;
            previewmenu.Enabled = false;
            printmenu.Enabled = false;
            zoommenu.Enabled = false;
            trackmenu.Enabled = false;
            trackmenu.MenuItems.Clear();
        }

        /* The Main function for this SheetMusic application. */
        [STAThread]
        public static void Main(string[] argv)
        {
            MidiSheetMusic form = new MidiSheetMusic();
            Application.Run(form);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // MidiSheetMusic
            // 
            this.ClientSize = new System.Drawing.Size(284, 261);
            this.Name = "MidiSheetMusic";
            this.ResumeLayout(false);

        }
    }

}
