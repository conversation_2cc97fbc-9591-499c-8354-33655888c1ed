﻿<Window x:Class="RecordMusicalNote.BackGround.Idol_Main"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:RecordMusicalNote.BackGround"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="星动数据管理" Height="700" Width="1200" MinHeight="600" MinWidth="1000"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <Style x:Key="contentCenterStyle" TargetType="{x:Type TextBlock}">
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="30"></RowDefinition>
            <RowDefinition Height="2*"></RowDefinition>
            <RowDefinition Height="3*"></RowDefinition>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"></ColumnDefinition>
            <ColumnDefinition Width="7*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        <ToolBarTray Grid.Row="0" Grid.ColumnSpan="2">
            <ToolBar>
                <Button Height="25" Width="50" Content="刷新" Name="txtFresh"></Button>
                <Button Height="25" Width="50" Content="导入" Name="txtImport" ></Button>
            </ToolBar>
        </ToolBarTray>
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="1*"></RowDefinition>
                <RowDefinition Height="3*"></RowDefinition>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"></ColumnDefinition>
                <ColumnDefinition Width="2*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <ComboBox Width="60" Height="28" Name="_cmbSearchType" VerticalAlignment="Center">
                <ComboBoxItem>歌名</ComboBoxItem>
                <ComboBoxItem>歌手</ComboBoxItem>
                <ComboBoxItem>BPM</ComboBoxItem>
                <ComboBoxItem>星数</ComboBoxItem>
            </ComboBox>
            <TextBox Grid.Column="1" Width="100" Height="30" Name="txtSearchConent" VerticalContentAlignment="Center"></TextBox>
            <DataGrid Grid.Column="0" Grid.Row="1" Grid.ColumnSpan="2" Name="_searchDataGrid" AutoGenerateColumns="False" IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn  Header="歌名" Width="70" Binding="{Binding SongName}"></DataGridTextColumn>
                    <DataGridTextColumn  Header="艺人" Width="50" Binding="{Binding Artist}"></DataGridTextColumn>
                    <DataGridTextColumn  Header="BPM" Width="50" Binding="{Binding BPM}"></DataGridTextColumn>
                    <DataGridTextColumn  Header="星数" Width="40" Binding="{Binding Star}"></DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
        
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"></ColumnDefinition>
                <ColumnDefinition Width="2*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition></RowDefinition>
                <RowDefinition></RowDefinition>
                <RowDefinition></RowDefinition>
                <RowDefinition></RowDefinition>
                <RowDefinition></RowDefinition>
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Center">歌名:</Label>
            <TextBox Grid.Row="0" Grid.Column="1" Name="txtSongName" Width="100" Height="30" VerticalContentAlignment="Center" ></TextBox>
            <Label Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Center">艺人:</Label>
            <TextBox Grid.Row="1" Grid.Column="1" Name="txtArtists" Width="100" Height="30" VerticalContentAlignment="Center"></TextBox>
            <Label Grid.Row="2" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Center">BPM:</Label>
            <TextBox Grid.Row="2" Grid.Column="1" Name="txtBPM" Width="100" Height="30" IsReadOnly="True" VerticalContentAlignment="Center"></TextBox>
            <Label Grid.Row="3" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Center">轨数:</Label>
            <TextBox Grid.Row="3" Grid.Column="1" Name="txtTrackCount" Width="100" Height="30" IsReadOnly="True" VerticalContentAlignment="Center"></TextBox>
            <Label Grid.Row="4" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Center">星数:</Label>
            <TextBox Grid.Row="4" Grid.Column="1" Name="txtStar" Width="100" Height="30" IsReadOnly="True" VerticalContentAlignment="Center"></TextBox>
        </Grid>
        <DataGrid Grid.Row="1" Grid.Column="1" Grid.RowSpan="2" Name="_idolMainData" AutoGenerateColumns="False" 
                  VerticalContentAlignment="Center" 
                  HorizontalContentAlignment="Center" >
          
            <DataGrid.Columns>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="Bar" Binding="{Binding Bar}" Width="50"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="Pos" Binding="{Binding Pos}" Width="50"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="FromTrack" Binding="{Binding FromTrack}" Width="80"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="TargetTrack" Binding="{Binding TargetTrack}" Width="80"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="EndTrack" Binding="{Binding EndTrack}" Width="80"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="EndBar" Binding="{Binding EndBar}" Width="50"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="EndPos" Binding="{Binding EndPos}" Width="60"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="NoteType" Binding="{Binding NoteType}" Width="70"></DataGridTextColumn>
                <DataGridTextColumn  ElementStyle="{StaticResource contentCenterStyle}" Header="连接序号" Binding="{Binding CombineNoteNum}" Width="60"></DataGridTextColumn>
            </DataGrid.Columns>
            
            
        </DataGrid>
    </Grid>
</Window>
